{"version": 3, "file": "AdvancedModeration-db913f6a.js", "sources": ["../../src/components/admin/AdvancedModeration.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  Shield,\r\n  Ban,\r\n  CheckCircle,\r\n  XCircle,\r\n  AlertTriangle,\r\n  Filter,\r\n  Clock,\r\n  Music,\r\n  User,\r\n  Settings,\r\n  Plus,\r\n  Trash2,\r\n  Edit,\r\n  Save,\r\n  RefreshCw,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { useRestaurantContext } from \"../restaurant/RestaurantDashboard\";\r\nimport { buildApiUrl } from \"../../config/api\";\r\nimport { apiService } from \"../../services/api\";\r\n\r\ninterface ModerationRule {\r\n  id: string;\r\n  name: string;\r\n  type: \"blacklist\" | \"whitelist\" | \"duration\" | \"explicit\" | \"genre\";\r\n  value: string;\r\n  isActive: boolean;\r\n  createdAt: string;\r\n}\r\n\r\ninterface PendingSuggestion {\r\n  id: string;\r\n  title: string;\r\n  artist: string;\r\n  duration: string;\r\n  thumbnailUrl: string;\r\n  submittedBy: string;\r\n  submittedAt: string;\r\n  votes: number;\r\n  flags: string[];\r\n  riskLevel: \"low\" | \"medium\" | \"high\";\r\n  autoModerationResult?: {\r\n    action: \"approve\" | \"reject\" | \"review\";\r\n    reason: string;\r\n    confidence: number;\r\n  };\r\n}\r\n\r\nconst AdvancedModeration: React.FC = () => {\r\n  const { restaurantId } = useRestaurantContext();\r\n  const [pendingSuggestions, setPendingSuggestions] = useState<\r\n    PendingSuggestion[]\r\n  >([]);\r\n  const [moderationRules, setModerationRules] = useState<ModerationRule[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // Debug: Log do restaurantId\r\n  useEffect(() => {\r\n    console.log(\"🔍 AdvancedModeration - restaurantId:\", restaurantId);\r\n  }, [restaurantId]);\r\n  const [activeTab, setActiveTab] = useState<\"pending\" | \"rules\" | \"settings\">(\r\n    \"pending\"\r\n  );\r\n  const [newRule, setNewRule] = useState({\r\n    name: \"\",\r\n    type: \"blacklist\" as ModerationRule[\"type\"],\r\n    value: \"\",\r\n  });\r\n  const [showAddRule, setShowAddRule] = useState(false);\r\n\r\n  // Configurações de moderação\r\n  const [moderationSettings, setModerationSettings] = useState({\r\n    autoApprove: false,\r\n    autoReject: true,\r\n    requireManualReview: true,\r\n    maxDuration: 600, // 10 minutos\r\n    minVotesForAutoApproval: 10,\r\n    maxVotesForAutoRejection: -5,\r\n    explicitContentFilter: true,\r\n    workingHours: {\r\n      enabled: true,\r\n      start: \"11:00\",\r\n      end: \"23:00\",\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (restaurantId) {\r\n      loadPendingSuggestions();\r\n      loadModerationRules();\r\n    }\r\n  }, [restaurantId]);\r\n\r\n  const loadPendingSuggestions = async () => {\r\n    if (!restaurantId) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      console.log(\r\n        \"🔄 Carregando sugestões pendentes para moderação:\",\r\n        restaurantId\r\n      );\r\n\r\n      // Buscar sugestões pendentes de moderação\r\n      const url = buildApiUrl(`/suggestions/${restaurantId}`, {\r\n        status: \"pending\",\r\n        limit: \"50\",\r\n        page: \"1\",\r\n      });\r\n\r\n      const response = await fetch(url);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Erro ao carregar sugestões: ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      console.log(\"📡 Sugestões pendentes carregadas:\", data);\r\n\r\n      // Mapear sugestões para o formato esperado\r\n      const suggestions: PendingSuggestion[] =\r\n        data.suggestions?.map((suggestion: any) => ({\r\n          id: suggestion.id,\r\n          title: suggestion.title,\r\n          artist: suggestion.artist,\r\n          duration: formatDuration(suggestion.duration),\r\n          thumbnailUrl:\r\n            suggestion.thumbnailUrl ||\r\n            `https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`,\r\n          submittedBy:\r\n            suggestion.client_name ||\r\n            `Mesa ${suggestion.table_number}` ||\r\n            \"Cliente\",\r\n          submittedAt: suggestion.createdAt,\r\n          votes: suggestion.voteCount || 0,\r\n          flags: suggestion.moderationFlags || [],\r\n          riskLevel: calculateRiskLevel(suggestion),\r\n          autoModerationResult: suggestion.moderationResult || null,\r\n        })) || [];\r\n\r\n      setPendingSuggestions(suggestions);\r\n    } catch (error) {\r\n      console.error(\"❌ Erro ao carregar sugestões pendentes:\", error);\r\n      toast.error(\"Erro ao carregar sugestões pendentes\");\r\n      // Fallback para dados vazios em caso de erro\r\n      setPendingSuggestions([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Função auxiliar para calcular nível de risco\r\n  const calculateRiskLevel = (suggestion: any): \"low\" | \"medium\" | \"high\" => {\r\n    const flags = suggestion.moderationFlags || [];\r\n    const voteCount = suggestion.voteCount || 0;\r\n    const duration = suggestion.duration || 0;\r\n\r\n    if (\r\n      flags.includes(\"explicit\") ||\r\n      flags.includes(\"inappropriate\") ||\r\n      voteCount < -3\r\n    ) {\r\n      return \"high\";\r\n    }\r\n\r\n    if (duration > 600 || flags.length > 0 || voteCount < 0) {\r\n      return \"medium\";\r\n    }\r\n\r\n    return \"low\";\r\n  };\r\n\r\n  // Função auxiliar para formatar duração\r\n  const formatDuration = (seconds: number): string => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\r\n  };\r\n\r\n  const loadModerationRules = async () => {\r\n    if (!restaurantId) return;\r\n\r\n    try {\r\n      console.log(\"🔄 Carregando regras de moderação para:\", restaurantId);\r\n\r\n      // Para agora, vamos usar dados mock melhorados até implementar API completa\r\n      const mockRules: ModerationRule[] = [\r\n        {\r\n          id: \"1\",\r\n          name: \"Palavras Proibidas\",\r\n          type: \"blacklist\",\r\n          value: \"explicit,inappropriate,offensive\",\r\n          isActive: true,\r\n          createdAt: \"2024-01-10T10:00:00Z\",\r\n        },\r\n        {\r\n          id: \"2\",\r\n          name: \"Gêneros Permitidos\",\r\n          type: \"whitelist\",\r\n          value: \"rock,pop,jazz,classical,blues\",\r\n          isActive: true,\r\n          createdAt: \"2024-01-10T10:05:00Z\",\r\n        },\r\n        {\r\n          id: \"3\",\r\n          name: \"Duração Máxima\",\r\n          type: \"duration\",\r\n          value: \"600\",\r\n          isActive: true,\r\n          createdAt: \"2024-01-10T10:10:00Z\",\r\n        },\r\n      ];\r\n\r\n      setModerationRules(mockRules);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar regras:\", error);\r\n    }\r\n  };\r\n\r\n  const moderateSuggestion = async (\r\n    suggestionId: string,\r\n    action: \"approve\" | \"reject\",\r\n    reason?: string\r\n  ) => {\r\n    if (!restaurantId) return;\r\n\r\n    try {\r\n      console.log(`🔍 Moderando sugestão ${suggestionId}:`, action, reason);\r\n\r\n      const url = buildApiUrl(\r\n        `/suggestions/${restaurantId}/${suggestionId}/${action}`\r\n      );\r\n\r\n      const response = await fetch(url, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          reason:\r\n            reason ||\r\n            `Moderação ${\r\n              action === \"approve\" ? \"aprovada\" : \"rejeitada\"\r\n            } pelo administrador`,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Erro ao moderar sugestão: ${response.statusText}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      console.log(\"✅ Moderação realizada:\", result);\r\n\r\n      // Remover da lista de pendentes\r\n      setPendingSuggestions((prev) =>\r\n        prev.filter((s) => s.id !== suggestionId)\r\n      );\r\n      toast.success(\r\n        `Sugestão ${\r\n          action === \"approve\" ? \"aprovada\" : \"rejeitada\"\r\n        } com sucesso!`\r\n      );\r\n\r\n      // Recarregar lista para garantir sincronia\r\n      setTimeout(() => {\r\n        loadPendingSuggestions();\r\n      }, 1000);\r\n    } catch (error) {\r\n      console.error(\"❌ Erro ao moderar sugestão:\", error);\r\n      toast.error(\"Erro ao moderar sugestão\");\r\n    }\r\n  };\r\n\r\n  const addModerationRule = async () => {\r\n    if (!newRule.name.trim() || !newRule.value.trim()) {\r\n      toast.error(\"Nome e valor da regra são obrigatórios\");\r\n      return;\r\n    }\r\n\r\n    const rule: ModerationRule = {\r\n      id: Date.now().toString(),\r\n      name: newRule.name,\r\n      type: newRule.type,\r\n      value: newRule.value,\r\n      isActive: true,\r\n      createdAt: new Date().toISOString(),\r\n    };\r\n\r\n    setModerationRules((prev) => [...prev, rule]);\r\n    setNewRule({ name: \"\", type: \"blacklist\", value: \"\" });\r\n    setShowAddRule(false);\r\n    toast.success(\"Regra adicionada com sucesso!\");\r\n  };\r\n\r\n  const toggleRule = (ruleId: string) => {\r\n    setModerationRules((prev) =>\r\n      prev.map((rule) =>\r\n        rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule\r\n      )\r\n    );\r\n  };\r\n\r\n  const deleteRule = (ruleId: string) => {\r\n    setModerationRules((prev) => prev.filter((rule) => rule.id !== ruleId));\r\n    toast.success(\"Regra removida\");\r\n  };\r\n\r\n  const getRiskLevelColor = (level: string) => {\r\n    switch (level) {\r\n      case \"high\":\r\n        return \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\";\r\n      case \"medium\":\r\n        return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400\";\r\n      default:\r\n        return \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\";\r\n    }\r\n  };\r\n\r\n  const getActionColor = (action: string) => {\r\n    switch (action) {\r\n      case \"approve\":\r\n        return \"text-green-600 dark:text-green-400\";\r\n      case \"reject\":\r\n        return \"text-red-600 dark:text-red-400\";\r\n      default:\r\n        return \"text-yellow-600 dark:text-yellow-400\";\r\n    }\r\n  };\r\n\r\n  const renderPendingSuggestions = () => (\r\n    <div className=\"space-y-4\">\r\n      {pendingSuggestions.length === 0 ? (\r\n        <div className=\"text-center py-12\">\r\n          <Shield className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n          <p className=\"text-gray-600 dark:text-gray-400\">\r\n            Nenhuma sugestão pendente de moderação\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        pendingSuggestions.map((suggestion) => (\r\n          <motion.div\r\n            key={suggestion.id}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\"\r\n          >\r\n            <div className=\"flex items-start justify-between\">\r\n              <div className=\"flex items-start space-x-4\">\r\n                <img\r\n                  src={suggestion.thumbnailUrl}\r\n                  alt={suggestion.title}\r\n                  className=\"w-16 h-12 object-cover rounded\"\r\n                />\r\n\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                    {suggestion.title}\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {suggestion.artist} • {suggestion.duration}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                    {suggestion.submittedBy} •{\" \"}\r\n                    {new Date(suggestion.submittedAt).toLocaleString(\"pt-BR\")}\r\n                  </p>\r\n\r\n                  {suggestion.flags.length > 0 && (\r\n                    <div className=\"flex flex-wrap gap-1 mt-2\">\r\n                      {suggestion.flags.map((flag) => (\r\n                        <span\r\n                          key={flag}\r\n                          className=\"px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 text-xs rounded-full\"\r\n                        >\r\n                          {flag}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n\r\n                  {suggestion.autoModerationResult && (\r\n                    <div className=\"mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs\">\r\n                      <p\r\n                        className={`font-medium ${getActionColor(\r\n                          suggestion.autoModerationResult.action\r\n                        )}`}\r\n                      >\r\n                        Auto-moderação: {suggestion.autoModerationResult.action}\r\n                      </p>\r\n                      <p className=\"text-gray-600 dark:text-gray-400\">\r\n                        {suggestion.autoModerationResult.reason} (\r\n                        {suggestion.autoModerationResult.confidence}% confiança)\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-3\">\r\n                <span\r\n                  className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(\r\n                    suggestion.riskLevel\r\n                  )}`}\r\n                >\r\n                  {suggestion.riskLevel}\r\n                </span>\r\n\r\n                <div className=\"text-right\">\r\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n                    {suggestion.votes} votos\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-1\">\r\n                  <button\r\n                    onClick={() => moderateSuggestion(suggestion.id, \"approve\")}\r\n                    className=\"p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg\"\r\n                    title=\"Aprovar\"\r\n                  >\r\n                    <CheckCircle className=\"w-4 h-4\" />\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => moderateSuggestion(suggestion.id, \"reject\")}\r\n                    className=\"p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg\"\r\n                    title=\"Rejeitar\"\r\n                  >\r\n                    <XCircle className=\"w-4 h-4\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        ))\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  const renderModerationRules = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-semibold\">Regras de Moderação</h3>\r\n        <button\r\n          onClick={() => setShowAddRule(!showAddRule)}\r\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\r\n        >\r\n          <Plus className=\"w-4 h-4\" />\r\n          <span>Nova Regra</span>\r\n        </button>\r\n      </div>\r\n\r\n      {showAddRule && (\r\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Nome da regra\"\r\n              value={newRule.name}\r\n              onChange={(e) => setNewRule({ ...newRule, name: e.target.value })}\r\n              className=\"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800\"\r\n            />\r\n\r\n            <select\r\n              value={newRule.type}\r\n              onChange={(e) =>\r\n                setNewRule({ ...newRule, type: e.target.value as any })\r\n              }\r\n              className=\"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800\"\r\n            >\r\n              <option value=\"blacklist\">Lista Negra</option>\r\n              <option value=\"whitelist\">Lista Branca</option>\r\n              <option value=\"duration\">Duração</option>\r\n              <option value=\"explicit\">Conteúdo Explícito</option>\r\n              <option value=\"genre\">Gênero</option>\r\n            </select>\r\n\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Valor (separado por vírgulas)\"\r\n              value={newRule.value}\r\n              onChange={(e) =>\r\n                setNewRule({ ...newRule, value: e.target.value })\r\n              }\r\n              className=\"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex space-x-2\">\r\n            <button\r\n              onClick={addModerationRule}\r\n              className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2\"\r\n            >\r\n              <Save className=\"w-4 h-4\" />\r\n              <span>Salvar</span>\r\n            </button>\r\n            <button\r\n              onClick={() => setShowAddRule(false)}\r\n              className=\"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700\"\r\n            >\r\n              Cancelar\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-3\">\r\n        {moderationRules.map((rule) => (\r\n          <div\r\n            key={rule.id}\r\n            className=\"flex items-center justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg\"\r\n          >\r\n            <div className=\"flex-1\">\r\n              <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                {rule.name}\r\n              </h4>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Tipo: {rule.type} • Valor: {rule.value}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                Criada em {new Date(rule.createdAt).toLocaleDateString(\"pt-BR\")}\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={rule.isActive}\r\n                  onChange={() => toggleRule(rule.id)}\r\n                  className=\"sr-only peer\"\r\n                />\r\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n              </label>\r\n\r\n              <button\r\n                onClick={() => deleteRule(rule.id)}\r\n                className=\"p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg\"\r\n              >\r\n                <Trash2 className=\"w-4 h-4\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <h3 className=\"text-lg font-semibold\">Configurações de Moderação</h3>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Aprovação Automática\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Aprovar automaticamente sugestões com muitos votos positivos\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={moderationSettings.autoApprove}\r\n              onChange={(e) =>\r\n                setModerationSettings((prev) => ({\r\n                  ...prev,\r\n                  autoApprove: e.target.checked,\r\n                }))\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Rejeição Automática\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Rejeitar automaticamente sugestões com muitos votos negativos\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={moderationSettings.autoReject}\r\n              onChange={(e) =>\r\n                setModerationSettings((prev) => ({\r\n                  ...prev,\r\n                  autoReject: e.target.checked,\r\n                }))\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Votos para Aprovação Automática\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              value={moderationSettings.minVotesForAutoApproval}\r\n              onChange={(e) =>\r\n                setModerationSettings((prev) => ({\r\n                  ...prev,\r\n                  minVotesForAutoApproval: parseInt(e.target.value),\r\n                }))\r\n              }\r\n              className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700\"\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Duração Máxima (segundos)\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              value={moderationSettings.maxDuration}\r\n              onChange={(e) =>\r\n                setModerationSettings((prev) => ({\r\n                  ...prev,\r\n                  maxDuration: parseInt(e.target.value),\r\n                }))\r\n              }\r\n              className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Validação do contexto do restaurante\r\n  if (!restaurantId) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <div className=\"text-center\">\r\n          <AlertTriangle className=\"w-12 h-12 text-amber-500 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n            Restaurante não encontrado\r\n          </h3>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">\r\n            Selecione um restaurante para acessar a moderação.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            Moderação Avançada\r\n          </h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Sistema inteligente de moderação de conteúdo\r\n          </p>\r\n        </div>\r\n\r\n        <button\r\n          onClick={loadPendingSuggestions}\r\n          className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg\"\r\n        >\r\n          <RefreshCw className=\"w-4 h-4\" />\r\n        </button>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n        <nav className=\"flex space-x-8\">\r\n          {[\r\n            { id: \"pending\", name: \"Pendentes\", icon: Clock },\r\n            { id: \"rules\", name: \"Regras\", icon: Shield },\r\n            { id: \"settings\", name: \"Configurações\", icon: Settings },\r\n          ].map((tab) => (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => setActiveTab(tab.id as any)}\r\n              className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${\r\n                activeTab === tab.id\r\n                  ? \"border-blue-500 text-blue-600 dark:text-blue-400\"\r\n                  : \"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300\"\r\n              }`}\r\n            >\r\n              <tab.icon className=\"w-4 h-4\" />\r\n              <span>{tab.name}</span>\r\n            </button>\r\n          ))}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n        {activeTab === \"pending\" && renderPendingSuggestions()}\r\n        {activeTab === \"rules\" && renderModerationRules()}\r\n        {activeTab === \"settings\" && renderSettings()}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdvancedModeration;\r\n"], "names": ["AdvancedModeration", "restaurantId", "useRestaurantContext", "pendingSuggestions", "setPendingSuggestions", "useState", "moderationRules", "setModerationRules", "loading", "setLoading", "useEffect", "activeTab", "setActiveTab", "newRule", "setNewRule", "showAddRule", "setShowAddRule", "moderationSettings", "setModerationSettings", "loadPendingSuggestions", "loadModerationRules", "url", "buildApiUrl", "response", "data", "suggestions", "_a", "suggestion", "formatDuration", "calculateRiskLevel", "error", "toast", "flags", "voteCount", "duration", "seconds", "minutes", "remainingSeconds", "moderateSuggestion", "suggestionId", "action", "reason", "result", "prev", "s", "addModerationRule", "rule", "toggleRule", "ruleId", "deleteRule", "getRiskLevelColor", "level", "getActionColor", "renderPendingSuggestions", "jsx", "jsxs", "Shield", "motion", "flag", "CheckCircle", "XCircle", "renderModerationRules", "Plus", "e", "Save", "Trash2", "renderSettings", "RefreshCw", "Clock", "Settings", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "4QAmDA,MAAMA,GAA+B,IAAM,CACnC,KAAA,CAAE,aAAAC,GAAiBC,IACnB,CAACC,EAAoBC,CAAqB,EAAIC,EAAA,SAElD,CAAE,CAAA,EACE,CAACC,EAAiBC,CAAkB,EAAIF,EAAA,SAA2B,CAAE,CAAA,EACrE,CAACG,EAASC,CAAU,EAAIJ,WAAS,EAAK,EAG5CK,EAAAA,UAAU,IAAM,CACN,QAAA,IAAI,wCAAyCT,CAAY,CAAA,EAChE,CAACA,CAAY,CAAC,EACX,KAAA,CAACU,EAAWC,CAAY,EAAIP,EAAA,SAChC,SAAA,EAEI,CAACQ,EAASC,CAAU,EAAIT,WAAS,CACrC,KAAM,GACN,KAAM,YACN,MAAO,EAAA,CACR,EACK,CAACU,EAAaC,CAAc,EAAIX,WAAS,EAAK,EAG9C,CAACY,EAAoBC,CAAqB,EAAIb,WAAS,CAC3D,YAAa,GACb,WAAY,GACZ,oBAAqB,GACrB,YAAa,IACb,wBAAyB,GACzB,yBAA0B,GAC1B,sBAAuB,GACvB,aAAc,CACZ,QAAS,GACT,MAAO,QACP,IAAK,OACP,CAAA,CACD,EAEDK,EAAAA,UAAU,IAAM,CACVT,IACqBkB,IACHC,IACtB,EACC,CAACnB,CAAY,CAAC,EAEjB,MAAMkB,EAAyB,SAAY,OACzC,GAAKlB,EAEL,CAAAQ,EAAW,EAAI,EACX,GAAA,CACM,QAAA,IACN,oDACAR,CAAA,EAIF,MAAMoB,EAAMC,EAAY,gBAAgBrB,CAAY,GAAI,CACtD,OAAQ,UACR,MAAO,KACP,KAAM,GAAA,CACP,EAEKsB,EAAW,MAAM,MAAMF,CAAG,EAE5B,GAAA,CAACE,EAAS,GACZ,MAAM,IAAI,MAAM,+BAA+BA,EAAS,UAAU,EAAE,EAGhE,MAAAC,EAAO,MAAMD,EAAS,OACpB,QAAA,IAAI,qCAAsCC,CAAI,EAGtD,MAAMC,IACJC,EAAAF,EAAK,cAAL,YAAAE,EAAkB,IAAKC,IAAqB,CAC1C,GAAIA,EAAW,GACf,MAAOA,EAAW,MAClB,OAAQA,EAAW,OACnB,SAAUC,EAAeD,EAAW,QAAQ,EAC5C,aACEA,EAAW,cACX,8BAA8BA,EAAW,cAAc,iBACzD,YACEA,EAAW,aACX,QAAQA,EAAW,YAAY,IAC/B,UACF,YAAaA,EAAW,UACxB,MAAOA,EAAW,WAAa,EAC/B,MAAOA,EAAW,iBAAmB,CAAC,EACtC,UAAWE,EAAmBF,CAAU,EACxC,qBAAsBA,EAAW,kBAAoB,IAAA,MAChD,CAAA,EAETvB,EAAsBqB,CAAW,QAC1BK,EAAO,CACN,QAAA,MAAM,0CAA2CA,CAAK,EAC9DC,EAAM,MAAM,sCAAsC,EAElD3B,EAAsB,CAAE,CAAA,CAAA,QACxB,CACAK,EAAW,EAAK,CAClB,EAAA,EAIIoB,EAAsBF,GAA+C,CACnE,MAAAK,EAAQL,EAAW,iBAAmB,GACtCM,EAAYN,EAAW,WAAa,EACpCO,EAAWP,EAAW,UAAY,EAGtC,OAAAK,EAAM,SAAS,UAAU,GACzBA,EAAM,SAAS,eAAe,GAC9BC,EAAY,GAEL,OAGLC,EAAW,KAAOF,EAAM,OAAS,GAAKC,EAAY,EAC7C,SAGF,KAAA,EAIHL,EAAkBO,GAA4B,CAClD,MAAMC,EAAU,KAAK,MAAMD,EAAU,EAAE,EACjCE,EAAmBF,EAAU,GAC5B,MAAA,GAAGC,CAAO,IAAIC,EAAiB,WAAW,SAAS,EAAG,GAAG,CAAC,EAAA,EAG7DjB,EAAsB,SAAY,CACtC,GAAKnB,EAED,GAAA,CACM,QAAA,IAAI,0CAA2CA,CAAY,EA8BnEM,EA3BoC,CAClC,CACE,GAAI,IACJ,KAAM,qBACN,KAAM,YACN,MAAO,mCACP,SAAU,GACV,UAAW,sBACb,EACA,CACE,GAAI,IACJ,KAAM,qBACN,KAAM,YACN,MAAO,gCACP,SAAU,GACV,UAAW,sBACb,EACA,CACE,GAAI,IACJ,KAAM,iBACN,KAAM,WACN,MAAO,MACP,SAAU,GACV,UAAW,sBACb,CAAA,CAG0B,QACrBuB,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,CACjD,CAAA,EAGIQ,EAAqB,MACzBC,EACAC,EACAC,IACG,CACH,GAAKxC,EAED,GAAA,CACF,QAAQ,IAAI,yBAAyBsC,CAAY,IAAKC,EAAQC,CAAM,EAEpE,MAAMpB,EAAMC,EACV,gBAAgBrB,CAAY,IAAIsC,CAAY,IAAIC,CAAM,EAAA,EAGlDjB,EAAW,MAAM,MAAMF,EAAK,CAChC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CACnB,OACEoB,GACA,aACED,IAAW,UAAY,WAAa,WACtC,qBAAA,CACH,CAAA,CACF,EAEG,GAAA,CAACjB,EAAS,GACZ,MAAM,IAAI,MAAM,6BAA6BA,EAAS,UAAU,EAAE,EAG9D,MAAAmB,EAAS,MAAMnB,EAAS,OACtB,QAAA,IAAI,yBAA0BmB,CAAM,EAG5CtC,EAAuBuC,GACrBA,EAAK,OAAQC,GAAMA,EAAE,KAAOL,CAAY,CAAA,EAEpCR,EAAA,QACJ,YACES,IAAW,UAAY,WAAa,WACtC,eAAA,EAIF,WAAW,IAAM,CACQrB,KACtB,GAAI,QACAW,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,EAClDC,EAAM,MAAM,0BAA0B,CACxC,CAAA,EAGIc,EAAoB,SAAY,CAChC,GAAA,CAAChC,EAAQ,KAAK,KAAA,GAAU,CAACA,EAAQ,MAAM,OAAQ,CACjDkB,EAAM,MAAM,wCAAwC,EACpD,MACF,CAEA,MAAMe,EAAuB,CAC3B,GAAI,KAAK,IAAI,EAAE,SAAS,EACxB,KAAMjC,EAAQ,KACd,KAAMA,EAAQ,KACd,MAAOA,EAAQ,MACf,SAAU,GACV,UAAW,IAAI,KAAK,EAAE,YAAY,CAAA,EAGpCN,EAAoBoC,GAAS,CAAC,GAAGA,EAAMG,CAAI,CAAC,EAC5ChC,EAAW,CAAE,KAAM,GAAI,KAAM,YAAa,MAAO,GAAI,EACrDE,EAAe,EAAK,EACpBe,EAAM,QAAQ,+BAA+B,CAAA,EAGzCgB,EAAcC,GAAmB,CACrCzC,EAAoBoC,GAClBA,EAAK,IAAKG,GACRA,EAAK,KAAOE,EAAS,CAAE,GAAGF,EAAM,SAAU,CAACA,EAAK,QAAa,EAAAA,CAC/D,CAAA,CACF,EAGIG,EAAcD,GAAmB,CAClBzC,EAACoC,GAASA,EAAK,OAAQG,GAASA,EAAK,KAAOE,CAAM,CAAC,EACtEjB,EAAM,QAAQ,gBAAgB,CAAA,EAG1BmB,EAAqBC,GAAkB,CAC3C,OAAQA,EAAO,CACb,IAAK,OACI,MAAA,+DACT,IAAK,SACI,MAAA,2EACT,QACS,MAAA,sEACX,CAAA,EAGIC,EAAkBZ,GAAmB,CACzC,OAAQA,EAAQ,CACd,IAAK,UACI,MAAA,qCACT,IAAK,SACI,MAAA,iCACT,QACS,MAAA,sCACX,CAAA,EAGIa,EAA2B,IAC9BC,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAmBnD,EAAA,SAAW,EAC7BoD,OAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACD,EAAAA,IAAAE,EAAA,CAAO,UAAU,sCAAuC,CAAA,EACxDF,EAAA,IAAA,IAAA,CAAE,UAAU,mCAAmC,SAEhD,yCAAA,CAAA,CAAA,CACF,EAEAnD,EAAmB,IAAKwB,GACtB2B,EAAA,IAACG,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAU,6DAEV,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6BACb,SAAA,CAAAD,EAAA,IAAC,MAAA,CACC,IAAK3B,EAAW,aAChB,IAAKA,EAAW,MAChB,UAAU,gCAAA,CACZ,EAEA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAD,EAAA,IAAC,KAAG,CAAA,UAAU,4CACX,SAAA3B,EAAW,MACd,EACA4B,EAAAA,KAAC,IAAE,CAAA,UAAU,2CACV,SAAA,CAAW5B,EAAA,OAAO,MAAIA,EAAW,QAAA,EACpC,EACA4B,EAAAA,KAAC,IAAE,CAAA,UAAU,2CACV,SAAA,CAAW5B,EAAA,YAAY,KAAG,IAC1B,IAAI,KAAKA,EAAW,WAAW,EAAE,eAAe,OAAO,CAAA,EAC1D,EAECA,EAAW,MAAM,OAAS,GACxB2B,EAAA,IAAA,MAAA,CAAI,UAAU,4BACZ,SAAW3B,EAAA,MAAM,IAAK+B,GACrBJ,EAAA,IAAC,OAAA,CAEC,UAAU,8FAET,SAAAI,CAAA,EAHIA,CAKR,CAAA,EACH,EAGD/B,EAAW,sBACT4B,OAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAAAA,EAAA,KAAC,IAAA,CACC,UAAW,eAAeH,EACxBzB,EAAW,qBAAqB,MAAA,CACjC,GACF,SAAA,CAAA,mBACkBA,EAAW,qBAAqB,MAAA,CAAA,CACnD,EACA4B,EAAAA,KAAC,IAAE,CAAA,UAAU,mCACV,SAAA,CAAA5B,EAAW,qBAAqB,OAAO,KACvCA,EAAW,qBAAqB,WAAW,cAAA,EAC9C,CAAA,EACF,CAAA,EAEJ,CAAA,EACF,EAEA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAD,EAAA,IAAC,OAAA,CACC,UAAW,8CAA8CJ,EACvDvB,EAAW,SAAA,CACZ,GAEA,SAAWA,EAAA,SAAA,CACd,QAEC,MAAI,CAAA,UAAU,aACb,SAAC4B,EAAA,KAAA,IAAA,CAAE,UAAU,oDACV,SAAA,CAAW5B,EAAA,MAAM,QAAA,CAAA,CACpB,CACF,CAAA,EAEA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAD,EAAA,IAAC,SAAA,CACC,QAAS,IAAMhB,EAAmBX,EAAW,GAAI,SAAS,EAC1D,UAAU,8EACV,MAAM,UAEN,SAAA2B,EAAAA,IAACK,EAAY,CAAA,UAAU,SAAU,CAAA,CAAA,CACnC,EAEAL,EAAA,IAAC,SAAA,CACC,QAAS,IAAMhB,EAAmBX,EAAW,GAAI,QAAQ,EACzD,UAAU,wEACV,MAAM,WAEN,SAAA2B,EAAAA,IAACM,EAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAC/B,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,EAzFKjC,EAAW,EA2FnB,CAAA,CAEL,CAAA,EAGIkC,EAAwB,IAC3BN,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACD,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAmB,sBAAA,EACzDC,EAAA,KAAC,SAAA,CACC,QAAS,IAAMvC,EAAe,CAACD,CAAW,EAC1C,UAAU,4FAEV,SAAA,CAACuC,EAAAA,IAAAQ,EAAA,CAAK,UAAU,SAAU,CAAA,EAC1BR,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,CAAA,EACF,EAECvC,GACCwC,EAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAAAD,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,gBACZ,MAAOzC,EAAQ,KACf,SAAWkD,GAAMjD,EAAW,CAAE,GAAGD,EAAS,KAAMkD,EAAE,OAAO,MAAO,EAChE,UAAU,4FAAA,CACZ,EAEAR,EAAA,KAAC,SAAA,CACC,MAAO1C,EAAQ,KACf,SAAWkD,GACTjD,EAAW,CAAE,GAAGD,EAAS,KAAMkD,EAAE,OAAO,MAAc,EAExD,UAAU,6FAEV,SAAA,CAACT,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAW,cAAA,EACpCA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAY,eAAA,EACrCA,EAAA,IAAA,SAAA,CAAO,MAAM,WAAW,SAAO,UAAA,EAC/BA,EAAA,IAAA,SAAA,CAAO,MAAM,WAAW,SAAkB,qBAAA,EAC1CA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAM,SAAA,CAAA,CAAA,CAC9B,EAEAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,gCACZ,MAAOzC,EAAQ,MACf,SAAWkD,GACTjD,EAAW,CAAE,GAAGD,EAAS,MAAOkD,EAAE,OAAO,MAAO,EAElD,UAAU,4FAAA,CACZ,CAAA,EACF,EAEAR,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAASV,EACT,UAAU,8FAEV,SAAA,CAACS,EAAAA,IAAAU,EAAA,CAAK,UAAU,SAAU,CAAA,EAC1BV,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CACd,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMtC,EAAe,EAAK,EACnC,UAAU,gEACX,SAAA,UAAA,CAED,CAAA,EACF,CAAA,EACF,QAGD,MAAI,CAAA,UAAU,YACZ,SAAgBV,EAAA,IAAKwC,GACpBS,EAAA,KAAC,MAAA,CAEC,UAAU,yHAEV,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAAAD,EAAA,IAAC,KAAG,CAAA,UAAU,4CACX,SAAAR,EAAK,KACR,EACAS,EAAAA,KAAC,IAAE,CAAA,UAAU,2CAA2C,SAAA,CAAA,SAC/CT,EAAK,KAAK,aAAWA,EAAK,KAAA,EACnC,EACAS,EAAAA,KAAC,IAAE,CAAA,UAAU,2CAA2C,SAAA,CAAA,aAC3C,IAAI,KAAKT,EAAK,SAAS,EAAE,mBAAmB,OAAO,CAAA,EAChE,CAAA,EACF,EAEAS,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,mDACf,SAAA,CAAAD,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASR,EAAK,SACd,SAAU,IAAMC,EAAWD,EAAK,EAAE,EAClC,UAAU,cAAA,CACZ,EACAQ,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAML,EAAWH,EAAK,EAAE,EACjC,UAAU,wEAEV,SAAAQ,EAAAA,IAACW,EAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAC9B,CAAA,EACF,CAAA,CAAA,EAhCKnB,EAAK,EAkCb,CAAA,EACH,CACF,CAAA,CAAA,EAGIoB,EAAiB,IACpBX,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACD,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAA0B,6BAAA,EAEhEC,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,uBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,+DAAA,CAAA,EACF,EACAC,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAD,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASrC,EAAmB,YAC5B,SAAW8C,GACT7C,EAAuByB,IAAU,CAC/B,GAAGA,EACH,YAAaoB,EAAE,OAAO,OAAA,EACtB,EAEJ,UAAU,cAAA,CACZ,EACAT,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAC,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,sBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,gEAAA,CAAA,EACF,EACAC,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAD,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASrC,EAAmB,WAC5B,SAAW8C,GACT7C,EAAuByB,IAAU,CAC/B,GAAGA,EACH,WAAYoB,EAAE,OAAO,OAAA,EACrB,EAEJ,UAAU,cAAA,CACZ,EACAT,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAC,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,kCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOrC,EAAmB,wBAC1B,SAAW8C,GACT7C,EAAuByB,IAAU,CAC/B,GAAGA,EACH,wBAAyB,SAASoB,EAAE,OAAO,KAAK,CAAA,EAChD,EAEJ,UAAU,mGAAA,CACZ,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACT,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,4BAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOrC,EAAmB,YAC1B,SAAW8C,GACT7C,EAAuByB,IAAU,CAC/B,GAAGA,EACH,YAAa,SAASoB,EAAE,OAAO,KAAK,CAAA,EACpC,EAEJ,UAAU,mGAAA,CACZ,CAAA,EACF,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,EAIF,OAAK9D,EAiBHsD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAEjE,qBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,+CAAA,CAAA,EACF,EAEAA,EAAA,IAAC,SAAA,CACC,QAASnC,EACT,UAAU,2FAEV,SAAAmC,EAAAA,IAACa,EAAU,CAAA,UAAU,SAAU,CAAA,CAAA,CACjC,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gDACb,SAACb,EAAA,IAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CACC,CAAE,GAAI,UAAW,KAAM,YAAa,KAAMc,CAAM,EAChD,CAAE,GAAI,QAAS,KAAM,SAAU,KAAMZ,CAAO,EAC5C,CAAE,GAAI,WAAY,KAAM,gBAAiB,KAAMa,CAAS,CAAA,EACxD,IAAKC,GACLf,EAAA,KAAC,SAAA,CAEC,QAAS,IAAM3C,EAAa0D,EAAI,EAAS,EACzC,UAAW,0FACT3D,IAAc2D,EAAI,GACd,mDACA,kGACN,GAEA,SAAA,CAAAhB,EAAAA,IAACgB,EAAI,KAAJ,CAAS,UAAU,SAAU,CAAA,EAC9BhB,EAAAA,IAAC,OAAM,CAAA,SAAAgB,EAAI,IAAK,CAAA,CAAA,CAAA,EATXA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGAf,EAAAA,KAAC,MAAI,CAAA,UAAU,kDACZ,SAAA,CAAA5C,IAAc,WAAa0C,EAAyB,EACpD1C,IAAc,SAAWkD,EAAsB,EAC/ClD,IAAc,YAAcuD,EAAe,CAAA,EAC9C,CACF,CAAA,CAAA,QAhEG,MAAI,CAAA,UAAU,uCACb,SAACX,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACD,EAAAA,IAAAiB,EAAA,CAAc,UAAU,uCAAwC,CAAA,EAChEjB,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,6BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,mCAAmC,SAEhD,qDAAA,CAAA,CACF,CAAA,CACF,CAAA,CAwDN"}