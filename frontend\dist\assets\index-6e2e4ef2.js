var Wa=Object.defineProperty;var Ha=(r,t,s)=>t in r?Wa(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s;var de=(r,t,s)=>(Ha(r,typeof t!="symbol"?t+"":t,s),s);import{r as p,b as Ka,c as Ga,a as H,g as Yt}from"./vendor-66b0ef43.js";import{u as gt,L as Ke,R as Ws,a as z,b as Ya,c as Ze,N as Me,d as Ur,B as Ja}from"./router-f729e475.js";import{A as Re,m as V,L as ys,X as Qe,B as Ue,M as Hs,a as Ks,E as Gs,b as Jt,c as te,S as hr,P as be,d as Ge,e as Xa,T as ve,Q as ut,U as et,f as Ee,H as Ys,g as Za,h as qt,V as en,R as ue,i as Ye,j as mr,k as qr,l as tn,n as Br,o as Qr,p as sn,q as rn,D as Js,r as zr,s as lt,t as an,C as ye,u as Wr,v as nn,G as on,w as ln,x as Je,y as Hr,z as Kr,F as cn,I as qe,J as Tt,K as Be,W as dn,N as un,O as hn,Y as mn,Z as pn,_ as Gr,$ as fn,a0 as xn,a1 as pr,a2 as gn,a3 as yn,a4 as Yr,a5 as bn,a6 as vn,a7 as bs,a8 as jn,a9 as wn,aa as Nn,ab as kn,ac as Sn,ad as En,ae as Cn,af as Rn}from"./ui-a5f8f5f0.js";import{_ as _n,a as Pn,v as ls}from"./utils-08f61814.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))a(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&a(o)}).observe(document,{childList:!0,subtree:!0});function s(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(n){if(n.ep)return;n.ep=!0;const i=s(n);fetch(n.href,i)}})();var Jr={exports:{}},Xt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var An=p,Tn=Symbol.for("react.element"),In=Symbol.for("react.fragment"),On=Object.prototype.hasOwnProperty,Dn=An.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ln={key:!0,ref:!0,__self:!0,__source:!0};function Xr(r,t,s){var a,n={},i=null,o=null;s!==void 0&&(i=""+s),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(a in t)On.call(t,a)&&!Ln.hasOwnProperty(a)&&(n[a]=t[a]);if(r&&r.defaultProps)for(a in t=r.defaultProps,t)n[a]===void 0&&(n[a]=t[a]);return{$$typeof:Tn,type:r,key:i,ref:o,props:n,_owner:Dn.current}}Xt.Fragment=In;Xt.jsx=Xr;Xt.jsxs=Xr;Jr.exports=Xt;var e=Jr.exports,vs={},fr=Ka;vs.createRoot=fr.createRoot,vs.hydrateRoot=fr.hydrateRoot;function tt(r,t){r.prototype=Object.create(t.prototype),r.prototype.constructor=r,_n(r,t)}var st=function(){function r(){this.listeners=[]}var t=r.prototype;return t.subscribe=function(a){var n=this,i=a||function(){};return this.listeners.push(i),this.onSubscribe(),function(){n.listeners=n.listeners.filter(function(o){return o!==i}),n.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},r}();function q(){return q=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(r[a]=s[a])}return r},q.apply(null,arguments)}var Bt=typeof window>"u";function ne(){}function $n(r,t){return typeof r=="function"?r(t):r}function js(r){return typeof r=="number"&&r>=0&&r!==1/0}function Qt(r){return Array.isArray(r)?r:[r]}function Zr(r,t){return Math.max(r+(t||0)-Date.now(),0)}function It(r,t,s){return yt(r)?typeof t=="function"?q({},s,{queryKey:r,queryFn:t}):q({},t,{queryKey:r}):r}function Vn(r,t,s){return yt(r)?typeof t=="function"?q({},s,{mutationKey:r,mutationFn:t}):q({},t,{mutationKey:r}):typeof r=="function"?q({},t,{mutationFn:r}):q({},r)}function Te(r,t,s){return yt(r)?[q({},t,{queryKey:r}),s]:[r||{},t]}function Fn(r,t){if(r===!0&&t===!0||r==null&&t==null)return"all";if(r===!1&&t===!1)return"none";var s=r??!t;return s?"active":"inactive"}function xr(r,t){var s=r.active,a=r.exact,n=r.fetching,i=r.inactive,o=r.predicate,l=r.queryKey,c=r.stale;if(yt(l)){if(a){if(t.queryHash!==Xs(l,t.options))return!1}else if(!zt(t.queryKey,l))return!1}var u=Fn(s,i);if(u==="none")return!1;if(u!=="all"){var d=t.isActive();if(u==="active"&&!d||u==="inactive"&&d)return!1}return!(typeof c=="boolean"&&t.isStale()!==c||typeof n=="boolean"&&t.isFetching()!==n||o&&!o(t))}function gr(r,t){var s=r.exact,a=r.fetching,n=r.predicate,i=r.mutationKey;if(yt(i)){if(!t.options.mutationKey)return!1;if(s){if(Ve(t.options.mutationKey)!==Ve(i))return!1}else if(!zt(t.options.mutationKey,i))return!1}return!(typeof a=="boolean"&&t.state.status==="loading"!==a||n&&!n(t))}function Xs(r,t){var s=(t==null?void 0:t.queryKeyHashFn)||Ve;return s(r)}function Ve(r){var t=Qt(r);return Mn(t)}function Mn(r){return JSON.stringify(r,function(t,s){return ws(s)?Object.keys(s).sort().reduce(function(a,n){return a[n]=s[n],a},{}):s})}function zt(r,t){return ea(Qt(r),Qt(t))}function ea(r,t){return r===t?!0:typeof r!=typeof t?!1:r&&t&&typeof r=="object"&&typeof t=="object"?!Object.keys(t).some(function(s){return!ea(r[s],t[s])}):!1}function Wt(r,t){if(r===t)return r;var s=Array.isArray(r)&&Array.isArray(t);if(s||ws(r)&&ws(t)){for(var a=s?r.length:Object.keys(r).length,n=s?t:Object.keys(t),i=n.length,o=s?[]:{},l=0,c=0;c<i;c++){var u=s?c:n[c];o[u]=Wt(r[u],t[u]),o[u]===r[u]&&l++}return a===i&&l===a?r:o}return t}function Un(r,t){if(r&&!t||t&&!r)return!1;for(var s in r)if(r[s]!==t[s])return!1;return!0}function ws(r){if(!yr(r))return!1;var t=r.constructor;if(typeof t>"u")return!0;var s=t.prototype;return!(!yr(s)||!s.hasOwnProperty("isPrototypeOf"))}function yr(r){return Object.prototype.toString.call(r)==="[object Object]"}function yt(r){return typeof r=="string"||Array.isArray(r)}function qn(r){return new Promise(function(t){setTimeout(t,r)})}function br(r){Promise.resolve().then(r).catch(function(t){return setTimeout(function(){throw t})})}function ta(){if(typeof AbortController=="function")return new AbortController}var Bn=function(r){tt(t,r);function t(){var a;return a=r.call(this)||this,a.setup=function(n){var i;if(!Bt&&((i=window)!=null&&i.addEventListener)){var o=function(){return n()};return window.addEventListener("visibilitychange",o,!1),window.addEventListener("focus",o,!1),function(){window.removeEventListener("visibilitychange",o),window.removeEventListener("focus",o)}}},a}var s=t.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},s.setEventListener=function(n){var i,o=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(l){typeof l=="boolean"?o.setFocused(l):o.onFocus()})},s.setFocused=function(n){this.focused=n,n&&this.onFocus()},s.onFocus=function(){this.listeners.forEach(function(n){n()})},s.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(st),ct=new Bn,Qn=function(r){tt(t,r);function t(){var a;return a=r.call(this)||this,a.setup=function(n){var i;if(!Bt&&((i=window)!=null&&i.addEventListener)){var o=function(){return n()};return window.addEventListener("online",o,!1),window.addEventListener("offline",o,!1),function(){window.removeEventListener("online",o),window.removeEventListener("offline",o)}}},a}var s=t.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},s.setEventListener=function(n){var i,o=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(l){typeof l=="boolean"?o.setOnline(l):o.onOnline()})},s.setOnline=function(n){this.online=n,n&&this.onOnline()},s.onOnline=function(){this.listeners.forEach(function(n){n()})},s.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(st),Ot=new Qn;function zn(r){return Math.min(1e3*Math.pow(2,r),3e4)}function Ht(r){return typeof(r==null?void 0:r.cancel)=="function"}var sa=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function Dt(r){return r instanceof sa}var ra=function(t){var s=this,a=!1,n,i,o,l;this.abort=t.abort,this.cancel=function(x){return n==null?void 0:n(x)},this.cancelRetry=function(){a=!0},this.continueRetry=function(){a=!1},this.continue=function(){return i==null?void 0:i()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(x,h){o=x,l=h});var c=function(h){s.isResolved||(s.isResolved=!0,t.onSuccess==null||t.onSuccess(h),i==null||i(),o(h))},u=function(h){s.isResolved||(s.isResolved=!0,t.onError==null||t.onError(h),i==null||i(),l(h))},d=function(){return new Promise(function(h){i=h,s.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){i=void 0,s.isPaused=!1,t.onContinue==null||t.onContinue()})},m=function x(){if(!s.isResolved){var h;try{h=t.fn()}catch(f){h=Promise.reject(f)}n=function(y){if(!s.isResolved&&(u(new sa(y)),s.abort==null||s.abort(),Ht(h)))try{h.cancel()}catch{}},s.isTransportCancelable=Ht(h),Promise.resolve(h).then(c).catch(function(f){var y,k;if(!s.isResolved){var b=(y=t.retry)!=null?y:3,v=(k=t.retryDelay)!=null?k:zn,C=typeof v=="function"?v(s.failureCount,f):v,F=b===!0||typeof b=="number"&&s.failureCount<b||typeof b=="function"&&b(s.failureCount,f);if(a||!F){u(f);return}s.failureCount++,t.onFail==null||t.onFail(s.failureCount,f),qn(C).then(function(){if(!ct.isFocused()||!Ot.isOnline())return d()}).then(function(){a?u(f):x()})}})}};m()},Wn=function(){function r(){this.queue=[],this.transactions=0,this.notifyFn=function(s){s()},this.batchNotifyFn=function(s){s()}}var t=r.prototype;return t.batch=function(a){var n;this.transactions++;try{n=a()}finally{this.transactions--,this.transactions||this.flush()}return n},t.schedule=function(a){var n=this;this.transactions?this.queue.push(a):br(function(){n.notifyFn(a)})},t.batchCalls=function(a){var n=this;return function(){for(var i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];n.schedule(function(){a.apply(void 0,o)})}},t.flush=function(){var a=this,n=this.queue;this.queue=[],n.length&&br(function(){a.batchNotifyFn(function(){n.forEach(function(i){a.notifyFn(i)})})})},t.setNotifyFunction=function(a){this.notifyFn=a},t.setBatchNotifyFunction=function(a){this.batchNotifyFn=a},r}(),X=new Wn,aa=console;function Kt(){return aa}function Hn(r){aa=r}var Kn=function(){function r(s){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=s.defaultOptions,this.setOptions(s.options),this.observers=[],this.cache=s.cache,this.queryKey=s.queryKey,this.queryHash=s.queryHash,this.initialState=s.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=s.meta,this.scheduleGc()}var t=r.prototype;return t.setOptions=function(a){var n;this.options=q({},this.defaultOptions,a),this.meta=a==null?void 0:a.meta,this.cacheTime=Math.max(this.cacheTime||0,(n=this.options.cacheTime)!=null?n:5*60*1e3)},t.setDefaultOptions=function(a){this.defaultOptions=a},t.scheduleGc=function(){var a=this;this.clearGcTimeout(),js(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){a.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(a,n){var i,o,l=this.state.data,c=$n(a,l);return(i=(o=this.options).isDataEqual)!=null&&i.call(o,l,c)?c=l:this.options.structuralSharing!==!1&&(c=Wt(l,c)),this.dispatch({data:c,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt}),c},t.setState=function(a,n){this.dispatch({type:"setState",state:a,setStateOptions:n})},t.cancel=function(a){var n,i=this.promise;return(n=this.retryer)==null||n.cancel(a),i?i.then(ne).catch(ne):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(a){return a.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(a){return a.getCurrentResult().isStale})},t.isStaleByTime=function(a){return a===void 0&&(a=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Zr(this.state.dataUpdatedAt,a)},t.onFocus=function(){var a,n=this.observers.find(function(i){return i.shouldFetchOnWindowFocus()});n&&n.refetch(),(a=this.retryer)==null||a.continue()},t.onOnline=function(){var a,n=this.observers.find(function(i){return i.shouldFetchOnReconnect()});n&&n.refetch(),(a=this.retryer)==null||a.continue()},t.addObserver=function(a){this.observers.indexOf(a)===-1&&(this.observers.push(a),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:a}))},t.removeObserver=function(a){this.observers.indexOf(a)!==-1&&(this.observers=this.observers.filter(function(n){return n!==a}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:a}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(a,n){var i=this,o,l,c;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return(u=this.retryer)==null||u.continueRetry(),this.promise}}if(a&&this.setOptions(a),!this.options.queryFn){var d=this.observers.find(function(v){return v.options.queryFn});d&&this.setOptions(d.options)}var m=Qt(this.queryKey),x=ta(),h={queryKey:m,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(x)return i.abortSignalConsumed=!0,x.signal}});var f=function(){return i.options.queryFn?(i.abortSignalConsumed=!1,i.options.queryFn(h)):Promise.reject("Missing queryFn")},y={fetchOptions:n,options:this.options,queryKey:m,state:this.state,fetchFn:f,meta:this.meta};if((o=this.options.behavior)!=null&&o.onFetch){var k;(k=this.options.behavior)==null||k.onFetch(y)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((l=y.fetchOptions)==null?void 0:l.meta)){var b;this.dispatch({type:"fetch",meta:(b=y.fetchOptions)==null?void 0:b.meta})}return this.retryer=new ra({fn:y.fetchFn,abort:x==null||(c=x.abort)==null?void 0:c.bind(x),onSuccess:function(C){i.setData(C),i.cache.config.onSuccess==null||i.cache.config.onSuccess(C,i),i.cacheTime===0&&i.optionalRemove()},onError:function(C){Dt(C)&&C.silent||i.dispatch({type:"error",error:C}),Dt(C)||(i.cache.config.onError==null||i.cache.config.onError(C,i),Kt().error(C)),i.cacheTime===0&&i.optionalRemove()},onFail:function(){i.dispatch({type:"failed"})},onPause:function(){i.dispatch({type:"pause"})},onContinue:function(){i.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(a){var n=this;this.state=this.reducer(this.state,a),X.batch(function(){n.observers.forEach(function(i){i.onQueryUpdate(a)}),n.cache.notify({query:n,type:"queryUpdated",action:a})})},t.getDefaultState=function(a){var n=typeof a.initialData=="function"?a.initialData():a.initialData,i=typeof a.initialData<"u",o=i?typeof a.initialDataUpdatedAt=="function"?a.initialDataUpdatedAt():a.initialDataUpdatedAt:0,l=typeof n<"u";return{data:n,dataUpdateCount:0,dataUpdatedAt:l?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:l?"success":"idle"}},t.reducer=function(a,n){var i,o;switch(n.type){case"failed":return q({},a,{fetchFailureCount:a.fetchFailureCount+1});case"pause":return q({},a,{isPaused:!0});case"continue":return q({},a,{isPaused:!1});case"fetch":return q({},a,{fetchFailureCount:0,fetchMeta:(i=n.meta)!=null?i:null,isFetching:!0,isPaused:!1},!a.dataUpdatedAt&&{error:null,status:"loading"});case"success":return q({},a,{data:n.data,dataUpdateCount:a.dataUpdateCount+1,dataUpdatedAt:(o=n.dataUpdatedAt)!=null?o:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var l=n.error;return Dt(l)&&l.revert&&this.revertState?q({},this.revertState):q({},a,{error:l,errorUpdateCount:a.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:a.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return q({},a,{isInvalidated:!0});case"setState":return q({},a,n.state);default:return a}},r}(),Gn=function(r){tt(t,r);function t(a){var n;return n=r.call(this)||this,n.config=a||{},n.queries=[],n.queriesMap={},n}var s=t.prototype;return s.build=function(n,i,o){var l,c=i.queryKey,u=(l=i.queryHash)!=null?l:Xs(c,i),d=this.get(u);return d||(d=new Kn({cache:this,queryKey:c,queryHash:u,options:n.defaultQueryOptions(i),state:o,defaultOptions:n.getQueryDefaults(c),meta:i.meta}),this.add(d)),d},s.add=function(n){this.queriesMap[n.queryHash]||(this.queriesMap[n.queryHash]=n,this.queries.push(n),this.notify({type:"queryAdded",query:n}))},s.remove=function(n){var i=this.queriesMap[n.queryHash];i&&(n.destroy(),this.queries=this.queries.filter(function(o){return o!==n}),i===n&&delete this.queriesMap[n.queryHash],this.notify({type:"queryRemoved",query:n}))},s.clear=function(){var n=this;X.batch(function(){n.queries.forEach(function(i){n.remove(i)})})},s.get=function(n){return this.queriesMap[n]},s.getAll=function(){return this.queries},s.find=function(n,i){var o=Te(n,i),l=o[0];return typeof l.exact>"u"&&(l.exact=!0),this.queries.find(function(c){return xr(l,c)})},s.findAll=function(n,i){var o=Te(n,i),l=o[0];return Object.keys(l).length>0?this.queries.filter(function(c){return xr(l,c)}):this.queries},s.notify=function(n){var i=this;X.batch(function(){i.listeners.forEach(function(o){o(n)})})},s.onFocus=function(){var n=this;X.batch(function(){n.queries.forEach(function(i){i.onFocus()})})},s.onOnline=function(){var n=this;X.batch(function(){n.queries.forEach(function(i){i.onOnline()})})},t}(st),Yn=function(){function r(s){this.options=q({},s.defaultOptions,s.options),this.mutationId=s.mutationId,this.mutationCache=s.mutationCache,this.observers=[],this.state=s.state||na(),this.meta=s.meta}var t=r.prototype;return t.setState=function(a){this.dispatch({type:"setState",state:a})},t.addObserver=function(a){this.observers.indexOf(a)===-1&&this.observers.push(a)},t.removeObserver=function(a){this.observers=this.observers.filter(function(n){return n!==a})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(ne).catch(ne)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var a=this,n,i=this.state.status==="loading",o=Promise.resolve();return i||(this.dispatch({type:"loading",variables:this.options.variables}),o=o.then(function(){a.mutationCache.config.onMutate==null||a.mutationCache.config.onMutate(a.state.variables,a)}).then(function(){return a.options.onMutate==null?void 0:a.options.onMutate(a.state.variables)}).then(function(l){l!==a.state.context&&a.dispatch({type:"loading",context:l,variables:a.state.variables})})),o.then(function(){return a.executeMutation()}).then(function(l){n=l,a.mutationCache.config.onSuccess==null||a.mutationCache.config.onSuccess(n,a.state.variables,a.state.context,a)}).then(function(){return a.options.onSuccess==null?void 0:a.options.onSuccess(n,a.state.variables,a.state.context)}).then(function(){return a.options.onSettled==null?void 0:a.options.onSettled(n,null,a.state.variables,a.state.context)}).then(function(){return a.dispatch({type:"success",data:n}),n}).catch(function(l){return a.mutationCache.config.onError==null||a.mutationCache.config.onError(l,a.state.variables,a.state.context,a),Kt().error(l),Promise.resolve().then(function(){return a.options.onError==null?void 0:a.options.onError(l,a.state.variables,a.state.context)}).then(function(){return a.options.onSettled==null?void 0:a.options.onSettled(void 0,l,a.state.variables,a.state.context)}).then(function(){throw a.dispatch({type:"error",error:l}),l})})},t.executeMutation=function(){var a=this,n;return this.retryer=new ra({fn:function(){return a.options.mutationFn?a.options.mutationFn(a.state.variables):Promise.reject("No mutationFn found")},onFail:function(){a.dispatch({type:"failed"})},onPause:function(){a.dispatch({type:"pause"})},onContinue:function(){a.dispatch({type:"continue"})},retry:(n=this.options.retry)!=null?n:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(a){var n=this;this.state=Jn(this.state,a),X.batch(function(){n.observers.forEach(function(i){i.onMutationUpdate(a)}),n.mutationCache.notify(n)})},r}();function na(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function Jn(r,t){switch(t.type){case"failed":return q({},r,{failureCount:r.failureCount+1});case"pause":return q({},r,{isPaused:!0});case"continue":return q({},r,{isPaused:!1});case"loading":return q({},r,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return q({},r,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return q({},r,{data:void 0,error:t.error,failureCount:r.failureCount+1,isPaused:!1,status:"error"});case"setState":return q({},r,t.state);default:return r}}var Xn=function(r){tt(t,r);function t(a){var n;return n=r.call(this)||this,n.config=a||{},n.mutations=[],n.mutationId=0,n}var s=t.prototype;return s.build=function(n,i,o){var l=new Yn({mutationCache:this,mutationId:++this.mutationId,options:n.defaultMutationOptions(i),state:o,defaultOptions:i.mutationKey?n.getMutationDefaults(i.mutationKey):void 0,meta:i.meta});return this.add(l),l},s.add=function(n){this.mutations.push(n),this.notify(n)},s.remove=function(n){this.mutations=this.mutations.filter(function(i){return i!==n}),n.cancel(),this.notify(n)},s.clear=function(){var n=this;X.batch(function(){n.mutations.forEach(function(i){n.remove(i)})})},s.getAll=function(){return this.mutations},s.find=function(n){return typeof n.exact>"u"&&(n.exact=!0),this.mutations.find(function(i){return gr(n,i)})},s.findAll=function(n){return this.mutations.filter(function(i){return gr(n,i)})},s.notify=function(n){var i=this;X.batch(function(){i.listeners.forEach(function(o){o(n)})})},s.onFocus=function(){this.resumePausedMutations()},s.onOnline=function(){this.resumePausedMutations()},s.resumePausedMutations=function(){var n=this.mutations.filter(function(i){return i.state.isPaused});return X.batch(function(){return n.reduce(function(i,o){return i.then(function(){return o.continue().catch(ne)})},Promise.resolve())})},t}(st);function Zn(){return{onFetch:function(t){t.fetchFn=function(){var s,a,n,i,o,l,c=(s=t.fetchOptions)==null||(a=s.meta)==null?void 0:a.refetchPage,u=(n=t.fetchOptions)==null||(i=n.meta)==null?void 0:i.fetchMore,d=u==null?void 0:u.pageParam,m=(u==null?void 0:u.direction)==="forward",x=(u==null?void 0:u.direction)==="backward",h=((o=t.state.data)==null?void 0:o.pages)||[],f=((l=t.state.data)==null?void 0:l.pageParams)||[],y=ta(),k=y==null?void 0:y.signal,b=f,v=!1,C=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},F=function(R,A,_,S){return b=S?[A].concat(b):[].concat(b,[A]),S?[_].concat(R):[].concat(R,[_])},M=function(R,A,_,S){if(v)return Promise.reject("Cancelled");if(typeof _>"u"&&!A&&R.length)return Promise.resolve(R);var O={queryKey:t.queryKey,signal:k,pageParam:_,meta:t.meta},G=C(O),J=Promise.resolve(G).then(function(Ne){return F(R,_,Ne,S)});if(Ht(G)){var he=J;he.cancel=G.cancel}return J},L;if(!h.length)L=M([]);else if(m){var j=typeof d<"u",D=j?d:vr(t.options,h);L=M(h,j,D)}else if(x){var N=typeof d<"u",T=N?d:ei(t.options,h);L=M(h,N,T,!0)}else(function(){b=[];var g=typeof t.options.getNextPageParam>"u",R=c&&h[0]?c(h[0],0,h):!0;L=R?M([],g,f[0]):Promise.resolve(F([],f[0],h[0]));for(var A=function(O){L=L.then(function(G){var J=c&&h[O]?c(h[O],O,h):!0;if(J){var he=g?f[O]:vr(t.options,G);return M(G,g,he)}return Promise.resolve(F(G,f[O],h[O]))})},_=1;_<h.length;_++)A(_)})();var E=L.then(function(g){return{pages:g,pageParams:b}}),P=E;return P.cancel=function(){v=!0,y==null||y.abort(),Ht(L)&&L.cancel()},E}}}}function vr(r,t){return r.getNextPageParam==null?void 0:r.getNextPageParam(t[t.length-1],t)}function ei(r,t){return r.getPreviousPageParam==null?void 0:r.getPreviousPageParam(t[0],t)}var ti=function(){function r(s){s===void 0&&(s={}),this.queryCache=s.queryCache||new Gn,this.mutationCache=s.mutationCache||new Xn,this.defaultOptions=s.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=r.prototype;return t.mount=function(){var a=this;this.unsubscribeFocus=ct.subscribe(function(){ct.isFocused()&&Ot.isOnline()&&(a.mutationCache.onFocus(),a.queryCache.onFocus())}),this.unsubscribeOnline=Ot.subscribe(function(){ct.isFocused()&&Ot.isOnline()&&(a.mutationCache.onOnline(),a.queryCache.onOnline())})},t.unmount=function(){var a,n;(a=this.unsubscribeFocus)==null||a.call(this),(n=this.unsubscribeOnline)==null||n.call(this)},t.isFetching=function(a,n){var i=Te(a,n),o=i[0];return o.fetching=!0,this.queryCache.findAll(o).length},t.isMutating=function(a){return this.mutationCache.findAll(q({},a,{fetching:!0})).length},t.getQueryData=function(a,n){var i;return(i=this.queryCache.find(a,n))==null?void 0:i.state.data},t.getQueriesData=function(a){return this.getQueryCache().findAll(a).map(function(n){var i=n.queryKey,o=n.state,l=o.data;return[i,l]})},t.setQueryData=function(a,n,i){var o=It(a),l=this.defaultQueryOptions(o);return this.queryCache.build(this,l).setData(n,i)},t.setQueriesData=function(a,n,i){var o=this;return X.batch(function(){return o.getQueryCache().findAll(a).map(function(l){var c=l.queryKey;return[c,o.setQueryData(c,n,i)]})})},t.getQueryState=function(a,n){var i;return(i=this.queryCache.find(a,n))==null?void 0:i.state},t.removeQueries=function(a,n){var i=Te(a,n),o=i[0],l=this.queryCache;X.batch(function(){l.findAll(o).forEach(function(c){l.remove(c)})})},t.resetQueries=function(a,n,i){var o=this,l=Te(a,n,i),c=l[0],u=l[1],d=this.queryCache,m=q({},c,{active:!0});return X.batch(function(){return d.findAll(c).forEach(function(x){x.reset()}),o.refetchQueries(m,u)})},t.cancelQueries=function(a,n,i){var o=this,l=Te(a,n,i),c=l[0],u=l[1],d=u===void 0?{}:u;typeof d.revert>"u"&&(d.revert=!0);var m=X.batch(function(){return o.queryCache.findAll(c).map(function(x){return x.cancel(d)})});return Promise.all(m).then(ne).catch(ne)},t.invalidateQueries=function(a,n,i){var o,l,c,u=this,d=Te(a,n,i),m=d[0],x=d[1],h=q({},m,{active:(o=(l=m.refetchActive)!=null?l:m.active)!=null?o:!0,inactive:(c=m.refetchInactive)!=null?c:!1});return X.batch(function(){return u.queryCache.findAll(m).forEach(function(f){f.invalidate()}),u.refetchQueries(h,x)})},t.refetchQueries=function(a,n,i){var o=this,l=Te(a,n,i),c=l[0],u=l[1],d=X.batch(function(){return o.queryCache.findAll(c).map(function(x){return x.fetch(void 0,q({},u,{meta:{refetchPage:c==null?void 0:c.refetchPage}}))})}),m=Promise.all(d).then(ne);return u!=null&&u.throwOnError||(m=m.catch(ne)),m},t.fetchQuery=function(a,n,i){var o=It(a,n,i),l=this.defaultQueryOptions(o);typeof l.retry>"u"&&(l.retry=!1);var c=this.queryCache.build(this,l);return c.isStaleByTime(l.staleTime)?c.fetch(l):Promise.resolve(c.state.data)},t.prefetchQuery=function(a,n,i){return this.fetchQuery(a,n,i).then(ne).catch(ne)},t.fetchInfiniteQuery=function(a,n,i){var o=It(a,n,i);return o.behavior=Zn(),this.fetchQuery(o)},t.prefetchInfiniteQuery=function(a,n,i){return this.fetchInfiniteQuery(a,n,i).then(ne).catch(ne)},t.cancelMutations=function(){var a=this,n=X.batch(function(){return a.mutationCache.getAll().map(function(i){return i.cancel()})});return Promise.all(n).then(ne).catch(ne)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(a){return this.mutationCache.build(this,a).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(a){this.defaultOptions=a},t.setQueryDefaults=function(a,n){var i=this.queryDefaults.find(function(o){return Ve(a)===Ve(o.queryKey)});i?i.defaultOptions=n:this.queryDefaults.push({queryKey:a,defaultOptions:n})},t.getQueryDefaults=function(a){var n;return a?(n=this.queryDefaults.find(function(i){return zt(a,i.queryKey)}))==null?void 0:n.defaultOptions:void 0},t.setMutationDefaults=function(a,n){var i=this.mutationDefaults.find(function(o){return Ve(a)===Ve(o.mutationKey)});i?i.defaultOptions=n:this.mutationDefaults.push({mutationKey:a,defaultOptions:n})},t.getMutationDefaults=function(a){var n;return a?(n=this.mutationDefaults.find(function(i){return zt(a,i.mutationKey)}))==null?void 0:n.defaultOptions:void 0},t.defaultQueryOptions=function(a){if(a!=null&&a._defaulted)return a;var n=q({},this.defaultOptions.queries,this.getQueryDefaults(a==null?void 0:a.queryKey),a,{_defaulted:!0});return!n.queryHash&&n.queryKey&&(n.queryHash=Xs(n.queryKey,n)),n},t.defaultQueryObserverOptions=function(a){return this.defaultQueryOptions(a)},t.defaultMutationOptions=function(a){return a!=null&&a._defaulted?a:q({},this.defaultOptions.mutations,this.getMutationDefaults(a==null?void 0:a.mutationKey),a,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},r}(),si=function(r){tt(t,r);function t(a,n){var i;return i=r.call(this)||this,i.client=a,i.options=n,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(n),i}var s=t.prototype;return s.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},s.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),jr(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},s.onUnsubscribe=function(){this.listeners.length||this.destroy()},s.shouldFetchOnReconnect=function(){return Ns(this.currentQuery,this.options,this.options.refetchOnReconnect)},s.shouldFetchOnWindowFocus=function(){return Ns(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},s.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},s.setOptions=function(n,i){var o=this.options,l=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(n),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=o.queryKey),this.updateQuery();var c=this.hasListeners();c&&wr(this.currentQuery,l,this.options,o)&&this.executeFetch(),this.updateResult(i),c&&(this.currentQuery!==l||this.options.enabled!==o.enabled||this.options.staleTime!==o.staleTime)&&this.updateStaleTimeout();var u=this.computeRefetchInterval();c&&(this.currentQuery!==l||this.options.enabled!==o.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)},s.getOptimisticResult=function(n){var i=this.client.defaultQueryObserverOptions(n),o=this.client.getQueryCache().build(this.client,i);return this.createResult(o,i)},s.getCurrentResult=function(){return this.currentResult},s.trackResult=function(n,i){var o=this,l={},c=function(d){o.trackedProps.includes(d)||o.trackedProps.push(d)};return Object.keys(n).forEach(function(u){Object.defineProperty(l,u,{configurable:!1,enumerable:!0,get:function(){return c(u),n[u]}})}),(i.useErrorBoundary||i.suspense)&&c("error"),l},s.getNextResult=function(n){var i=this;return new Promise(function(o,l){var c=i.subscribe(function(u){u.isFetching||(c(),u.isError&&(n!=null&&n.throwOnError)?l(u.error):o(u))})})},s.getCurrentQuery=function(){return this.currentQuery},s.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},s.refetch=function(n){return this.fetch(q({},n,{meta:{refetchPage:n==null?void 0:n.refetchPage}}))},s.fetchOptimistic=function(n){var i=this,o=this.client.defaultQueryObserverOptions(n),l=this.client.getQueryCache().build(this.client,o);return l.fetch().then(function(){return i.createResult(l,o)})},s.fetch=function(n){var i=this;return this.executeFetch(n).then(function(){return i.updateResult(),i.currentResult})},s.executeFetch=function(n){this.updateQuery();var i=this.currentQuery.fetch(this.options,n);return n!=null&&n.throwOnError||(i=i.catch(ne)),i},s.updateStaleTimeout=function(){var n=this;if(this.clearStaleTimeout(),!(Bt||this.currentResult.isStale||!js(this.options.staleTime))){var i=Zr(this.currentResult.dataUpdatedAt,this.options.staleTime),o=i+1;this.staleTimeoutId=setTimeout(function(){n.currentResult.isStale||n.updateResult()},o)}},s.computeRefetchInterval=function(){var n;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(n=this.options.refetchInterval)!=null?n:!1},s.updateRefetchInterval=function(n){var i=this;this.clearRefetchInterval(),this.currentRefetchInterval=n,!(Bt||this.options.enabled===!1||!js(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(i.options.refetchIntervalInBackground||ct.isFocused())&&i.executeFetch()},this.currentRefetchInterval))},s.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},s.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},s.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},s.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},s.createResult=function(n,i){var o=this.currentQuery,l=this.options,c=this.currentResult,u=this.currentResultState,d=this.currentResultOptions,m=n!==o,x=m?n.state:this.currentQueryInitialState,h=m?this.currentResult:this.previousQueryResult,f=n.state,y=f.dataUpdatedAt,k=f.error,b=f.errorUpdatedAt,v=f.isFetching,C=f.status,F=!1,M=!1,L;if(i.optimisticResults){var j=this.hasListeners(),D=!j&&jr(n,i),N=j&&wr(n,o,i,l);(D||N)&&(v=!0,y||(C="loading"))}if(i.keepPreviousData&&!f.dataUpdateCount&&(h!=null&&h.isSuccess)&&C!=="error")L=h.data,y=h.dataUpdatedAt,C=h.status,F=!0;else if(i.select&&typeof f.data<"u")if(c&&f.data===(u==null?void 0:u.data)&&i.select===this.selectFn)L=this.selectResult;else try{this.selectFn=i.select,L=i.select(f.data),i.structuralSharing!==!1&&(L=Wt(c==null?void 0:c.data,L)),this.selectResult=L,this.selectError=null}catch(P){Kt().error(P),this.selectError=P}else L=f.data;if(typeof i.placeholderData<"u"&&typeof L>"u"&&(C==="loading"||C==="idle")){var T;if(c!=null&&c.isPlaceholderData&&i.placeholderData===(d==null?void 0:d.placeholderData))T=c.data;else if(T=typeof i.placeholderData=="function"?i.placeholderData():i.placeholderData,i.select&&typeof T<"u")try{T=i.select(T),i.structuralSharing!==!1&&(T=Wt(c==null?void 0:c.data,T)),this.selectError=null}catch(P){Kt().error(P),this.selectError=P}typeof T<"u"&&(C="success",L=T,M=!0)}this.selectError&&(k=this.selectError,L=this.selectResult,b=Date.now(),C="error");var E={status:C,isLoading:C==="loading",isSuccess:C==="success",isError:C==="error",isIdle:C==="idle",data:L,dataUpdatedAt:y,error:k,errorUpdatedAt:b,failureCount:f.fetchFailureCount,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>x.dataUpdateCount||f.errorUpdateCount>x.errorUpdateCount,isFetching:v,isRefetching:v&&C!=="loading",isLoadingError:C==="error"&&f.dataUpdatedAt===0,isPlaceholderData:M,isPreviousData:F,isRefetchError:C==="error"&&f.dataUpdatedAt!==0,isStale:Zs(n,i),refetch:this.refetch,remove:this.remove};return E},s.shouldNotifyListeners=function(n,i){if(!i)return!0;var o=this.options,l=o.notifyOnChangeProps,c=o.notifyOnChangePropsExclusions;if(!l&&!c||l==="tracked"&&!this.trackedProps.length)return!0;var u=l==="tracked"?this.trackedProps:l;return Object.keys(n).some(function(d){var m=d,x=n[m]!==i[m],h=u==null?void 0:u.some(function(y){return y===d}),f=c==null?void 0:c.some(function(y){return y===d});return x&&!f&&(!u||h)})},s.updateResult=function(n){var i=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!Un(this.currentResult,i)){var o={cache:!0};(n==null?void 0:n.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,i)&&(o.listeners=!0),this.notify(q({},o,n))}},s.updateQuery=function(){var n=this.client.getQueryCache().build(this.client,this.options);if(n!==this.currentQuery){var i=this.currentQuery;this.currentQuery=n,this.currentQueryInitialState=n.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(i==null||i.removeObserver(this),n.addObserver(this))}},s.onQueryUpdate=function(n){var i={};n.type==="success"?i.onSuccess=!0:n.type==="error"&&!Dt(n.error)&&(i.onError=!0),this.updateResult(i),this.hasListeners()&&this.updateTimers()},s.notify=function(n){var i=this;X.batch(function(){n.onSuccess?(i.options.onSuccess==null||i.options.onSuccess(i.currentResult.data),i.options.onSettled==null||i.options.onSettled(i.currentResult.data,null)):n.onError&&(i.options.onError==null||i.options.onError(i.currentResult.error),i.options.onSettled==null||i.options.onSettled(void 0,i.currentResult.error)),n.listeners&&i.listeners.forEach(function(o){o(i.currentResult)}),n.cache&&i.client.getQueryCache().notify({query:i.currentQuery,type:"observerResultsUpdated"})})},t}(st);function ri(r,t){return t.enabled!==!1&&!r.state.dataUpdatedAt&&!(r.state.status==="error"&&t.retryOnMount===!1)}function jr(r,t){return ri(r,t)||r.state.dataUpdatedAt>0&&Ns(r,t,t.refetchOnMount)}function Ns(r,t,s){if(t.enabled!==!1){var a=typeof s=="function"?s(r):s;return a==="always"||a!==!1&&Zs(r,t)}return!1}function wr(r,t,s,a){return s.enabled!==!1&&(r!==t||a.enabled===!1)&&(!s.suspense||r.state.status!=="error")&&Zs(r,s)}function Zs(r,t){return r.isStaleByTime(t.staleTime)}var ai=function(r){tt(t,r);function t(a,n){var i;return i=r.call(this)||this,i.client=a,i.setOptions(n),i.bindMethods(),i.updateResult(),i}var s=t.prototype;return s.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},s.setOptions=function(n){this.options=this.client.defaultMutationOptions(n)},s.onUnsubscribe=function(){if(!this.listeners.length){var n;(n=this.currentMutation)==null||n.removeObserver(this)}},s.onMutationUpdate=function(n){this.updateResult();var i={listeners:!0};n.type==="success"?i.onSuccess=!0:n.type==="error"&&(i.onError=!0),this.notify(i)},s.getCurrentResult=function(){return this.currentResult},s.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},s.mutate=function(n,i){return this.mutateOptions=i,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,q({},this.options,{variables:typeof n<"u"?n:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},s.updateResult=function(){var n=this.currentMutation?this.currentMutation.state:na(),i=q({},n,{isLoading:n.status==="loading",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=i},s.notify=function(n){var i=this;X.batch(function(){i.mutateOptions&&(n.onSuccess?(i.mutateOptions.onSuccess==null||i.mutateOptions.onSuccess(i.currentResult.data,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(i.currentResult.data,null,i.currentResult.variables,i.currentResult.context)):n.onError&&(i.mutateOptions.onError==null||i.mutateOptions.onError(i.currentResult.error,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(void 0,i.currentResult.error,i.currentResult.variables,i.currentResult.context))),n.listeners&&i.listeners.forEach(function(o){o(i.currentResult)})})},t}(st),ni=Ga.unstable_batchedUpdates;X.setBatchNotifyFunction(ni);var ii=console;Hn(ii);var Nr=H.createContext(void 0),ia=H.createContext(!1);function oa(r){return r&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Nr),window.ReactQueryClientContext):Nr}var er=function(){var t=H.useContext(oa(H.useContext(ia)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},oi=function(t){var s=t.client,a=t.contextSharing,n=a===void 0?!1:a,i=t.children;H.useEffect(function(){return s.mount(),function(){s.unmount()}},[s]);var o=oa(n);return H.createElement(ia.Provider,{value:n},H.createElement(o.Provider,{value:s},i))};function li(){var r=!1;return{clearReset:function(){r=!1},reset:function(){r=!0},isReset:function(){return r}}}var ci=H.createContext(li()),di=function(){return H.useContext(ci)};function la(r,t,s){return typeof t=="function"?t.apply(void 0,s):typeof t=="boolean"?t:!!r}function ui(r,t,s){var a=H.useRef(!1),n=H.useState(0),i=n[1],o=Vn(r,t,s),l=er(),c=H.useRef();c.current?c.current.setOptions(o):c.current=new ai(l,o);var u=c.current.getCurrentResult();H.useEffect(function(){a.current=!0;var m=c.current.subscribe(X.batchCalls(function(){a.current&&i(function(x){return x+1})}));return function(){a.current=!1,m()}},[]);var d=H.useCallback(function(m,x){c.current.mutate(m,x).catch(ne)},[]);if(u.error&&la(void 0,c.current.options.useErrorBoundary,[u.error]))throw u.error;return q({},u,{mutate:d,mutateAsync:u.mutate})}function hi(r,t){var s=H.useRef(!1),a=H.useState(0),n=a[1],i=er(),o=di(),l=i.defaultQueryObserverOptions(r);l.optimisticResults=!0,l.onError&&(l.onError=X.batchCalls(l.onError)),l.onSuccess&&(l.onSuccess=X.batchCalls(l.onSuccess)),l.onSettled&&(l.onSettled=X.batchCalls(l.onSettled)),l.suspense&&(typeof l.staleTime!="number"&&(l.staleTime=1e3),l.cacheTime===0&&(l.cacheTime=1)),(l.suspense||l.useErrorBoundary)&&(o.isReset()||(l.retryOnMount=!1));var c=H.useState(function(){return new t(i,l)}),u=c[0],d=u.getOptimisticResult(l);if(H.useEffect(function(){s.current=!0,o.clearReset();var m=u.subscribe(X.batchCalls(function(){s.current&&n(function(x){return x+1})}));return u.updateResult(),function(){s.current=!1,m()}},[o,u]),H.useEffect(function(){u.setOptions(l,{listeners:!1})},[l,u]),l.suspense&&d.isLoading)throw u.fetchOptimistic(l).then(function(m){var x=m.data;l.onSuccess==null||l.onSuccess(x),l.onSettled==null||l.onSettled(x,null)}).catch(function(m){o.clearReset(),l.onError==null||l.onError(m),l.onSettled==null||l.onSettled(void 0,m)});if(d.isError&&!o.isReset()&&!d.isFetching&&la(l.suspense,l.useErrorBoundary,[d.error,u.getCurrentQuery()]))throw d.error;return l.notifyOnChangeProps==="tracked"&&(d=u.trackResult(d,l)),d}function cs(r,t,s){var a=It(r,t,s);return hi(a,si)}let mi={data:""},pi=r=>typeof window=="object"?((r?r.querySelector("#_goober"):window._goober)||Object.assign((r||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:r||mi,fi=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,xi=/\/\*[^]*?\*\/|  +/g,kr=/\n+/g,Ie=(r,t)=>{let s="",a="",n="";for(let i in r){let o=r[i];i[0]=="@"?i[1]=="i"?s=i+" "+o+";":a+=i[1]=="f"?Ie(o,i):i+"{"+Ie(o,i[1]=="k"?"":t)+"}":typeof o=="object"?a+=Ie(o,t?t.replace(/([^,])+/g,l=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,l):l?l+" "+c:c)):i):o!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=Ie.p?Ie.p(i,o):i+":"+o+";")}return s+(t&&n?t+"{"+n+"}":n)+a},Se={},ca=r=>{if(typeof r=="object"){let t="";for(let s in r)t+=s+ca(r[s]);return t}return r},gi=(r,t,s,a,n)=>{let i=ca(r),o=Se[i]||(Se[i]=(c=>{let u=0,d=11;for(;u<c.length;)d=101*d+c.charCodeAt(u++)>>>0;return"go"+d})(i));if(!Se[o]){let c=i!==r?r:(u=>{let d,m,x=[{}];for(;d=fi.exec(u.replace(xi,""));)d[4]?x.shift():d[3]?(m=d[3].replace(kr," ").trim(),x.unshift(x[0][m]=x[0][m]||{})):x[0][d[1]]=d[2].replace(kr," ").trim();return x[0]})(r);Se[o]=Ie(n?{["@keyframes "+o]:c}:c,s?"":"."+o)}let l=s&&Se.g?Se.g:null;return s&&(Se.g=Se[o]),((c,u,d,m)=>{m?u.data=u.data.replace(m,c):u.data.indexOf(c)===-1&&(u.data=d?c+u.data:u.data+c)})(Se[o],t,a,l),o},yi=(r,t,s)=>r.reduce((a,n,i)=>{let o=t[i];if(o&&o.call){let l=o(s),c=l&&l.props&&l.props.className||/^go/.test(l)&&l;o=c?"."+c:l&&typeof l=="object"?l.props?"":Ie(l,""):l===!1?"":l}return a+n+(o??"")},"");function Zt(r){let t=this||{},s=r.call?r(t.p):r;return gi(s.unshift?s.raw?yi(s,[].slice.call(arguments,1),t.p):s.reduce((a,n)=>Object.assign(a,n&&n.call?n(t.p):n),{}):s,pi(t.target),t.g,t.o,t.k)}let da,ks,Ss;Zt.bind({g:1});let Ce=Zt.bind({k:1});function bi(r,t,s,a){Ie.p=t,da=r,ks=s,Ss=a}function De(r,t){let s=this||{};return function(){let a=arguments;function n(i,o){let l=Object.assign({},i),c=l.className||n.className;s.p=Object.assign({theme:ks&&ks()},l),s.o=/ *go\d+/.test(c),l.className=Zt.apply(s,a)+(c?" "+c:""),t&&(l.ref=o);let u=r;return r[0]&&(u=l.as||r,delete l.as),Ss&&u[0]&&Ss(l),da(u,l)}return t?t(n):n}}var vi=r=>typeof r=="function",Gt=(r,t)=>vi(r)?r(t):r,ji=(()=>{let r=0;return()=>(++r).toString()})(),ua=(()=>{let r;return()=>{if(r===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");r=!t||t.matches}return r}})(),wi=20,ha=(r,t)=>{switch(t.type){case 0:return{...r,toasts:[t.toast,...r.toasts].slice(0,wi)};case 1:return{...r,toasts:r.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:s}=t;return ha(r,{type:r.toasts.find(i=>i.id===s.id)?1:0,toast:s});case 3:let{toastId:a}=t;return{...r,toasts:r.toasts.map(i=>i.id===a||a===void 0?{...i,dismissed:!0,visible:!1}:i)};case 4:return t.toastId===void 0?{...r,toasts:[]}:{...r,toasts:r.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...r,pausedAt:t.time};case 6:let n=t.time-(r.pausedAt||0);return{...r,pausedAt:void 0,toasts:r.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},Lt=[],Fe={toasts:[],pausedAt:void 0},ze=r=>{Fe=ha(Fe,r),Lt.forEach(t=>{t(Fe)})},Ni={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},ki=(r={})=>{let[t,s]=p.useState(Fe),a=p.useRef(Fe);p.useEffect(()=>(a.current!==Fe&&s(Fe),Lt.push(s),()=>{let i=Lt.indexOf(s);i>-1&&Lt.splice(i,1)}),[]);let n=t.toasts.map(i=>{var o,l,c;return{...r,...r[i.type],...i,removeDelay:i.removeDelay||((o=r[i.type])==null?void 0:o.removeDelay)||(r==null?void 0:r.removeDelay),duration:i.duration||((l=r[i.type])==null?void 0:l.duration)||(r==null?void 0:r.duration)||Ni[i.type],style:{...r.style,...(c=r[i.type])==null?void 0:c.style,...i.style}}});return{...t,toasts:n}},Si=(r,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:r,pauseDuration:0,...s,id:(s==null?void 0:s.id)||ji()}),bt=r=>(t,s)=>{let a=Si(t,r,s);return ze({type:2,toast:a}),a.id},w=(r,t)=>bt("blank")(r,t);w.error=bt("error");w.success=bt("success");w.loading=bt("loading");w.custom=bt("custom");w.dismiss=r=>{ze({type:3,toastId:r})};w.remove=r=>ze({type:4,toastId:r});w.promise=(r,t,s)=>{let a=w.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof r=="function"&&(r=r()),r.then(n=>{let i=t.success?Gt(t.success,n):void 0;return i?w.success(i,{id:a,...s,...s==null?void 0:s.success}):w.dismiss(a),n}).catch(n=>{let i=t.error?Gt(t.error,n):void 0;i?w.error(i,{id:a,...s,...s==null?void 0:s.error}):w.dismiss(a)}),r};var Ei=(r,t)=>{ze({type:1,toast:{id:r,height:t}})},Ci=()=>{ze({type:5,time:Date.now()})},dt=new Map,Ri=1e3,_i=(r,t=Ri)=>{if(dt.has(r))return;let s=setTimeout(()=>{dt.delete(r),ze({type:4,toastId:r})},t);dt.set(r,s)},Pi=r=>{let{toasts:t,pausedAt:s}=ki(r);p.useEffect(()=>{if(s)return;let i=Date.now(),o=t.map(l=>{if(l.duration===1/0)return;let c=(l.duration||0)+l.pauseDuration-(i-l.createdAt);if(c<0){l.visible&&w.dismiss(l.id);return}return setTimeout(()=>w.dismiss(l.id),c)});return()=>{o.forEach(l=>l&&clearTimeout(l))}},[t,s]);let a=p.useCallback(()=>{s&&ze({type:6,time:Date.now()})},[s]),n=p.useCallback((i,o)=>{let{reverseOrder:l=!1,gutter:c=8,defaultPosition:u}=o||{},d=t.filter(h=>(h.position||u)===(i.position||u)&&h.height),m=d.findIndex(h=>h.id===i.id),x=d.filter((h,f)=>f<m&&h.visible).length;return d.filter(h=>h.visible).slice(...l?[x+1]:[0,x]).reduce((h,f)=>h+(f.height||0)+c,0)},[t]);return p.useEffect(()=>{t.forEach(i=>{if(i.dismissed)_i(i.id,i.removeDelay);else{let o=dt.get(i.id);o&&(clearTimeout(o),dt.delete(i.id))}})},[t]),{toasts:t,handlers:{updateHeight:Ei,startPause:Ci,endPause:a,calculateOffset:n}}},Ai=Ce`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Ti=Ce`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ii=Ce`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Oi=De("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${r=>r.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ai} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Ti} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${r=>r.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ii} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Di=Ce`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Li=De("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${r=>r.secondary||"#e0e0e0"};
  border-right-color: ${r=>r.primary||"#616161"};
  animation: ${Di} 1s linear infinite;
`,$i=Ce`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Vi=Ce`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Fi=De("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${r=>r.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${$i} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Vi} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${r=>r.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Mi=De("div")`
  position: absolute;
`,Ui=De("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,qi=Ce`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Bi=De("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${qi} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Qi=({toast:r})=>{let{icon:t,type:s,iconTheme:a}=r;return t!==void 0?typeof t=="string"?p.createElement(Bi,null,t):t:s==="blank"?null:p.createElement(Ui,null,p.createElement(Li,{...a}),s!=="loading"&&p.createElement(Mi,null,s==="error"?p.createElement(Oi,{...a}):p.createElement(Fi,{...a})))},zi=r=>`
0% {transform: translate3d(0,${r*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Wi=r=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${r*-150}%,-1px) scale(.6); opacity:0;}
`,Hi="0%{opacity:0;} 100%{opacity:1;}",Ki="0%{opacity:1;} 100%{opacity:0;}",Gi=De("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Yi=De("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Ji=(r,t)=>{let s=r.includes("top")?1:-1,[a,n]=ua()?[Hi,Ki]:[zi(s),Wi(s)];return{animation:t?`${Ce(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Ce(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Xi=p.memo(({toast:r,position:t,style:s,children:a})=>{let n=r.height?Ji(r.position||t||"top-center",r.visible):{opacity:0},i=p.createElement(Qi,{toast:r}),o=p.createElement(Yi,{...r.ariaProps},Gt(r.message,r));return p.createElement(Gi,{className:r.className,style:{...n,...s,...r.style}},typeof a=="function"?a({icon:i,message:o}):p.createElement(p.Fragment,null,i,o))});bi(p.createElement);var Zi=({id:r,className:t,style:s,onHeightUpdate:a,children:n})=>{let i=p.useCallback(o=>{if(o){let l=()=>{let c=o.getBoundingClientRect().height;a(r,c)};l(),new MutationObserver(l).observe(o,{subtree:!0,childList:!0,characterData:!0})}},[r,a]);return p.createElement("div",{ref:i,className:t,style:s},n)},eo=(r,t)=>{let s=r.includes("top"),a=s?{top:0}:{bottom:0},n=r.includes("center")?{justifyContent:"center"}:r.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:ua()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...a,...n}},to=Zt`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Et=16,so=({reverseOrder:r,position:t="top-center",toastOptions:s,gutter:a,children:n,containerStyle:i,containerClassName:o})=>{let{toasts:l,handlers:c}=Pi(s);return p.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Et,left:Et,right:Et,bottom:Et,pointerEvents:"none",...i},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(u=>{let d=u.position||t,m=c.calculateOffset(u,{reverseOrder:r,gutter:a,defaultPosition:t}),x=eo(d,m);return p.createElement(Zi,{id:u.id,key:u.id,onHeightUpdate:c.updateHeight,className:u.visible?to:"",style:x},u.type==="custom"?Gt(u.message,u):n?n(u):p.createElement(Xi,{toast:u,position:d}))}))},pd=w;const Sr=r=>{let t;const s=new Set,a=(d,m)=>{const x=typeof d=="function"?d(t):d;if(!Object.is(x,t)){const h=t;t=m??(typeof x!="object"||x===null)?x:Object.assign({},t,x),s.forEach(f=>f(t,h))}},n=()=>t,c={setState:a,getState:n,getInitialState:()=>u,subscribe:d=>(s.add(d),()=>s.delete(d)),destroy:()=>{s.clear()}},u=t=r(a,n,c);return c},ro=r=>r?Sr(r):Sr;var ma={exports:{}},pa={},fa={exports:{}},xa={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xe=p;function ao(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var no=typeof Object.is=="function"?Object.is:ao,io=Xe.useState,oo=Xe.useEffect,lo=Xe.useLayoutEffect,co=Xe.useDebugValue;function uo(r,t){var s=t(),a=io({inst:{value:s,getSnapshot:t}}),n=a[0].inst,i=a[1];return lo(function(){n.value=s,n.getSnapshot=t,ds(n)&&i({inst:n})},[r,s,t]),oo(function(){return ds(n)&&i({inst:n}),r(function(){ds(n)&&i({inst:n})})},[r]),co(s),s}function ds(r){var t=r.getSnapshot;r=r.value;try{var s=t();return!no(r,s)}catch{return!0}}function ho(r,t){return t()}var mo=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?ho:uo;xa.useSyncExternalStore=Xe.useSyncExternalStore!==void 0?Xe.useSyncExternalStore:mo;fa.exports=xa;var po=fa.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var es=p,fo=po;function xo(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var go=typeof Object.is=="function"?Object.is:xo,yo=fo.useSyncExternalStore,bo=es.useRef,vo=es.useEffect,jo=es.useMemo,wo=es.useDebugValue;pa.useSyncExternalStoreWithSelector=function(r,t,s,a,n){var i=bo(null);if(i.current===null){var o={hasValue:!1,value:null};i.current=o}else o=i.current;i=jo(function(){function c(h){if(!u){if(u=!0,d=h,h=a(h),n!==void 0&&o.hasValue){var f=o.value;if(n(f,h))return m=f}return m=h}if(f=m,go(d,h))return f;var y=a(h);return n!==void 0&&n(f,y)?(d=h,f):(d=h,m=y)}var u=!1,d,m,x=s===void 0?null:s;return[function(){return c(t())},x===null?void 0:function(){return c(x())}]},[t,s,a,n]);var l=yo(r,i[0],i[1]);return vo(function(){o.hasValue=!0,o.value=l},[l]),wo(l),l};ma.exports=pa;var No=ma.exports;const ko=Yt(No),{useDebugValue:So}=H,{useSyncExternalStoreWithSelector:Eo}=ko;const Co=r=>r;function Ro(r,t=Co,s){const a=Eo(r.subscribe,r.getState,r.getServerState||r.getInitialState,t,s);return So(a),a}const Er=r=>{const t=typeof r=="function"?ro(r):r,s=(a,n)=>Ro(t,a,n);return Object.assign(s,t),s},_o=r=>r?Er(r):Er,Es=new Map,Ct=r=>{const t=Es.get(r);return t?Object.fromEntries(Object.entries(t.stores).map(([s,a])=>[s,a.getState()])):{}},Po=(r,t,s)=>{if(r===void 0)return{type:"untracked",connection:t.connect(s)};const a=Es.get(s.name);if(a)return{type:"tracked",store:r,...a};const n={connection:t.connect(s),stores:{}};return Es.set(s.name,n),{type:"tracked",store:r,...n}},Ao=(r,t={})=>(s,a,n)=>{const{enabled:i,anonymousActionType:o,store:l,...c}=t;let u;try{u=(i??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!u)return r(s,a,n);const{connection:d,...m}=Po(l,u,c);let x=!0;n.setState=(y,k,b)=>{const v=s(y,k);if(!x)return v;const C=b===void 0?{type:o||"anonymous"}:typeof b=="string"?{type:b}:b;return l===void 0?(d==null||d.send(C,a()),v):(d==null||d.send({...C,type:`${l}/${C.type}`},{...Ct(c.name),[l]:n.getState()}),v)};const h=(...y)=>{const k=x;x=!1,s(...y),x=k},f=r(n.setState,a,n);if(m.type==="untracked"?d==null||d.init(f):(m.stores[m.store]=n,d==null||d.init(Object.fromEntries(Object.entries(m.stores).map(([y,k])=>[y,y===m.store?f:k.getState()])))),n.dispatchFromDevtools&&typeof n.dispatch=="function"){let y=!1;const k=n.dispatch;n.dispatch=(...b)=>{k(...b)}}return d.subscribe(y=>{var k;switch(y.type){case"ACTION":if(typeof y.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return us(y.payload,b=>{if(b.type==="__setState"){if(l===void 0){h(b.state);return}Object.keys(b.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const v=b.state[l];if(v==null)return;JSON.stringify(n.getState())!==JSON.stringify(v)&&h(v);return}n.dispatchFromDevtools&&typeof n.dispatch=="function"&&n.dispatch(b)});case"DISPATCH":switch(y.payload.type){case"RESET":return h(f),l===void 0?d==null?void 0:d.init(n.getState()):d==null?void 0:d.init(Ct(c.name));case"COMMIT":if(l===void 0){d==null||d.init(n.getState());return}return d==null?void 0:d.init(Ct(c.name));case"ROLLBACK":return us(y.state,b=>{if(l===void 0){h(b),d==null||d.init(n.getState());return}h(b[l]),d==null||d.init(Ct(c.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return us(y.state,b=>{if(l===void 0){h(b);return}JSON.stringify(n.getState())!==JSON.stringify(b[l])&&h(b[l])});case"IMPORT_STATE":{const{nextLiftedState:b}=y.payload,v=(k=b.computedStates.slice(-1)[0])==null?void 0:k.state;if(!v)return;h(l===void 0?v:v[l]),d==null||d.send(null,b);return}case"PAUSE_RECORDING":return x=!x}return}}),f},To=Ao,us=(r,t)=>{let s;try{s=JSON.parse(r)}catch(a){console.error("[zustand devtools middleware] Could not parse the received json",a)}s!==void 0&&t(s)};function Io(r,t){let s;try{s=r()}catch{return}return{getItem:n=>{var i;const o=c=>c===null?null:JSON.parse(c,t==null?void 0:t.reviver),l=(i=s.getItem(n))!=null?i:null;return l instanceof Promise?l.then(o):o(l)},setItem:(n,i)=>s.setItem(n,JSON.stringify(i,t==null?void 0:t.replacer)),removeItem:n=>s.removeItem(n)}}const ht=r=>t=>{try{const s=r(t);return s instanceof Promise?s:{then(a){return ht(a)(s)},catch(a){return this}}}catch(s){return{then(a){return this},catch(a){return ht(a)(s)}}}},Oo=(r,t)=>(s,a,n)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:k=>k,version:0,merge:(k,b)=>({...b,...k}),...t},o=!1;const l=new Set,c=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return r((...k)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...k)},a,n);const d=ht(i.serialize),m=()=>{const k=i.partialize({...a()});let b;const v=d({state:k,version:i.version}).then(C=>u.setItem(i.name,C)).catch(C=>{b=C});if(b)throw b;return v},x=n.setState;n.setState=(k,b)=>{x(k,b),m()};const h=r((...k)=>{s(...k),m()},a,n);let f;const y=()=>{var k;if(!u)return;o=!1,l.forEach(v=>v(a()));const b=((k=i.onRehydrateStorage)==null?void 0:k.call(i,a()))||void 0;return ht(u.getItem.bind(u))(i.name).then(v=>{if(v)return i.deserialize(v)}).then(v=>{if(v)if(typeof v.version=="number"&&v.version!==i.version){if(i.migrate)return i.migrate(v.state,v.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return v.state}).then(v=>{var C;return f=i.merge(v,(C=a())!=null?C:h),s(f,!0),m()}).then(()=>{b==null||b(f,void 0),o=!0,c.forEach(v=>v(f))}).catch(v=>{b==null||b(void 0,v)})};return n.persist={setOptions:k=>{i={...i,...k},k.getStorage&&(u=k.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>y(),hasHydrated:()=>o,onHydrate:k=>(l.add(k),()=>{l.delete(k)}),onFinishHydration:k=>(c.add(k),()=>{c.delete(k)})},y(),f||h},Do=(r,t)=>(s,a,n)=>{let i={storage:Io(()=>localStorage),partialize:y=>y,version:0,merge:(y,k)=>({...k,...y}),...t},o=!1;const l=new Set,c=new Set;let u=i.storage;if(!u)return r((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...y)},a,n);const d=()=>{const y=i.partialize({...a()});return u.setItem(i.name,{state:y,version:i.version})},m=n.setState;n.setState=(y,k)=>{m(y,k),d()};const x=r((...y)=>{s(...y),d()},a,n);n.getInitialState=()=>x;let h;const f=()=>{var y,k;if(!u)return;o=!1,l.forEach(v=>{var C;return v((C=a())!=null?C:x)});const b=((k=i.onRehydrateStorage)==null?void 0:k.call(i,(y=a())!=null?y:x))||void 0;return ht(u.getItem.bind(u))(i.name).then(v=>{if(v)if(typeof v.version=="number"&&v.version!==i.version){if(i.migrate)return[!0,i.migrate(v.state,v.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,v.state];return[!1,void 0]}).then(v=>{var C;const[F,M]=v;if(h=i.merge(M,(C=a())!=null?C:x),s(h,!0),F)return d()}).then(()=>{b==null||b(h,void 0),h=a(),o=!0,c.forEach(v=>v(h))}).catch(v=>{b==null||b(void 0,v)})};return n.persist={setOptions:y=>{i={...i,...y},y.storage&&(u=y.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>f(),hasHydrated:()=>o,onHydrate:y=>(l.add(y),()=>{l.delete(y)}),onFinishHydration:y=>(c.add(y),()=>{c.delete(y)})},i.skipHydration||f(),h||x},Lo=(r,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?Oo(r,t):Do(r,t),$o=Lo,Cr={user:null,isAuthenticated:!1,authToken:null,currentRestaurant:null,suggestions:[],playQueue:null,currentlyPlaying:null,theme:"auto",deviceType:"desktop",isOnline:navigator.onLine,connectionStatus:"disconnected",notifications:[],settings:{autoRefresh:!0,soundEnabled:!0,showNotifications:!0,language:"pt-BR",maxSuggestionsPerSession:3,notificationPosition:"top-left"},loading:{suggestions:!1,queue:!1,search:!1,voting:!1}},rt=_o()(To($o((r,t)=>({...Cr,setUser:s=>r(a=>({user:s,isAuthenticated:!!s&&!!a.authToken})),setAuthToken:s=>{r(a=>({authToken:s,isAuthenticated:!!s&&!!a.user})),s?localStorage.setItem("authToken",s):localStorage.removeItem("authToken")},setCurrentRestaurant:s=>r({currentRestaurant:s}),setSuggestions:s=>r({suggestions:s}),addSuggestion:s=>r(a=>({suggestions:[s,...a.suggestions]})),updateSuggestion:(s,a)=>r(n=>({suggestions:n.suggestions.map(i=>i.id===s?{...i,...a}:i)})),removeSuggestion:s=>r(a=>({suggestions:a.suggestions.filter(n=>n.id!==s)})),setPlayQueue:s=>r({playQueue:s}),setCurrentlyPlaying:s=>r({currentlyPlaying:s}),setTheme:s=>{r({theme:s});const a=document.documentElement;s==="dark"?a.classList.add("dark"):s==="light"?a.classList.remove("dark"):window.matchMedia("(prefers-color-scheme: dark)").matches?a.classList.add("dark"):a.classList.remove("dark")},setDeviceType:s=>r({deviceType:s}),setOnlineStatus:s=>r({isOnline:s}),setConnectionStatus:s=>r({connectionStatus:s}),addNotification:s=>{const a=Date.now().toString(),n={...s,id:a,createdAt:new Date().toISOString()};r(i=>({notifications:[n,...i.notifications]})),s.duration&&s.duration>0&&setTimeout(()=>{t().removeNotification(a)},s.duration)},removeNotification:s=>r(a=>({notifications:a.notifications.filter(n=>n.id!==s)})),clearNotifications:()=>r({notifications:[]}),updateSettings:s=>r(a=>({settings:{...a.settings,...s}})),setLoading:(s,a)=>r(n=>({loading:{...n.loading,[s]:a}})),reset:()=>r(()=>({...Cr,suggestions:[],notifications:[],isOnline:navigator.onLine}))}),{name:"restaurant-playlist-store",partialize:r=>({user:r.user,isAuthenticated:r.isAuthenticated,authToken:r.authToken,theme:r.theme,settings:r.settings})}),{name:"restaurant-playlist-store"})),vt=()=>{const{user:r,isAuthenticated:t,authToken:s,setUser:a,setAuthToken:n}=rt();return{user:r,isAuthenticated:t,authToken:s,setUser:a,setAuthToken:n}},Vo=()=>{const{notifications:r,addNotification:t,removeNotification:s,clearNotifications:a}=rt();return{notifications:r,addNotification:t,removeNotification:s,clearNotifications:a}},ga=()=>{const{settings:r,updateSettings:t}=rt();return{settings:r,updateSettings:t}},Fo=()=>{var a;const r=rt.getState(),t=()=>{const n=window.innerWidth;return n<768?"mobile":n<1024?"tablet":"desktop"};r.setDeviceType(t()),r.setTheme(r.theme);try{const n=(a=r.settings)==null?void 0:a.notificationPosition,o=n?{"top-right":"top-left","bottom-right":"bottom-left"}[n]||n:"top-left";o!==n&&r.updateSettings({notificationPosition:o})}catch{}window.addEventListener("online",()=>r.setOnlineStatus(!0)),window.addEventListener("offline",()=>r.setOnlineStatus(!1)),window.addEventListener("resize",()=>{r.setDeviceType(t())}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{r.theme==="auto"&&r.setTheme("auto")})};function ya(r){var t,s,a="";if(typeof r=="string"||typeof r=="number")a+=r;else if(typeof r=="object")if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(s=ya(r[t]))&&(a&&(a+=" "),a+=s)}else for(s in r)r[s]&&(a&&(a+=" "),a+=s);return a}function Cs(){for(var r,t,s=0,a="",n=arguments.length;s<n;s++)(r=arguments[s])&&(t=ya(r))&&(a&&(a+=" "),a+=t);return a}const pe=({variant:r="primary",size:t="md",loading:s=!1,icon:a,children:n,className:i,disabled:o,...l})=>{const c="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",u={primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",outline:"border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800"},d={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"};return e.jsxs("button",{className:Cs(c,u[r],d[t],i),disabled:o||s,...l,children:[s&&e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!s&&a&&e.jsx("span",{className:"mr-2",children:a}),n]})},ba={BASE_URL:"http://localhost:8001",API_VERSION:"v1",get API_BASE(){return`${this.BASE_URL}/api/${this.API_VERSION}`},ENDPOINTS:{RESTAURANTS:"/restaurants",SUGGESTIONS:"/suggestions",PLAYBACK:"/playback",ANALYTICS:"/analytics",NOTIFICATIONS:"/notifications",GENRES:"/genres"}},Z=(r,t)=>{try{const a="http://localhost:8001",n=(a&&a.trim()!==""?a:window.location.origin).replace(/\/$/,""),i=new URL(`/api/${ba.API_VERSION}${r}`,n);return t&&Object.entries(t).forEach(([o,l])=>{l&&l.trim()!==""&&i.searchParams.append(o,l)}),`${i.pathname}${i.search}`}catch(s){return console.error("❌ Erro ao construir URL da API:",s,{endpoint:r,params:t}),`/api/v1${r}`}},hs=(r="application/json")=>{const t=typeof localStorage<"u"?localStorage.getItem("authToken"):null,s={};return r&&(s["Content-Type"]=r),t&&(s.Authorization=`Bearer ${t}`),s},Mo=({isOpen:r,onClose:t,onLogin:s})=>{const[a,n]=p.useState(""),[i,o]=p.useState(""),[l,c]=p.useState(""),[u,d]=p.useState(!1),[m,x]=p.useState("admin"),[h,f]=p.useState(!1),y=async b=>{if(b.preventDefault(),!a||!i){w.error("Por favor, preencha todos os campos");return}if(m==="restaurant"&&!l){w.error("Por favor, informe o ID do restaurante");return}f(!0);try{const v=await fetch(Z("/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:i})});if(!v.ok){const F=await v.json();throw new Error(F.error||"Erro ao fazer login")}const C=await v.json();localStorage.setItem("authToken",C.token),localStorage.setItem("user",JSON.stringify(C.user)),C.restaurant&&localStorage.setItem("restaurant",JSON.stringify(C.restaurant)),s({email:a,password:i,restaurantId:m==="restaurant"?l:void 0}),w.success("Login realizado com sucesso!"),t(),n(""),o(""),c("")}catch(v){console.error("Erro no login:",v),w.error(v.message||"Erro ao fazer login. Verifique suas credenciais.")}finally{f(!1)}},k=()=>{n("<EMAIL>"),o("demo123"),m==="restaurant"&&c("demo-restaurant")};return r?e.jsx(Re,{children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4",children:e.jsxs(V.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{duration:.2},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md overflow-hidden",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:e.jsx(ys,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Login"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"Acesse sua conta"})]})]}),e.jsx("button",{onClick:t,className:"p-2 hover:bg-white/20 rounded-lg transition-colors",children:e.jsx(Qe,{className:"w-5 h-5"})})]})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6",children:[e.jsx("button",{type:"button",onClick:()=>x("admin"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${m==="admin"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}`,children:"Admin Geral"}),e.jsx("button",{type:"button",onClick:()=>x("restaurant"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${m==="restaurant"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}`,children:"Restaurante"})]}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[m==="restaurant"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"ID do Restaurante"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ue,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"text",value:l,onChange:b=>c(b.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"demo-restaurant"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Hs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",value:a,onChange:b=>n(b.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ks,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:u?"text":"password",value:i,onChange:b=>o(b.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"••••••••",required:!0}),e.jsx("button",{type:"button",onClick:()=>d(!u),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:u?e.jsx(Gs,{className:"w-5 h-5"}):e.jsx(Jt,{className:"w-5 h-5"})})]})]}),e.jsx("button",{type:"button",onClick:k,className:"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-center py-2",children:"Usar credenciais de demonstração"}),e.jsx(pe,{type:"submit",disabled:h,className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3",children:h?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Entrando..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(ys,{className:"w-5 h-5"}),e.jsx("span",{children:"Entrar"})]})})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Não tem uma conta?"," ",e.jsx("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium",children:"Entre em contato"})]})})]})]})})}):null},Uo=()=>{const r=gt(),[t,s]=p.useState("");p.useState("");const[a,n]=p.useState(!1),i=()=>{t.trim()&&r(`/restaurant/${t.trim()}`)},o=()=>{r("/restaurant/demo-restaurant")},l=()=>{r("/admin/login")},c=()=>{r("/login")},u=h=>{console.log("Login credentials:",h),h.restaurantId?r(`/restaurant/${h.restaurantId}/admin`):r("/admin")},d=[{icon:ut,title:"QR Code Inteligente",description:"Clientes escaneiam QR codes únicos por mesa e acessam instantaneamente o sistema de sugestões musicais",color:"from-blue-500 to-cyan-500"},{icon:te,title:"Integração YouTube Premium",description:"Acesso completo ao catálogo do YouTube sem anúncios, com suas próprias playlists personalizadas",color:"from-red-500 to-pink-500"},{icon:et,title:"Experiência Colaborativa",description:"Clientes sugerem e votam em músicas em tempo real, criando uma atmosfera única e envolvente",color:"from-green-500 to-emerald-500"},{icon:Ee,title:"Analytics Avançados",description:"Dashboard completo com métricas, relatórios e insights sobre preferências musicais dos clientes",color:"from-purple-500 to-violet-500"}],m=[{icon:ve,title:"Aumente o Engajamento",description:"Clientes ficam mais tempo no restaurante quando participam da experiência musical",stats:"+35% tempo de permanência"},{icon:Ys,title:"Melhore a Satisfação",description:"Música personalizada pelos próprios clientes resulta em experiências mais memoráveis",stats:"94% aprovação dos clientes"},{icon:Za,title:"Reduza Reclamações",description:"Elimine reclamações sobre música inadequada - os clientes escolhem o que querem ouvir",stats:"-80% reclamações sobre música"},{icon:hr,title:"Diferencial Competitivo",description:"Seja o primeiro restaurante da região com tecnologia interativa de música",stats:"Inovação garantida"}],x=[{step:"1",title:"Cliente Escaneia QR Code",description:"Cada mesa tem um QR code único que direciona para a interface de sugestões",icon:ut},{step:"2",title:"Sugere Músicas",description:"Busca no YouTube ou nas playlists do restaurante e sugere suas favoritas",icon:qt},{step:"3",title:"Comunidade Vota",description:"Outros clientes votam nas sugestões, criando uma fila democrática",icon:en},{step:"4",title:"Música Toca Automaticamente",description:"Sistema reproduz as músicas mais votadas sem intervenção manual",icon:be}];return e.jsxs("div",{className:"min-h-screen bg-white dark:bg-gray-900",children:[e.jsx("nav",{className:"fixed top-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md z-40 border-b border-gray-200 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:e.jsx(te,{className:"w-6 h-6 text-white"})}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"PlaylistInterativa"})]}),e.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[e.jsx("a",{href:"#features",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Recursos"}),e.jsx("a",{href:"#how-it-works",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Como Funciona"}),e.jsx("a",{href:"#benefits",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Benefícios"}),e.jsx(pe,{onClick:c,variant:"outline",size:"sm",children:"Login"}),e.jsx(pe,{onClick:o,size:"sm",children:"Demo Grátis"})]})]})})}),e.jsx("section",{className:"pt-24 pb-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[e.jsxs("div",{className:"inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-800 dark:text-blue-200 text-sm font-medium mb-8",children:[e.jsx(hr,{className:"w-4 h-4 mr-2"}),"Revolucione a experiência musical do seu restaurante"]}),e.jsxs("h1",{className:"text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 leading-tight",children:["Seus Clientes"," ",e.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Escolhem"})," ","a Música"]}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed",children:"Sistema interativo que permite aos clientes sugerir e votar em músicas através de QR codes. Transforme seu restaurante em uma experiência única e memorável."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16",children:[e.jsxs(pe,{onClick:o,size:"lg",className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-lg px-8 py-4",children:[e.jsx(be,{className:"w-5 h-5 mr-2"}),"Experimentar Demo"]}),e.jsxs(pe,{onClick:l,variant:"outline",size:"lg",className:"text-lg px-8 py-4",children:[e.jsx(Ge,{className:"w-5 h-5 mr-2"}),"Área Administrativa"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 max-w-md mx-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Acesso Rápido"}),e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("input",{type:"text",placeholder:"Código do restaurante (ex.: demo-restaurant)",value:t,onChange:h=>s(h.target.value),className:"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:h=>h.key==="Enter"&&i()}),e.jsx(pe,{onClick:i,className:"px-6 py-3",children:e.jsx(Xa,{className:"w-4 h-4"})})]})]})]})})}),e.jsx("section",{id:"features",className:"py-20 bg-white dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Recursos Principais"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Tecnologia avançada para criar a experiência musical perfeita no seu restaurante"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:d.map((h,f)=>e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:f*.1},className:"bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:`w-12 h-12 bg-gradient-to-r ${h.color} rounded-lg flex items-center justify-center mb-4`,children:e.jsx(h.icon,{className:"w-6 h-6 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:h.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:h.description})]},h.title))})]})}),e.jsx("section",{id:"how-it-works",className:"py-20 bg-gray-50 dark:bg-gray-800",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Como Funciona"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Processo simples e intuitivo que seus clientes vão adorar"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:x.map((h,f)=>e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:f*.1},className:"text-center",children:[e.jsxs("div",{className:"relative mb-6",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(h.icon,{className:"w-8 h-8 text-white"})}),e.jsx("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:h.step})]}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:h.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:h.description})]},h.step))})]})}),e.jsx("section",{id:"benefits",className:"py-20 bg-white dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Por que Escolher Nossa Solução?"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Resultados comprovados que transformam a experiência do seu restaurante"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:m.map((h,f)=>e.jsx(V.div,{initial:{opacity:0,x:f%2===0?-20:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:f*.1},className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(h.icon,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:h.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-3",children:h.description}),e.jsxs("div",{className:"inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full text-green-800 dark:text-green-200 text-sm font-medium",children:[e.jsx(ve,{className:"w-4 h-4 mr-1"}),h.stats]})]})]})},h.title))})]})}),e.jsx("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-purple-600",children:e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[e.jsx("h2",{className:"text-4xl font-bold text-white mb-6",children:"Pronto para Revolucionar seu Restaurante?"}),e.jsx("p",{className:"text-xl text-blue-100 mb-8 max-w-2xl mx-auto",children:"Junte-se aos restaurantes que já estão oferecendo uma experiência musical única aos seus clientes"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[e.jsxs("button",{onClick:o,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-blue-600 bg-white rounded-lg shadow-lg hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",children:[e.jsx(be,{className:"w-5 h-5 mr-2"}),"Testar Gratuitamente"]}),e.jsxs("button",{onClick:l,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg shadow-lg hover:bg-white hover:text-blue-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",children:[e.jsx(Jt,{className:"w-5 h-5 mr-2"}),"Ver Dashboard"]})]})]})})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"col-span-1 md:col-span-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:e.jsx(te,{className:"w-6 h-6 text-white"})}),e.jsx("span",{className:"text-xl font-bold",children:"PlaylistInterativa"})]}),e.jsx("p",{className:"text-gray-400 mb-4 max-w-md",children:"Transformando a experiência musical em restaurantes através de tecnologia interativa e colaborativa."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(pe,{onClick:o,size:"sm",children:"Experimentar Demo"}),e.jsx(pe,{onClick:l,variant:"outline",size:"sm",children:"Área Admin"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Recursos"}),e.jsxs("ul",{className:"space-y-2 text-gray-400",children:[e.jsx("li",{children:"QR Codes Dinâmicos"}),e.jsx("li",{children:"Integração YouTube"}),e.jsx("li",{children:"Analytics Avançados"}),e.jsx("li",{children:"Dashboard Admin"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Suporte"}),e.jsxs("ul",{className:"space-y-2 text-gray-400",children:[e.jsx("li",{children:"Documentação"}),e.jsx("li",{children:"Tutoriais"}),e.jsx("li",{children:"Suporte Técnico"}),e.jsx("li",{children:"FAQ"})]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:e.jsxs("p",{children:["© 2025 Uniqsuporte. Todos os direitos reservados. |"," ",e.jsx("a",{href:"https://www.uniqsuporte.com.br",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"www.uniqsuporte.com.br"})," ","|"," ",e.jsx("a",{href:"tel:+5522997986724",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"22 99798-6724"})]})})]})}),e.jsx(Mo,{isOpen:a,onClose:()=>n(!1),onLogin:u})]})},at=({size:r="md",color:t="primary",className:s})=>{const a={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"},n={primary:"text-primary-600 dark:text-primary-400",secondary:"text-secondary-600 dark:text-secondary-400",white:"text-white",gray:"text-gray-600 dark:text-gray-400"};return e.jsx(V.div,{initial:{opacity:0},animate:{opacity:1},className:Cs("inline-block",s),children:e.jsxs("svg",{className:Cs("animate-spin",a[r],n[t]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})},qo="http://localhost:8001";class Bo{constructor(){de(this,"client");de(this,"sessionId",null);this.client=Pn.create({baseURL:`${qo}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),this.initializeSessionId()}setupInterceptors(){this.client.interceptors.request.use(t=>{const s=this.getAuthToken();return s&&(t.headers.Authorization=`Bearer ${s}`),this.sessionId&&(t.headers["X-Session-ID"]=this.sessionId),t.headers["X-Device-Info"]=JSON.stringify({userAgent:navigator.userAgent,language:navigator.language,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,screenResolution:`${screen.width}x${screen.height}`}),t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,t=>(this.handleApiError(t),Promise.reject(t)))}initializeSessionId(){let t=localStorage.getItem("sessionId");t||(t=this.generateSessionId(),localStorage.setItem("sessionId",t)),this.sessionId=t}generateSessionId(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const s=Math.random()*16|0;return(t==="x"?s:s&3|8).toString(16)})}getAuthToken(){return localStorage.getItem("authToken")}setAuthToken(t){localStorage.setItem("authToken",t)}removeAuthToken(){localStorage.removeItem("authToken")}handleApiError(t){var a,n;const s=(a=t.response)==null?void 0:a.data;switch((n=t.response)==null?void 0:n.status){case 401:this.removeAuthToken(),window.location.pathname.includes("/admin")&&(window.location.href="/admin/login");break;case 403:w.error("Acesso negado");break;case 404:w.error("Recurso não encontrado");break;case 429:w.error("Muitas requisições. Tente novamente em alguns minutos.");break;case 500:w.error("Erro interno do servidor");break;default:s!=null&&s.message?w.error(s.message):t.message?w.error(t.message):w.error("Erro inesperado")}}async login(t,s){const a=await this.client.post("/auth/login",{email:t,password:s});return this.setAuthToken(a.data.token),a.data}async logout(){this.removeAuthToken()}async getCurrentUser(){return(await this.client.get("/auth/me")).data}async getRestaurant(t){return(await this.client.get(`/restaurants/${t}`)).data}async searchYouTube(t,s,a=10,n,i=!1){return(await this.client.get("/youtube/search",{params:{q:t,maxResults:a,pageToken:n,useYouTubeAPI:i.toString(),restaurantId:s}})).data}async getVideoInfo(t){return(await this.client.get(`/youtube/video/${t}`)).data}async createSuggestion(t){return(await this.client.post("/suggestions",t)).data}async getSuggestions(t,s){const a={...s};return a.status==="all"&&delete a.status,(await this.client.get(`/suggestions/${t}`,{params:a})).data}async getPlayQueue(t){return(await this.client.get(`/playback-queue/${t}`)).data}async voteSuggestion(t,s){return(await this.client.post(`/suggestions/${t}/vote`,{voteType:s})).data}async getPlaylists(t){return(await this.client.get(`/playlists/${t}`)).data}async getPlaylist(t){return(await this.client.get(`/playlists/${t}`)).data}async getAnalytics(t,s){return(await this.client.get(`/analytics/dashboard/${t}`,{params:s})).data}async getYouTubeQuota(){return(await this.client.get("/youtube/quota")).data}getSessionId(){return this.sessionId}isAuthenticated(){return!!this.getAuthToken()}async request(t){return(await this.client.request(t)).data}}const oe=new Bo,Qo=()=>{const r=gt(),{setUser:t,setAuthToken:s}=vt(),[a,n]=p.useState({email:"",password:""}),[i,o]=p.useState(!1),l=ui(({email:d,password:m})=>oe.login(d,m),{onSuccess:d=>{s(d.token),t(d.user),w.success("Login realizado com sucesso!"),r("/admin/dashboard")},onError:d=>{var x,h;const m=((h=(x=d.response)==null?void 0:x.data)==null?void 0:h.message)||"Erro ao fazer login";w.error(m)}}),c=async d=>{if(d.preventDefault(),!a.email||!a.password){w.error("Preencha todos os campos");return}try{if(await new Promise(m=>setTimeout(m,1e3)),a.email==="<EMAIL>"&&a.password==="Adm!n2024#Secure$"){console.log("Login Admin Principal válido, redirecionando para /admin/dashboard");const m={id:"super-admin-1",name:"Admin Principal",email:"<EMAIL>",role:"super_admin",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},x="mock-admin-token-"+Date.now();t(m),s(x),w.success("Login realizado com sucesso!"),r("/admin/dashboard")}else{w.error("Credenciais inválidas. Verifique email e senha.");return}}catch{w.error("Erro ao fazer login. Verifique suas credenciais.")}},u=d=>{const{name:m,value:x}=d.target;n(h=>({...h,[m]:x}))};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4",children:e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(V.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg",children:e.jsx(te,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Admin Principal"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Gerencie todos os restaurantes da plataforma"})]}),e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("form",{onSubmit:c,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Hs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:u,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ks,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{id:"password",name:"password",type:i?"text":"password",autoComplete:"current-password",required:!0,value:a.password,onChange:u,className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"••••••••"}),e.jsx("button",{type:"button",onClick:()=>o(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:i?e.jsx(Gs,{className:"w-5 h-5"}):e.jsx(Jt,{className:"w-5 h-5"})})]})]}),e.jsx(pe,{type:"submit",disabled:l.isLoading,className:"w-full",size:"lg",children:l.isLoading?e.jsxs(e.Fragment,{children:[e.jsx(at,{size:"sm"}),"Entrando..."]}):"Entrar"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("button",{onClick:()=>r("/"),className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors",children:"← Voltar para página inicial"})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500 dark:text-gray-400",children:"Restaurant Playlist System © 2024"})]})})},zo=()=>{const r=gt(),{setUser:t,setAuthToken:s}=vt(),[a,n]=p.useState("<EMAIL>"),[i,o]=p.useState("demo123"),[l,c]=p.useState(!1),[u,d]=p.useState(!1),m=async h=>{if(h.preventDefault(),!a||!i){w.error("Por favor, preencha todos os campos");return}d(!0);try{const f=await fetch(Z("/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:i})});if(!f.ok){const k=await f.json();throw new Error(k.error||"Erro ao fazer login")}const y=await f.json();console.log("🔐 Login realizado com sucesso:",y),localStorage.setItem("authToken",y.token),localStorage.setItem("user",JSON.stringify(y.user)),y.restaurant&&localStorage.setItem("restaurant",JSON.stringify(y.restaurant)),t(y.user),s(y.token),w.success("Login realizado com sucesso!"),y.restaurant?(console.log(`🔄 Redirecionando para /restaurant/${y.restaurant.id}/dashboard`),r(`/restaurant/${y.restaurant.id}/dashboard`,{replace:!0})):r("/restaurant/demo-restaurant/dashboard",{replace:!0})}catch(f){console.error("Erro no login:",f),w.error(f.message||"Erro ao fazer login. Verifique suas credenciais.")}finally{d(!1)}},x=()=>{n("<EMAIL>"),o("demo123")};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(V.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4",children:e.jsx(te,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Restaurante Admin"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Acesse o painel do seu restaurante"})]}),e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.5},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8",children:[e.jsxs("form",{onSubmit:m,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Hs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",value:a,onChange:h=>n(h.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ks,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:l?"text":"password",value:i,onChange:h=>o(h.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"••••••••",required:!0}),e.jsx("button",{type:"button",onClick:()=>c(!l),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:l?e.jsx(Gs,{className:"w-5 h-5"}):e.jsx(Jt,{className:"w-5 h-5"})})]})]}),e.jsx("button",{type:"button",onClick:x,className:"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-center py-2 transition-colors",children:"← Usar credenciais de demonstração"}),e.jsx(pe,{type:"submit",disabled:u,className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 font-medium transition-all duration-200 transform hover:scale-[1.02]",children:u?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Entrando..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(ys,{className:"w-5 h-5"}),e.jsx("span",{children:"Entrar"})]})})]}),e.jsxs("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2",children:"Credenciais para demonstração:"}),e.jsxs("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Email:"})," <EMAIL>"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Senha:"})," demo123"]})]})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Não tem uma conta?"," ",e.jsx("button",{onClick:()=>r("/"),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors",children:"Voltar ao início"})]})})]})]})})},Wo=()=>{const[r,t]=p.useState([]),[s,a]=p.useState(!0),[n,i]=p.useState(null),[o,l]=p.useState(!1),[c,u]=p.useState(null),[d,m]=p.useState({email:"",password:""}),[x,h]=p.useState({name:"",email:"",description:"",address:"",phone:"",password:""});p.useEffect(()=>{f()},[]);const f=async(j=!1)=>{var D;try{a(!0),i(null);const N=await oe.client.get("/admin/restaurants");console.log("Resposta da API:",N.data);const T=((D=N.data.restaurants)==null?void 0:D.map(E=>({...E,status:E.isActive?"active":"inactive"})))||[];t(T),j&&w.success("Lista de restaurantes atualizada com sucesso!")}catch(N){console.error("Erro ao carregar restaurantes:",N),i("Erro ao carregar restaurantes");const T=[{id:"demo-restaurant",name:"Restaurante Demo",email:"<EMAIL>",description:"Restaurante de demonstração",phone:"(11) 99999-9999",address:"Rua Demo, 123",isActive:!0,status:"active",createdAt:new Date().toISOString(),settings:{allowSuggestions:!0}}];t(T),w.error("Erro ao carregar restaurantes - usando dados de demonstração")}finally{a(!1)}},y=async()=>{var j,D;try{const T=(await oe.client.post("/admin/restaurants",x)).data;w.success("Restaurante criado com sucesso!"),w.success(`Login: ${T.credentials.email}
Senha: ${T.credentials.password}
URL: ${T.loginUrl}`,{duration:1e4}),l(!1),h({name:"",email:"",description:"",address:"",phone:"",password:""}),f()}catch(N){console.error("Erro ao criar restaurante:",N),w.error(((D=(j=N.response)==null?void 0:j.data)==null?void 0:D.error)||"Erro ao criar restaurante")}},k=async()=>{var j,D;if(c)try{const N={name:c.name,description:c.description,phone:c.phone,address:c.address};d.email.trim()&&(N.email=d.email),d.password.trim()&&(N.password=d.password);const T=await oe.client.put(`/admin/restaurants/${c.id}`,N);w.success("Restaurante atualizado com sucesso!"),u(null),m({email:"",password:""}),f()}catch(N){console.error("Erro ao atualizar restaurante:",N),w.error(((D=(j=N.response)==null?void 0:j.data)==null?void 0:D.error)||"Erro ao atualizar restaurante")}},b=async(j,D)=>{var N,T;try{const E=await oe.client.patch(`/admin/restaurants/${j}/status`,{isActive:D}),P=D?"ativado":"desativado";w.success(`Restaurante ${P} com sucesso!`),f()}catch(E){console.error("Erro ao atualizar restaurante:",E),w.error(((T=(N=E.response)==null?void 0:N.data)==null?void 0:T.error)||"Erro ao atualizar restaurante")}},v=async j=>{var D,N;if(confirm("Tem certeza que deseja suspender este restaurante?"))try{await oe.client.patch(`/admin/restaurants/${j}/status`,{isActive:!1}),w.success("Restaurante suspenso com sucesso!"),f()}catch(T){console.error("Erro ao suspender restaurante:",T),w.error(((N=(D=T.response)==null?void 0:D.data)==null?void 0:N.error)||"Erro ao suspender restaurante")}},C=async j=>{var D,N;try{await oe.client.patch(`/admin/restaurants/${j}/status`,{isActive:!0}),w.success("Restaurante reativado com sucesso!"),f()}catch(T){console.error("Erro ao reativar restaurante:",T),w.error(((N=(D=T.response)==null?void 0:D.data)==null?void 0:N.error)||"Erro ao reativar restaurante")}},F=async j=>{var D,N;if(confirm("Tem certeza que deseja deletar este restaurante? Esta ação não pode ser desfeita."))try{await oe.client.delete(`/admin/restaurants/${j}`),w.success("Restaurante deletado com sucesso!"),f()}catch(T){console.error("Erro ao deletar restaurante:",T),w.error(((N=(D=T.response)==null?void 0:D.data)==null?void 0:N.error)||"Erro ao deletar restaurante")}},M=j=>{switch(j.status){case"active":return"text-green-600 bg-green-100 dark:bg-green-900/20";case"inactive":return"text-gray-600 bg-gray-100 dark:bg-gray-900/20";case"suspended":return"text-red-600 bg-red-100 dark:bg-red-900/20";case"trial":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}},L=j=>{switch(j.status){case"active":return"Ativo";case"inactive":return"Inativo";case"suspended":return"Suspenso";case"trial":return"Trial";default:return"Desconhecido"}};return s?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciamento de Restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie todos os restaurantes da plataforma"})]})}),e.jsxs("div",{className:"flex flex-col justify-center items-center h-64 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"w-12 h-12 animate-spin text-blue-600"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-blue-100 dark:border-blue-900/20 rounded-full animate-pulse"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Carregando restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Por favor, aguarde..."})]})]})]}):n&&r.length===0?e.jsxs("div",{className:"flex flex-col justify-center items-center h-64 space-y-4",children:[e.jsx(Ye,{className:"w-12 h-12 text-red-500"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Erro ao carregar restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:n}),e.jsxs("button",{onClick:()=>f(!0),disabled:s,className:"mt-6 inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ue,{className:`w-5 h-5 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Tentar novamente"})]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciamento de Restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie todos os restaurantes da plataforma"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>f(!0),disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar lista de restaurantes",children:[e.jsx(ue,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>l(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(mr,{className:"w-4 h-4"}),e.jsx("span",{children:"Novo Restaurante"})]})]})]}),r.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4",children:e.jsx(et,{className:"w-12 h-12 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Nenhum restaurante encontrado"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Comece criando seu primeiro restaurante na plataforma."}),e.jsxs("button",{onClick:()=>l(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(mr,{className:"w-5 h-5"}),e.jsx("span",{children:"Criar Primeiro Restaurante"})]})]}):e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:r.map(j=>{var D,N;return e.jsxs(V.div,{layout:!0,initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300",children:[e.jsxs("div",{className:"flex justify-between items-start mb-6",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:j.name}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 flex items-center",children:[e.jsxs("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e.jsx("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),j.email]})]}),e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${M(j)}`,children:L(j)})]}),j.description&&e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg",children:j.description})}),e.jsxs("div",{className:"space-y-2 mb-6",children:[j.phone&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),j.phone]}),j.address&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),j.address]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Sugestões"}),e.jsx("span",{className:`font-medium ${(D=j.settings)!=null&&D.allowSuggestions?"text-green-600":"text-red-600"}`,children:(N=j.settings)!=null&&N.allowSuggestions?"Ativas":"Inativas"})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Criado em"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:new Date(j.createdAt).toLocaleDateString("pt-BR")})]}),j.lastActivityAt&&e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Última atividade"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:new Date(j.lastActivityAt).toLocaleDateString("pt-BR")})]})]}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("button",{onClick:()=>window.open(`/restaurant/${j.id}/dashboard`,"_blank"),className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium",title:"Acessar Dashboard do Restaurante",children:[e.jsx(qr,{className:"w-4 h-4"}),e.jsx("span",{children:"Acessar Dashboard"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("button",{onClick:()=>{u(j),j.adminUser?m({email:j.adminUser.email||"",password:""}):m({email:"",password:""})},className:"flex items-center justify-center space-x-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm",title:"Editar Restaurante",children:[e.jsx(tn,{className:"w-4 h-4"}),e.jsx("span",{children:"Editar"})]}),e.jsxs("button",{onClick:()=>b(j.id,!j.isActive),className:`flex items-center justify-center space-x-1 px-3 py-2 rounded-lg transition-colors text-sm font-medium ${j.isActive?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50":"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50"}`,title:j.isActive?"Desativar Restaurante":"Ativar Restaurante",children:[j.isActive?e.jsx(Br,{className:"w-4 h-4"}):e.jsx(be,{className:"w-4 h-4"}),e.jsx("span",{children:j.isActive?"Pausar":"Ativar"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[j.status!=="suspended"?e.jsxs("button",{onClick:()=>v(j.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors text-sm",title:"Suspender Restaurante",children:[e.jsx(Qr,{className:"w-4 h-4"}),e.jsx("span",{children:"Suspender"})]}):e.jsxs("button",{onClick:()=>C(j.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors text-sm",title:"Reativar Restaurante",children:[e.jsx(sn,{className:"w-4 h-4"}),e.jsx("span",{children:"Reativar"})]}),e.jsxs("button",{onClick:()=>F(j.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors text-sm",title:"Deletar Restaurante",children:[e.jsx(rn,{className:"w-4 h-4"}),e.jsx("span",{children:"Deletar"})]})]})]})]},j.id)})}),e.jsxs(Re,{children:[o&&e.jsx(V.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(V.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Criar Novo Restaurante"}),"              ",e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Restaurante *"}),e.jsx("input",{type:"text",value:x.name,onChange:j=>h({...x,name:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Restaurante do João"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email de Login *"}),e.jsx("input",{type:"email",value:x.email,onChange:j=>h({...x,email:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Senha *"}),e.jsx("input",{type:"password",value:x.password,onChange:j=>h({...x,password:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Senha segura"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:x.description,onChange:j=>h({...x,description:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:2,placeholder:"Descrição do restaurante"})]})]}),e.jsxs("div",{className:"flex space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>l(!1),className:"flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300",children:"Cancelar"}),e.jsx("button",{onClick:y,disabled:!x.name||!x.email||!x.password,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Criar"})]})]})}),c&&e.jsx(V.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(V.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Editar Restaurante"}),"              ",e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Restaurante *"}),e.jsx("input",{type:"text",value:c.name,onChange:j=>u({...c,name:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Restaurante do João"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:c.description,onChange:j=>u({...c,description:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:2,placeholder:"Descrição do restaurante"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Telefone"}),e.jsx("input",{type:"text",value:c.phone||"",onChange:j=>u({...c,phone:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"(11) 99999-9999"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Endereço"}),e.jsx("input",{type:"text",value:c.address||"",onChange:j=>u({...c,address:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Rua, número, bairro"})]}),e.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"Credenciais de Login"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email de Login"}),e.jsx("input",{type:"email",value:d.email,onChange:j=>m({...d,email:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Deixe em branco para manter o email atual"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nova Senha"}),e.jsx("input",{type:"password",value:d.password,onChange:j=>m({...d,password:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Nova senha (opcional)"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Deixe em branco para manter a senha atual"})]})]})]})]}),e.jsxs("div",{className:"flex space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>{u(null),m({email:"",password:""})},className:"flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300",children:"Cancelar"}),e.jsx("button",{onClick:k,disabled:!c.name,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Salvar"})]})]})})]})]})},Ho=()=>{const[r,t]=p.useState(null),[s,a]=p.useState(!0),[n,i]=p.useState("30d"),[o,l]=p.useState("overview");p.useEffect(()=>{c()},[n]);const c=async()=>{var d;try{a(!0);const{data:m}=await oe.client.get("/admin/analytics");if(m!=null&&m.success&&((d=m.analytics)!=null&&d.overview)){const x=m.analytics.overview,h=m.analytics.topRestaurants||[];t({totalRestaurants:x.totalRestaurants??0,activeRestaurants:x.activeRestaurants??0,totalRevenue:x.totalRevenue??0,monthlyRevenue:x.monthlyGrowth??0,totalPayments:x.totalPayments??0,totalVotes:x.totalVotes??0,topRestaurants:h.map(f=>({id:f.id,name:f.name,revenue:f.revenue??0,transactions:f.transactions??f.payments??0})),revenueByMonth:[]})}else throw new Error("Resposta inválida do servidor")}catch(m){console.error("Erro ao carregar analytics globais:",m),w.error("Erro ao carregar dados de analytics")}finally{a(!1)}},u=async()=>{try{w.success("Exportando dados...")}catch{w.error("Erro ao exportar dados")}};return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(at,{})}):r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Analytics Globais"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Métricas consolidadas de todos os restaurantes"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("select",{value:n,onChange:d=>i(d.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"})]}),e.jsxs("button",{onClick:u,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar dados de analytics",children:[e.jsx(Js,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar"})]}),e.jsxs("button",{onClick:c,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados de analytics",children:[e.jsx(ue,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>l("revenue"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(zr,{className:"w-4 h-4"}),e.jsx("span",{children:"Relatório"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{key:"overview",label:"Visão Geral",icon:Ee},{key:"revenue",label:"Receitas",icon:lt},{key:"restaurants",label:"Restaurantes",icon:Ue}].map(d=>e.jsxs("button",{onClick:()=>l(d.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${o===d.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(d.icon,{className:"w-4 h-4"}),e.jsx("span",{children:d.label})]},d.key))}),o==="overview"&&e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Total de Restaurantes",value:r.totalRestaurants,subtitle:`${r.activeRestaurants} ativos`,icon:Ue,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Total",value:`R$ ${r.totalRevenue.toFixed(2)}`,subtitle:`+R$ ${r.monthlyRevenue.toFixed(2)} este mês`,icon:lt,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Pagamentos (Supervoto)",value:r.totalPayments.toLocaleString(),subtitle:"Últimos 30 dias",icon:lt,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Votos Totais",value:r.totalVotes.toLocaleString(),subtitle:"Engajamento na plataforma",icon:an,color:"text-yellow-600",bgColor:"bg-yellow-100 dark:bg-yellow-900/20"}].map((d,m)=>e.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:m*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:d.title}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:d.value}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:d.subtitle})]}),e.jsx("div",{className:`p-3 rounded-lg ${d.bgColor}`,children:e.jsx(d.icon,{className:`w-6 h-6 ${d.color}`})})]})},m))}),o==="restaurants"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Restaurantes por Receita"}),e.jsx("div",{className:"space-y-4",children:r.topRestaurants.map((d,m)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 text-blue-600 rounded-full text-sm font-bold",children:m+1}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:d.name}),typeof d.transactions=="number"&&e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[d.transactions," transações"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",d.revenue.toFixed(2)]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"receita"})]})]},d.id))})]}),o==="revenue"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Receita por Mês"}),e.jsx("div",{className:"space-y-3",children:r.revenueByMonth.map(d=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:d.month}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",d.revenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[d.payments," pagamentos"]})]})]},d.month))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Atividade de Pagamentos"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Em breve: distribuição por método de pagamento, horários de pico e mais."})]})]})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Nenhum dado disponível"})})},Ko=()=>{const[r,t]=p.useState(null),[s,a]=p.useState(!0),[n,i]=p.useState("30d"),[o,l]=p.useState("overview"),c=10;p.useEffect(()=>{u()},[n]);const u=async()=>{try{a(!0);const x=await(await fetch(`/api/v1/admin/revenue?period=${n}`)).json();if(x.success)t(x.revenue);else throw new Error("Erro ao carregar dados de receita")}catch(m){console.error("Erro ao carregar dados de receita:",m),w.error("Erro ao carregar dados de receita"),t({totalRevenue:12450.75,platformRevenue:1245.08,restaurantRevenue:11205.67,monthlyGrowth:15.3,totalTransactions:623,averageTransactionValue:2,revenueByRestaurant:[{restaurantId:"1",restaurantName:"Restaurante A",totalRevenue:3250.5,platformShare:325.05,transactions:162,averageValue:2.01},{restaurantId:"2",restaurantName:"Restaurante B",totalRevenue:2890.3,platformShare:289.03,transactions:144,averageValue:2.01},{restaurantId:"3",restaurantName:"Restaurante C",totalRevenue:2150.2,platformShare:215.02,transactions:107,averageValue:2.01},{restaurantId:"4",restaurantName:"Restaurante D",totalRevenue:1980.15,platformShare:198.02,transactions:99,averageValue:2},{restaurantId:"5",restaurantName:"Restaurante E",totalRevenue:1679.6,platformShare:167.96,transactions:84,averageValue:1.99}],revenueByMonth:[{month:"Jan",totalRevenue:8200,platformRevenue:820,transactions:410},{month:"Fev",totalRevenue:9350,platformRevenue:935,transactions:467},{month:"Mar",totalRevenue:10180,platformRevenue:1018,transactions:509},{month:"Abr",totalRevenue:11420,platformRevenue:1142,transactions:571},{month:"Mai",totalRevenue:12450,platformRevenue:1245,transactions:623}],paymentMethods:[{method:"PIX",count:589,revenue:11780,percentage:94.6},{method:"Cartão",count:34,revenue:670.75,percentage:5.4}]})}finally{a(!1)}},d=async()=>{try{w.success("Exportando relatório de receitas...");const m=await fetch(`/api/v1/admin/revenue/export?period=${n}`);if(!m.ok)throw new Error("Falha ao exportar");const x=await m.blob(),h=window.URL.createObjectURL(x),f=document.createElement("a");f.href=h,f.download=`revenue_${n}_${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(f),f.click(),f.remove(),window.URL.revokeObjectURL(h)}catch{w.error("Erro ao exportar dados")}};return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(at,{})}):r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Dashboard de Receitas"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Controle financeiro e receitas compartilhadas"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("select",{value:n,onChange:m=>i(m.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"}),e.jsx("option",{value:"1y",children:"Último ano"})]}),e.jsxs("button",{onClick:d,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar dados de receita",children:[e.jsx(Js,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar"})]}),e.jsxs("button",{onClick:u,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados de receita",children:[e.jsx(ue,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>l("overview"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(zr,{className:"w-4 h-4"}),e.jsx("span",{children:"Relatório"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{key:"overview",label:"Visão Geral",icon:Ee},{key:"restaurants",label:"Por Restaurante",icon:Ue},{key:"trends",label:"Tendências",icon:ve}].map(m=>e.jsxs("button",{onClick:()=>l(m.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${o===m.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(m.icon,{className:"w-4 h-4"}),e.jsx("span",{children:m.label})]},m.key))}),o==="overview"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Receita Total",value:`R$ ${r.totalRevenue.toFixed(2)}`,subtitle:`${r.totalTransactions} transações`,icon:lt,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Nossa Receita",value:`R$ ${r.platformRevenue.toFixed(2)}`,subtitle:`${c}% da receita total`,icon:ve,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Restaurantes",value:`R$ ${r.restaurantRevenue.toFixed(2)}`,subtitle:`${100-c}% da receita total`,icon:Ue,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Ticket Médio",value:`R$ ${r.averageTransactionValue.toFixed(2)}`,subtitle:`+${r.monthlyGrowth}% este mês`,icon:ye,color:"text-orange-600",bgColor:"bg-orange-100 dark:bg-orange-900/20"}].map((m,x)=>e.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:x*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:m.title}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:m.value}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:m.subtitle})]}),e.jsx("div",{className:`p-3 rounded-lg ${m.bgColor}`,children:e.jsx(m.icon,{className:`w-6 h-6 ${m.color}`})})]})},x))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Métodos de Pagamento"}),e.jsx("div",{className:"space-y-4",children:r.paymentMethods.map(m=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:m.method})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",m.revenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[m.count," transações (",m.percentage,"%)"]})]})]},m.method))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Distribuição de Receita"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),e.jsxs("span",{className:"text-gray-900 dark:text-white font-medium",children:["Plataforma (",c,"%)"]})]}),e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",r.platformRevenue.toFixed(2)]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsxs("span",{className:"text-gray-900 dark:text-white font-medium",children:["Restaurantes (",100-c,"%)"]})]}),e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",r.restaurantRevenue.toFixed(2)]})]})]})]})]})]}),o==="restaurants"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Receita por Restaurante"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Restaurante"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Receita Total"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Nossa Parte"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Transações"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Ticket Médio"})]})}),e.jsx("tbody",{children:r.revenueByRestaurant.map(m=>e.jsxs("tr",{className:"border-b border-gray-100 dark:border-gray-800",children:[e.jsx("td",{className:"py-3 px-4",children:e.jsx("div",{className:"font-medium text-gray-900 dark:text-white",children:m.restaurantName})}),e.jsxs("td",{className:"py-3 px-4 text-right font-semibold text-gray-900 dark:text-white",children:["R$ ",m.totalRevenue.toFixed(2)]}),e.jsxs("td",{className:"py-3 px-4 text-right font-semibold text-green-600",children:["R$ ",m.platformShare.toFixed(2)]}),e.jsx("td",{className:"py-3 px-4 text-right text-gray-600 dark:text-gray-400",children:m.transactions}),e.jsxs("td",{className:"py-3 px-4 text-right text-gray-600 dark:text-gray-400",children:["R$ ",m.averageValue.toFixed(2)]})]},m.restaurantId))})]})})]}),o==="trends"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Evolução da Receita"}),e.jsx("div",{className:"space-y-4",children:r.revenueByMonth.map(m=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:m.month}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[m.transactions," transações"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",m.totalRevenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-green-600",children:["+R$ ",m.platformRevenue.toFixed(2)," nossa parte"]})]})]},m.month))})]})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Nenhum dado de receita disponível"})})},Go=()=>{const[r,t]=p.useState(null),[s,a]=p.useState(!0),[n,i]=p.useState(!1),[o,l]=p.useState("platform");p.useEffect(()=>{c()},[]);const c=async()=>{try{a(!0);const{data:h}=await oe.client.get("/admin/settings");if(h.success)t({platform:{name:"Restaurant Playlist System",version:"2.1.0",maintenanceMode:h.settings.system.maintenanceMode,maxRestaurants:h.settings.system.maxRestaurantsPerPlan,defaultLanguage:"pt-BR",allowNewRegistrations:!!h.settings.system.allowNewRegistrations,defaultTrialDays:Number(h.settings.system.defaultTrialDays||30)},revenue:{platformFeePercentage:h.settings.payments.commissionRate,minimumPaymentAmount:2,paymentMethods:["pix","credit_card"],autoPayoutEnabled:!0,payoutFrequency:"weekly",defaultCurrency:h.settings.payments.defaultCurrency||"BRL"},notifications:{emailEnabled:h.settings.notifications.emailNotifications,smsEnabled:h.settings.notifications.smsNotifications,webhookEnabled:!!h.settings.notifications.webhookUrl,webhookUrl:h.settings.notifications.webhookUrl||"",adminNotifications:{newRestaurant:!0,paymentIssues:!0,systemErrors:!0,maintenanceAlerts:!0}},security:{sessionTimeout:3600,maxLoginAttempts:5,requireTwoFactor:!1,allowedDomains:["localhost","yourdomain.com"],rateLimitEnabled:!0,rateLimitRequests:100},integrations:{mercadoPago:{enabled:h.settings.payments.pixEnabled,environment:"sandbox",webhookUrl:h.settings.notifications.webhookUrl||""},youtube:{enabled:h.settings.features.analyticsEnabled,apiQuotaLimit:1e4,cacheEnabled:!0}}});else throw new Error("Erro ao carregar configurações")}catch(h){console.error("Erro ao carregar configurações:",h),w.error("Erro ao carregar configurações"),t({platform:{name:"Restaurant Playlist System",version:"2.1.0",maintenanceMode:!1,maxRestaurants:100,defaultLanguage:"pt-BR",allowNewRegistrations:!0,defaultTrialDays:30},revenue:{platformFeePercentage:10,minimumPaymentAmount:2,paymentMethods:["pix","credit_card"],autoPayoutEnabled:!0,payoutFrequency:"weekly",defaultCurrency:"BRL"},notifications:{emailEnabled:!0,smsEnabled:!1,webhookEnabled:!0,webhookUrl:"https://yourdomain.com/api/v1/payments/webhook",adminNotifications:{newRestaurant:!0,paymentIssues:!0,systemErrors:!0,maintenanceAlerts:!0}},security:{sessionTimeout:3600,maxLoginAttempts:5,requireTwoFactor:!1,allowedDomains:["localhost","yourdomain.com"],rateLimitEnabled:!0,rateLimitRequests:100},integrations:{mercadoPago:{enabled:!0,environment:"sandbox",webhookUrl:"https://yourdomain.com/api/v1/payments/webhook"},youtube:{enabled:!0,apiQuotaLimit:1e4,cacheEnabled:!0}}})}finally{a(!1)}},u=async()=>{if(r)try{i(!0);const h={settings:{system:{maintenanceMode:r.platform.maintenanceMode,allowNewRegistrations:!!r.platform.allowNewRegistrations,maxRestaurantsPerPlan:r.platform.maxRestaurants,defaultTrialDays:r.platform.defaultTrialDays??30},notifications:{emailNotifications:r.notifications.emailEnabled,smsNotifications:r.notifications.smsEnabled,webhookUrl:r.notifications.webhookEnabled&&r.notifications.webhookUrl||""},payments:{stripeEnabled:r.revenue.paymentMethods.includes("credit_card"),pixEnabled:r.integrations.mercadoPago.enabled,defaultCurrency:r.revenue.defaultCurrency||"BRL",commissionRate:r.revenue.platformFeePercentage},features:{analyticsEnabled:r.integrations.youtube.enabled,competitiveVotingEnabled:!0,playlistSchedulingEnabled:!0,qrCodeGenerationEnabled:!0}}};await oe.client.put("/admin/settings",h),w.success("Configurações salvas com sucesso!")}catch(h){console.error("Erro ao salvar configurações:",h),w.error("Erro ao salvar configurações")}finally{i(!1)}},d=(h,f,y)=>{r&&t({...r,[h]:{...r[h],[f]:y}})},m=(h,f,y,k)=>{r&&t({...r,[h]:{...r[h],[f]:{...r[h][f],[y]:k}}})};if(s)return e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(at,{})});if(!r)return e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Erro ao carregar configurações"})});const x=[{key:"platform",label:"Plataforma",icon:on},{key:"revenue",label:"Receitas",icon:lt},{key:"notifications",label:"Notificações",icon:ln},{key:"security",label:"Segurança",icon:Qr},{key:"integrations",label:"Integrações",icon:Ge}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Configurações do Sistema"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Configurações globais da plataforma"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:c,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar configurações",children:[e.jsx(ue,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:u,disabled:n||s,className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",title:"Salvar todas as configurações",children:[e.jsx(Wr,{className:`w-4 h-4 ${n?"animate-pulse":""}`}),e.jsx("span",{children:n?"Salvando...":"Salvar"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/backup",className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(nn,{className:"w-4 h-4"}),e.jsx("span",{children:"Backup"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:x.map(h=>e.jsxs("button",{onClick:()=>l(h.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${o===h.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(h.icon,{className:"w-4 h-4"}),e.jsx("span",{children:h.label})]},h.key))}),o==="platform"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações da Plataforma"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome da Plataforma"}),e.jsx("input",{type:"text",value:r.platform.name,onChange:h=>d("platform","name",h.target.value),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Versão"}),e.jsx("input",{type:"text",value:r.platform.version,onChange:h=>d("platform","version",h.target.value),className:"input-field",readOnly:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Restaurantes"}),e.jsx("input",{type:"number",value:r.platform.maxRestaurants,onChange:h=>d("platform","maxRestaurants",parseInt(h.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Idioma Padrão"}),e.jsxs("select",{value:r.platform.defaultLanguage,onChange:h=>d("platform","defaultLanguage",h.target.value),className:"input-field",children:[e.jsx("option",{value:"pt-BR",children:"Português (Brasil)"}),e.jsx("option",{value:"en-US",children:"English (US)"}),e.jsx("option",{value:"es-ES",children:"Español"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Dias de Teste (Trial)"}),e.jsx("input",{type:"number",min:0,value:r.platform.defaultTrialDays??30,onChange:h=>d("platform","defaultTrialDays",parseInt(h.target.value||"0")),className:"input-field"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.platform.maintenanceMode,onChange:h=>d("platform","maintenanceMode",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Modo de Manutenção"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Quando ativado, apenas administradores podem acessar o sistema"}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:!!r.platform.allowNewRegistrations,onChange:h=>d("platform","allowNewRegistrations",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Permitir novos cadastros"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Controla se novos restaurantes podem se registrar na plataforma"})]})]})]}),o==="revenue"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações de Receita"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Taxa da Plataforma (%)"}),e.jsx("input",{type:"number",min:0,max:100,step:.1,value:r.revenue.platformFeePercentage,onChange:h=>d("revenue","platformFeePercentage",parseFloat(h.target.value)),className:"input-field"}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Porcentagem que a plataforma recebe de cada transação"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Moeda Padrão"}),e.jsxs("select",{value:r.revenue.defaultCurrency||"BRL",onChange:h=>d("revenue","defaultCurrency",h.target.value),className:"input-field",children:[e.jsx("option",{value:"BRL",children:"BRL (R$)"}),e.jsx("option",{value:"USD",children:"USD ($)"}),e.jsx("option",{value:"EUR",children:"EUR (€)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Valor Mínimo de Pagamento (R$)"}),e.jsx("input",{type:"number",min:0,step:.01,value:r.revenue.minimumPaymentAmount,onChange:h=>d("revenue","minimumPaymentAmount",parseFloat(h.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Frequência de Repasse"}),e.jsxs("select",{value:r.revenue.payoutFrequency,onChange:h=>d("revenue","payoutFrequency",h.target.value),className:"input-field",children:[e.jsx("option",{value:"daily",children:"Diário"}),e.jsx("option",{value:"weekly",children:"Semanal"}),e.jsx("option",{value:"monthly",children:"Mensal"})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.revenue.autoPayoutEnabled,onChange:h=>d("revenue","autoPayoutEnabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Repasse Automático"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Quando ativado, os repasses são feitos automaticamente"})]})]}),o==="notifications"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Notificações"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40",children:[e.jsxs("label",{className:"flex items-center space-x-3 mb-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.emailEnabled,onChange:h=>d("notifications","emailEnabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"Email"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.smsEnabled,onChange:h=>d("notifications","smsEnabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"SMS"})]})]}),e.jsxs("div",{className:"p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40",children:[e.jsxs("label",{className:"flex items-center space-x-3 mb-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.webhookEnabled,onChange:h=>d("notifications","webhookEnabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"Webhook"})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"URL do Webhook"}),e.jsx("input",{type:"url",placeholder:"https://seuservico.com/webhook",value:r.notifications.webhookUrl||"",onChange:h=>d("notifications","webhookUrl",h.target.value),className:"input-field",disabled:!r.notifications.webhookEnabled})]})]})]})]}),o==="security"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações de Segurança"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Timeout de Sessão (segundos)"}),e.jsx("input",{type:"number",min:"300",value:r.security.sessionTimeout,onChange:h=>d("security","sessionTimeout",parseInt(h.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Tentativas de Login"}),e.jsx("input",{type:"number",min:"1",max:"10",value:r.security.maxLoginAttempts,onChange:h=>d("security","maxLoginAttempts",parseInt(h.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Rate Limit (requisições/minuto)"}),e.jsx("input",{type:"number",min:"10",value:r.security.rateLimitRequests,onChange:h=>d("security","rateLimitRequests",parseInt(h.target.value)),className:"input-field"})]})]}),e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.security.requireTwoFactor,onChange:h=>d("security","requireTwoFactor",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Exigir Autenticação de Dois Fatores"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.security.rateLimitEnabled,onChange:h=>d("security","rateLimitEnabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Rate Limiting"})]})]})]}),o==="integrations"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Mercado Pago"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Ambiente"}),e.jsxs("select",{value:r.integrations.mercadoPago.environment,onChange:h=>m("integrations","mercadoPago","environment",h.target.value),className:"input-field",children:[e.jsx("option",{value:"sandbox",children:"Sandbox (Teste)"}),e.jsx("option",{value:"production",children:"Produção"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Webhook URL"}),e.jsx("input",{type:"url",value:r.integrations.mercadoPago.webhookUrl,onChange:h=>m("integrations","mercadoPago","webhookUrl",h.target.value),className:"input-field"})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.mercadoPago.enabled,onChange:h=>m("integrations","mercadoPago","enabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Integração Mercado Pago"})]})})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"YouTube API"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Limite de Quota da API"}),e.jsx("input",{type:"number",min:"1000",value:r.integrations.youtube.apiQuotaLimit,onChange:h=>m("integrations","youtube","apiQuotaLimit",parseInt(h.target.value)),className:"input-field"})]})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.youtube.enabled,onChange:h=>m("integrations","youtube","enabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Integração YouTube"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.youtube.cacheEnabled,onChange:h=>m("integrations","youtube","cacheEnabled",h.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Cache de Dados"})]})]})]})]})]})},ms=()=>{var c;const[r,t]=p.useState(null),[s,a]=p.useState(!0),[n,i]=p.useState(null);p.useEffect(()=>{o()},[]);const o=async()=>{var u,d;try{a(!0);const[m,x]=await Promise.all([fetch("/api/v1/admin/analytics"),fetch("/api/v1/admin/revenue?period=30d")]),[h,f]=await Promise.all([m.json(),x.json()]);if(h!=null&&h.success&&((u=h.analytics)!=null&&u.overview))t({totalRestaurants:h.analytics.overview.totalRestaurants||0,activeRestaurants:h.analytics.overview.activeRestaurants||0,totalRevenue:h.analytics.overview.totalRevenue||0,monthlyRevenue:h.analytics.overview.monthlyGrowth||0,totalSuggestions:h.analytics.overview.totalSuggestions||0,totalPayments:h.analytics.overview.totalPayments||0,averageRating:h.analytics.overview.averageRating||0,systemUptime:h.analytics.overview.systemUptime||"—"}),f!=null&&f.success&&((d=f.revenue)==null?void 0:d.totalRevenue)!=null&&i({totalRevenue:f.revenue.totalRevenue});else throw new Error("Resposta inválida de /admin/analytics")}catch(m){console.error("Erro ao carregar estatísticas globais:",m),w.error("Erro ao carregar dados do dashboard")}finally{a(!1)}},l=[{title:"Restaurantes Ativos",value:(r==null?void 0:r.activeRestaurants)||"0",total:(r==null?void 0:r.totalRestaurants)||"0",change:"+2 este mês",icon:Ue,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Total",value:`R$ ${((n==null?void 0:n.totalRevenue)??(r==null?void 0:r.totalRevenue)??0).toFixed(2)}`,change:`+R$ ${((c=r==null?void 0:r.monthlyRevenue)==null?void 0:c.toFixed(2))||"0,00"} este mês`,icon:ve,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Pagamentos (Supervoto)",value:(r==null?void 0:r.totalPayments)||"0",change:"Últimos 30 dias",icon:ve,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Uptime do Sistema",value:(r==null?void 0:r.systemUptime)||"—",change:"Últimas 24h",icon:ue,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20"}];return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(at,{})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Visão geral do sistema e controle de receitas"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:o,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados do dashboard",children:[e.jsx(ue,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/analytics",className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(Ee,{className:"w-4 h-4"}),e.jsx("span",{children:"Ver Analytics"})]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:l.map((u,d)=>e.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:d*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:u.title}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[u.value,u.total&&e.jsxs("span",{className:"text-sm text-gray-500 ml-1",children:["/ ",u.total]})]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[u.change," vs ontem"]})]}),e.jsx("div",{className:`w-12 h-12 rounded-lg ${u.bgColor} flex items-center justify-center`,children:e.jsx(u.icon,{className:`w-6 h-6 ${u.color}`})})]})},d))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Transações Recentes (Supervoto)"}),e.jsx("div",{className:"space-y-3",children:[1,2,3,4].map(u=>e.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:e.jsx(ve,{className:"w-4 h-4 text-green-600 dark:text-green-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["R$ ",(5*u).toFixed(2)," • Restaurante #",u]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["há ",u," min • Supervoto"]})]})]},u))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Restaurantes por Receita (30d)"}),e.jsx("div",{className:"space-y-3",children:[1,2,3,4].map(u=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded text-xs flex items-center justify-center font-medium",children:u}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Restaurante #",u]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["ID: demo-",u]})]})]}),e.jsxs("div",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:["R$ ",(1e3*u).toFixed(2)]})]},u))})]})]})]})},Yo=()=>e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(Ke,{to:"/admin",className:"flex items-center space-x-3 hover:opacity-80 transition-opacity",children:[e.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:e.jsx(te,{className:"w-5 h-5 text-white"})}),e.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Restaurant Playlist Admin"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Admin"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e.jsx(Je,{className:"w-4 h-4 text-gray-600 dark:text-gray-300"})}),e.jsxs(Ke,{to:"/",className:"flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium",title:"Sair do Admin",children:[e.jsx(Hr,{className:"w-4 h-4"}),e.jsx("span",{children:"Sair"})]})]})]})]})})}),e.jsx("nav",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex space-x-8",children:[{name:"Dashboard",icon:Kr,path:"/admin/dashboard"},{name:"Restaurantes",icon:Ue,path:"/admin/restaurants"},{name:"Analytics Globais",icon:Ee,path:"/admin/analytics"},{name:"Receitas",icon:ve,path:"/admin/revenue"},{name:"Configurações",icon:Ge,path:"/admin/settings"}].map(r=>e.jsxs(Ke,{to:r.path,className:"flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300",children:[e.jsx(r.icon,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:r.name})]},r.name))})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs(Ws,{children:[e.jsx(z,{path:"/",element:e.jsx(ms,{})}),e.jsx(z,{path:"/dashboard",element:e.jsx(ms,{})}),e.jsx(z,{path:"/restaurants",element:e.jsx(Wo,{})}),e.jsx(z,{path:"/analytics",element:e.jsx(Ho,{})}),e.jsx(z,{path:"/revenue",element:e.jsx(Ko,{})}),e.jsx(z,{path:"/settings",element:e.jsx(Go,{})}),e.jsx(z,{path:"*",element:e.jsx(ms,{})})]})})]}),Jo="modulepreload",Xo=function(r){return"/"+r},Rr={},xe=function(t,s,a){if(!s||s.length===0)return t();const n=document.getElementsByTagName("link");return Promise.all(s.map(i=>{if(i=Xo(i),i in Rr)return;Rr[i]=!0;const o=i.endsWith(".css"),l=o?'[rel="stylesheet"]':"";if(!!a)for(let d=n.length-1;d>=0;d--){const m=n[d];if(m.href===i&&(!o||m.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${l}`))return;const u=document.createElement("link");if(u.rel=o?"stylesheet":Jo,o||(u.as="script",u.crossOrigin=""),u.href=i,document.head.appendChild(u),o)return new Promise((d,m)=>{u.addEventListener("load",d),u.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=i,window.dispatchEvent(o),!o.defaultPrevented)throw i})},we=Object.create(null);we.open="0";we.close="1";we.ping="2";we.pong="3";we.message="4";we.upgrade="5";we.noop="6";const $t=Object.create(null);Object.keys(we).forEach(r=>{$t[we[r]]=r});const Rs={type:"error",data:"parser error"},va=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",ja=typeof ArrayBuffer=="function",wa=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r&&r.buffer instanceof ArrayBuffer,tr=({type:r,data:t},s,a)=>va&&t instanceof Blob?s?a(t):_r(t,a):ja&&(t instanceof ArrayBuffer||wa(t))?s?a(t):_r(new Blob([t]),a):a(we[r]+(t||"")),_r=(r,t)=>{const s=new FileReader;return s.onload=function(){const a=s.result.split(",")[1];t("b"+(a||""))},s.readAsDataURL(r)};function Pr(r){return r instanceof Uint8Array?r:r instanceof ArrayBuffer?new Uint8Array(r):new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}let ps;function Zo(r,t){if(va&&r.data instanceof Blob)return r.data.arrayBuffer().then(Pr).then(t);if(ja&&(r.data instanceof ArrayBuffer||wa(r.data)))return t(Pr(r.data));tr(r,!1,s=>{ps||(ps=new TextEncoder),t(ps.encode(s))})}const Ar="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ot=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let r=0;r<Ar.length;r++)ot[Ar.charCodeAt(r)]=r;const el=r=>{let t=r.length*.75,s=r.length,a,n=0,i,o,l,c;r[r.length-1]==="="&&(t--,r[r.length-2]==="="&&t--);const u=new ArrayBuffer(t),d=new Uint8Array(u);for(a=0;a<s;a+=4)i=ot[r.charCodeAt(a)],o=ot[r.charCodeAt(a+1)],l=ot[r.charCodeAt(a+2)],c=ot[r.charCodeAt(a+3)],d[n++]=i<<2|o>>4,d[n++]=(o&15)<<4|l>>2,d[n++]=(l&3)<<6|c&63;return u},tl=typeof ArrayBuffer=="function",sr=(r,t)=>{if(typeof r!="string")return{type:"message",data:Na(r,t)};const s=r.charAt(0);return s==="b"?{type:"message",data:sl(r.substring(1),t)}:$t[s]?r.length>1?{type:$t[s],data:r.substring(1)}:{type:$t[s]}:Rs},sl=(r,t)=>{if(tl){const s=el(r);return Na(s,t)}else return{base64:!0,data:r}},Na=(r,t)=>{switch(t){case"blob":return r instanceof Blob?r:new Blob([r]);case"arraybuffer":default:return r instanceof ArrayBuffer?r:r.buffer}},ka=String.fromCharCode(30),rl=(r,t)=>{const s=r.length,a=new Array(s);let n=0;r.forEach((i,o)=>{tr(i,!1,l=>{a[o]=l,++n===s&&t(a.join(ka))})})},al=(r,t)=>{const s=r.split(ka),a=[];for(let n=0;n<s.length;n++){const i=sr(s[n],t);if(a.push(i),i.type==="error")break}return a};function nl(){return new TransformStream({transform(r,t){Zo(r,s=>{const a=s.length;let n;if(a<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,a);else if(a<65536){n=new Uint8Array(3);const i=new DataView(n.buffer);i.setUint8(0,126),i.setUint16(1,a)}else{n=new Uint8Array(9);const i=new DataView(n.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(a))}r.data&&typeof r.data!="string"&&(n[0]|=128),t.enqueue(n),t.enqueue(s)})}})}let fs;function Rt(r){return r.reduce((t,s)=>t+s.length,0)}function _t(r,t){if(r[0].length===t)return r.shift();const s=new Uint8Array(t);let a=0;for(let n=0;n<t;n++)s[n]=r[0][a++],a===r[0].length&&(r.shift(),a=0);return r.length&&a<r[0].length&&(r[0]=r[0].slice(a)),s}function il(r,t){fs||(fs=new TextDecoder);const s=[];let a=0,n=-1,i=!1;return new TransformStream({transform(o,l){for(s.push(o);;){if(a===0){if(Rt(s)<1)break;const c=_t(s,1);i=(c[0]&128)===128,n=c[0]&127,n<126?a=3:n===126?a=1:a=2}else if(a===1){if(Rt(s)<2)break;const c=_t(s,2);n=new DataView(c.buffer,c.byteOffset,c.length).getUint16(0),a=3}else if(a===2){if(Rt(s)<8)break;const c=_t(s,8),u=new DataView(c.buffer,c.byteOffset,c.length),d=u.getUint32(0);if(d>Math.pow(2,53-32)-1){l.enqueue(Rs);break}n=d*Math.pow(2,32)+u.getUint32(4),a=3}else{if(Rt(s)<n)break;const c=_t(s,n);l.enqueue(sr(i?c:fs.decode(c),t)),a=0}if(n===0||n>r){l.enqueue(Rs);break}}}})}const Sa=4;function se(r){if(r)return ol(r)}function ol(r){for(var t in se.prototype)r[t]=se.prototype[t];return r}se.prototype.on=se.prototype.addEventListener=function(r,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+r]=this._callbacks["$"+r]||[]).push(t),this};se.prototype.once=function(r,t){function s(){this.off(r,s),t.apply(this,arguments)}return s.fn=t,this.on(r,s),this};se.prototype.off=se.prototype.removeListener=se.prototype.removeAllListeners=se.prototype.removeEventListener=function(r,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var s=this._callbacks["$"+r];if(!s)return this;if(arguments.length==1)return delete this._callbacks["$"+r],this;for(var a,n=0;n<s.length;n++)if(a=s[n],a===t||a.fn===t){s.splice(n,1);break}return s.length===0&&delete this._callbacks["$"+r],this};se.prototype.emit=function(r){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),s=this._callbacks["$"+r],a=1;a<arguments.length;a++)t[a-1]=arguments[a];if(s){s=s.slice(0);for(var a=0,n=s.length;a<n;++a)s[a].apply(this,t)}return this};se.prototype.emitReserved=se.prototype.emit;se.prototype.listeners=function(r){return this._callbacks=this._callbacks||{},this._callbacks["$"+r]||[]};se.prototype.hasListeners=function(r){return!!this.listeners(r).length};const ts=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,s)=>s(t,0))(),fe=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),ll="arraybuffer";function Ea(r,...t){return t.reduce((s,a)=>(r.hasOwnProperty(a)&&(s[a]=r[a]),s),{})}const cl=fe.setTimeout,dl=fe.clearTimeout;function ss(r,t){t.useNativeTimers?(r.setTimeoutFn=cl.bind(fe),r.clearTimeoutFn=dl.bind(fe)):(r.setTimeoutFn=fe.setTimeout.bind(fe),r.clearTimeoutFn=fe.clearTimeout.bind(fe))}const ul=1.33;function hl(r){return typeof r=="string"?ml(r):Math.ceil((r.byteLength||r.size)*ul)}function ml(r){let t=0,s=0;for(let a=0,n=r.length;a<n;a++)t=r.charCodeAt(a),t<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(a++,s+=4);return s}function Ca(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function pl(r){let t="";for(let s in r)r.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(r[s]));return t}function fl(r){let t={},s=r.split("&");for(let a=0,n=s.length;a<n;a++){let i=s[a].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}class xl extends Error{constructor(t,s,a){super(t),this.description=s,this.context=a,this.type="TransportError"}}class rr extends se{constructor(t){super(),this.writable=!1,ss(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,s,a){return super.emitReserved("error",new xl(t,s,a)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const s=sr(t,this.socket.binaryType);this.onPacket(s)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,s={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(s)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const s=pl(t);return s.length?"?"+s:""}}class gl extends rr{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const s=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let a=0;this._polling&&(a++,this.once("pollComplete",function(){--a||s()})),this.writable||(a++,this.once("drain",function(){--a||s()}))}else s()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const s=a=>{if(this.readyState==="opening"&&a.type==="open"&&this.onOpen(),a.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(a)};al(t,this.socket.binaryType).forEach(s),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,rl(t,s=>{this.doWrite(s,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",s=this.query||{};return this.opts.timestampRequests!==!1&&(s[this.opts.timestampParam]=Ca()),!this.supportsBinary&&!s.sid&&(s.b64=1),this.createUri(t,s)}}let Ra=!1;try{Ra=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const yl=Ra;function bl(){}class vl extends gl{constructor(t){if(super(t),typeof location<"u"){const s=location.protocol==="https:";let a=location.port;a||(a=s?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||a!==t.port}}doWrite(t,s){const a=this.request({method:"POST",data:t});a.on("success",s),a.on("error",(n,i)=>{this.onError("xhr post error",n,i)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(s,a)=>{this.onError("xhr poll error",s,a)}),this.pollXhr=t}}class je extends se{constructor(t,s,a){super(),this.createRequest=t,ss(this,a),this._opts=a,this._method=a.method||"GET",this._uri=s,this._data=a.data!==void 0?a.data:null,this._create()}_create(){var t;const s=Ea(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");s.xdomain=!!this._opts.xd;const a=this._xhr=this.createRequest(s);try{a.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){a.setDisableHeaderCheck&&a.setDisableHeaderCheck(!0);for(let n in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(n)&&a.setRequestHeader(n,this._opts.extraHeaders[n])}}catch{}if(this._method==="POST")try{a.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{a.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(a),"withCredentials"in a&&(a.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(a.timeout=this._opts.requestTimeout),a.onreadystatechange=()=>{var n;a.readyState===3&&((n=this._opts.cookieJar)===null||n===void 0||n.parseCookies(a.getResponseHeader("set-cookie"))),a.readyState===4&&(a.status===200||a.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof a.status=="number"?a.status:0)},0))},a.send(this._data)}catch(n){this.setTimeoutFn(()=>{this._onError(n)},0);return}typeof document<"u"&&(this._index=je.requestsCount++,je.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=bl,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete je.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}je.requestsCount=0;je.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Tr);else if(typeof addEventListener=="function"){const r="onpagehide"in fe?"pagehide":"unload";addEventListener(r,Tr,!1)}}function Tr(){for(let r in je.requests)je.requests.hasOwnProperty(r)&&je.requests[r].abort()}const jl=function(){const r=_a({xdomain:!1});return r&&r.responseType!==null}();class wl extends vl{constructor(t){super(t);const s=t&&t.forceBase64;this.supportsBinary=jl&&!s}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new je(_a,this.uri(),t)}}function _a(r){const t=r.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||yl))return new XMLHttpRequest}catch{}if(!t)try{return new fe[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Pa=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Nl extends rr{get name(){return"websocket"}doOpen(){const t=this.uri(),s=this.opts.protocols,a=Pa?{}:Ea(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(a.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,s,a)}catch(n){return this.emitReserved("error",n)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const a=t[s],n=s===t.length-1;tr(a,this.supportsBinary,i=>{try{this.doWrite(a,i)}catch{}n&&ts(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=Ca()),this.supportsBinary||(s.b64=1),this.createUri(t,s)}}const xs=fe.WebSocket||fe.MozWebSocket;class kl extends Nl{createSocket(t,s,a){return Pa?new xs(t,s,a):s?new xs(t,s):new xs(t)}doWrite(t,s){this.ws.send(s)}}class Sl extends rr{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const s=il(Number.MAX_SAFE_INTEGER,this.socket.binaryType),a=t.readable.pipeThrough(s).getReader(),n=nl();n.readable.pipeTo(t.writable),this._writer=n.writable.getWriter();const i=()=>{a.read().then(({done:l,value:c})=>{l||(this.onPacket(c),i())}).catch(l=>{})};i();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const a=t[s],n=s===t.length-1;this._writer.write(a).then(()=>{n&&ts(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const El={websocket:kl,webtransport:Sl,polling:wl},Cl=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Rl=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function _s(r){if(r.length>8e3)throw"URI too long";const t=r,s=r.indexOf("["),a=r.indexOf("]");s!=-1&&a!=-1&&(r=r.substring(0,s)+r.substring(s,a).replace(/:/g,";")+r.substring(a,r.length));let n=Cl.exec(r||""),i={},o=14;for(;o--;)i[Rl[o]]=n[o]||"";return s!=-1&&a!=-1&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=_l(i,i.path),i.queryKey=Pl(i,i.query),i}function _l(r,t){const s=/\/{2,9}/g,a=t.replace(s,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&a.splice(0,1),t.slice(-1)=="/"&&a.splice(a.length-1,1),a}function Pl(r,t){const s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(a,n,i){n&&(s[n]=i)}),s}const Ps=typeof addEventListener=="function"&&typeof removeEventListener=="function",Vt=[];Ps&&addEventListener("offline",()=>{Vt.forEach(r=>r())},!1);class Oe extends se{constructor(t,s){if(super(),this.binaryType=ll,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(s=t,t=null),t){const a=_s(t);s.hostname=a.host,s.secure=a.protocol==="https"||a.protocol==="wss",s.port=a.port,a.query&&(s.query=a.query)}else s.host&&(s.hostname=_s(s.host).host);ss(this,s),this.secure=s.secure!=null?s.secure:typeof location<"u"&&location.protocol==="https:",s.hostname&&!s.port&&(s.port=this.secure?"443":"80"),this.hostname=s.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=s.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},s.transports.forEach(a=>{const n=a.prototype.name;this.transports.push(n),this._transportsByName[n]=a}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=fl(this.opts.query)),Ps&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Vt.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const s=Object.assign({},this.opts.query);s.EIO=Sa,s.transport=t,this.id&&(s.sid=this.id);const a=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](a)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Oe.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const s=this.createTransport(t);s.open(),this.setTransport(s)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",s=>this._onClose("transport close",s))}onOpen(){this.readyState="open",Oe.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const s=new Error("server error");s.code=t.data,this._onError(s);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let s=1;for(let a=0;a<this.writeBuffer.length;a++){const n=this.writeBuffer[a].data;if(n&&(s+=hl(n)),a>0&&s>this._maxPayload)return this.writeBuffer.slice(0,a);s+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,ts(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,s,a){return this._sendPacket("message",t,s,a),this}send(t,s,a){return this._sendPacket("message",t,s,a),this}_sendPacket(t,s,a,n){if(typeof s=="function"&&(n=s,s=void 0),typeof a=="function"&&(n=a,a=null),this.readyState==="closing"||this.readyState==="closed")return;a=a||{},a.compress=a.compress!==!1;const i={type:t,data:s,options:a};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},s=()=>{this.off("upgrade",s),this.off("upgradeError",s),t()},a=()=>{this.once("upgrade",s),this.once("upgradeError",s)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?a():t()}):this.upgrading?a():t()),this}_onError(t){if(Oe.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,s){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ps&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const a=Vt.indexOf(this._offlineEventListener);a!==-1&&Vt.splice(a,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,s),this.writeBuffer=[],this._prevBufferLen=0}}}Oe.protocol=Sa;class Al extends Oe{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let s=this.createTransport(t),a=!1;Oe.priorWebsocketSuccess=!1;const n=()=>{a||(s.send([{type:"ping",data:"probe"}]),s.once("packet",m=>{if(!a)if(m.type==="pong"&&m.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",s),!s)return;Oe.priorWebsocketSuccess=s.name==="websocket",this.transport.pause(()=>{a||this.readyState!=="closed"&&(d(),this.setTransport(s),s.send([{type:"upgrade"}]),this.emitReserved("upgrade",s),s=null,this.upgrading=!1,this.flush())})}else{const x=new Error("probe error");x.transport=s.name,this.emitReserved("upgradeError",x)}}))};function i(){a||(a=!0,d(),s.close(),s=null)}const o=m=>{const x=new Error("probe error: "+m);x.transport=s.name,i(),this.emitReserved("upgradeError",x)};function l(){o("transport closed")}function c(){o("socket closed")}function u(m){s&&m.name!==s.name&&i()}const d=()=>{s.removeListener("open",n),s.removeListener("error",o),s.removeListener("close",l),this.off("close",c),this.off("upgrading",u)};s.once("open",n),s.once("error",o),s.once("close",l),this.once("close",c),this.once("upgrading",u),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{a||s.open()},200):s.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const s=[];for(let a=0;a<t.length;a++)~this.transports.indexOf(t[a])&&s.push(t[a]);return s}}let Tl=class extends Al{constructor(t,s={}){const a=typeof t=="object"?t:s;(!a.transports||a.transports&&typeof a.transports[0]=="string")&&(a.transports=(a.transports||["polling","websocket","webtransport"]).map(n=>El[n]).filter(n=>!!n)),super(t,a)}};function Il(r,t="",s){let a=r;s=s||typeof location<"u"&&location,r==null&&(r=s.protocol+"//"+s.host),typeof r=="string"&&(r.charAt(0)==="/"&&(r.charAt(1)==="/"?r=s.protocol+r:r=s.host+r),/^(https?|wss?):\/\//.test(r)||(typeof s<"u"?r=s.protocol+"//"+r:r="https://"+r),a=_s(r)),a.port||(/^(http|ws)$/.test(a.protocol)?a.port="80":/^(http|ws)s$/.test(a.protocol)&&(a.port="443")),a.path=a.path||"/";const i=a.host.indexOf(":")!==-1?"["+a.host+"]":a.host;return a.id=a.protocol+"://"+i+":"+a.port+t,a.href=a.protocol+"://"+i+(s&&s.port===a.port?"":":"+a.port),a}const Ol=typeof ArrayBuffer=="function",Dl=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r.buffer instanceof ArrayBuffer,Aa=Object.prototype.toString,Ll=typeof Blob=="function"||typeof Blob<"u"&&Aa.call(Blob)==="[object BlobConstructor]",$l=typeof File=="function"||typeof File<"u"&&Aa.call(File)==="[object FileConstructor]";function ar(r){return Ol&&(r instanceof ArrayBuffer||Dl(r))||Ll&&r instanceof Blob||$l&&r instanceof File}function Ft(r,t){if(!r||typeof r!="object")return!1;if(Array.isArray(r)){for(let s=0,a=r.length;s<a;s++)if(Ft(r[s]))return!0;return!1}if(ar(r))return!0;if(r.toJSON&&typeof r.toJSON=="function"&&arguments.length===1)return Ft(r.toJSON(),!0);for(const s in r)if(Object.prototype.hasOwnProperty.call(r,s)&&Ft(r[s]))return!0;return!1}function Vl(r){const t=[],s=r.data,a=r;return a.data=As(s,t),a.attachments=t.length,{packet:a,buffers:t}}function As(r,t){if(!r)return r;if(ar(r)){const s={_placeholder:!0,num:t.length};return t.push(r),s}else if(Array.isArray(r)){const s=new Array(r.length);for(let a=0;a<r.length;a++)s[a]=As(r[a],t);return s}else if(typeof r=="object"&&!(r instanceof Date)){const s={};for(const a in r)Object.prototype.hasOwnProperty.call(r,a)&&(s[a]=As(r[a],t));return s}return r}function Fl(r,t){return r.data=Ts(r.data,t),delete r.attachments,r}function Ts(r,t){if(!r)return r;if(r&&r._placeholder===!0){if(typeof r.num=="number"&&r.num>=0&&r.num<t.length)return t[r.num];throw new Error("illegal attachments")}else if(Array.isArray(r))for(let s=0;s<r.length;s++)r[s]=Ts(r[s],t);else if(typeof r=="object")for(const s in r)Object.prototype.hasOwnProperty.call(r,s)&&(r[s]=Ts(r[s],t));return r}const Ml=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Ul=5;var Q;(function(r){r[r.CONNECT=0]="CONNECT",r[r.DISCONNECT=1]="DISCONNECT",r[r.EVENT=2]="EVENT",r[r.ACK=3]="ACK",r[r.CONNECT_ERROR=4]="CONNECT_ERROR",r[r.BINARY_EVENT=5]="BINARY_EVENT",r[r.BINARY_ACK=6]="BINARY_ACK"})(Q||(Q={}));class ql{constructor(t){this.replacer=t}encode(t){return(t.type===Q.EVENT||t.type===Q.ACK)&&Ft(t)?this.encodeAsBinary({type:t.type===Q.EVENT?Q.BINARY_EVENT:Q.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let s=""+t.type;return(t.type===Q.BINARY_EVENT||t.type===Q.BINARY_ACK)&&(s+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(s+=t.nsp+","),t.id!=null&&(s+=t.id),t.data!=null&&(s+=JSON.stringify(t.data,this.replacer)),s}encodeAsBinary(t){const s=Vl(t),a=this.encodeAsString(s.packet),n=s.buffers;return n.unshift(a),n}}function Ir(r){return Object.prototype.toString.call(r)==="[object Object]"}class nr extends se{constructor(t){super(),this.reviver=t}add(t){let s;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");s=this.decodeString(t);const a=s.type===Q.BINARY_EVENT;a||s.type===Q.BINARY_ACK?(s.type=a?Q.EVENT:Q.ACK,this.reconstructor=new Bl(s),s.attachments===0&&super.emitReserved("decoded",s)):super.emitReserved("decoded",s)}else if(ar(t)||t.base64)if(this.reconstructor)s=this.reconstructor.takeBinaryData(t),s&&(this.reconstructor=null,super.emitReserved("decoded",s));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let s=0;const a={type:Number(t.charAt(0))};if(Q[a.type]===void 0)throw new Error("unknown packet type "+a.type);if(a.type===Q.BINARY_EVENT||a.type===Q.BINARY_ACK){const i=s+1;for(;t.charAt(++s)!=="-"&&s!=t.length;);const o=t.substring(i,s);if(o!=Number(o)||t.charAt(s)!=="-")throw new Error("Illegal attachments");a.attachments=Number(o)}if(t.charAt(s+1)==="/"){const i=s+1;for(;++s&&!(t.charAt(s)===","||s===t.length););a.nsp=t.substring(i,s)}else a.nsp="/";const n=t.charAt(s+1);if(n!==""&&Number(n)==n){const i=s+1;for(;++s;){const o=t.charAt(s);if(o==null||Number(o)!=o){--s;break}if(s===t.length)break}a.id=Number(t.substring(i,s+1))}if(t.charAt(++s)){const i=this.tryParse(t.substr(s));if(nr.isPayloadValid(a.type,i))a.data=i;else throw new Error("invalid payload")}return a}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,s){switch(t){case Q.CONNECT:return Ir(s);case Q.DISCONNECT:return s===void 0;case Q.CONNECT_ERROR:return typeof s=="string"||Ir(s);case Q.EVENT:case Q.BINARY_EVENT:return Array.isArray(s)&&(typeof s[0]=="number"||typeof s[0]=="string"&&Ml.indexOf(s[0])===-1);case Q.ACK:case Q.BINARY_ACK:return Array.isArray(s)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Bl{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const s=Fl(this.reconPack,this.buffers);return this.finishedReconstruction(),s}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Ql=Object.freeze(Object.defineProperty({__proto__:null,Decoder:nr,Encoder:ql,get PacketType(){return Q},protocol:Ul},Symbol.toStringTag,{value:"Module"}));function ge(r,t,s){return r.on(t,s),function(){r.off(t,s)}}const zl=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Ta extends se{constructor(t,s,a){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=s,a&&a.auth&&(this.auth=a.auth),this._opts=Object.assign({},a),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[ge(t,"open",this.onopen.bind(this)),ge(t,"packet",this.onpacket.bind(this)),ge(t,"error",this.onerror.bind(this)),ge(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...s){var a,n,i;if(zl.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(s.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(s),this;const o={type:Q.EVENT,data:s};if(o.options={},o.options.compress=this.flags.compress!==!1,typeof s[s.length-1]=="function"){const d=this.ids++,m=s.pop();this._registerAckCallback(d,m),o.id=d}const l=(n=(a=this.io.engine)===null||a===void 0?void 0:a.transport)===null||n===void 0?void 0:n.writable,c=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!l||(c?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(t,s){var a;const n=(a=this.flags.timeout)!==null&&a!==void 0?a:this._opts.ackTimeout;if(n===void 0){this.acks[t]=s;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===t&&this.sendBuffer.splice(l,1);s.call(this,new Error("operation has timed out"))},n),o=(...l)=>{this.io.clearTimeoutFn(i),s.apply(this,l)};o.withError=!0,this.acks[t]=o}emitWithAck(t,...s){return new Promise((a,n)=>{const i=(o,l)=>o?n(o):a(l);i.withError=!0,s.push(i),this.emit(t,...s)})}_addToQueue(t){let s;typeof t[t.length-1]=="function"&&(s=t.pop());const a={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((n,...i)=>a!==this._queue[0]?void 0:(n!==null?a.tryCount>this._opts.retries&&(this._queue.shift(),s&&s(n)):(this._queue.shift(),s&&s(null,...i)),a.pending=!1,this._drainQueue())),this._queue.push(a),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const s=this._queue[0];s.pending&&!t||(s.pending=!0,s.tryCount++,this.flags=s.flags,this.emit.apply(this,s.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:Q.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,s){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,s),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(a=>String(a.id)===t)){const a=this.acks[t];delete this.acks[t],a.withError&&a.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case Q.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case Q.EVENT:case Q.BINARY_EVENT:this.onevent(t);break;case Q.ACK:case Q.BINARY_ACK:this.onack(t);break;case Q.DISCONNECT:this.ondisconnect();break;case Q.CONNECT_ERROR:this.destroy();const a=new Error(t.data.message);a.data=t.data.data,this.emitReserved("connect_error",a);break}}onevent(t){const s=t.data||[];t.id!=null&&s.push(this.ack(t.id)),this.connected?this.emitEvent(s):this.receiveBuffer.push(Object.freeze(s))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const s=this._anyListeners.slice();for(const a of s)a.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const s=this;let a=!1;return function(...n){a||(a=!0,s.packet({type:Q.ACK,id:t,data:n}))}}onack(t){const s=this.acks[t.id];typeof s=="function"&&(delete this.acks[t.id],s.withError&&t.data.unshift(null),s.apply(this,t.data))}onconnect(t,s){this.id=t,this.recovered=s&&this._pid===s,this._pid=s,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:Q.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const s=this._anyListeners;for(let a=0;a<s.length;a++)if(t===s[a])return s.splice(a,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const s=this._anyOutgoingListeners;for(let a=0;a<s.length;a++)if(t===s[a])return s.splice(a,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const s=this._anyOutgoingListeners.slice();for(const a of s)a.apply(this,t.data)}}}function nt(r){r=r||{},this.ms=r.min||100,this.max=r.max||1e4,this.factor=r.factor||2,this.jitter=r.jitter>0&&r.jitter<=1?r.jitter:0,this.attempts=0}nt.prototype.duration=function(){var r=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*r);r=Math.floor(t*10)&1?r+s:r-s}return Math.min(r,this.max)|0};nt.prototype.reset=function(){this.attempts=0};nt.prototype.setMin=function(r){this.ms=r};nt.prototype.setMax=function(r){this.max=r};nt.prototype.setJitter=function(r){this.jitter=r};class Is extends se{constructor(t,s){var a;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(s=t,t=void 0),s=s||{},s.path=s.path||"/socket.io",this.opts=s,ss(this,s),this.reconnection(s.reconnection!==!1),this.reconnectionAttempts(s.reconnectionAttempts||1/0),this.reconnectionDelay(s.reconnectionDelay||1e3),this.reconnectionDelayMax(s.reconnectionDelayMax||5e3),this.randomizationFactor((a=s.randomizationFactor)!==null&&a!==void 0?a:.5),this.backoff=new nt({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(s.timeout==null?2e4:s.timeout),this._readyState="closed",this.uri=t;const n=s.parser||Ql;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=s.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var s;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(s=this.backoff)===null||s===void 0||s.setMin(t),this)}randomizationFactor(t){var s;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(s=this.backoff)===null||s===void 0||s.setJitter(t),this)}reconnectionDelayMax(t){var s;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(s=this.backoff)===null||s===void 0||s.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Tl(this.uri,this.opts);const s=this.engine,a=this;this._readyState="opening",this.skipReconnect=!1;const n=ge(s,"open",function(){a.onopen(),t&&t()}),i=l=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",l),t?t(l):this.maybeReconnectOnOpen()},o=ge(s,"error",i);if(this._timeout!==!1){const l=this._timeout,c=this.setTimeoutFn(()=>{n(),i(new Error("timeout")),s.close()},l);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}return this.subs.push(n),this.subs.push(o),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(ge(t,"ping",this.onping.bind(this)),ge(t,"data",this.ondata.bind(this)),ge(t,"error",this.onerror.bind(this)),ge(t,"close",this.onclose.bind(this)),ge(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(s){this.onclose("parse error",s)}}ondecoded(t){ts(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,s){let a=this.nsps[t];return a?this._autoConnect&&!a.active&&a.connect():(a=new Ta(this,t,s),this.nsps[t]=a),a}_destroy(t){const s=Object.keys(this.nsps);for(const a of s)if(this.nsps[a].active)return;this._close()}_packet(t){const s=this.encoder.encode(t);for(let a=0;a<s.length;a++)this.engine.write(s[a],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,s){var a;this.cleanup(),(a=this.engine)===null||a===void 0||a.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,s),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const s=this.backoff.duration();this._reconnecting=!0;const a=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(n=>{n?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",n)):t.onreconnect()}))},s);this.opts.autoUnref&&a.unref(),this.subs.push(()=>{this.clearTimeoutFn(a)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const it={};function Mt(r,t){typeof r=="object"&&(t=r,r=void 0),t=t||{};const s=Il(r,t.path||"/socket.io"),a=s.source,n=s.id,i=s.path,o=it[n]&&i in it[n].nsps,l=t.forceNew||t["force new connection"]||t.multiplex===!1||o;let c;return l?c=new Is(a,t):(it[n]||(it[n]=new Is(a,t)),c=it[n]),s.query&&!t.query&&(t.query=s.queryKey),c.socket(s.path,t)}Object.assign(Mt,{Manager:Is,Socket:Ta,io:Mt,connect:Mt});class Wl{constructor(){de(this,"socket",null);de(this,"connectionStatus","disconnected");de(this,"reconnectAttempts",0);de(this,"maxReconnectAttempts",5);de(this,"listeners",new Map);de(this,"statusListeners",new Set);this.connect()}connect(){const t=window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1",s=t?"http://localhost:8001":window.location.origin;this.setConnectionStatus("connecting");try{this.socket=Mt(s,{transports:["websocket","polling"],timeout:5e3,forceNew:!1,reconnection:!0,reconnectionAttempts:3,reconnectionDelay:2e3,autoConnect:!0,...t&&{withCredentials:!0,extraHeaders:{"Access-Control-Allow-Origin":"*"}}}),this.setupEventListeners()}catch(a){console.warn("WebSocket não disponível:",a),this.setConnectionStatus("disconnected")}}setupEventListeners(){if(!this.socket)return;this.socket.on("connect",()=>{console.log("✅ WebSocket conectado"),this.setConnectionStatus("connected"),this.reconnectAttempts=0,this.connectionStatus!=="connected"&&w.success("Conectado ao servidor")}),this.socket.on("disconnect",a=>{var n;console.log("❌ WebSocket desconectado:",a),this.setConnectionStatus("disconnected"),a==="io server disconnect"&&((n=this.socket)==null||n.connect())}),this.socket.on("connect_error",a=>{console.warn("WebSocket connection failed:",a.message),this.setConnectionStatus("error"),this.reconnectAttempts++,this.reconnectAttempts>=this.maxReconnectAttempts&&console.warn("WebSocket: Máximo de tentativas de reconexão atingido")}),this.socket.on("reconnect",a=>{console.log(`🔄 WebSocket reconectado após ${a} tentativas`),this.setConnectionStatus("connected"),w.success("Reconectado ao servidor")}),this.socket.on("reconnect_attempt",a=>{console.log(`🔄 Tentativa de reconexão ${a}`),this.setConnectionStatus("connecting")}),this.socket.on("reconnect_error",a=>{console.error("❌ Erro na reconexão:",a),this.setConnectionStatus("error")}),this.socket.on("reconnect_failed",()=>{console.error("❌ Falha na reconexão após máximo de tentativas"),this.setConnectionStatus("error"),w.error("Falha na conexão com o servidor")}),this.setupApplicationEvents();const t=localStorage.getItem("currentRestaurantId"),s=localStorage.getItem("authToken");t&&s&&(this.socket.emit("authenticate",{token:s,restaurantId:t}),this.socket.emit("joinRoom",{restaurantId:t,role:"admin"}))}setupApplicationEvents(){this.socket&&(this.socket.on("notification",t=>{this.emit("notification",t)}),this.socket.on("new-suggestion",t=>{console.log("🎵 Nova sugestão recebida:",t),this.emit("new-suggestion",t),w.success(`Nova música sugerida: ${t.title}`,{duration:4e3})}),this.socket.on("vote-update",t=>{console.log("👍 Atualização de votos:",t),this.emit("vote-update",t)}),this.socket.on("queue-update",t=>{console.log("📋 Fila atualizada:",t),this.emit("queue-update",t)}),this.socket.on("suggestion-approved",t=>{console.log("✅ Sugestão aprovada:",t),this.emit("suggestion-approved",t),w.success(`Música aprovada: ${t.title}`,{duration:4e3})}),this.socket.on("suggestion-rejected",t=>{console.log("❌ Sugestão rejeitada:",t),this.emit("suggestion-rejected",t),w.error(`Sugestão rejeitada: ${t.reason}`,{duration:5e3})}),this.socket.on("now-playing",t=>{console.log("🎵 Tocando agora:",t),this.emit("now-playing",t),w(`Tocando agora: ${t.suggestion.title}`,{icon:"🎵",duration:6e3})}),this.socket.on("newSuggestion",t=>{console.log("🎵 (camelCase) Nova sugestão recebida:",t),this.emit("new-suggestion",t)}),this.socket.on("voteUpdate",t=>{console.log("👍 (camelCase) Atualização de votos:",t),this.emit("vote-update",t)}),this.socket.on("queueUpdate",t=>{console.log("📋 (camelCase) Fila atualizada:",t),this.emit("queue-update",t)}),this.socket.on("playbackStart",t=>{console.log("▶️ (camelCase) Início de reprodução:",t),this.emit("now-playing",{suggestion:t.track})}),this.socket.on("suggestionStatusUpdate",t=>{console.log("ℹ️ (camelCase) Status de sugestão:",t),(t==null?void 0:t.status)==="approved"?this.emit("suggestion-approved",t):(t==null?void 0:t.status)==="rejected"&&this.emit("suggestion-rejected",t)}),this.socket.on("playlistReordered",t=>{console.log("🔄 Playlist reordenada:",t),this.emit("playlistReordered",t)}),this.socket.on("song-ended",t=>{console.log("⏭️ Música finalizada:",t),this.emit("song-ended",t)}),this.socket.on("superVoteReceived",t=>{console.log("⭐ SuperVoto recebido:",t),this.emit("superVoteReceived",t)}))}setConnectionStatus(t){this.connectionStatus=t,this.statusListeners.forEach(s=>s(t))}offConnectionStatusChange(t){this.statusListeners.delete(t)}joinRestaurant(t){this.socket&&this.connectionStatus==="connected"&&(this.socket.emit("join-restaurant",t),console.log(`🏪 Entrou na sala do restaurante: ${t}`))}leaveRestaurant(t){this.socket&&this.connectionStatus==="connected"&&(this.socket.emit("leave-restaurant",t),console.log(`🚪 Saiu da sala do restaurante: ${t}`))}on(t,s){this.listeners.has(t)||this.listeners.set(t,new Set),this.listeners.get(t).add(s)}off(t,s){const a=this.listeners.get(t);a&&a.delete(s)}emit(t,s){const a=this.listeners.get(t);a&&a.forEach(n=>n(s))}publicEmit(t,s){if(!(!this.socket||this.connectionStatus!=="connected"))try{this.socket.emit(t,s)}catch(a){console.warn("Falha ao emitir evento via WS:",t,a)}}onConnectionStatusChange(t){return this.statusListeners.add(t),()=>{this.statusListeners.delete(t)}}getConnectionStatus(){return this.connectionStatus}isConnected(){return this.connectionStatus==="connected"}onConnectionChange(t){this.onConnectionStatusChange(s=>{t(s==="connected")})}reconnect(){this.socket?(this.socket.disconnect(),this.socket.connect()):this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}removeAllListeners(){this.listeners.clear(),this.statusListeners.clear()}getDebugInfo(){var t,s;return{connected:this.isConnected(),status:this.connectionStatus,reconnectAttempts:this.reconnectAttempts,socketId:(t=this.socket)==null?void 0:t.id,transport:(s=this.socket)==null?void 0:s.io.engine.transport.name,listenersCount:Array.from(this.listeners.entries()).reduce((a,[n,i])=>({...a,[n]:i.size}),{})}}}const re=new Wl,jt=()=>({service:re,isConnected:re.isConnected(),status:re.getConnectionStatus(),joinRestaurant:re.joinRestaurant.bind(re),leaveRestaurant:re.leaveRestaurant.bind(re),on:re.on.bind(re),off:re.off.bind(re),reconnect:re.reconnect.bind(re),emit:re.publicEmit.bind(re)}),Hl=Object.freeze(Object.defineProperty({__proto__:null,default:re,useWebSocket:jt,wsService:re},Symbol.toStringTag,{value:"Module"})),Kl=p.lazy(()=>xe(()=>import("./PlaylistManager-e94771c8.js"),["assets/PlaylistManager-e94771c8.js","assets/vendor-66b0ef43.js","assets/utils-08f61814.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js"])),Gl=p.lazy(()=>xe(()=>import("./MusicPlayer-c1da3b65.js"),["assets/MusicPlayer-c1da3b65.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Yl=p.lazy(()=>xe(()=>import("./PlaybackController-d0d556e1.js"),["assets/PlaybackController-d0d556e1.js","assets/vendor-66b0ef43.js","assets/router-f729e475.js","assets/ui-a5f8f5f0.js","assets/utils-08f61814.js"])),Jl=p.lazy(()=>xe(()=>import("./UnifiedAnalytics-f94ce56f.js"),["assets/UnifiedAnalytics-f94ce56f.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Xl=p.lazy(()=>xe(()=>import("./GenreManager-f5564d47.js"),["assets/GenreManager-f5564d47.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Zl=p.lazy(()=>xe(()=>import("./RestaurantProfile-60dba3be.js"),["assets/RestaurantProfile-60dba3be.js","assets/vendor-66b0ef43.js","assets/router-f729e475.js","assets/ui-a5f8f5f0.js","assets/utils-08f61814.js"])),ec=p.lazy(()=>xe(()=>import("./QRCodeManager-a9b0104f.js"),["assets/QRCodeManager-a9b0104f.js","assets/vendor-66b0ef43.js","assets/router-f729e475.js","assets/ui-a5f8f5f0.js","assets/utils-08f61814.js"])),tc=p.lazy(()=>xe(()=>import("./ProblematicTracksAlert-5fb6ff31.js"),["assets/ProblematicTracksAlert-5fb6ff31.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),sc=p.lazy(()=>xe(()=>import("./AdvancedModeration-db913f6a.js"),["assets/AdvancedModeration-db913f6a.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),rc=p.lazy(()=>xe(()=>import("./RestaurantSettings-36105006.js"),["assets/RestaurantSettings-36105006.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),ac=p.lazy(()=>xe(()=>import("./EnhancedRestaurantProfile-c6ee7bab.js"),["assets/EnhancedRestaurantProfile-c6ee7bab.js","assets/vendor-66b0ef43.js","assets/ui-a5f8f5f0.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Ia=p.createContext({restaurantId:"",isConnected:!1,lastUpdate:new Date}),nc=()=>p.useContext(Ia);class Ae{static setCache(t,s,a=5*60*1e3){const n={data:s,timestamp:Date.now(),ttl:a};localStorage.setItem(this.CACHE_PREFIX+t,JSON.stringify(n))}static getCache(t){try{const s=localStorage.getItem(this.CACHE_PREFIX+t);if(!s)return null;const{data:a,timestamp:n,ttl:i}=JSON.parse(s);return Date.now()-n>i?(localStorage.removeItem(this.CACHE_PREFIX+t),null):a}catch{return null}}static clearCache(){Object.keys(localStorage).filter(t=>t.startsWith(this.CACHE_PREFIX)).forEach(t=>localStorage.removeItem(t))}}de(Ae,"CACHE_PREFIX","restaurant_dashboard_");const Os=()=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse",children:[e.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"}),e.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]}),ic=H.memo(({title:r,value:t,change:s,icon:a,color:n,bgColor:i,description:o})=>e.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow",children:e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:r}),e.jsx("div",{className:`w-10 h-10 rounded-lg ${i} flex items-center justify-center`,children:e.jsx(a,{className:`w-5 h-5 ${n}`})})]}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-1",children:t}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:o}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:`text-sm font-medium ${s.startsWith("+")?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400"}`,children:s}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-1",children:"hoje"})]})]})})})),oc=()=>{const{restaurantId:r,isConnected:t}=nc(),s=er(),{on:a,off:n,emit:i}=jt(),[o,l]=p.useState({}),[c,u]=p.useState([]),[d,m]=p.useState([]),[x,h]=p.useState(new Date),{data:f,isLoading:y,error:k}=cs(["analytics",r],async()=>{try{const E=await oe.getAnalytics(r);return Ae.setCache(`stats_${r}`,E),E}catch(E){const P=Ae.getCache(`stats_${r}`);if(P)return w("Usando dados em cache devido à falha de conexão",{icon:"ℹ️"}),P;throw E}},{staleTime:2*60*1e3,retry:3,retryDelay:E=>Math.min(1e3*2**E,3e4)}),{data:b,isLoading:v}=cs(["suggestions",r],async()=>{try{const E=await oe.getSuggestions(r,{limit:10});return Ae.setCache(`suggestions_${r}`,E),E}catch(E){const P=Ae.getCache(`suggestions_${r}`);if(P)return P;throw E}},{staleTime:30*1e3,retry:2}),{data:C,isLoading:F}=cs(["queue",r],async()=>{try{const E=await oe.getPlayQueue(r);return Ae.setCache(`queue_${r}`,E),E}catch(E){const P=Ae.getCache(`queue_${r}`);if(P)return P;throw E}},{staleTime:15*1e3,retry:2}),M=p.useCallback(E=>E.reduce((P,g,R)=>R===0?0:P+(g.duration||180),0),[]);p.useEffect(()=>{if(!r||!t)return;const E=A=>{u(_=>[{...A,type:"suggestion"},..._.slice(0,9)]),l(_=>{var S,O;return{..._,totalSuggestions:(_.totalSuggestions||0)+1,dailyStats:{..._.dailyStats,suggestions:(((S=_.dailyStats)==null?void 0:S.suggestions)||0)+1,votes:((O=_.dailyStats)==null?void 0:O.votes)||0}}}),w.success(`🎵 Nova sugestão: ${A.title}`,{duration:3e3,position:"bottom-right"})},P=A=>{m(A);const _=M(A);l(S=>({...S,estimatedWaitTime:_})),s.setQueryData(["queue",r],{queue:A})},g=A=>{l(_=>{var S,O;return{..._,totalVotes:(_.totalVotes||0)+1,dailyStats:{suggestions:((S=_.dailyStats)==null?void 0:S.suggestions)||0,votes:(((O=_.dailyStats)==null?void 0:O.votes)||0)+1}}})},R=A=>{l(_=>({..._,currentlyPlaying:A?{title:A.title,artist:A.artist,remainingTime:A.remainingTime}:void 0}))};return a("new-suggestion",E),a("queue-update",P),a("vote-update",g),a("now-playing",R),i("join-restaurant",{restaurantId:r}),()=>{n("new-suggestion",E),n("queue-update",P),n("vote-update",g),n("now-playing",R),i("leave-restaurant",{restaurantId:r})}},[r,t,a,n,i,s,M]);const L=H.useMemo(()=>{var P,g;const E=(f==null?void 0:f.summary)||{};return{totalSuggestions:o.totalSuggestions??E.totalSuggestions??0,totalVotes:o.totalVotes??E.totalVotes??0,pendingSuggestions:o.pendingSuggestions??E.pendingSuggestions??0,dailyStats:{suggestions:((P=o.dailyStats)==null?void 0:P.suggestions)??E.dailySuggestions??0,votes:((g=o.dailyStats)==null?void 0:g.votes)??E.dailyVotes??0},totalPlays:o.totalPlays??E.totalPlays??0,activeUsers:o.activeUsers??E.activeUsers??0,averageRating:o.averageRating??E.averageRating??0,growthRate:o.growthRate??E.growthRate??0,peakHour:o.peakHour??E.peakHour??"0:00",topGenre:o.topGenre??E.topGenre??"N/A",estimatedWaitTime:o.estimatedWaitTime,currentlyPlaying:o.currentlyPlaying}},[f,o]),j=H.useMemo(()=>{const E=(b==null?void 0:b.suggestions)||[];return[...c,...E.map(R=>({id:R.id||`suggestion-${Date.now()}-${Math.random()}`,title:R.title||"Título não disponível",artist:R.artist||"Artista desconhecido",createdAt:R.createdAt||new Date().toISOString(),upvotes:R.upvotes||0,type:"suggestion"}))].filter((R,A,_)=>_.findIndex(S=>S.id===R.id)===A).slice(0,10)},[b,c]),D=H.useMemo(()=>{const E=(C==null?void 0:C.queue)||[];return(d.length>0?d:E).slice(0,10).map((g,R)=>{const A=new Date;return A.setSeconds(A.getSeconds()+R*180),{id:g.id||`queue-${Date.now()}-${Math.random()}`,title:g.title||"Título não disponível",artist:g.artist||"Artista desconhecido",upvotes:g.upvotes||0,downvotes:g.downvotes||0,duration:g.duration||180,priority:g.priority||"normal",estimatedPlayTime:A}})},[C,d]),N=H.useMemo(()=>[{title:"Sugestões Hoje",value:L.dailyStats.suggestions.toString(),change:L.growthRate>0?`+${L.growthRate.toFixed(1)}%`:"0%",icon:te,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",description:"Novas sugestões recebidas hoje",trend:L.growthRate>0?"up":"stable"},{title:"Total de Votos",value:L.totalVotes.toString(),change:`+${L.dailyStats.votes}`,icon:Tt,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",description:"Votos acumulados de clientes",trend:L.dailyStats.votes>0?"up":"stable"},{title:"Fila de Espera",value:D.length.toString(),change:L.estimatedWaitTime?`${Math.round(L.estimatedWaitTime/60)}min`:"0min",icon:Be,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",description:L.estimatedWaitTime?"Tempo estimado de espera":"Fila vazia",trend:D.length>5?"up":D.length>0?"stable":"down"},{title:"Usuários Ativos",value:L.activeUsers.toString(),change:"+0.2",icon:et,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/20",description:"Clientes interagindo agora",trend:L.activeUsers>0?"up":"stable"}],[L,D.length]),T=y||v||F;return k&&!f?e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Ye,{className:"w-8 h-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-red-900 dark:text-red-100",children:"Erro ao carregar dados"}),e.jsx("p",{className:"text-red-700 dark:text-red-300 mt-1",children:"Não foi possível conectar ao servidor. Tentando usar dados em cache..."}),e.jsxs("button",{onClick:()=>{Ae.clearCache(),window.location.reload()},className:"mt-3 flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[e.jsx(ue,{className:"w-4 h-4"}),e.jsx("span",{children:"Tentar Novamente"})]})]})]})})}):T?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg animate-pulse",children:[e.jsx("div",{className:"h-8 bg-white/20 rounded w-1/2 mb-2"}),e.jsx("div",{className:"h-4 bg-white/20 rounded w-3/4"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array(4).fill(0).map((E,P)=>e.jsx(Os,{},P))})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Dashboard do Restaurante"}),e.jsx("p",{className:"text-blue-100 text-lg",children:"Visão geral das atividades e estatísticas em tempo real"}),e.jsxs("div",{className:"flex items-center space-x-6 mt-4",children:[e.jsx("div",{className:"flex items-center space-x-2",children:t?e.jsxs(e.Fragment,{children:[e.jsx(dn,{className:"w-4 h-4 text-green-300"}),e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-sm text-green-100 font-medium",children:"Sistema Online"})]}):e.jsxs(e.Fragment,{children:[e.jsx(un,{className:"w-4 h-4 text-red-300"}),e.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),e.jsx("span",{className:"text-sm text-red-100",children:"Modo Offline"})]})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Be,{className:"w-4 h-4 text-blue-200"}),e.jsxs("span",{className:"text-sm text-blue-100",children:["Atualizado:"," ",x.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",second:"2-digit"})]})]}),L.currentlyPlaying&&e.jsxs("div",{className:"flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-1",children:[e.jsx(be,{className:"w-4 h-4 text-green-300"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-green-100 font-medium",children:"Tocando:"}),e.jsxs("span",{className:"text-blue-100 ml-2",children:[L.currentlyPlaying.title," -"," ",L.currentlyPlaying.artist]})]})]})]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsxs("div",{className:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm relative",children:[e.jsx(Ee,{className:"w-12 h-12 text-white"}),!t&&e.jsx("div",{className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center",children:e.jsx(Ye,{className:"w-4 h-4 text-white"})})]})})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:N.map((E,P)=>e.jsx(ic,{...E},E.title))}),e.jsx(p.Suspense,{fallback:e.jsx(Os,{}),children:e.jsx(tc,{})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Atividade Recente"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Ao vivo"})]})]}),e.jsx("div",{className:"space-y-3",children:j.length>0?j.map(E=>e.jsxs(V.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm",children:e.jsx(te,{className:"w-5 h-5 text-white"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:['Nova sugestão: "',E.title,'"']}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:E.artist}),e.jsx("span",{className:"text-xs text-gray-400",children:"•"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(E.createdAt).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Tt,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-xs text-gray-500",children:E.upvotes||0})]})]},E.id)):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(te,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Nenhuma sugestão recente"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"As sugestões mais recentes dos clientes aparecerão aqui"}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:"🎵 Compartilhe o QR Code para clientes sugerirem músicas"}),e.jsx("p",{className:"text-xs text-gray-500",children:'Acesse "QR Code" no menu principal'})]})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Fila Atual"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(be,{className:"w-4 h-4 text-green-500"}),e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[D.length," músicas"]})]})]}),e.jsx("div",{className:"space-y-3",children:D.length>0?D.map((E,P)=>e.jsxs(V.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:`flex items-center space-x-3 p-3 rounded-lg transition-colors ${P===0?"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700":"bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"}`,children:[e.jsx("div",{className:`w-8 h-8 rounded-full text-xs flex items-center justify-center font-bold ${P===0?"bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-sm":"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}`,children:P===0?e.jsx(be,{className:"w-3 h-3"}):P+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:`text-sm font-medium truncate ${P===0?"text-gray-900 dark:text-white font-semibold":"text-gray-900 dark:text-white"}`,children:E.title}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:E.artist}),e.jsx("span",{className:"text-xs text-gray-400",children:"•"}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Tt,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-xs text-gray-500",children:E.upvotes-E.downvotes})]})]})]}),P===0&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"Tocando"})]})]},E.id)):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(be,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Fila de reprodução vazia"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Aguardando músicas serem aprovadas e adicionadas à fila"}),e.jsx("div",{className:"mt-4 space-y-2",children:e.jsx("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:'💡 Dica: Use "Controle de Reprodução" para acompanhar fila e votação'})})]})})]})]})]})},lc=()=>{const r=gt(),t=Ya(),{restaurantId:s}=Ze(),{user:a,setUser:n,setAuthToken:i}=vt(),o="Restaurante Demo";if(!s)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600",children:"Erro de Rota"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"ID do restaurante não fornecido na URL"})]})});const l=()=>{n(null),i(null),localStorage.removeItem("authToken"),w.success("Logout realizado com sucesso!"),r("/")},c=H.useMemo(()=>[{name:"Player",icon:cn,path:`/restaurant/${s}/dashboard/player`},{name:"Controle de Reprodução",icon:Ge,path:`/restaurant/${s}/dashboard/playback-control`},{name:"Playlists",icon:te,path:`/restaurant/${s}/dashboard/playlists`},{name:"Gêneros",icon:qe,path:`/restaurant/${s}/dashboard/genres`},{name:"Analytics",icon:Ee,path:`/restaurant/${s}/dashboard/analytics`},{name:"QR Code",icon:ut,path:`/restaurant/${s}/dashboard/qrcode`},{name:"Moderação",icon:Ye,path:`/restaurant/${s}/dashboard/moderation`},{name:"Configurações",icon:Ge,path:`/restaurant/${s}/dashboard/settings`}],[s]);return e.jsx(Ia.Provider,{value:{restaurantId:s,isConnected:status==="connected",lastUpdate:new Date},children:e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(Ke,{to:`/restaurant/${s}/dashboard`,className:"flex items-center space-x-3 hover:opacity-80 transition-all duration-200 hover:scale-105 cursor-pointer",title:"Voltar ao Dashboard Principal",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg",children:e.jsx(te,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:o}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Dashboard de Gerenciamento"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:o}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ke,{to:"profile",className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer",title:"Perfil do Restaurante",children:e.jsx(Je,{className:"w-4 h-4 text-white"})}),e.jsxs("button",{onClick:l,className:"flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium",title:"Sair",children:[e.jsx(Hr,{className:"w-4 h-4"}),e.jsx("span",{children:"Sair"})]})]})]})]})})}),e.jsx("nav",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex space-x-8 overflow-x-auto",children:c.map(u=>e.jsxs(Ke,{to:u.path,className:`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors whitespace-nowrap ${t.pathname===u.path?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"}`,children:[e.jsx(u.icon,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:u.name})]},u.name))})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsx(p.Suspense,{fallback:e.jsx(Os,{}),children:e.jsxs(Ws,{children:[e.jsx(z,{path:"/",element:e.jsx(oc,{})}),e.jsx(z,{path:"/player",element:e.jsx(Gl,{})}),e.jsx(z,{path:"/playback-control",element:e.jsx(Yl,{})}),e.jsx(z,{path:"/playlists",element:e.jsx(Kl,{})}),e.jsx(z,{path:"/genres",element:e.jsx(Xl,{})}),e.jsx(z,{path:"/suggestions",element:e.jsx(Me,{to:`/restaurant/${s}/dashboard/playback-control`,replace:!0})}),e.jsx(z,{path:"/moderation",element:e.jsx(sc,{})}),e.jsx(z,{path:"/qrcode",element:e.jsx(ec,{})}),e.jsx(z,{path:"/analytics",element:e.jsx(Jl,{})}),e.jsx(z,{path:"/settings",element:e.jsx(rc,{})}),e.jsx(z,{path:"/profile",element:e.jsx(Zl,{})}),e.jsx(z,{path:"/enhanced-profile",element:e.jsx(ac,{})}),e.jsx(z,{path:"*",element:e.jsx(Me,{to:"/",replace:!0})})]})})})]})})},cc=()=>{const{restaurantId:r}=Ze(),[t,s]=p.useState(null),[a,n]=p.useState([]),[i,o]=p.useState(null),[l,c]=p.useState("7d"),[u,d]=p.useState(!0),[m,x]=p.useState(null),h=r||"demo-restaurant";p.useEffect(()=>{f()},[l,h]);const f=async()=>{d(!0),x(null);try{const b=await(await fetch(`http://localhost:8001/api/v1/analytics/dashboard/${h}?period=${l}`)).json();s(b);const C=await(await fetch(`http://localhost:8001/api/v1/analytics/popular-songs/${h}?limit=5`)).json();n(C.songs);const M=await(await fetch(`http://localhost:8001/api/v1/analytics/engagement/${h}?period=${l}`)).json();o(M)}catch(k){x("Erro ao carregar dados de analytics"),console.error("Analytics error:",k)}finally{d(!1)}},y=async()=>{try{await fetch(`http://localhost:8001/api/v1/analytics/generate-test-data/${h}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({days:7})}),f()}catch(k){console.error("Error generating test data:",k)}};return u?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Carregando analytics..."})]})}):m?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-red-600 mb-4",children:m}),e.jsx("button",{onClick:f,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Tentar Novamente"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Analytics Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Restaurante Demo - Métricas e Relatórios"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("select",{value:l,onChange:k=>c(k.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"24h",children:"Últimas 24h"}),e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"})]}),e.jsxs("button",{onClick:y,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2",children:[e.jsx(Ee,{className:"w-4 h-4"}),e.jsx("span",{children:"Gerar Dados Teste"})]})]})]})})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(te,{className:"w-8 h-8 text-blue-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Plays"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalPlays})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ve,{className:"w-8 h-8 text-green-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Votos"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalVotes})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(et,{className:"w-8 h-8 text-purple-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sugestões"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalSuggestions})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ee,{className:"w-8 h-8 text-orange-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Engajamento"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[t.summary.engagementRate,"%"]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Músicas Mais Populares"})}),e.jsx("div",{className:"p-6",children:a.length>0?e.jsx("div",{className:"space-y-4",children:a.map((k,b)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:b+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:k.title}),e.jsx("p",{className:"text-sm text-gray-600",children:k.artist})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["Score: ",k.stats.popularityScore]}),e.jsxs("p",{className:"text-xs text-gray-600",children:[k.stats.plays," plays • ",k.stats.upvotes," ","upvotes"]})]})]},k.id))}):e.jsx("p",{className:"text-gray-500 text-center py-8",children:"Nenhum dado disponível"})})]}),i&&e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Métricas de Engajamento"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Taxa de Engajamento"}),e.jsxs("span",{className:"font-semibold text-green-600",children:[i.metrics.engagementRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Taxa de Positividade"}),e.jsxs("span",{className:"font-semibold text-blue-600",children:[i.metrics.positivityRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Votos por Sugestão"}),e.jsx("span",{className:"font-semibold text-purple-600",children:i.metrics.averageVotesPerSuggestion})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Upvotes"}),e.jsx("span",{className:"font-semibold text-green-600",children:i.metrics.upvotes})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Downvotes"}),e.jsx("span",{className:"font-semibold text-red-600",children:i.metrics.downvotes})]})]})})]})]}),e.jsx("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Exportar Dados"}),e.jsx("p",{className:"text-gray-600",children:"Baixe relatórios detalhados em formato JSON"})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsxs("a",{href:`http://localhost:8001/api/v1/analytics/export/${h}?type=all&format=json`,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2",target:"_blank",rel:"noopener noreferrer",children:[e.jsx(Js,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar Tudo"})]})})]})})]})]})},dc=()=>{const r=gt();return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4",children:e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center max-w-md mx-auto",children:[e.jsx(V.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"mb-8",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"text-8xl md:text-9xl font-bold text-gray-200 dark:text-gray-700 select-none",children:"404"}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx(V.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center shadow-lg",children:e.jsx(te,{className:"w-8 h-8 text-white"})})})]})}),e.jsxs(V.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"mb-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Página Não Encontrada"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-2",children:"Ops! A página que você está procurando não existe ou foi movida."}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Que tal voltar e descobrir uma nova música?"})]}),e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(pe,{onClick:()=>r("/"),icon:e.jsx(Kr,{className:"w-4 h-4"}),size:"lg",children:"Página Inicial"}),e.jsx(pe,{onClick:()=>r(-1),variant:"outline",icon:e.jsx(hn,{className:"w-4 h-4"}),size:"lg",children:"Voltar"})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:()=>r("/"),className:"inline-flex items-center space-x-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors",children:[e.jsx(qt,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Procurar restaurantes"})]})})]}),e.jsxs(V.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},className:"mt-12 p-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"Você pode estar procurando por:"}),e.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Página inicial do sistema"}),e.jsx("li",{children:"• Painel administrativo (/admin)"}),e.jsx("li",{children:"• Página de um restaurante específico"})]})]}),e.jsxs("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[e.jsx(V.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-32 h-32 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"}),e.jsx(V.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-32 h-32 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"})]})]})})};class uc{constructor(){de(this,"sessionToken",null);de(this,"session",null);de(this,"apiUrl",ba.BASE_URL+"/api/v1");this.restoreSession()}async createSession(t,s,a){try{const n=ls(),i=this.getDeviceInfo(),o=await fetch(`${this.apiUrl}/client/session`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionToken:n,restaurantId:t,tableNumber:s,clientName:a,deviceInfo:i})});if(o.ok){const l=await o.json();return this.session=l,this.sessionToken=l.sessionToken,this.saveSession(),l}else return this.createLocalSession(t,s,a)}catch(n){return console.error("Error creating session:",n),this.createLocalSession(t,s,a)}}createLocalSession(t,s,a){const n=ls(),i=new Date().toISOString(),o={id:ls(),sessionToken:n,restaurantId:t,tableNumber:s,clientName:a,deviceInfo:this.getDeviceInfo(),lastActivity:i,suggestionsCount:0,votesCount:0,pageViews:1,sessionDuration:0,formattedDuration:"0m",isActive:!0,isSessionActive:!0,isNewSession:!0,engagementLevel:"low",createdAt:i,updatedAt:i,points:0,level:1,badges:[],streak:0};return this.session=o,this.sessionToken=n,this.saveSession(),o}getSession(){return this.session}getSessionToken(){return this.sessionToken}updateStats(t){this.session&&(this.session={...this.session,...t},this.saveSession())}incrementSuggestions(){this.session&&(this.session.suggestionsCount++,this.session.points+=20,this.updateLevel(),this.saveSession())}incrementVotes(){this.session&&(this.session.votesCount++,this.session.points+=10,this.updateLevel(),this.saveSession())}updateLevel(){if(this.session){const t=Math.floor(this.session.points/100)+1;t>this.session.level&&(this.session.level=t)}}awardBadge(t){return this.session&&!this.session.badges.includes(t)?(this.session.badges.push(t),this.saveSession(),!0):!1}getDeviceInfo(){const t=navigator.userAgent,s=`${screen.width}x${screen.height}`,a=navigator.language,n=Intl.DateTimeFormat().resolvedOptions().timeZone;let i="desktop";return/Mobile|Android|iPhone|iPad/.test(t)&&(i=/iPad/.test(t)?"tablet":"mobile"),{type:i,screenResolution:s,language:a,timezone:n}}saveSession(){this.session&&this.sessionToken&&(localStorage.setItem("clientSession",JSON.stringify(this.session)),localStorage.setItem("sessionToken",this.sessionToken))}restoreSession(){try{const t=localStorage.getItem("clientSession"),s=localStorage.getItem("sessionToken");t&&s&&(this.session=JSON.parse(t),this.sessionToken=s)}catch(t){console.error("Error restoring session:",t),this.clearSession()}}clearSession(){this.session=null,this.sessionToken=null,localStorage.removeItem("clientSession"),localStorage.removeItem("sessionToken")}isSessionValid(){if(!this.session||!this.sessionToken)return!1;const t=Date.now()-new Date(this.session.createdAt).getTime(),s=24*60*60*1e3;return t<s}async forceNewSession(t,s,a){return this.clearSession(),this.createSession(t,s,a)}}const gs=new uc,hc=[{label:"R$ 5,00",amount:500},{label:"R$ 20,00",amount:2e3},{label:"R$ 50,00",amount:5e3}],mc=({isOpen:r,onClose:t,suggestion:s,sessionId:a,onPaymentSuccess:n})=>{const{restaurantId:i}=Ze(),[o]=Ur(),l=o.get("table"),[c,u]=p.useState("Cliente"),[d,m]=p.useState(""),[x,h]=p.useState("confirm"),[f,y]=p.useState(null),[k,b]=p.useState(!1),[v,C]=p.useState(1800),[F,M]=p.useState(!1),[L,j]=p.useState(!1),[D,N]=p.useState(null);if(!i)return console.error("❌ RestaurantId não encontrado na URL"),null;p.useEffect(()=>{if(x==="payment"&&v>0){const _=setInterval(()=>{C(S=>S<=1?(h("error"),w.error("O tempo para pagamento expirou"),0):S-1)},1e3);return()=>clearInterval(_)}},[x,v]),p.useEffect(()=>{if(x==="payment"&&f&&!F){const _=setInterval(async()=>{await P()},5e3);return()=>clearInterval(_)}},[x,f,F]);const T=_=>{const S=Math.floor(_/60),O=_%60;return`${S.toString().padStart(2,"0")}:${O.toString().padStart(2,"0")}`},E=async _=>{try{b(!0),N(_);const S=await fetch("http://localhost:8001/api/v1/payments/pix/suggestion",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({restaurantId:i,youtubeId:s.youtubeVideoId||s.id,title:s.title,artist:s.artist,clientName:c,clientMessage:d,tableNumber:l?parseInt(l):1,sessionId:a,amount:_})});if(S.ok){const O=await S.json();console.log("💳 DEBUG: Dados do pagamento recebidos:",O);const G=O.payment.qrCodeData==="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";(!O.payment.qrCodeData.startsWith("data:image/")||G)&&(console.warn("⚠️ QR Code placeholder ou inválido, mas continuando..."),j(!0)),y({paymentId:O.payment.id,qrCode:O.payment.pixCode,qrCodeBase64:O.payment.qrCodeData,ticketUrl:O.payment.ticketUrl||"",amount:O.payment.amount}),h("payment"),w.success("Pagamento Pix gerado com sucesso!")}else{const O=await S.json();console.error("❌ Erro na resposta do servidor:",O),w.error(O.error||"Erro ao gerar pagamento"),h("error")}}catch(S){console.error("Erro ao criar pagamento:",S),w.error("Erro ao conectar com o servidor de pagamento"),h("error")}finally{b(!1)}},P=async()=>{var _,S;if(!(!f||F))try{M(!0);const O=await fetch(`http://localhost:8001/api/v1/payments/${f.paymentId}/status`);if(O.ok){const G=await O.json(),J=((_=G.payment)==null?void 0:_.status)||((S=G.status)==null?void 0:S.status);if(J==="approved"||J==="paid"){h("success"),w.success("Pagamento aprovado! Sua música foi adicionada à fila.");const he=D??Math.round((f.amount||0)*100);n==null||n({paymentId:f.paymentId,amountCents:he,clientMessage:d,clientName:c})}else(J==="rejected"||J==="cancelled")&&(h("error"),w.error("Pagamento rejeitado ou cancelado"))}else console.error("❌ Erro ao verificar status do pagamento:",O.status)}catch(O){console.error("Erro ao verificar pagamento:",O)}finally{M(!1)}},g=()=>{f!=null&&f.qrCode&&(navigator.clipboard.writeText(f.qrCode),w.success("Código Pix copiado!"))},R=()=>{f!=null&&f.ticketUrl&&window.open(f.ticketUrl,"_blank")},A=()=>{h("confirm"),y(null),C(1800),j(!1),t()};return r?e.jsx(Re,{children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4 overflow-y-auto",children:e.jsxs(V.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"bg-white dark:bg-gray-800 rounded-lg max-w-md w-full my-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[x==="confirm"&&"Escolha seu SuperVoto",x==="payment"&&"Pagamento Pix",x==="success"&&"Pagamento Aprovado!",x==="error"&&"Erro no Pagamento"]}),e.jsx("button",{onClick:A,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":"Fechar modal",children:e.jsx(Qe,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[s.thumbnailUrl&&e.jsx("img",{src:s.thumbnailUrl,alt:`Capa de ${s.title} por ${s.artist}`,className:"w-16 h-16 rounded-lg object-cover"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:s.title}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:s.artist}),s.duration&&e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:[Math.floor(s.duration/60),":",(s.duration%60).toString().padStart(2,"0")]})]})]}),x==="confirm"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(ye,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Escolha seu SuperVoto"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Seu SuperVoto prioriza a música na fila. Selecione um valor:"})]}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:hc.map(_=>e.jsxs("button",{onClick:()=>E(_.amount),disabled:k,className:"px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Gerar Pix de ${_.label}`,children:[e.jsx(ut,{className:"w-4 h-4"}),e.jsx("span",{children:_.label})]},_.amount))}),e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(mn,{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Como funciona:"}),e.jsxs("ul",{className:"text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• Pague via Pix"}),e.jsx("li",{children:"• Sua música vai para a fila prioritária"}),e.jsx("li",{children:'• Acompanhe as letras no "Cante Comigo"'}),e.jsx("li",{children:"• Outros clientes podem votar na sua performance"})]})]})]})})]}),x==="payment"&&f&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Mensagem para o restaurante (opcional)"}),e.jsx("textarea",{value:d,onChange:_=>m(_.target.value),rows:2,maxLength:200,placeholder:"Escreva uma mensagem para acompanhar seu Supervoto...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Máx. 200 caracteres"})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400",children:[e.jsx(Be,{className:"w-5 h-5"}),e.jsx("span",{className:"font-mono text-lg",children:T(v)}),e.jsx("span",{className:"text-sm",children:"para pagar"})]}),e.jsxs("div",{className:"text-center",children:[L?e.jsxs("div",{className:"bg-yellow-100 dark:bg-yellow-900/20 p-4 rounded-lg mb-4",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(ut,{className:"w-8 h-8 text-yellow-600 dark:text-yellow-400"})}),e.jsx("p",{className:"text-sm text-yellow-800 dark:text-yellow-200 font-medium mb-2",children:"QR Code temporariamente indisponível"}),e.jsx("p",{className:"text-xs text-yellow-700 dark:text-yellow-300",children:"Use o código PIX abaixo para fazer o pagamento manualmente no seu app do banco"})]}):e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block mb-4",children:e.jsx("img",{src:f.qrCodeBase64,alt:`QR Code para pagamento de ${s.title}`,className:"w-48 h-48 mx-auto",onError:_=>{console.error("❌ Erro ao carregar QR Code:",f.qrCodeBase64),j(!0),_.currentTarget.style.display="none"},onLoad:()=>{console.log("✅ QR Code carregado com sucesso!")}})}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Escaneie o QR Code com seu app do banco"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Ou copie o código Pix:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"text",value:f.qrCode,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm font-mono text-gray-900 dark:text-gray-100","aria-label":"Código Pix"}),e.jsx("button",{onClick:g,className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Copiar código Pix",children:e.jsx(pn,{className:"w-4 h-4"})})]})]}),f.ticketUrl&&e.jsxs("button",{onClick:R,className:"w-full flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors","aria-label":"Abrir pagamento no Mercado Pago",children:[e.jsx(qr,{className:"w-4 h-4"}),e.jsx("span",{children:"Abrir no Mercado Pago"})]}),e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"text-sm",children:"Aguardando pagamento..."})]})})]}),x==="success"&&e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(Gr,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Pagamento Aprovado!"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Sua música foi adicionada à fila prioritária e tocará em breve."})]}),e.jsx("button",{onClick:A,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors","aria-label":"Continuar após pagamento aprovado",children:"Continuar"})]}),x==="error"&&e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(Ye,{className:"w-8 h-8 text-red-600 dark:text-red-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Erro no Pagamento"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Não foi possível processar o pagamento. Tente novamente."})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{h("confirm"),y(null),C(1800),j(!1)},className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors","aria-label":"Tentar pagamento novamente",children:"Tentar Novamente"}),e.jsx("button",{onClick:A,className:"w-full py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors","aria-label":"Cancelar pagamento",children:"Cancelar"})]})]})]})]})})}):null};var Oa={exports:{}},pc="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",fc=pc,xc=fc;function Da(){}function La(){}La.resetWarningCache=Da;var gc=function(){function r(a,n,i,o,l,c){if(c!==xc){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}r.isRequired=r;function t(){return r}var s={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:t,element:r,elementType:r,instanceOf:t,node:r,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:La,resetWarningCache:Da};return s.PropTypes=s,s};Oa.exports=gc();var yc=Oa.exports;const ae=Yt(yc);var bc=function r(t,s){if(t===s)return!0;if(t&&s&&typeof t=="object"&&typeof s=="object"){if(t.constructor!==s.constructor)return!1;var a,n,i;if(Array.isArray(t)){if(a=t.length,a!=s.length)return!1;for(n=a;n--!==0;)if(!r(t[n],s[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if(i=Object.keys(t),a=i.length,a!==Object.keys(s).length)return!1;for(n=a;n--!==0;)if(!Object.prototype.hasOwnProperty.call(s,i[n]))return!1;for(n=a;n--!==0;){var o=i[n];if(!r(t[o],s[o]))return!1}return!0}return t!==t&&s!==s};const vc=Yt(bc);var Ds={exports:{}},$a;/**
* @link https://github.com/gajus/sister for the canonical source repository
* @license https://github.com/gajus/sister/blob/master/LICENSE BSD 3-Clause
*/$a=function(){var r={},t={};return r.on=function(s,a){var n={name:s,handler:a};return t[s]=t[s]||[],t[s].unshift(n),n},r.off=function(s){var a=t[s.name].indexOf(s);a!==-1&&t[s.name].splice(a,1)},r.trigger=function(s,a){var n=t[s],i;if(n)for(i=n.length;i--;)n[i].handler(a)},r};var jc=$a,Ls={exports:{}},wc=function(t,s,a){var n=document.head||document.getElementsByTagName("head")[0],i=document.createElement("script");typeof s=="function"&&(a=s,s={}),s=s||{},a=a||function(){},i.type=s.type||"text/javascript",i.charset=s.charset||"utf8",i.async="async"in s?!!s.async:!0,i.src=t,s.attrs&&Nc(i,s.attrs),s.text&&(i.text=""+s.text);var o="onload"in i?Or:kc;o(i,a),i.onload||Or(i,a),n.appendChild(i)};function Nc(r,t){for(var s in t)r.setAttribute(s,t[s])}function Or(r,t){r.onload=function(){this.onerror=this.onload=null,t(null,r)},r.onerror=function(){this.onerror=this.onload=null,t(new Error("Failed to load "+this.src),r)}}function kc(r,t){r.onreadystatechange=function(){this.readyState!="complete"&&this.readyState!="loaded"||(this.onreadystatechange=null,t(null,r))}}(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=wc,a=n(s);function n(i){return i&&i.__esModule?i:{default:i}}t.default=function(i){var o=new Promise(function(l){if(window.YT&&window.YT.Player&&window.YT.Player instanceof Function){l(window.YT);return}else{var c=window.location.protocol==="http:"?"http:":"https:";(0,a.default)(c+"//www.youtube.com/iframe_api",function(d){d&&i.trigger("error",d)})}var u=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=function(){u&&u(),l(window.YT)}});return o},r.exports=t.default})(Ls,Ls.exports);var Sc=Ls.exports,$s={exports:{}},Vs={exports:{}},Fs={exports:{}},mt=1e3,pt=mt*60,ft=pt*60,xt=ft*24,Ec=xt*365.25,Cc=function(r,t){t=t||{};var s=typeof r;if(s==="string"&&r.length>0)return Rc(r);if(s==="number"&&isNaN(r)===!1)return t.long?Pc(r):_c(r);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(r))};function Rc(r){if(r=String(r),!(r.length>100)){var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(r);if(t){var s=parseFloat(t[1]),a=(t[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return s*Ec;case"days":case"day":case"d":return s*xt;case"hours":case"hour":case"hrs":case"hr":case"h":return s*ft;case"minutes":case"minute":case"mins":case"min":case"m":return s*pt;case"seconds":case"second":case"secs":case"sec":case"s":return s*mt;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}function _c(r){return r>=xt?Math.round(r/xt)+"d":r>=ft?Math.round(r/ft)+"h":r>=pt?Math.round(r/pt)+"m":r>=mt?Math.round(r/mt)+"s":r+"ms"}function Pc(r){return Pt(r,xt,"day")||Pt(r,ft,"hour")||Pt(r,pt,"minute")||Pt(r,mt,"second")||r+" ms"}function Pt(r,t,s){if(!(r<t))return r<t*1.5?Math.floor(r/t)+" "+s:Math.ceil(r/t)+" "+s+"s"}(function(r,t){t=r.exports=n.debug=n.default=n,t.coerce=c,t.disable=o,t.enable=i,t.enabled=l,t.humanize=Cc,t.names=[],t.skips=[],t.formatters={};var s;function a(u){var d=0,m;for(m in u)d=(d<<5)-d+u.charCodeAt(m),d|=0;return t.colors[Math.abs(d)%t.colors.length]}function n(u){function d(){if(d.enabled){var m=d,x=+new Date,h=x-(s||x);m.diff=h,m.prev=s,m.curr=x,s=x;for(var f=new Array(arguments.length),y=0;y<f.length;y++)f[y]=arguments[y];f[0]=t.coerce(f[0]),typeof f[0]!="string"&&f.unshift("%O");var k=0;f[0]=f[0].replace(/%([a-zA-Z%])/g,function(v,C){if(v==="%%")return v;k++;var F=t.formatters[C];if(typeof F=="function"){var M=f[k];v=F.call(m,M),f.splice(k,1),k--}return v}),t.formatArgs.call(m,f);var b=d.log||t.log||console.log.bind(console);b.apply(m,f)}}return d.namespace=u,d.enabled=t.enabled(u),d.useColors=t.useColors(),d.color=a(u),typeof t.init=="function"&&t.init(d),d}function i(u){t.save(u),t.names=[],t.skips=[];for(var d=(typeof u=="string"?u:"").split(/[\s,]+/),m=d.length,x=0;x<m;x++)d[x]&&(u=d[x].replace(/\*/g,".*?"),u[0]==="-"?t.skips.push(new RegExp("^"+u.substr(1)+"$")):t.names.push(new RegExp("^"+u+"$")))}function o(){t.enable("")}function l(u){var d,m;for(d=0,m=t.skips.length;d<m;d++)if(t.skips[d].test(u))return!1;for(d=0,m=t.names.length;d<m;d++)if(t.names[d].test(u))return!0;return!1}function c(u){return u instanceof Error?u.stack||u.message:u}})(Fs,Fs.exports);var Ac=Fs.exports;(function(r,t){t=r.exports=Ac,t.log=n,t.formatArgs=a,t.save=i,t.load=o,t.useColors=s,t.storage=typeof chrome<"u"&&typeof chrome.storage<"u"?chrome.storage.local:l(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"];function s(){return typeof window<"u"&&window.process&&window.process.type==="renderer"?!0:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}t.formatters.j=function(c){try{return JSON.stringify(c)}catch(u){return"[UnexpectedJSONParseError]: "+u.message}};function a(c){var u=this.useColors;if(c[0]=(u?"%c":"")+this.namespace+(u?" %c":" ")+c[0]+(u?"%c ":" ")+"+"+t.humanize(this.diff),!!u){var d="color: "+this.color;c.splice(1,0,d,"color: inherit");var m=0,x=0;c[0].replace(/%[a-zA-Z%]/g,function(h){h!=="%%"&&(m++,h==="%c"&&(x=m))}),c.splice(x,0,d)}}function n(){return typeof console=="object"&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function i(c){try{c==null?t.storage.removeItem("debug"):t.storage.debug=c}catch{}}function o(){var c;try{c=t.storage.debug}catch{}return!c&&typeof process<"u"&&"env"in process&&(c={}.DEBUG),c}t.enable(o());function l(){try{return window.localStorage}catch{}}})(Vs,Vs.exports);var Tc=Vs.exports,Ms={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=["cueVideoById","loadVideoById","cueVideoByUrl","loadVideoByUrl","playVideo","pauseVideo","stopVideo","getVideoLoadedFraction","cuePlaylist","loadPlaylist","nextVideo","previousVideo","playVideoAt","setShuffle","setLoop","getPlaylist","getPlaylistIndex","setOption","mute","unMute","isMuted","setVolume","getVolume","seekTo","getPlayerState","getPlaybackRate","setPlaybackRate","getAvailablePlaybackRates","getPlaybackQuality","setPlaybackQuality","getAvailableQualityLevels","getCurrentTime","getDuration","removeEventListener","getVideoUrl","getVideoEmbedCode","getOptions","getOption","addEventListener","destroy","setSize","getIframe"],r.exports=t.default})(Ms,Ms.exports);var Ic=Ms.exports,Us={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=["ready","stateChange","playbackQualityChange","playbackRateChange","error","apiChange","volumeChange"],r.exports=t.default})(Us,Us.exports);var Oc=Us.exports,qs={exports:{}},Bs={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default={BUFFERING:3,ENDED:0,PAUSED:2,PLAYING:1,UNSTARTED:-1,VIDEO_CUED:5},r.exports=t.default})(Bs,Bs.exports);var Dc=Bs.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=Dc,a=n(s);function n(i){return i&&i.__esModule?i:{default:i}}t.default={pauseVideo:{acceptableStates:[a.default.ENDED,a.default.PAUSED],stateChangeRequired:!1},playVideo:{acceptableStates:[a.default.ENDED,a.default.PLAYING],stateChangeRequired:!1},seekTo:{acceptableStates:[a.default.ENDED,a.default.PLAYING,a.default.PAUSED],stateChangeRequired:!0,timeout:3e3}},r.exports=t.default})(qs,qs.exports);var Lc=qs.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=Tc,a=d(s),n=Ic,i=d(n),o=Oc,l=d(o),c=Lc,u=d(c);function d(h){return h&&h.__esModule?h:{default:h}}var m=(0,a.default)("youtube-player"),x={};x.proxyEvents=function(h){var f={},y=function(j){var D="on"+j.slice(0,1).toUpperCase()+j.slice(1);f[D]=function(N){m('event "%s"',D,N),h.trigger(j,N)}},k=!0,b=!1,v=void 0;try{for(var C=l.default[Symbol.iterator](),F;!(k=(F=C.next()).done);k=!0){var M=F.value;y(M)}}catch(L){b=!0,v=L}finally{try{!k&&C.return&&C.return()}finally{if(b)throw v}}return f},x.promisifyPlayer=function(h){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,y={},k=function(D){f&&u.default[D]?y[D]=function(){for(var N=arguments.length,T=Array(N),E=0;E<N;E++)T[E]=arguments[E];return h.then(function(P){var g=u.default[D],R=P.getPlayerState(),A=P[D].apply(P,T);return g.stateChangeRequired||Array.isArray(g.acceptableStates)&&g.acceptableStates.indexOf(R)===-1?new Promise(function(_){var S=function O(){var G=P.getPlayerState(),J=void 0;typeof g.timeout=="number"&&(J=setTimeout(function(){P.removeEventListener("onStateChange",O),_()},g.timeout)),Array.isArray(g.acceptableStates)&&g.acceptableStates.indexOf(G)!==-1&&(P.removeEventListener("onStateChange",O),clearTimeout(J),_())};P.addEventListener("onStateChange",S)}).then(function(){return A}):A})}:y[D]=function(){for(var N=arguments.length,T=Array(N),E=0;E<N;E++)T[E]=arguments[E];return h.then(function(P){return P[D].apply(P,T)})}},b=!0,v=!1,C=void 0;try{for(var F=i.default[Symbol.iterator](),M;!(b=(M=F.next()).done);b=!0){var L=M.value;k(L)}}catch(j){v=!0,C=j}finally{try{!b&&F.return&&F.return()}finally{if(v)throw C}}return y},t.default=x,r.exports=t.default})($s,$s.exports);var $c=$s.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(m){return typeof m}:function(m){return m&&typeof Symbol=="function"&&m.constructor===Symbol&&m!==Symbol.prototype?"symbol":typeof m},a=jc,n=u(a),i=Sc,o=u(i),l=$c,c=u(l);function u(m){return m&&m.__esModule?m:{default:m}}var d=void 0;t.default=function(m){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,f=(0,n.default)();if(d||(d=(0,o.default)(f)),x.events)throw new Error("Event handlers cannot be overwritten.");if(typeof m=="string"&&!document.getElementById(m))throw new Error('Element "'+m+'" does not exist.');x.events=c.default.proxyEvents(f);var y=new Promise(function(b){if((typeof m>"u"?"undefined":s(m))==="object"&&m.playVideo instanceof Function){var v=m;b(v)}else d.then(function(C){var F=new C.Player(m,x);return f.on("ready",function(){b(F)}),null})}),k=c.default.promisifyPlayer(y,h);return k.on=f.on,k.off=f.off,k},r.exports=t.default})(Ds,Ds.exports);var Vc=Ds.exports;const Fc=Yt(Vc);var Mc=Object.defineProperty,Uc=Object.defineProperties,qc=Object.getOwnPropertyDescriptors,Dr=Object.getOwnPropertySymbols,Bc=Object.prototype.hasOwnProperty,Qc=Object.prototype.propertyIsEnumerable,Lr=(r,t,s)=>t in r?Mc(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s,Qs=(r,t)=>{for(var s in t||(t={}))Bc.call(t,s)&&Lr(r,s,t[s]);if(Dr)for(var s of Dr(t))Qc.call(t,s)&&Lr(r,s,t[s]);return r},zs=(r,t)=>Uc(r,qc(t)),zc=(r,t,s)=>new Promise((a,n)=>{var i=c=>{try{l(s.next(c))}catch(u){n(u)}},o=c=>{try{l(s.throw(c))}catch(u){n(u)}},l=c=>c.done?a(c.value):Promise.resolve(c.value).then(i,o);l((s=s.apply(r,t)).next())});function Wc(r,t){var s,a;if(r.videoId!==t.videoId)return!0;const n=((s=r.opts)==null?void 0:s.playerVars)||{},i=((a=t.opts)==null?void 0:a.playerVars)||{};return n.start!==i.start||n.end!==i.end}function $r(r={}){return zs(Qs({},r),{height:0,width:0,playerVars:zs(Qs({},r.playerVars),{autoplay:0,start:0,end:0})})}function Hc(r,t){return r.videoId!==t.videoId||!vc($r(r.opts),$r(t.opts))}function Kc(r,t){var s,a,n,i;return r.id!==t.id||r.className!==t.className||((s=r.opts)==null?void 0:s.width)!==((a=t.opts)==null?void 0:a.width)||((n=r.opts)==null?void 0:n.height)!==((i=t.opts)==null?void 0:i.height)||r.iframeClassName!==t.iframeClassName||r.title!==t.title}var Gc={videoId:"",id:"",className:"",iframeClassName:"",style:{},title:"",loading:void 0,opts:{},onReady:()=>{},onError:()=>{},onPlay:()=>{},onPause:()=>{},onEnd:()=>{},onStateChange:()=>{},onPlaybackRateChange:()=>{},onPlaybackQualityChange:()=>{}},Yc={videoId:ae.string,id:ae.string,className:ae.string,iframeClassName:ae.string,style:ae.object,title:ae.string,loading:ae.oneOf(["lazy","eager"]),opts:ae.objectOf(ae.any),onReady:ae.func,onError:ae.func,onPlay:ae.func,onPause:ae.func,onEnd:ae.func,onStateChange:ae.func,onPlaybackRateChange:ae.func,onPlaybackQualityChange:ae.func},Ut=class extends H.Component{constructor(r){super(r),this.destroyPlayerPromise=void 0,this.onPlayerReady=t=>{var s,a;return(a=(s=this.props).onReady)==null?void 0:a.call(s,t)},this.onPlayerError=t=>{var s,a;return(a=(s=this.props).onError)==null?void 0:a.call(s,t)},this.onPlayerStateChange=t=>{var s,a,n,i,o,l,c,u;switch((a=(s=this.props).onStateChange)==null||a.call(s,t),t.data){case Ut.PlayerState.ENDED:(i=(n=this.props).onEnd)==null||i.call(n,t);break;case Ut.PlayerState.PLAYING:(l=(o=this.props).onPlay)==null||l.call(o,t);break;case Ut.PlayerState.PAUSED:(u=(c=this.props).onPause)==null||u.call(c,t);break}},this.onPlayerPlaybackRateChange=t=>{var s,a;return(a=(s=this.props).onPlaybackRateChange)==null?void 0:a.call(s,t)},this.onPlayerPlaybackQualityChange=t=>{var s,a;return(a=(s=this.props).onPlaybackQualityChange)==null?void 0:a.call(s,t)},this.destroyPlayer=()=>this.internalPlayer?(this.destroyPlayerPromise=this.internalPlayer.destroy().then(()=>this.destroyPlayerPromise=void 0),this.destroyPlayerPromise):Promise.resolve(),this.createPlayer=()=>{if(typeof document>"u")return;if(this.destroyPlayerPromise){this.destroyPlayerPromise.then(this.createPlayer);return}const t=zs(Qs({},this.props.opts),{videoId:this.props.videoId});this.internalPlayer=Fc(this.container,t),this.internalPlayer.on("ready",this.onPlayerReady),this.internalPlayer.on("error",this.onPlayerError),this.internalPlayer.on("stateChange",this.onPlayerStateChange),this.internalPlayer.on("playbackRateChange",this.onPlayerPlaybackRateChange),this.internalPlayer.on("playbackQualityChange",this.onPlayerPlaybackQualityChange),(this.props.title||this.props.loading)&&this.internalPlayer.getIframe().then(s=>{this.props.title&&s.setAttribute("title",this.props.title),this.props.loading&&s.setAttribute("loading",this.props.loading)})},this.resetPlayer=()=>this.destroyPlayer().then(this.createPlayer),this.updatePlayer=()=>{var t;(t=this.internalPlayer)==null||t.getIframe().then(s=>{this.props.id?s.setAttribute("id",this.props.id):s.removeAttribute("id"),this.props.iframeClassName?s.setAttribute("class",this.props.iframeClassName):s.removeAttribute("class"),this.props.opts&&this.props.opts.width?s.setAttribute("width",this.props.opts.width.toString()):s.removeAttribute("width"),this.props.opts&&this.props.opts.height?s.setAttribute("height",this.props.opts.height.toString()):s.removeAttribute("height"),this.props.title?s.setAttribute("title",this.props.title):s.setAttribute("title","YouTube video player"),this.props.loading?s.setAttribute("loading",this.props.loading):s.removeAttribute("loading")})},this.getInternalPlayer=()=>this.internalPlayer,this.updateVideo=()=>{var t,s,a,n;if(typeof this.props.videoId>"u"||this.props.videoId===null){(t=this.internalPlayer)==null||t.stopVideo();return}let i=!1;const o={videoId:this.props.videoId};if((s=this.props.opts)!=null&&s.playerVars&&(i=this.props.opts.playerVars.autoplay===1,"start"in this.props.opts.playerVars&&(o.startSeconds=this.props.opts.playerVars.start),"end"in this.props.opts.playerVars&&(o.endSeconds=this.props.opts.playerVars.end)),i){(a=this.internalPlayer)==null||a.loadVideoById(o);return}(n=this.internalPlayer)==null||n.cueVideoById(o)},this.refContainer=t=>{this.container=t},this.container=null,this.internalPlayer=null}componentDidMount(){this.createPlayer()}componentDidUpdate(r){return zc(this,null,function*(){Kc(r,this.props)&&this.updatePlayer(),Hc(r,this.props)&&(yield this.resetPlayer()),Wc(r,this.props)&&this.updateVideo()})}componentWillUnmount(){this.destroyPlayer()}render(){return H.createElement("div",{className:this.props.className,style:this.props.style},H.createElement("div",{id:this.props.id,className:this.props.iframeClassName,ref:this.refContainer}))}},rs=Ut;rs.propTypes=Yc;rs.defaultProps=Gc;rs.PlayerState={UNSTARTED:-1,ENDED:0,PLAYING:1,PAUSED:2,BUFFERING:3,CUED:5};var At=rs;const Jc=({isOpen:r,onClose:t,suggestion:s,sessionId:a,onVoteRequest:n})=>{var $e,We;const[i,o]=p.useState(null),[l,c]=p.useState(0),[u,d]=p.useState(!1),[m,x]=p.useState(!1),[h,f]=p.useState(!1),[y,k]=p.useState(!1),[b,v]=p.useState(24),[C,F]=p.useState("#3B82F6"),[M,L]=p.useState(!1),[j,D]=p.useState(-1),[N,T]=p.useState(0),E=p.useRef(null),P=p.useRef(null),g=p.useRef(null),R=p.useCallback(async()=>{if(!s.youtubeVideoId){w.error("ID do vídeo não fornecido");return}try{L(!0);const $=await fetch(Z(`/lyrics/search?title=${encodeURIComponent(s.title)}&artist=${encodeURIComponent(s.artist)}&youtubeVideoId=${s.youtubeVideoId}`));if($.ok){const B=await $.json();if(B.success&&B.lyrics)o(B.lyrics),w.success("Letras carregadas! 🎤");else{const ce=await fetch(Z(`/lyrics/test?title=${encodeURIComponent(s.title)}&artist=${encodeURIComponent(s.artist)}`));if(ce.ok){const me=await ce.json();o(me.lyrics),w("Usando letras de demonstração 🎵",{icon:"ℹ️"})}else throw new Error("Letras de teste não disponíveis")}}else throw new Error("Erro na resposta da API de letras")}catch($){console.error("Erro ao carregar letras:",$),w.error("Não foi possível carregar as letras")}finally{L(!1)}},[s.title,s.artist,s.youtubeVideoId]),A=p.useCallback($=>{if(!i)return;let B=-1,ce=0;for(let me=0;me<i.lines.length&&$>=i.lines[me].time;me++)B=me;if(B>=0&&B<i.lines.length-1){const me=i.lines[B],wt=i.lines[B+1].time-me.time,Nt=$-me.time;ce=Math.min(1,Math.max(0,Nt/wt))}D(B),T(ce)},[i]),_=p.useCallback(()=>{if(!u||!E.current||!i)return;E.current.getInternalPlayer().getCurrentTime().then(B=>{c(B),A(B)})},[u,i,A]),S=p.useCallback(()=>{var B;const $=(B=E.current)==null?void 0:B.getInternalPlayer();$&&(u?$.pauseVideo():$.playVideo(),d(!u))},[u]),O=p.useCallback(()=>{var B;const $=(B=E.current)==null?void 0:B.getInternalPlayer();$&&(m?($.unMute(),$.setVolume(50)):$.mute(),x(!m))},[m]),G=p.useCallback(()=>{var B;const $=(B=E.current)==null?void 0:B.getInternalPlayer();$&&($.seekTo(0),u||($.playVideo(),d(!0))),c(0),D(-1),T(0)},[u]),J=p.useCallback(()=>{var $,B,ce;P.current&&(h?(ce=document.exitFullscreen)==null||ce.call(document):(B=($=P.current).requestFullscreen)==null||B.call($),f(!h))},[h]),he=p.useCallback($=>{$.target.setVolume(m?0:50)},[m]),Ne=p.useCallback($=>{$.data===At.PlayerState.PLAYING?d(!0):$.data===At.PlayerState.PAUSED?d(!1):$.data===At.PlayerState.ENDED&&(d(!1),c(0),D(-1),T(0))},[]),_e=p.useCallback($=>{const B=Math.floor($/60),ce=Math.floor($%60);return`${B}:${ce.toString().padStart(2,"0")}`},[]),ke=p.useCallback(()=>!i||j<0?null:i.lines[j]||null,[i,j]),Le=p.useCallback(()=>!i||j<0||j>=i.lines.length-1?null:i.lines[j+1]||null,[i,j]),Pe=p.useCallback(()=>{if(!i||j<0)return[];const $=Math.max(0,j-2),B=Math.min(i.lines.length,j+3);return i.lines.slice($,B)},[i,j]);return p.useEffect(()=>(r&&s.youtubeVideoId&&R(),()=>{o(null),c(0),D(-1),T(0),d(!1),g.current&&(clearInterval(g.current),g.current=null)}),[r,s.youtubeVideoId,R]),p.useEffect(()=>(u&&i?g.current=setInterval(_,50):g.current&&(clearInterval(g.current),g.current=null),()=>{g.current&&(clearInterval(g.current),g.current=null)}),[u,i,_]),r?e.jsx(Re,{children:e.jsxs(V.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:`fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex flex-col z-50 ${h?"p-0":"p-4"}`,ref:P,role:"dialog","aria-modal":"true","aria-labelledby":"karaoke-title",children:[e.jsxs("header",{className:"flex items-center justify-between p-4 sm:p-6 bg-black/30 backdrop-blur-md",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[s.thumbnailUrl&&e.jsx("img",{src:s.thumbnailUrl,alt:`Capa de ${s.title}`,className:"w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover","aria-hidden":"true"}),e.jsxs("div",{children:[e.jsx("h2",{id:"karaoke-title",className:"text-xl sm:text-2xl font-bold text-white",children:s.title}),e.jsx("p",{className:"text-sm sm:text-lg text-gray-300",children:s.artist})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>k(!y),className:"p-2 text-white hover:bg-white/20 rounded-lg transition-colors","aria-label":y?"Fechar configurações":"Abrir configurações",children:e.jsx(Ge,{className:"w-5 h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:J,className:"p-2 text-white hover:bg-white/20 rounded-lg transition-colors","aria-label":h?"Sair da tela cheia":"Entrar na tela cheia",children:h?e.jsx(fn,{className:"w-5 h-5","aria-hidden":"true"}):e.jsx(xn,{className:"w-5 h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:t,className:"p-2 text-white hover:bg-white/20 rounded-lg transition-colors","aria-label":"Fechar player de karaokê",children:e.jsx(Qe,{className:"w-5 h-5","aria-hidden":"true"})})]})]}),y&&e.jsx(V.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"bg-black/50 backdrop-blur-md p-4 mx-4 sm:mx-6 rounded-lg",children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-4 sm:gap-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-white text-sm",children:"Tamanho da fonte:"}),e.jsx("input",{type:"range",min:"16",max:"48",value:b,onChange:$=>v(Number($.target.value)),className:"w-20","aria-label":"Ajustar tamanho da fonte"}),e.jsxs("span",{className:"text-white text-sm",children:[b,"px"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-white text-sm",children:"Cor de destaque:"}),e.jsx("input",{type:"color",value:C,onChange:$=>F($.target.value),className:"w-8 h-8 rounded border-none","aria-label":"Escolher cor de destaque"})]})]})}),e.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center p-4 sm:p-6",children:[s.youtubeVideoId&&e.jsx("div",{className:"w-full max-w-4xl mb-4 sm:mb-6",children:e.jsx(At,{videoId:s.youtubeVideoId,opts:{width:"100%",height:h?"400":"300",playerVars:{autoplay:0,controls:0,rel:0,showinfo:0,modestbranding:1}},onReady:he,onStateChange:Ne,ref:E,className:"rounded-lg overflow-hidden"})}),M?e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("p",{className:"text-white text-lg sm:text-xl",children:"Carregando letras..."})]}):i?e.jsxs("div",{className:"text-center max-w-4xl w-full",children:[e.jsxs(V.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},className:"mb-6 sm:mb-8","aria-live":"polite",children:[e.jsx("div",{className:"text-white font-bold leading-relaxed mb-4",style:{fontSize:`${b}px`,color:ke()?C:"white",textShadow:"2px 2px 4px rgba(0,0,0,0.8)"},children:(($e=ke())==null?void 0:$e.text)||"🎵 Aguardando..."}),ke()&&e.jsx("div",{className:"w-full bg-white/20 rounded-full h-2 mb-4",children:e.jsx("div",{className:"h-2 rounded-full transition-all duration-100",style:{width:`${N*100}%`,backgroundColor:C},role:"progressbar","aria-valuenow":N*100,"aria-valuemin":0,"aria-valuemax":100})})]},j),Le()&&e.jsxs("div",{className:"text-gray-300 opacity-60 mb-6 sm:mb-8",style:{fontSize:`${b*.8}px`},children:["Próxima: ",(We=Le())==null?void 0:We.text]}),e.jsx("div",{className:"space-y-2 opacity-40",children:Pe().map(($,B)=>i.lines.indexOf($)===j?null:e.jsx("div",{className:"text-gray-400",style:{fontSize:`${b*.6}px`},children:$.text},B))})]}):e.jsxs("div",{className:"text-center",children:[e.jsx(pr,{className:"w-16 h-16 sm:w-24 sm:h-24 text-white opacity-50 mx-auto mb-4","aria-hidden":"true"}),e.jsx("p",{className:"text-white text-lg sm:text-xl mb-4",children:"🎤 Modo Karaokê Ativo!"}),e.jsx("p",{className:"text-gray-300 text-sm sm:text-base mb-2",children:"Letras sincronizadas não disponíveis."}),e.jsx("p",{className:"text-gray-300 text-sm sm:text-base",children:"Cante junto com o vídeo! 🎵"})]})]}),e.jsxs("footer",{className:"bg-black/30 backdrop-blur-md p-4 sm:p-6",children:[i&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between text-white text-sm mb-2",children:[e.jsx("span",{children:_e(l)}),e.jsx("span",{children:_e(s.duration||i.duration)})]}),e.jsx("div",{className:"w-full bg-white/20 rounded-full h-2",children:e.jsx("div",{className:"bg-white h-2 rounded-full transition-all duration-100",style:{width:`${l/(s.duration||i.duration)*100}%`},role:"progressbar","aria-valuenow":l/(s.duration||i.duration)*100,"aria-valuemin":0,"aria-valuemax":100})})]}),e.jsxs("div",{className:"flex items-center justify-center gap-4 sm:gap-6",children:[e.jsx("button",{onClick:G,className:"p-2 sm:p-3 bg-white/20 text-white rounded-full hover:bg-white/30 transition-colors","aria-label":"Reiniciar música",children:e.jsx(gn,{className:"w-5 h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:S,className:"p-3 sm:p-4 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors","aria-label":u?"Pausar música":"Tocar música",children:u?e.jsx(Br,{className:"w-6 h-6","aria-hidden":"true"}):e.jsx(be,{className:"w-6 h-6","aria-hidden":"true"})}),e.jsx("button",{onClick:O,className:"p-2 sm:p-3 bg-white/20 text-white rounded-full hover:bg-white/30 transition-colors","aria-label":m?"Ativar som":"Silenciar som",children:m?e.jsx(yn,{className:"w-5 h-5","aria-hidden":"true"}):e.jsx(Yr,{className:"w-5 h-5","aria-hidden":"true"})}),n&&e.jsx("button",{onClick:n,className:"p-2 sm:p-3 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors","aria-label":"Solicitar votação da performance",children:e.jsx(Ys,{className:"w-5 h-5","aria-hidden":"true"})})]}),e.jsxs("div",{className:"flex items-center justify-center gap-4 sm:gap-6 mt-4 text-white text-xs sm:text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(pr,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Cante Comigo"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(et,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Karaokê Interativo"})]}),(i==null?void 0:i.hasTimestamps)&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(qe,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Sincronizado"})]})]})]})]})}):null},Xc=({restaurantId:r,sessionId:t,isCollapsed:s=!1,onToggleCollapse:a})=>{var T,E,P;const[n,i]=p.useState([]),[o,l]=p.useState(null),[c,u]=p.useState(!1),[d,m]=p.useState(null),[x,h]=p.useState([]),{on:f,off:y,joinRestaurant:k}=jt(),[b,v]=p.useState({});p.useEffect(()=>{C(),k(r)},[r]),p.useEffect(()=>{const g=R=>{try{R!=null&&R.queue&&i(R.queue),R!=null&&R.stats&&l(R.stats),R!=null&&R.currentlyPlaying&&m(R.currentlyPlaying)}catch(A){console.warn("Falha ao aplicar queue-update:",A)}F(),M()};return f("queue-update",g),()=>y("queue-update",g)},[f,y]);const C=p.useCallback(async()=>{try{u(!0);const[g,R]=await Promise.all([fetch(Z(`/playback-queue/${r}`)),fetch(Z(`/collaborative-playlist/${r}/ranking`))]);if(g.ok){const A=await g.json();i(A.queue||[]),l(A.stats),m(A.currentlyPlaying||null)}if(R.ok){const A=await R.json();h(Array.isArray(A==null?void 0:A.data)?A.data:[])}}catch(g){console.error("Erro ao carregar fila/ranking:",g)}finally{u(!1)}},[r]),F=p.useCallback(async()=>{try{const g=await fetch(Z(`/collaborative-playlist/${r}/ranking`));if(g.ok){const R=await g.json();h(Array.isArray(R==null?void 0:R.data)?R.data:[])}}catch(g){console.warn("Falha ao recarregar ranking:",g)}},[r]),M=p.useCallback(async()=>{try{const g=n.map(_=>_.youtubeVideoId).filter(_=>!!_);if(g.length===0)return;const R=await fetch(Z(`/playback-queue/${r}/likes?ids=${encodeURIComponent(g.join(","))}`));if(!R.ok)return;const A=await R.json();v(A.counts||{})}catch{}},[r,n]);p.useEffect(()=>{M()},[n,M]);const L=async(g,R)=>{try{const _=await(await fetch(Z(`/playback-queue/${r}/like`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:g,action:R})})).json();_!=null&&_.success&&v(S=>({...S,[g]:{likeCount:_.likeCount,dislikeCount:_.dislikeCount}}))}catch{w.error("Falha ao registrar preferência")}},j=g=>{const R=Math.floor(g/60),A=g%60;return`${R}:${A.toString().padStart(2,"0")}`},D=g=>{const R=Math.floor(g/60);if(R>60){const A=Math.floor(R/60),_=R%60;return`${A}h ${_}m`}return`${R}m`},N=g=>!1;return c&&n.length===0?e.jsx("div",{className:"bg-white/5 rounded-lg p-4 border border-white/10",children:e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"text-white",children:"Carregando fila..."})]})}):e.jsxs("div",{className:"bg-white/5 rounded-xl border border-white/10 overflow-hidden shadow-lg shadow-black/20",children:[e.jsx("div",{className:"p-4 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm cursor-pointer",onClick:a,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(te,{className:"w-5 h-5 text-purple-400"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-semibold",children:"Fila de Reprodução"}),o&&e.jsxs("p",{className:"text-purple-200 text-sm",children:[o.totalItems," músicas • ",o.paidItems," pagas • ",D(o.estimatedWaitTime)," total"]}),x.length>0&&e.jsxs("p",{className:"text-indigo-300 text-xs mt-1",children:["Próxima por votação: ",((T=x[0])==null?void 0:T.title)||((E=x[0])==null?void 0:E.youtubeVideoId)," • ",(P=x[0])==null?void 0:P.voteCount," votos"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[o&&o.paidItems>0&&e.jsxs("div",{className:"flex items-center space-x-1 text-yellow-400",children:[e.jsx(ye,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:o.paidItems})]}),a&&e.jsx("button",{className:"p-1 text-purple-300 hover:text-white transition-colors",children:s?e.jsx(bn,{className:"w-4 h-4"}):e.jsx(vn,{className:"w-4 h-4"})})]})]})}),d&&!s&&e.jsx("div",{className:"p-4 bg-green-600/10 border-y border-white/10",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:d.thumbnailUrl||`https://img.youtube.com/vi/${d.suggestionId}/mqdefault.jpg`,alt:d.title,className:"w-12 h-12 rounded-lg object-cover"}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg",children:e.jsx(Yr,{className:"w-4 h-4 text-green-400"})})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(be,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm font-medium",children:"TOCANDO AGORA"}),d.isPaid&&e.jsx(ye,{className:"w-3 h-3 text-yellow-400"})]}),e.jsx("h4",{className:"text-white font-medium truncate",children:d.title}),e.jsx("p",{className:"text-gray-300 text-sm truncate",children:d.artist}),d.tableName&&e.jsxs("p",{className:"text-purple-300 text-xs",children:["Por ",d.tableName]})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:"text-white text-sm",children:j(d.duration)})})]})}),!s&&x.length>0&&e.jsxs("div",{className:"px-4 py-3 border-t border-white/10 bg-white/5",children:[e.jsx("h4",{className:"text-sm font-semibold text-indigo-300 mb-2",children:"Mais votadas agora"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:x.slice(0,5).map((g,R)=>e.jsxs("div",{className:"flex items-center justify-between text-xs text-purple-200",children:[e.jsxs("div",{className:"truncate",children:[e.jsxs("span",{className:"text-indigo-400 mr-2",children:["#",R+1]}),e.jsx("span",{className:"truncate inline-block max-w-[220px] align-middle",children:g.title||g.youtubeVideoId}),R===0&&e.jsx("span",{className:"ml-2 px-2 py-0.5 bg-emerald-600/20 text-emerald-300 rounded-full",children:"Próxima por votação"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[g.isPaid&&e.jsx(ye,{className:"w-3 h-3 text-yellow-400"}),e.jsxs("span",{className:"text-indigo-300",children:[g.voteCount," votos"]})]})]},g.youtubeVideoId))})]}),e.jsx(Re,{children:!s&&e.jsx(V.div,{initial:{height:0},animate:{height:"auto"},exit:{height:0},className:"overflow-hidden",children:n.length===0?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(te,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Nenhuma música na fila"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Seja o primeiro a sugerir!"})]}):e.jsx("div",{className:"max-h-96 overflow-y-auto divide-y divide-white/10",children:n.map((g,R)=>{var A,_;return e.jsx(V.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:R*.05},className:"p-3 hover:bg-white/5 transition-colors ",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 w-8 text-center",children:e.jsxs("div",{className:`text-sm font-medium ${g.isPaid?"text-yellow-400":"text-purple-300"}`,children:["#",g.position]})}),e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:g.thumbnailUrl||`https://img.youtube.com/vi/${g.suggestionId}/mqdefault.jpg`,alt:g.title,className:"w-10 h-10 rounded object-cover"}),g.isPaid&&e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center",children:e.jsx(ye,{className:"w-2 h-2 text-white"})})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium truncate text-white",children:g.title}),e.jsx("p",{className:"text-gray-400 text-sm truncate",children:g.artist}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[g.tableName&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(et,{className:"w-3 h-3 text-purple-400"}),e.jsx("span",{className:"text-purple-300 text-xs",children:g.tableName})]}),g.isPaid&&g.paymentAmount&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(ye,{className:"w-3 h-3 text-yellow-400"}),e.jsxs("span",{className:"text-yellow-300 text-xs",children:["R$ ",Number(g.paymentAmount||0).toFixed(2)]})]}),typeof g.voteCount=="number"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(qe,{className:"w-3 h-3 text-indigo-400"}),e.jsxs("span",{className:"text-indigo-300 text-xs",children:[g.voteCount," votos"]})]}),g.youtubeVideoId&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{className:"px-2 py-0.5 text-xs rounded border border-white/10 hover:bg-green-600/30 text-green-300",onClick:()=>g.youtubeVideoId&&L(g.youtubeVideoId,"like"),title:"Curtir (não altera a votação)",children:["👍 ",((A=b[g.youtubeVideoId])==null?void 0:A.likeCount)??g.likeCount??0]}),e.jsxs("button",{className:"px-2 py-0.5 text-xs rounded border border-white/10 hover:bg-red-600/30 text-red-300",onClick:()=>g.youtubeVideoId&&L(g.youtubeVideoId,"dislike"),title:"Não curtir (não altera a votação)",children:["👎 ",((_=b[g.youtubeVideoId])==null?void 0:_.dislikeCount)??g.dislikeCount??0]})]}),N()]})]}),e.jsxs("div",{className:"text-right flex-shrink-0",children:[e.jsx("div",{className:"text-white text-sm",children:j(g.duration)}),g.estimatedPlayTime&&e.jsxs("div",{className:"flex items-center space-x-1 text-gray-400 text-xs",children:[e.jsx(Be,{className:"w-3 h-3"}),e.jsx("span",{children:new Date(g.estimatedPlayTime).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]})]})]})},g.id)})})})}),!s&&o&&o.totalItems>0&&e.jsx("div",{className:"p-3 bg-white/5 border-t border-white/10",children:e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-purple-300",children:[e.jsx(te,{className:"w-3 h-3"}),e.jsxs("span",{children:[o.totalItems," total"]})]}),o.paidItems>0&&e.jsxs("div",{className:"flex items-center space-x-1 text-yellow-400",children:[e.jsx(ye,{className:"w-3 h-3"}),e.jsxs("span",{children:[o.paidItems," pagas"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-gray-400",children:[e.jsx(Be,{className:"w-3 h-3"}),e.jsxs("span",{children:[D(o.totalDuration)," total"]})]})]})})]})},Zc=({isOpen:r,onClose:t,restaurantId:s,sessionId:a})=>{const[n,i]=p.useState(!1),[o,l]=p.useState(!1),[c,u]=p.useState({name:"",avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}}),[d,m]=p.useState([]),x=p.useCallback(()=>{l(!0);try{const b=localStorage.getItem(`clientProfile_${a}`);if(b)u(JSON.parse(b));else{const v={name:`Cliente ${a.slice(0,8)}`,avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}};u(v),localStorage.setItem(`clientProfile_${a}`,JSON.stringify(v))}}catch(b){console.error("Error loading profile:",b)}finally{l(!1)}},[a]),h=p.useCallback(async()=>{l(!0);try{const b=await fetch(Z(`/collaborative-playlist/${s}/ranking`));if(b.ok){const{ranking:v}=await b.json();m(v)}else throw new Error("Falha ao buscar ranking")}catch(b){console.error("Erro ao carregar ranking:",b),m([{title:"Música Exemplo",artist:"Artista",points:61,voteCount:12,superVote5:3,superVote20:1,superVote40:1},{title:"Outra Música",artist:"Outro",points:40,voteCount:5,superVote5:2,superVote20:1,superVote40:0}]),w.error("Erro ao carregar ranking, usando dados de exemplo")}finally{l(!1)}},[s]),f=p.useCallback(()=>{try{localStorage.setItem(`clientProfile_${a}`,JSON.stringify(c)),i(!1),w.success("Perfil salvo com sucesso!")}catch(b){console.error("Error saving profile:",b),w.error("Erro ao salvar perfil")}},[c,a]),y=p.useCallback(b=>{var C;const v=(C=b.target.files)==null?void 0:C[0];if(v&&v.type.startsWith("image/")){const F=new FileReader;F.onload=()=>{u(M=>({...M,avatar:F.result}))},F.readAsDataURL(v)}else w.error("Por favor, selecione uma imagem válida")},[]),k=p.useCallback(()=>Math.min(c.experience/c.nextLevelExp*100,100),[c.experience,c.nextLevelExp]);return p.useCallback(b=>{const v={Iniciante:"text-gray-600 bg-gray-100 dark:bg-gray-700",Ativo:"text-blue-600 bg-blue-100 dark:bg-blue-900/30",Engajado:"text-green-600 bg-green-100 dark:bg-green-900/30",Expert:"text-purple-600 bg-purple-100 dark:bg-purple-900/30"};return v[b]||v.Iniciante},[]),p.useEffect(()=>{r&&a&&(x(),h())},[r,a,x,h]),r?e.jsx(Re,{children:e.jsx(V.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:t,role:"dialog","aria-modal":"true","aria-labelledby":"profile-modal-title",children:e.jsxs(V.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:b=>b.stopPropagation(),className:"bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[e.jsx("header",{className:"bg-gradient-to-r from-purple-600 to-blue-600 p-4 sm:p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center",children:c.avatar?e.jsx("img",{src:c.avatar,alt:`Avatar de ${c.name}`,className:"w-full h-full rounded-full object-cover"}):e.jsx(Je,{className:"w-8 h-8","aria-hidden":"true"})}),e.jsxs("div",{children:[e.jsx("h2",{id:"profile-modal-title",className:"text-xl sm:text-2xl font-bold",children:c.name}),e.jsx("p",{className:"text-blue-100 text-sm",children:c.title}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs("span",{className:"text-sm",children:["Nível ",c.level]}),e.jsx("div",{className:"w-24 h-2 bg-white/20 rounded-full",children:e.jsx("div",{className:"h-full bg-white rounded-full transition-all duration-300",style:{width:`${k()}%`},role:"progressbar","aria-valuenow":Math.round(k()),"aria-valuemin":0,"aria-valuemax":100})})]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("button",{onClick:t,className:"p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors","aria-label":"Fechar perfil",children:e.jsx(Qe,{className:"w-5 h-5","aria-hidden":"true"})})})]})}),e.jsx("div",{className:"p-4 sm:p-6 max-h-[60vh] overflow-y-auto",children:o?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Carregando perfil..."})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome"}),n?e.jsx("input",{type:"text",value:c.name,onChange:b=>u({...c,name:b.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Editar nome do perfil"}):e.jsx("p",{className:"text-gray-900 dark:text-white",children:c.name})]}),n&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Avatar"}),e.jsx("input",{type:"file",accept:"image/*",onChange:y,className:"w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200","aria-label":"Fazer upload de avatar"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Membro desde"}),e.jsx("p",{className:"text-gray-900 dark:text-white",children:new Date(c.joinedAt).toLocaleDateString("pt-BR",{day:"2-digit",month:"long",year:"numeric"})})]})]}),e.jsx("div",{className:"flex justify-end items-center",children:n?e.jsxs("button",{onClick:f,className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors","aria-label":"Salvar alterações do perfil",children:[e.jsx(Wr,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Salvar"})]}):e.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Editar perfil",children:[e.jsx(Je,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Editar"})]})})]})})})]})})}):null},ed=({restaurantId:r,currentTableNumber:t,isVisible:s,onClose:a})=>{const[n,i]=p.useState([]),[o,l]=p.useState(!1),[c,u]=p.useState("points");p.useEffect(()=>{if(s){d();const f=setInterval(d,3e4);return()=>clearInterval(f)}},[s,r]);const d=async()=>{l(!0);try{const f=await fetch(`http://localhost:8001/api/v1/analytics/tables/${r}/leaderboard`);if(f.ok){const k=(await f.json()).tables.map((b,v)=>({tableNumber:b.tableNumber||`Mesa ${v+1}`,totalSuggestions:b.totalSuggestions||0,totalVotes:b.totalVotes||0,averageScore:b.averageScore||0,paidSuggestions:b.paidSuggestions||0,points:b.points||0,rank:v+1,isCurrentTable:b.tableNumber===t}));i(k)}}catch(f){console.error("Erro ao carregar estatísticas das mesas:",f),i([{tableNumber:"Mesa 5",totalSuggestions:8,totalVotes:45,averageScore:4.2,paidSuggestions:3,points:850,rank:1,isCurrentTable:t==="5"},{tableNumber:"Mesa 10",totalSuggestions:6,totalVotes:38,averageScore:3.8,paidSuggestions:2,points:720,rank:2,isCurrentTable:t==="10"},{tableNumber:"Mesa 3",totalSuggestions:5,totalVotes:32,averageScore:3.5,paidSuggestions:1,points:650,rank:3,isCurrentTable:t==="3"},{tableNumber:"Mesa 7",totalSuggestions:4,totalVotes:28,averageScore:3.2,paidSuggestions:1,points:580,rank:4,isCurrentTable:t==="7"},{tableNumber:"Mesa 12",totalSuggestions:3,totalVotes:22,averageScore:2.9,paidSuggestions:0,points:450,rank:5,isCurrentTable:t==="12"}])}finally{l(!1)}},m=[...n].sort((f,y)=>{switch(c){case"suggestions":return y.totalSuggestions-f.totalSuggestions;case"votes":return y.totalVotes-f.totalVotes;default:return y.points-f.points}}),x=f=>{switch(f){case 1:return e.jsx(Nn,{className:"w-5 h-5 text-yellow-500"});case 2:return e.jsx(wn,{className:"w-5 h-5 text-gray-400"});case 3:return e.jsx(jn,{className:"w-5 h-5 text-orange-600"});default:return e.jsx(bs,{className:"w-5 h-5 text-gray-500"})}},h=f=>{switch(f){case 1:return"from-yellow-500 to-yellow-600";case 2:return"from-gray-400 to-gray-500";case 3:return"from-orange-500 to-orange-600";default:return"from-gray-600 to-gray-700"}};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs(V.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md max-h-[80vh] overflow-hidden",children:[e.jsx("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(bs,{className:"w-6 h-6"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Ranking das Mesas"}),e.jsx("p",{className:"text-purple-100 text-sm",children:"Competição em tempo real"})]})]}),e.jsx("button",{onClick:a,className:"text-white/80 hover:text-white transition-colors",children:e.jsx(Qe,{className:"w-6 h-6"})})]})}),e.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"flex space-x-2",children:[{key:"points",label:"Pontos"},{key:"suggestions",label:"Sugestões"},{key:"votes",label:"Votos"}].map(f=>e.jsx("button",{onClick:()=>u(f.key),className:`px-3 py-1 rounded-full text-sm font-medium transition-colors ${c===f.key?"bg-purple-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-gray-600"}`,children:f.label},f.key))})}),e.jsx("div",{className:"p-4 max-h-96 overflow-y-auto",children:o?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"ml-3 text-gray-600 dark:text-gray-400",children:"Carregando ranking..."})]}):e.jsx("div",{className:"space-y-3",children:e.jsx(Re,{children:m.map((f,y)=>e.jsxs(V.div,{layout:!0,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,delay:y*.05},className:`p-4 rounded-lg border-2 transition-all ${f.isCurrentTable?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`p-2 rounded-full bg-gradient-to-r ${h(y+1)}`,children:x(y+1)}),e.jsxs("div",{children:[e.jsxs("h3",{className:`font-bold ${f.isCurrentTable?"text-purple-700 dark:text-purple-300":"text-gray-900 dark:text-white"}`,children:[f.tableNumber,f.isCurrentTable&&e.jsx("span",{className:"ml-2 text-xs bg-purple-600 text-white px-2 py-1 rounded-full",children:"Você"})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[e.jsxs("span",{children:[f.points," pts"]}),e.jsxs("span",{children:[f.totalSuggestions," sugestões"]}),e.jsxs("span",{children:[f.totalVotes," votos"]})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-yellow-500",children:[e.jsx(qe,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:f.averageScore.toFixed(1)})]}),f.paidSuggestions>0&&e.jsxs("div",{className:"text-xs text-green-600 dark:text-green-400",children:[f.paidSuggestions," pagas"]})]})]}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full bg-gradient-to-r ${h(y+1)} transition-all duration-500`,style:{width:`${Math.min(f.points/Math.max(...m.map(k=>k.points))*100,100)}%`}})})})]},f.tableNumber))})})}),e.jsx("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 text-center",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"🎵 Continue sugerindo e votando para subir no ranking!"})})]})}):null},td=()=>p.useCallback(async(r,t={},s=3,a=1e3)=>{let n;for(let i=0;i<s;i++)try{const o=await fetch(r,t);if(o.ok)return o;let l=`Erro na requisição: ${o.status} ${o.statusText}`;try{const c=await o.json();c.error?l=c.error:c.message&&(l=c.message)}catch{}if(n=new Error(l),o.status===401||o.status===403||o.status===409)throw n}catch(o){if(n=o instanceof Error?o:new Error(String(o)),i===s-1)break;console.warn(`Tentativa ${i+1} falhou, tentando novamente em ${a}ms...`),await new Promise(l=>setTimeout(l,a))}throw n},[]),sd=()=>({getBadges:p.useCallback(t=>{const s=[];return t.suggestionsCount>=1&&s.push("🎵 Primeira Sugestão"),t.suggestionsCount>=5&&s.push("🎶 Melomaníaco"),t.suggestionsCount>=10&&s.push("🎸 DJ Amador"),t.votesCount>=10&&s.push("👍 Crítico Musical"),t.votesCount>=25&&s.push("⭐ Especialista"),t.streak>=3&&s.push("🔥 Em Chamas"),t.points>=500&&s.push("🏆 Lenda"),s},[])}),Vr=()=>{const{restaurantId:r}=Ze(),[t]=Ur(),s=t.get("table"),a=td(),{getBadges:n}=sd(),[i,o]=p.useState(null),[l,c]=p.useState(""),[u,d]=p.useState([]),[m,x]=p.useState(!1),[h,f]=p.useState([]),[y,k]=p.useState(1),[b,v]=p.useState(!1),[C,F]=p.useState(!0),[M,L]=p.useState(0),[j,D]=p.useState(null),[N,T]=p.useState(!0),[E,P]=p.useState(!1),[g,R]=p.useState(null),[A,_]=p.useState({points:0,level:1,badges:[],suggestionsCount:0,votesCount:0,streak:0}),[S,O]=p.useState(""),[G,J]=p.useState(!0),[he,Ne]=p.useState(!1),[_e,ke]=p.useState(null),[Le,Pe]=p.useState(!1),[$e,We]=p.useState(!1),[$,B]=p.useState(null),[ce,me]=p.useState(!1),[as,wt]=p.useState(null),[Nt,ir]=p.useState(!1),[Fa,or]=p.useState(!1),{isConnected:lr,joinRestaurant:Ma,on:ns,off:is}=jt(),cr=p.useCallback(async()=>{var I;if(!r){w.error("ID do restaurante não encontrado");return}try{const U=await(await a(Z(`/restaurants/${r}`),{headers:{"Content-Type":"application/json"}})).json();o(U.restaurant||{id:r,name:"Restaurante Demo",description:"Ambiente acolhedor com música interativa",isActive:!0,isOpen:!0}),console.log(`🏪 Entrou na sala do restaurante: ${((I=U.restaurant)==null?void 0:I.name)||"Restaurante Demo"}`)}catch(W){console.error("Erro ao carregar restaurante:",W),o({id:r,name:"Restaurante Demo",description:"Ambiente acolhedor com música interativa",isActive:!0,isOpen:!0}),w.error("Erro ao carregar dados do restaurante",{duration:4e3,icon:"⚠️"})}},[r,a]),kt=p.useCallback(async()=>{var I;if(!r){w.error("ID do restaurante não encontrado");return}try{const U=await(await a(Z(`/playback/${r}/state`),{headers:{"Content-Type":"application/json"}})).json();if(U.success===!1)throw new Error(U.message||"Erro ao carregar música atual");D(((I=U.state)==null?void 0:I.currentTrack)||null)}catch(W){console.error("Erro ao carregar música atual:",W),D(null)}},[r,a]),He=p.useCallback(async(I=1,W=!1)=>{if(!r){w.error("ID do restaurante não encontrado");return}v(!0);try{const ee=new URLSearchParams({page:I.toString(),limit:24 .toString()});if(h.length){const ie=h.filter(os=>["rock","pop","sertanejo","mpb","eletronica","funk"].includes(os));ie.length&&ee.append("genres",ie.join(","));const Y=h.filter(os=>["happy","sad","energetic","calm"].includes(os));Y.length&&ee.append("moods",Y.join(","))}const le=await(await a(Z(`/restaurants/${r}/playlist?${ee.toString()}`),{headers:{"Content-Type":"application/json"}})).json();if(le.success&&le.results){const ie=le.results.map(Y=>({id:Y.youtubeVideoId||Y.id,title:Y.title,artist:Y.artist,duration:Y.duration||0,formattedDuration:Y.formattedDuration||"0:00",thumbnailUrl:Y.thumbnailUrl||`https://img.youtube.com/vi/${Y.youtubeVideoId||Y.id}/mqdefault.jpg`,youtubeVideoId:Y.youtubeVideoId||Y.id,channelName:Y.artist,viewCount:Y.viewCount||0,publishedAt:Y.addedAt||new Date().toISOString(),genre:Array.isArray(Y.genres)&&Y.genres.length?Y.genres[0]:Y.genre}));d(Y=>W?[...Y,...ie]:ie),L(le.total||ie.length),F(ie.length===24),k(I)}else W||(d([]),w("Nenhuma música encontrada na playlist",{icon:"ℹ️"}))}catch(U){console.error("Erro ao carregar playlist:",U),w.error("Erro ao carregar playlist do restaurante"),W||d([])}finally{v(!1)}},[r,h,a]),dr=p.useCallback(async()=>{if(!l.trim()){w.error("Digite uma busca válida");return}if(!r){w.error("ID do restaurante não encontrado");return}x(!0);try{const I=new URLSearchParams({q:encodeURIComponent(l.trim())});if(h.length){const ee=h.filter(le=>["rock","pop","sertanejo","mpb","eletronica","funk"].includes(le));ee.length&&I.append("genres",ee.join(","));const K=h.filter(le=>["happy","sad","energetic","calm"].includes(le));K.length&&I.append("moods",K.join(","))}const U=await(await a(Z(`/search/music?${I.toString()}`),{headers:{"Content-Type":"application/json"}})).json();if(U.success&&U.results){const ee=U.results.map(K=>({id:K.youtubeVideoId||K.id,title:K.title,artist:K.artist,duration:K.duration||0,formattedDuration:K.formattedDuration||"0:00",thumbnailUrl:K.thumbnailUrl||`https://img.youtube.com/vi/${K.youtubeVideoId||K.id}/mqdefault.jpg`,youtubeVideoId:K.youtubeVideoId||K.id,channelName:K.artist,viewCount:K.viewCount||0,publishedAt:K.addedAt||new Date().toISOString(),genre:Array.isArray(K.genres)&&K.genres.length?K.genres[0]:K.genre}));d(ee),L(U.total||ee.length),F(!1),k(1),ee.length>0?w.success(`${ee.length} música(s) encontrada(s)`,{icon:"🔍"}):w(`Nenhuma música encontrada para "${l}"`,{icon:"🔍"})}else d([]),w(`Nenhuma música encontrada para "${l}"`,{icon:"🔍"})}catch(I){console.error("Erro ao buscar músicas:",I),w.error("Erro ao buscar músicas"),d([])}finally{x(!1)}},[l,r,h,a]),Ua=p.useCallback(async()=>{if(!r){w.error("ID do restaurante não encontrado");return}J(!0);try{const I=await gs.forceNewSession(r,s||void 0,S||void 0);R(I),_({points:I.points||0,level:I.level||1,badges:I.badges||[],suggestionsCount:I.suggestionsCount||0,votesCount:I.votesCount||0,streak:I.streak||0}),!I.clientName&&!S&&Pe(!0)}catch(I){console.error("Erro ao inicializar sessão:",I),w.error("Erro ao inicializar sessão",{duration:4e3,icon:"⚠️"})}finally{J(!1)}},[r,s,S]),St=p.useCallback((I,W=10)=>{_(U=>{const ee={...U,points:U.points+W,suggestionsCount:I==="suggest"?U.suggestionsCount+1:U.suggestionsCount,votesCount:I==="vote"?U.votesCount+1:U.votesCount,streak:U.streak+1},K=Math.floor(ee.points/100)+1,le=n(ee);K>U.level&&(Ne(!0),setTimeout(()=>Ne(!1),3e3),w.success(`🎉 Level Up! Agora você é nível ${K}!`,{duration:5e3,icon:"🏆"}));const ie=le.find(Y=>!U.badges.includes(Y));return ie&&(gs.awardBadge(ie),ke(ie),setTimeout(()=>ke(null),3e3),w.success(`🏆 Nova conquista: ${ie}!`,{duration:5e3,icon:"🎖️"})),{...ee,level:K,badges:le}})},[n]),qa=p.useCallback(I=>{B(I),We(!0)},[]),Ba=p.useCallback(async I=>{if(!g||!r){w.error("Sessão ou restaurante não encontrado");return}try{const U=await(await a(Z(`/collaborative-playlist/${r}/vote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:I.youtubeVideoId,tableNumber:g.tableNumber,clientSessionId:g.id})})).json();if((U==null?void 0:U.success)===!1)throw new Error((U==null?void 0:U.message)||"Falha ao registrar voto");w.success(`Voto registrado para "${I.title}" ✅`),St("vote",10)}catch(W){console.error("Erro ao registrar voto:",W),w.error(W instanceof Error?W.message:"Erro ao registrar voto")}},[g,r,a,St]),Qa=p.useCallback(async({paymentId:I,amountCents:W,clientMessage:U,clientName:ee})=>{if(!$||!g||!r){w.error("Dados insuficientes para processar pagamento");return}try{const le=await(await a(Z(`/collaborative-playlist/${r}/supervote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:$.youtubeVideoId,paymentAmount:(W||0)/100,paymentId:I,tableNumber:g.tableNumber,clientSessionId:g.id,clientMessage:U||void 0,clientName:ee||S||void 0})})).json();if(le.success===!1)throw new Error(le.message||"Erro ao processar pagamento");w.success(`SuperVoto aplicado em "${$.title}"! ⭐`),St("vote",25),c(""),l.trim()&&He(1),wt({id:$.id,title:$.title,artist:$.artist,thumbnailUrl:$.thumbnailUrl,duration:$.duration,youtubeVideoId:$.youtubeVideoId,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString(),isPaid:!0,clientSessionId:g.id}),We(!1),setTimeout(()=>{w(ie=>e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{children:'🎤 Quer cantar junto? Ative o "Cante Comigo"!'}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{me(!0),w.dismiss(ie.id)},className:"px-3 py-1 bg-blue-600 text-white rounded text-sm",children:"Sim, vamos cantar!"}),e.jsx("button",{onClick:()=>w.dismiss(ie.id),className:"px-3 py-1 bg-gray-600 text-white rounded text-sm",children:"Não, obrigado"})]})]}),{duration:8e3})},2e3)}catch(K){console.error("Erro após pagamento:",K),w.error("Erro ao processar pagamento",{duration:4e3,icon:"❌"})}finally{B(null)}},[$,g,r,He,l,a,St]),ur=p.useCallback(async()=>{if(!S.trim()){w.error("Por favor, digite seu nome");return}if(!r){w.error("ID do restaurante não encontrado");return}try{const I=await gs.createSession(r,s||void 0,S);R(I),Pe(!1);const W=`clientProfile_${I.id}`,U=localStorage.getItem(W);if(U){const ee=JSON.parse(U);ee.name=S,localStorage.setItem(W,JSON.stringify(ee))}else localStorage.setItem(W,JSON.stringify({name:S,avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}}));w.success(`Bem-vindo, ${S}! 🎵`,{duration:4e3,icon:"👋"})}catch(I){console.error("Erro ao salvar nome:",I),w.error("Erro ao salvar seu nome")}},[S,r,s]),za=p.useCallback(()=>{const I=()=>{kt(),w.success("Fila atualizada pela votação!",{icon:"🔄"})},W=U=>{D(U.suggestion),w(`🎵 Tocando agora: ${U.suggestion.title}`,{duration:5e3,icon:"🎧"})};return ns("now-playing",W),ns("playlistReordered",I),()=>{is("now-playing",W),is("playlistReordered",I)}},[ns,is,kt]);return p.useEffect(()=>{if(!r){w.error("ID do restaurante não encontrado");return}(async()=>{J(!0);try{await Promise.all([Ua(),cr(),kt(),He()]),Ma(r)}catch(U){console.error("Erro na inicialização:",U),w.error("Erro ao carregar dados iniciais")}finally{J(!1)}})();const W=za();return()=>{W()}},[r]),p.useEffect(()=>{r&&(k(1),He(1))},[h,r]),G&&!i?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-white",children:[e.jsx(ue,{className:"w-12 h-12 animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("h2",{className:"text-xl font-semibold",children:"Carregando..."}),e.jsx("p",{className:"text-sm text-purple-300 mt-2",children:"Conectando ao restaurante"}),e.jsx("button",{onClick:()=>{cr(),kt(),He()},className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Tentar novamente",children:"Tentar Novamente"})]})}):e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white",children:[e.jsx("header",{className:"bg-black/30 backdrop-blur-md border-b border-white/10 p-4 sm:p-6 sticky top-0 z-10",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center",children:e.jsx(te,{className:"w-6 h-6","aria-hidden":"true"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold",children:i==null?void 0:i.name}),s&&e.jsxs("p",{className:"text-sm text-purple-200",children:["Mesa ",s]})]})]}),e.jsx("p",{className:"text-sm text-purple-200 mb-4",children:"Escolha e vote nas músicas que vão animar seu momento!"}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 sm:gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1",title:"Seu nível atual",children:[e.jsx(qe,{className:"w-4 h-4 text-yellow-400","aria-hidden":"true"}),e.jsxs("span",{children:["Nível ",A.level]})]}),e.jsxs("div",{className:"flex items-center gap-1",title:"Seus pontos",children:[e.jsx(ve,{className:"w-4 h-4 text-green-400","aria-hidden":"true"}),e.jsxs("span",{children:[A.points," pts"]})]}),e.jsxs("div",{className:"flex items-center gap-1",title:"Músicas sugeridas",children:[e.jsx(Ys,{className:"w-4 h-4 text-red-400","aria-hidden":"true"}),e.jsx("span",{children:A.suggestionsCount})]}),e.jsxs("div",{className:"flex items-center gap-1",title:"Votos realizados",children:[e.jsx(Tt,{className:"w-4 h-4 text-blue-400","aria-hidden":"true"}),e.jsx("span",{children:A.votesCount})]}),e.jsxs("button",{onClick:()=>ir(!0),className:"flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full hover:from-blue-600 hover:to-indigo-600 transition-transform hover:scale-105","aria-label":"Ver perfil",children:[e.jsx(Je,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Perfil"})]}),e.jsxs("button",{onClick:()=>or(!0),className:"flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:from-purple-600 hover:to-pink-600 transition-transform hover:scale-105","aria-label":"Ver ranking",children:[e.jsx(bs,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Ranking"})]})]}),A.badges.length>0&&e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 mt-3",children:[A.badges.slice(0,3).map((I,W)=>e.jsx("span",{className:"px-2 py-1 bg-white/10 rounded-full text-xs border border-white/20",title:I,children:I},W)),A.badges.length>3&&e.jsxs("span",{className:"px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20",title:`${A.badges.length-3} badges adicionais`,children:["+",A.badges.length-3," mais"]})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-center gap-2",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${lr?"bg-green-400 animate-pulse":"bg-red-400"}`}),e.jsx("span",{className:"text-xs text-purple-200",children:lr?"Conectado ao restaurante":"Reconectando..."})]})]})}),e.jsxs("main",{className:"max-w-4xl mx-auto px-4 py-6 space-y-6",children:[j&&e.jsxs(V.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"now-playing",children:[e.jsxs("h2",{id:"now-playing",className:"flex items-center gap-2 text-xl font-bold mb-4",children:[e.jsx(be,{className:"w-5 h-5 text-green-400","aria-hidden":"true"}),"Tocando Agora"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:j.thumbnailUrl||`https://img.youtube.com/vi/${j.youtubeVideoId}/mqdefault.jpg`,alt:`Capa de ${j.title}`,className:"w-16 h-16 rounded-lg object-cover",loading:"lazy"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold truncate",children:j.title}),e.jsx("p",{className:"text-purple-200 truncate",children:j.artist})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-xl font-bold text-green-400",children:[j.score>0?"+":"",j.score]}),e.jsx("div",{className:"text-xs text-purple-200",children:"votos"})]})]})]}),e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"search",children:[e.jsxs("h2",{id:"search",className:"flex items-center gap-2 text-xl font-bold mb-4",children:[e.jsx(qt,{className:"w-5 h-5","aria-hidden":"true"}),"Buscar Músicas"]}),e.jsxs("div",{className:"flex gap-3 mb-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:l,onChange:I=>c(I.target.value),onKeyPress:I=>I.key==="Enter"&&dr(),placeholder:"Busque por música ou artista...",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500","aria-label":"Buscar músicas por título ou artista"}),l&&e.jsx("button",{onClick:()=>c(""),className:"absolute right-12 top-1/2 -translate-y-1/2 text-white/60 hover:text-white","aria-label":"Limpar busca",children:e.jsx(Qe,{className:"w-4 h-4"})})]}),e.jsxs("button",{onClick:dr,disabled:m||!l.trim(),className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 whitespace-nowrap","aria-label":"Buscar músicas",children:[m?e.jsx(ue,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(qt,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Buscar"})]})]})]}),e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"playlist",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{id:"playlist",className:"flex items-center gap-2 text-xl font-bold",children:[e.jsx(te,{className:"w-5 h-5","aria-hidden":"true"}),l?"Resultados da Busca":"Playlist do Restaurante"]}),M>0&&e.jsxs("div",{className:"text-sm text-purple-200",children:[u.length," de ",M," músicas"]})]}),b&&u.length===0?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(ue,{className:"w-8 h-8 text-purple-400 animate-spin","aria-hidden":"true"}),e.jsx("span",{className:"ml-3 text-purple-200",children:"Carregando músicas..."})]}):u.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(kn,{className:"w-12 h-12 text-purple-400 mx-auto mb-3","aria-hidden":"true"}),e.jsx("p",{className:"text-purple-200",children:"Nenhuma música encontrada."}),e.jsx("p",{className:"text-sm text-purple-300",children:"Use a busca ou filtros para encontrar músicas."})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:u.map(I=>e.jsxs(V.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},whileHover:{scale:1.03},className:"bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all",role:"article","aria-labelledby":`song-${I.id}`,children:[e.jsx("img",{src:I.thumbnailUrl,alt:`Capa de ${I.title}`,className:"w-full h-32 object-cover rounded-lg mb-3",loading:"lazy"}),e.jsx("h3",{id:`song-${I.id}`,className:"font-semibold text-sm truncate",children:I.title}),e.jsx("p",{className:"text-purple-200 text-xs truncate mb-2",children:I.artist}),e.jsxs("div",{className:"flex justify-between text-xs text-purple-300 mb-3",children:[e.jsx("span",{children:I.formattedDuration}),e.jsx("span",{children:I.genre||"N/A"})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("button",{onClick:()=>Ba(I),disabled:G,className:"px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Registrar voto para ${I.title}`,children:[e.jsx(te,{className:"w-3 h-3","aria-hidden":"true"}),e.jsx("span",{children:"Voto"})]}),e.jsxs("button",{onClick:()=>qa(I),disabled:G,className:"px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Adicionar ${I.title} com SuperVoto`,children:[e.jsx(ye,{className:"w-3 h-3","aria-hidden":"true"}),e.jsx("span",{children:"SuperVoto"})]})]})]},I.id))}),C&&e.jsx("div",{className:"text-center",children:e.jsxs("button",{onClick:()=>He(y+1,!0),disabled:b,className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 mx-auto","aria-label":"Carregar mais músicas da playlist",children:[b?e.jsx(ue,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(te,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:b?"Carregando...":"Carregar Mais Músicas"})]})}),!C&&M>24&&e.jsxs("p",{className:"text-center text-sm text-purple-300",children:["🎵 Todas as ",M," músicas foram carregadas!"]})]})]}),N&&e.jsx("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"playback-queue",children:e.jsx(Xc,{restaurantId:r||"",sessionId:(g==null?void 0:g.id)||"",isCollapsed:E,onToggleCollapse:()=>P(!E)})}),e.jsxs("footer",{className:"text-center py-6",children:[e.jsx("p",{className:"text-sm text-purple-300",children:"🎵 Powered by Sistema de Playlist Interativa"}),e.jsx("p",{className:"text-xs text-purple-400 mt-1",children:"Sugestões são moderadas e podem levar alguns minutos para aparecer na fila"})]})]}),e.jsxs(Re,{children:[he&&e.jsx(V.div,{initial:{opacity:0,scale:.5,y:50},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:-50},className:"fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",role:"alert","aria-live":"polite",children:e.jsxs("div",{className:"bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🎉"}),e.jsx("h3",{className:"text-xl font-bold mb-1",children:"LEVEL UP!"}),e.jsxs("p",{className:"text-sm",children:["Você alcançou o nível ",A.level,"!"]})]})}),_e&&e.jsx(V.div,{initial:{opacity:0,scale:.5,y:50},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:-50},className:"fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",role:"alert","aria-live":"polite",children:e.jsxs("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🏆"}),e.jsx("h3",{className:"text-xl font-bold mb-1",children:"NOVA CONQUISTA!"}),e.jsx("p",{className:"text-sm",children:_e})]})}),Le&&e.jsx(V.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",role:"dialog","aria-modal":"true","aria-labelledby":"name-modal-title",children:e.jsxs(V.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"bg-white dark:bg-gray-900 rounded-xl p-6 max-w-md w-full shadow-2xl",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(Je,{className:"w-12 h-12 text-purple-500 mx-auto mb-3","aria-hidden":"true"}),e.jsx("h3",{id:"name-modal-title",className:"text-xl font-bold text-gray-800 dark:text-white mb-2",children:"Bem-vindo! 🎵"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Como podemos te chamar? (Opcional)"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("input",{type:"text",value:S,onChange:I=>O(I.target.value),placeholder:"Digite seu nome...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100",onKeyPress:I=>I.key==="Enter"&&ur(),autoFocus:!0,"aria-label":"Digite seu nome"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:ur,disabled:!S.trim(),className:"flex-1 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50","aria-label":"Confirmar nome",children:"Continuar"}),e.jsx("button",{onClick:()=>Pe(!1),className:"flex-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors","aria-label":"Pular entrada de nome",children:"Pular"})]})]})]})})]}),$e&&$&&e.jsx(mc,{isOpen:$e,onClose:()=>{We(!1),B(null)},suggestion:{id:$.id,title:$.title,artist:$.artist,thumbnailUrl:$.thumbnailUrl,duration:$.duration,youtubeVideoId:$.youtubeVideoId},sessionId:(g==null?void 0:g.sessionToken)||"",restaurantId:r||"",onPaymentSuccess:Qa}),as&&e.jsx(Jc,{isOpen:ce,onClose:()=>{me(!1),wt(null)},suggestion:as,sessionId:(g==null?void 0:g.id)||"",onVoteRequest:()=>w.success("Votação solicitada! 🗳️",{duration:4e3})}),Nt&&g&&e.jsx(Zc,{isOpen:Nt,onClose:()=>ir(!1),restaurantId:r||"",sessionId:g.id}),e.jsx(ed,{restaurantId:r||"",currentTableNumber:s||void 0,isVisible:Fa,onClose:()=>or(!1)})]})},Fr=r=>{const t=Math.floor(r/60),s=r%60;return`${t}:${String(s).padStart(2,"0")}`},Mr=()=>{const{restaurantId:r}=Ze(),{on:t,off:s,joinRestaurant:a}=jt(),[n,i]=p.useState(null),[o,l]=p.useState([]),[c,u]=p.useState([]),[d,m]=p.useState([]),[x,h]=p.useState(null),[f,y]=p.useState(Date.now()),[k,b]=p.useState([]),[v,C]=p.useState(!1),[F,M]=p.useState(null),L=async()=>{if(!r)return;C(!0),M(null);const N=hs();let T=0;const E=3;for(;T<E;)try{const P=await fetch(Z(`/restaurants/${r}/playlist`),{headers:N});if(!P.ok){if((P.status===502||P.status===500)&&T<E-1){T++,await new Promise(A=>setTimeout(A,500*T));continue}throw new Error(`HTTP ${P.status}`)}const g=await P.json(),R=Array.isArray(g==null?void 0:g.results)?g.results:[];R.sort((A,_)=>(A.position||0)-(_.position||0)),b(R),C(!1);return}catch(P){if(T<E-1){T++,await new Promise(g=>setTimeout(g,500*T));continue}M((P==null?void 0:P.message)||"Falha ao carregar playlist"),C(!1);return}};p.useEffect(()=>{if(!r)return;a(r),(async()=>{var T;try{const E=hs(),[P,g]=await Promise.all([fetch(Z(`/playback-queue/${r}`),{headers:E}),fetch(Z(`/collaborative-playlist/${r}/ranking`),{headers:E})]);if(P.ok){const R=await P.json();l(R.queue||[]),i(R.currentlyPlaying||null)}if(g.ok){const R=await g.json();u(Array.isArray(R==null?void 0:R.data)?R.data:[])}try{const R=await fetch(Z("/playlist-reorder/status"),{headers:E});if(R.ok){const A=await R.json(),_=(T=A==null?void 0:A.status)==null?void 0:T.nextExecution;_&&!x&&h(new Date(_).getTime())}}catch{}await L()}catch{}})()},[r,a]),p.useEffect(()=>{const N=S=>{Array.isArray(S==null?void 0:S.queue)&&l(S.queue),S!=null&&S.currentlyPlaying&&i(S.currentlyPlaying)},T=S=>{if(S!=null&&S.suggestion){const O=S.suggestion;i({id:O.id||O.youtubeVideoId,suggestionId:O.youtubeVideoId,youtubeVideoId:O.youtubeVideoId,title:O.title,artist:O.artist,duration:O.duration||0,thumbnailUrl:O.thumbnailUrl,isPaid:!!O.isPaid,paymentAmount:O.paymentAmount,position:0})}},E=S=>{var G,J,he,Ne,_e,ke,Le,Pe;const O={id:`${Date.now()}_${Math.random()}`,at:(S==null?void 0:S.timestamp)||new Date().toISOString(),amount:Number((G=S==null?void 0:S.payment)==null?void 0:G.amount)||0,voteWeight:Number((J=S==null?void 0:S.payment)==null?void 0:J.voteWeight)||0,title:(he=S==null?void 0:S.suggestion)==null?void 0:he.title,artist:(Ne=S==null?void 0:S.suggestion)==null?void 0:Ne.artist,youtubeVideoId:(_e=S==null?void 0:S.suggestion)==null?void 0:_e.youtubeVideoId,clientName:(ke=S==null?void 0:S.payment)==null?void 0:ke.clientName,tableNumber:(Le=S==null?void 0:S.payment)==null?void 0:Le.tableNumber,message:(Pe=S==null?void 0:S.payment)==null?void 0:Pe.message};m($e=>[O,...$e].slice(0,50))},P={id:0},g={t:0},R=async()=>{try{const S=await fetch(Z(`/collaborative-playlist/${r}/ranking`),{headers:hs()});if(S.ok){const O=await S.json();u(Array.isArray(O==null?void 0:O.data)?O.data:[])}}catch{}},A=S=>{const O=Date.now();if(!(O-g.t<1e3)){g.t=O,P.id&&clearTimeout(P.id),P.id=setTimeout(()=>{R(),P.id=0},300);try{const G=S!=null&&S.timestamp?new Date(S.timestamp).getTime():Date.now();h(G+5*60*1e3)}catch{}}},_=S=>{var O;try{const G=(O=S==null?void 0:S.adminDetails)==null?void 0:O.nextReorderTime;if(G)h(new Date(G).getTime());else{const J=S!=null&&S.timestamp?new Date(S.timestamp).getTime():Date.now();h(J+5*60*1e3)}}catch{h(Date.now()+5*60*1e3)}L()};return t("queue-update",N),t("now-playing",T),t("superVoteReceived",E),t("vote-update",A),t("playlistReordered",_),()=>{P.id&&clearTimeout(P.id),s("queue-update",N),s("now-playing",T),s("superVoteReceived",E),s("vote-update",A),s("playlistReordered",_)}},[r]),p.useEffect(()=>{const N=setInterval(()=>y(Date.now()),1e3);return()=>clearInterval(N)},[]);const j=p.useMemo(()=>{if(!x)return null;const N=x-f;if(N<=0)return"00:00";const T=Math.floor(N/6e4),E=Math.floor(N%6e4/1e3);return`${String(T).padStart(2,"0")}:${String(E).padStart(2,"0")}`},[x,f]),D=p.useMemo(()=>c.slice(0,5),[c]);return e.jsx("div",{className:"min-h-screen px-4 py-6 md:px-8 md:py-10 bg-gradient-to-br from-gray-900 via-gray-950 to-black text-white",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsxs("header",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold",children:"Painel do Músico (Couvert)"}),e.jsx("p",{className:"text-gray-300 mt-1 text-sm",children:"Acompanhe a fila, ranking por votos e recados dos fãs em tempo real."}),j&&e.jsxs("div",{className:"mt-3 inline-flex items-center gap-2 text-sm text-indigo-300 bg-indigo-500/10 border border-indigo-500/20 rounded px-3 py-1.5",children:[e.jsx(Sn,{className:"w-4 h-4"}),"Próxima reordenação em",e.jsx("span",{className:"font-mono font-semibold text-indigo-200",children:j})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6",children:[e.jsxs("section",{className:"lg:col-span-2 bg-white/5 border border-white/10 rounded-xl p-4",children:[e.jsx("h2",{className:"text-lg font-semibold mb-3",children:"Sequência (Fila)"}),e.jsx("div",{className:"max-h-[460px] overflow-y-auto divide-y divide-white/10",children:o.length===0?e.jsx("div",{className:"p-6 text-gray-400",children:"Fila vazia."}):o.map(N=>e.jsxs("div",{className:"p-3 flex items-center gap-3 hover:bg-white/5",children:[e.jsxs("div",{className:"w-8 text-center text-purple-300",children:["#",N.position]}),e.jsx("img",{src:N.thumbnailUrl||(N.youtubeVideoId?`https://img.youtube.com/vi/${N.youtubeVideoId}/mqdefault.jpg`:""),alt:N.title,className:"w-12 h-10 rounded object-cover"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-white font-medium truncate",children:N.title}),e.jsx("div",{className:"text-gray-400 text-sm truncate",children:N.artist}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-2 mt-1",children:[e.jsx(Be,{className:"w-3 h-3"}),Fr(N.duration),N.isPaid&&N.paymentAmount&&e.jsxs("span",{className:"inline-flex items-center gap-1 text-yellow-300",children:[e.jsx(ye,{className:"w-3 h-3"}),"R$ ",Number(N.paymentAmount).toFixed(2)]})]})]})]},N.id))})]}),e.jsxs("section",{className:"bg-white/5 border border-white/10 rounded-xl p-4",children:[e.jsxs("h2",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[e.jsx(qe,{className:"w-5 h-5 text-indigo-400"}),"Mais votadas"]}),D.length===0?e.jsx("div",{className:"text-gray-400 text-sm",children:"Sem votos ainda."}):e.jsx("ol",{className:"space-y-2",children:D.map((N,T)=>e.jsxs("li",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[e.jsxs("span",{className:"text-indigo-300 w-5",children:["#",T+1]}),e.jsx("img",{src:N.thumbnailUrl||(N.youtubeVideoId?`https://img.youtube.com/vi/${N.youtubeVideoId}/default.jpg`:""),className:"w-8 h-6 rounded object-cover"}),e.jsxs("div",{className:"truncate",children:[e.jsx("div",{className:"truncate max-w-[180px]",children:N.title||N.youtubeVideoId}),N.artist&&e.jsx("div",{className:"text-xs text-gray-400 truncate",children:N.artist})]})]}),e.jsxs("div",{className:"text-indigo-300",children:[N.voteCount," votos"]})]},N.youtubeVideoId))})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("section",{className:"bg-white/5 border border-white/10 rounded-xl p-4 lg:col-span-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Playlist completa"}),e.jsx("div",{className:"text-xs text-gray-400",children:v?"carregando...":e.jsx("button",{onClick:L,className:"underline hover:text-gray-200",children:"atualizar"})})]}),F&&e.jsx("div",{className:"text-sm text-red-300 mb-2",children:F}),e.jsx("div",{className:"max-h-[460px] overflow-y-auto divide-y divide-white/10",children:k.length===0&&!v?e.jsx("div",{className:"p-6 text-gray-400",children:"Nenhuma música cadastrada na playlist."}):k.map((N,T)=>e.jsxs("div",{className:"p-3 flex items-center gap-3 hover:bg-white/5",children:[e.jsxs("div",{className:"w-8 text-center text-indigo-300",children:["#",N.position&&N.position>0?N.position:T+1]}),e.jsx("img",{src:N.thumbnailUrl||(N.youtubeVideoId?`https://img.youtube.com/vi/${N.youtubeVideoId}/mqdefault.jpg`:""),alt:N.title,className:"w-12 h-10 rounded object-cover"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-white font-medium truncate",children:N.title}),e.jsx("div",{className:"text-gray-400 text-sm truncate",children:N.artist}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-2 mt-1",children:[e.jsx(Be,{className:"w-3 h-3"}),N.formattedDuration||Fr(Math.max(0,Math.floor(N.duration||0))),N.isAvailable===!1&&e.jsx("span",{className:"text-amber-300",children:"indisponível"})]})]})]},N.id||N.youtubeVideoId||T))})]}),e.jsxs("section",{className:"bg-white/5 border border-white/10 rounded-xl",children:[e.jsxs("div",{className:"p-4 border-b border-white/10 flex items-center justify-between",children:[e.jsxs("h2",{className:"text-lg font-semibold flex items-center gap-2",children:[e.jsx(En,{className:"w-5 h-5 text-emerald-400"}),"Recados do público"]}),e.jsx("span",{className:"text-xs text-gray-400",children:"tempo real"})]}),e.jsx("div",{className:"max-h-[460px] overflow-y-auto",children:d.length===0?e.jsx("div",{className:"p-6 text-gray-400",children:"Sem recados ainda. Mensagens aparecem aqui quando um SuperVoto é pago."}):e.jsx("ul",{className:"divide-y divide-white/10",children:d.map(N=>e.jsx("li",{className:"p-3",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-emerald-600/20 flex items-center justify-center text-emerald-300 font-semibold",children:"SV"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-white font-medium truncate",children:N.title||N.youtubeVideoId||"Música"}),e.jsx("div",{className:"text-xs text-gray-400",children:new Date(N.at).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]}),N.artist&&e.jsx("div",{className:"text-xs text-gray-400 truncate",children:N.artist}),e.jsx("div",{className:"text-sm text-gray-200 mt-1 whitespace-pre-wrap break-words",children:N.message||""}),e.jsxs("div",{className:"text-xs text-gray-400 mt-1 flex items-center gap-3",children:[e.jsxs("span",{className:"inline-flex items-center gap-1 text-yellow-300",children:[e.jsx(ye,{className:"w-3 h-3"}),"R$ ",N.amount.toFixed(2)]}),e.jsxs("span",{className:"inline-flex items-center gap-1 text-indigo-300",children:[e.jsx(qe,{className:"w-3 h-3"}),"+",N.voteWeight]}),N.clientName&&e.jsxs("span",{children:["por ",N.clientName]}),N.tableNumber&&e.jsxs("span",{children:["mesa ",N.tableNumber]})]})]})]})},N.id))})})]})]})]})})},rd=()=>{var l;const{notifications:r,removeNotification:t,addNotification:s}=Vo(),{settings:a}=ga();p.useEffect(()=>{let c;return xe(()=>Promise.resolve().then(()=>Hl),void 0).then(({wsService:u})=>{const d=m=>{const x=m.notification||m;s({type:x.type||"info",title:x.title||"Notificação",message:x.message||String((x==null?void 0:x.message)??""),duration:x.duration||5e3})};u.on("notification",d),c=()=>u.off("notification",d)}).catch(()=>{}),()=>{c&&c()}},[s]);const n=c=>{switch(c){case"success":return{icon:Gr,bgColor:"bg-green-50 dark:bg-green-900/20",borderColor:"border-green-200 dark:border-green-800",iconColor:"text-green-600 dark:text-green-400",titleColor:"text-green-900 dark:text-green-100",messageColor:"text-green-700 dark:text-green-300"};case"error":return{icon:Ye,bgColor:"bg-red-50 dark:bg-red-900/20",borderColor:"border-red-200 dark:border-red-800",iconColor:"text-red-600 dark:text-red-400",titleColor:"text-red-900 dark:text-red-100",messageColor:"text-red-700 dark:text-red-300"};case"warning":return{icon:Rn,bgColor:"bg-yellow-50 dark:bg-yellow-900/20",borderColor:"border-yellow-200 dark:border-yellow-800",iconColor:"text-yellow-600 dark:text-yellow-400",titleColor:"text-yellow-900 dark:text-yellow-100",messageColor:"text-yellow-700 dark:text-yellow-300"};case"info":default:return{icon:Cn,bgColor:"bg-blue-50 dark:bg-blue-900/20",borderColor:"border-blue-200 dark:border-blue-800",iconColor:"text-blue-600 dark:text-blue-400",titleColor:"text-blue-900 dark:text-blue-100",messageColor:"text-blue-700 dark:text-blue-300"}}},i=(()=>{switch(a.notificationPosition){case"top-left":return"top-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":default:return"top-4 right-4"}})(),o=(l=a.notificationPosition)==null?void 0:l.includes("left");return e.jsx("div",{className:`fixed ${i} z-50 space-y-2 max-w-sm w-full`,children:e.jsx(Re,{children:r.map(c=>{const u=n(c.type),d=u.icon;return e.jsxs(V.div,{initial:{opacity:0,x:o?-300:300,scale:.9},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:o?-300:300,scale:.9},transition:{type:"spring",stiffness:500,damping:30},className:`
                ${u.bgColor} ${u.borderColor}
                border rounded-lg shadow-lg p-4 backdrop-blur-sm
              `,children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(d,{className:`w-5 h-5 ${u.iconColor}`})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:`text-sm font-medium ${u.titleColor}`,children:c.title}),e.jsx("p",{className:`text-sm mt-1 ${u.messageColor}`,children:c.message}),c.action&&e.jsx("button",{onClick:c.action.onClick,className:`
                        text-sm font-medium mt-2 hover:underline
                        ${u.iconColor}
                      `,children:c.action.label})]}),e.jsx("button",{onClick:()=>t(c.id),className:`
                    flex-shrink-0 p-1 rounded-md hover:bg-black/5 dark:hover:bg-white/5
                    ${u.iconColor} transition-colors
                  `,children:e.jsx(Qe,{className:"w-4 h-4"})})]}),c.duration&&c.duration>0&&e.jsx(V.div,{initial:{width:"100%"},animate:{width:"0%"},transition:{duration:c.duration/1e3,ease:"linear"},className:`
                    h-1 mt-3 rounded-full
                    ${u.iconColor.replace("text-","bg-")}
                    opacity-30
                  `})]},c.id)})})})},ad=({children:r})=>{const{isAuthenticated:t,user:s,authToken:a}=vt();return Ze(),console.log("🔐 ProtectedRoute - Estado:",{isAuthenticated:t,user:!!s,authToken:!!a}),a&&!s?(console.log("🔐 Carregando autenticação..."),e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(at,{size:"lg"}),e.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Verificando autenticação..."})]})})):!t||!s||!a?(console.log("🔐 Não autenticado, redirecionando para login"),e.jsx(Me,{to:"/admin/login",replace:!0})):["admin","moderator","staff"].includes(s.role)?(console.log("🔐 Acesso autorizado ao dashboard"),e.jsx(e.Fragment,{children:r})):(console.log("🔐 Sem permissão, redirecionando para home"),e.jsx(Me,{to:"/",replace:!0}))},nd=()=>{const{authToken:r,setUser:t,setAuthToken:s,isAuthenticated:a}=vt();p.useEffect(()=>{(()=>{const i=localStorage.getItem("authToken");if(!i){r&&(s(null),t(null));return}r!==i&&(oe.setAuthToken(i),s(i),t({id:"admin-user",name:"Admin",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}),console.log("🔐 Token de autenticação restaurado"))})()},[])};const id=new ti({defaultOptions:{queries:{retry:3,retryDelay:r=>Math.min(1e3*2**r,3e4),staleTime:5*60*1e3,cacheTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}}),od=({children:r})=>{const{isAuthenticated:t,user:s}=rt();return!t||!s?e.jsx(Me,{to:"/admin/login",replace:!0}):["admin","moderator","super_admin"].includes(s.role)?e.jsx(e.Fragment,{children:r}):e.jsx(Me,{to:"/",replace:!0})},ld=()=>{const{isOnline:r,connectionStatus:t,setOnlineStatus:s,setConnectionStatus:a}=rt(),{settings:n}=ga();return nd(),p.useEffect(()=>{Fo();const i=re.onConnectionStatusChange(c=>{a(c)}),o=()=>s(!0),l=()=>s(!1);return window.addEventListener("online",o),window.addEventListener("offline",l),()=>{i(),window.removeEventListener("online",o),window.removeEventListener("offline",l)}},[s,a]),e.jsxs(oi,{client:id,children:[e.jsx(Ja,{children:e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200",children:[e.jsxs(Ws,{children:[e.jsx(z,{path:"/",element:e.jsx(Uo,{})}),e.jsx(z,{path:"/restaurant/:restaurantId/dashboard/*",element:e.jsx(ad,{children:e.jsx(lc,{})})}),e.jsx(z,{path:"/restaurant/dashboard/*",element:e.jsx(Me,{to:"/restaurant/demo-restaurant/dashboard",replace:!0})}),e.jsx(z,{path:"/test",element:e.jsx("div",{style:{padding:"20px",fontSize:"24px"},children:"Teste de Rota Funcionando!"})}),e.jsx(z,{path:"/restaurant/public/:restaurantId",element:e.jsx(Vr,{})}),e.jsx(z,{path:"/couvert/:restaurantId",element:e.jsx(Mr,{})}),e.jsx(z,{path:"/cover/:restaurantId",element:e.jsx(Mr,{})}),e.jsx(z,{path:"/admin/login",element:e.jsx(Qo,{})}),e.jsx(z,{path:"/admin/*",element:e.jsx(od,{children:e.jsx(Yo,{})})}),e.jsx(z,{path:"/login",element:e.jsx(zo,{})}),e.jsx(z,{path:"/analytics",element:e.jsx(cc,{})}),e.jsx(z,{path:"/client/:restaurantId",element:e.jsx(Vr,{})}),e.jsx(z,{path:"*",element:e.jsx(dc,{})})]}),e.jsx(rd,{}),e.jsx(so,{position:n.notificationPosition||"top-left",toastOptions:{duration:4e3,style:{background:"var(--toast-bg)",color:"var(--toast-color)",border:"1px solid var(--toast-border)"},success:{iconTheme:{primary:"#10B981",secondary:"#FFFFFF"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FFFFFF"}}}})]})}),!1]})};"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(r=>{console.log("SW registered: ",r)}).catch(r=>{console.log("SW registration failed: ",r)})});const Va=()=>{const r=document.documentElement;r.style.setProperty("--toast-bg","rgb(255 255 255)"),r.style.setProperty("--toast-color","rgb(17 24 39)"),r.style.setProperty("--toast-border","rgb(229 231 235)"),window.matchMedia("(prefers-color-scheme: dark)").matches&&(r.style.setProperty("--toast-bg","rgb(31 41 55)"),r.style.setProperty("--toast-color","rgb(243 244 246)"),r.style.setProperty("--toast-border","rgb(75 85 99)"))};Va();window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Va);vs.createRoot(document.getElementById("root")).render(e.jsx(H.StrictMode,{children:e.jsx(ld,{})}));export{ba as A,so as O,pd as V,At as Y,q as _,tt as a,Z as b,w as c,jt as d,oe as e,ga as f,e as j,nc as u,re as w};
//# sourceMappingURL=index-6e2e4ef2.js.map
