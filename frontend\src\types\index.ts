// Tipos principais do sistema

export interface Restaurant {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  address?: {
    street?: string;
    number?: string;
    complement?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    tiktok?: string;
    youtube?: string;
  };
  isOpen: boolean;
  settings?: {
    interface?: {
      theme?: "light" | "dark" | "auto";
      primaryColor?: string;
      secondaryColor?: string;
      showVoteCount?: boolean;
      showQueuePosition?: boolean;
      allowAnonymousSuggestions?: boolean;
    };
    playlist?: {
      showVoteCount?: boolean;
      showQueuePosition?: boolean;
    };
    ecad?: {
      enabled?: boolean;
    };
  };
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: "super_admin" | "admin" | "moderator" | "staff";
  isActive: boolean;
  phone?: string;
  avatar?: string;
  lastLoginAt?: string;
  preferences?: {
    language?: string;
    timezone?: string;
    notifications?: {
      email?: boolean;
      push?: boolean;
      newSuggestions?: boolean;
      moderationAlerts?: boolean;
    };
  };
  emailVerifiedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  type: "custom" | "youtube_import" | "auto_generated" | "suggestions";
  status: "active" | "inactive" | "archived";
  coverImage?: string;
  genreTags?: string[];
  moodTags?: string[];
  isPublic: boolean;
  trackCount: number;
  totalDuration: number;
  formattedDuration: string;
  playCount: number;
  averageRating: number;
  isActiveNow: boolean;
  settings?: {
    allowVoting?: boolean;
    allowSuggestions?: boolean;
  };
}

export interface Suggestion {
  id: string;
  youtubeVideoId: string;
  title: string;
  artist?: string;
  channelName?: string;
  duration?: number;
  formattedDuration?: string;
  thumbnailUrl?: string;
  status:
    | "pending"
    | "approved"
    | "rejected"
    | "playing"
    | "played"
    | "skipped";
  voteCount: number;
  upvotes: number;
  downvotes: number;
  queuePosition?: number;
  score: number;
  canBeVoted: boolean;
  isInQueue: boolean;
  isExplicit: boolean;
  isLive: boolean;
  youtubeUrl: string;
  createdAt: string;
  metadata?: {
    genre?: string[];
    mood?: string[];
    language?: string;
  };
}

export interface Vote {
  id: string;
  voteType: "up" | "down";
  createdAt: string;
  voter: {
    id?: string;
    name?: string;
    sessionId?: string;
    type: "user" | "session";
  };
}

export interface VideoInfo {
  youtubeVideoId: string;
  title: string;
  artist: string;
  channelName: string;
  duration: number;
  thumbnailUrl: string;
  description: string;
  metadata: {
    genre?: string[];
    mood?: string[];
    language?: string;
    explicit?: boolean;
    live?: boolean;
    publishedAt: string;
    viewCount: number;
    likeCount?: number;
    tags?: string[];
    categoryId: string;
    embeddable: boolean;
    madeForKids: boolean;
  };
}

export interface SearchResult {
  videos: VideoInfo[];
  nextPageToken?: string;
  totalResults: number;
  quotaUsed?: number;
  quotaRemaining?: number;
}

// Item de fila padronizado (para admin/restaurant)
export interface QueueItem {
  id: string;
  title: string;
  artist?: string;
  duration?: string | number;
  thumbnailUrl?: string;
  addedBy?: string;
  addedAt?: string;
  votes?: number;
  isPlaying?: boolean;
  position?: number;
}

export interface PlayQueue {
  // Fila atual; aceitar tanto Suggestion quanto QueueItem para compatibilidade
  queue: Array<Suggestion | QueueItem>;
  totalItems: number;
  estimatedDuration?: number;
  // Item tocando
  currentlyPlaying?: (Suggestion | QueueItem) | null;
  // Histórico de reprodução, quando disponível
  history?: Array<Suggestion | QueueItem>;
  // Estado do player, quando disponível
  playbackState?: {
    isPlaying?: boolean;
    currentTime?: number;
    duration?: number;
    volume?: number;
    isMuted?: boolean;
  };
  estimatedWaitTime?: number;
  lastUpdated?: string;
}

export interface ClientSession {
  id: string;
  sessionToken: string;
  deviceInfo?: {
    type?: "mobile" | "tablet" | "desktop";
    os?: string;
    browser?: string;
    screenResolution?: string;
    language?: string;
    timezone?: string;
  };
  lastActivity: string;
  suggestionsCount: number;
  votesCount: number;
  pageViews: number;
  sessionDuration: number;
  formattedDuration: string;
  preferences?: {
    favoriteGenres?: string[];
    language?: string;
    theme?: "light" | "dark";
    notifications?: boolean;
  };
  isActive: boolean;
  isSessionActive: boolean;
  isNewSession: boolean;
  engagementLevel: "low" | "medium" | "high";
  createdAt: string;
  updatedAt: string;
}

// Tipos para API responses
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
  code?: string;
  timestamp?: string;
  path?: string;
  method?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface AuthResponse {
  token: string;
  user: User;
  restaurant?: Restaurant;
}

export interface ModerationResult {
  autoApproved: boolean;
  autoRejected: boolean;
  reason?: string;
}

// Tipos para WebSocket events
export interface WebSocketEvents {
  "new-suggestion": Suggestion;
  "vote-update": {
    suggestionId: string;
    voteCount: number;
    upvotes: number;
    downvotes: number;
  };
  "queue-update": PlayQueue;
  "suggestion-approved": Suggestion;
  "suggestion-rejected": {
    suggestionId: string;
    reason: string;
  };
  "now-playing": {
    suggestion: Suggestion;
    position: number;
    estimatedEndTime: string;
  };
  "song-ended": {
    suggestionId: string;
    nextSong?: Suggestion;
  };
  // Evento emitido quando um SuperVoto é recebido (pago)
  superVoteReceived: {
    type: "supervote";
    suggestion: {
      id?: string;
      title?: string;
      artist?: string;
      youtubeVideoId?: string;
      thumbnailUrl?: string;
      duration?: number;
    };
    payment: {
      amount: number; // em reais
      voteWeight: number;
      tableNumber?: number;
      // Pode vir no futuro via backend
      message?: string;
      clientName?: string;
    };
    timestamp: string;
  };
  // Evento emitido quando a playlist foi reordenada por votos
  playlistReordered: {
    event: string; // "playlistReordered"
    data: {
      type: "playlist_reordered";
      playlist: {
        id: string;
        name: string;
        tracksReordered: number;
      };
      topTracks: Array<{
        position: number;
        title?: string;
        artist?: string;
        voteCount?: number;
        isPaid?: boolean;
        paymentAmount?: number;
      }>;
      timestamp: string;
      message: string;
    };
    timestamp: string;
    restaurantId?: string;
  };
  notification: Notification;
}

// Tipos para formulários
export interface SuggestionForm {
  youtubeVideoId: string;
  restaurantId: string;
  playlistId?: string;
}

export interface VoteForm {
  voteType: "up" | "down";
}

export interface SearchForm {
  query: string;
  maxResults?: number;
  pageToken?: string;
}

// Tipos para configurações
export interface AppConfig {
  apiUrl: string;
  wsUrl: string;
  youtubeApiKey?: string;
  environment: "development" | "production";
  version: string;
}

// Tipos para erros
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
  statusCode?: number;
}

// Tipos para analytics
export interface Analytics {
  totalSuggestions: number;
  approvedSuggestions: number;
  rejectedSuggestions: number;
  totalVotes: number;
  uniqueSessions: number;
  averageSessionDuration: number;
  topGenres: Array<{
    genre: string;
    count: number;
    percentage: number;
  }>;
  hourlyActivity: Array<{
    hour: number;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
}

// Tipos para notificações
export interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message: string;
  duration?: number;
  priority?: "low" | "normal" | "high" | "urgent";
  action?: {
    label: string;
    onClick: () => void;
  };
  createdAt: string;
}

// Tipos para temas
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  fonts: {
    sans: string;
    display: string;
  };
}

// Tipos para dispositivos
export type DeviceType = "mobile" | "tablet" | "desktop";

// Tipos para status de conexão
export type ConnectionStatus =
  | "connected"
  | "disconnected"
  | "connecting"
  | "error";

// Tipos para estados de loading
export type LoadingState = "idle" | "loading" | "success" | "error";

// Tipos para filtros
export interface SuggestionFilters {
  status?: Suggestion["status"];
  genre?: string;
  mood?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  sortBy?: "createdAt" | "voteCount" | "duration" | "title";
  sortOrder?: "asc" | "desc";
}
