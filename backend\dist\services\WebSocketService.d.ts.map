{"version": 3, "file": "WebSocketService.d.ts", "sourceRoot": "", "sources": ["../../src/services/WebSocketService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,cAAc,EAAU,MAAM,WAAW,CAAC;AAM7D;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;IACnE,MAAM,EAAE,OAAO,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,MAAM;IACrB,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,UAAU;IACzB,EAAE,EAAE,MAAM,CAAC;IACX,YAAY,EAAE,MAAM,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;IACvB,MAAM,EAAE,OAAO,CAAC;IAChB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,IAAI,CAAC;IACd,iBAAiB,CAAC,EAAE,IAAI,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,gBAAgB;IAC/B,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,qBAAqB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9C,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACzD,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,GAAG,CAAC;IACV,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,qBAAa,kBAAkB;IAG7B,YAAY,EAAE,MAAM,CAAC;IAIrB,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,GAAG,CAAC;CACX;AAED,qBAAa,aAAc,SAAQ,kBAAkB;IAEnD,WAAW,EAAE,MAAM,CAAC;CACrB;AAED,qBAAa,aAAa;IAGxB,YAAY,EAAE,MAAM,CAAC;IAIrB,YAAY,EAAE,MAAM,CAAC;IAIrB,KAAK,EAAE,MAAM,CAAC;CACf;AAED,qBAAa,mBAAmB;IAG9B,YAAY,EAAE,MAAM,CAAC;IAIrB,YAAY,EAAE,MAAM,CAAC;IAIrB,MAAM,EAAE,MAAM,CAAC;IAIf,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,KAAK;IACvC,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,aAAa,EAAE,OAAO,CAAC;gBAGrC,OAAO,EAAE,MAAM,EACf,IAAI,GAAE,MAA0B,EAChC,UAAU,GAAE,MAAY,EACxB,aAAa,GAAE,OAAc;CAUhC;AAED;;;;;;;;;;;;;GAaG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAmB;IAC1C,OAAO,CAAC,EAAE,CAAiB;IAC3B,OAAO,CAAC,oBAAoB,CAGd;IACd,OAAO,CAAC,SAAS,CAAkC;IACnD,OAAO,CAAC,WAAW,CAAuC;gBAE9C,EAAE,EAAE,cAAc;IAM9B;;;;OAIG;IACH,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,cAAc,GAAG,gBAAgB;IAOzD;;;OAGG;IACH,OAAO,CAAC,kBAAkB;IAgD1B;;;OAGG;YACW,kBAAkB;IAyChC;;;OAGG;IACH,OAAO,CAAC,WAAW;IAmBnB;;;OAGG;YACW,cAAc;IAoC5B;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAmBxB;;;OAGG;IACH,OAAO,CAAC,iBAAiB;IAYzB;;;OAGG;YACW,wBAAwB;IAuBtC;;;OAGG;IACH,OAAO,CAAC,eAAe;IAWvB;;;OAGG;IACH,OAAO,CAAC,oBAAoB;IAM5B;;;OAGG;YACW,oBAAoB;IAalC;;;OAGG;YACW,aAAa;IAsB3B;;;OAGG;IACH,OAAO,CAAC,YAAY;IAIpB;;;;;OAKG;IACG,gBAAgB,CACpB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,GAAG,GACR,OAAO,CAAC,IAAI,CAAC;IA0ChB;;;;;OAKG;IACG,qBAAqB,CACzB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,GAAG,GACR,OAAO,CAAC,IAAI,CAAC;IAIhB;;;;;OAKG;IACG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAazE;;;;OAIG;IACG,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAQxD;;;;;;OAMG;IACG,WAAW,CACf,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,MAAM,EACnB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,GAAG,GACR,OAAO,CAAC,IAAI,CAAC;IA8ChB;;;;;;OAMG;IACG,YAAY,CAChB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,GAAG,EACT,YAAY,GAAE,MAAgB,GAC7B,OAAO,CAAC,IAAI,CAAC;IAkEhB;;;OAGG;IACH,OAAO,CAAC,eAAe;IASvB,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI;IAS1C;;;;OAIG;IACG,mBAAmB,CACvB,YAAY,EAAE,MAAM,EACpB,UAAU,EAAE,WAAW,GACtB,OAAO,CAAC,IAAI,CAAC;IA6BhB;;;;OAIG;IACG,wBAAwB,CAC5B,YAAY,EAAE,MAAM,EACpB,UAAU,EAAE,WAAW,GACtB,OAAO,CAAC,IAAI,CAAC;IAgChB;;;;;OAKG;IACG,gBAAgB,CACpB,YAAY,EAAE,MAAM,EACpB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,GAC3D,OAAO,CAAC,IAAI,CAAC;IA6BhB;;;;;;OAMG;IACG,4BAA4B,CAChC,YAAY,EAAE,MAAM,EACpB,YAAY,EAAE,MAAM,EACpB,MAAM,EAAE,MAAM,EACd,MAAM,CAAC,EAAE,MAAM,GACd,OAAO,CAAC,IAAI,CAAC;IAkDhB;;;;OAIG;IACG,mBAAmB,CACvB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,MAAM,GACZ,OAAO,CAAC,IAAI,CAAC;IAuBhB;;;;OAIG;IACG,iBAAiB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAuB3E;;;;OAIG;IACG,iBAAiB,CACrB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,UAAU,EAAE,GAClB,OAAO,CAAC,IAAI,CAAC;IA4BhB;;;;OAIG;IACG,oBAAoB,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAsB1E;;;;;OAKG;IACG,qBAAqB,CACzB,YAAY,EAAE,MAAM,EACpB,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,MAAM,GACnB,OAAO,CAAC,IAAI,CAAC;IA4BhB;;;;;OAKG;IACG,kBAAkB,CACtB,YAAY,EAAE,MAAM,EACpB,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,MAAM,GACZ,OAAO,CAAC,IAAI,CAAC;IAsBhB;;;OAGG;IACH,kBAAkB,IAAI,gBAAgB;IAyDtC;;;;OAIG;IACH,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAIlC;;OAEG;IACG,0BAA0B,IAAI,OAAO,CAAC,IAAI,CAAC;CAgClD"}