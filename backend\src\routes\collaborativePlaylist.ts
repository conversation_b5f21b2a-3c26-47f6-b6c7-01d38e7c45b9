import { Router, Request, Response } from "express";
import { param, body, query, validationResult } from "../utils/validation";
import asyncHandler from "../middleware/asyncHandler";
import { authMiddleware, optionalAuth } from "../middleware/auth";
import { collaborativePlaylistService } from "../services/CollaborativePlaylistService";
import { ValidationError } from "../middleware/errorHandler";

const router = Router();

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/create:
 *   post:
 *     summary: Criar playlist colaborativa para restaurante
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               playlistName:
 *                 type: string
 *                 example: "Playlist Principal"
 *               description:
 *                 type: string
 *                 example: "Playlist colaborativa do restaurante"
 *               initialTracks:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dQw4w9WgXcQ", "kJQP7kiw5Fk"]
 *     responses:
 *       201:
 *         description: Playlist colaborativa criada com sucesso
 */
router.post(
  "/:restaurantId/create",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("playlistName")
      .notEmpty()
      .withMessage("Nome da playlist é obrigatório"),
    body("description").optional().isString(),
    body("initialTracks").optional().isArray(),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { playlistName, description, initialTracks = [] } = req.body;

    console.log(
      `🎵 Criando playlist colaborativa: ${playlistName} para ${restaurantId}`
    );

    // 1. Criar playlist colaborativa
    const result = await collaborativePlaylistService.createRestaurantPlaylist(
      restaurantId,
      playlistName,
      description
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message,
      });
    }

    // 2. Adicionar músicas iniciais se fornecidas
    let initialTracksResult = null;
    if (initialTracks.length > 0 && result.playlistId) {
      initialTracksResult = await collaborativePlaylistService.addInitialTracks(
        result.playlistId,
        initialTracks
      );
    }

    res.status(201).json({
      success: true,
      message: result.message,
      data: {
        playlistId: result.playlistId,
        youtubePlaylistId: result.youtubePlaylistId,
        initialTracksAdded: initialTracksResult?.addedCount || 0,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/stats:
 *   get:
 *     summary: Obter estatísticas da playlist colaborativa
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas da playlist colaborativa
 */
router.get(
  "/:restaurantId/stats",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const result = await collaborativePlaylistService.getPlaylistStats(
      restaurantId
    );

    if (!result.success) {
      return res.status(404).json({
        success: false,
        message: result.message,
      });
    }

    res.json({
      success: true,
      message: result.message,
      data: result.data,
    });
  })
);

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/vote:
 *   post:
 *     summary: Processar voto normal (gratuito)
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 example: "dQw4w9WgXcQ"
 *               tableNumber:
 *                 type: integer
 *                 example: 5
 *               clientSessionId:
 *                 type: string
 *                 example: "session_123"
 *     responses:
 *       200:
 *         description: Voto registrado com sucesso
 */
router.post(
  "/:restaurantId/vote",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId")
      .notEmpty()
      .withMessage("ID do vídeo do YouTube é obrigatório"),
    body("tableNumber").optional().isInt(),
    body("clientSessionId").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { youtubeVideoId, tableNumber, clientSessionId } = req.body;

    const result = await collaborativePlaylistService.processNormalVote(
      restaurantId,
      youtubeVideoId,
      tableNumber,
      clientSessionId
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message,
      });
    }

    res.json({
      success: true,
      message: result.message,
      data: {
        voteWeight: result.voteWeight,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/supervote:
 *   post:
 *     summary: Processar supervoto (pago)
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 example: "dQw4w9WgXcQ"
 *               paymentAmount:
 *                 type: number
 *                 example: 5.00
 *                 description: "Valor em reais (R$ 5, R$ 20 ou R$ 50)"
 *               paymentId:
 *                 type: string
 *                 example: "pix_123456"
 *               tableNumber:
 *                 type: integer
 *                 example: 5
 *               clientSessionId:
 *                 type: string
 *                 example: "session_123"
 *     responses:
 *       200:
 *         description: Supervoto processado com sucesso
 */
router.post(
  "/:restaurantId/supervote",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId")
      .notEmpty()
      .withMessage("ID do vídeo do YouTube é obrigatório"),
    body("paymentAmount")
      .isFloat({ min: 5 })
      .withMessage("Valor do pagamento deve ser no mínimo R$ 5,00"),
    body("paymentId").notEmpty().withMessage("ID do pagamento é obrigatório"),
    body("tableNumber").optional().isInt(),
    body("clientSessionId").optional().isString(),
  body("clientMessage").optional().isString().isLength({ max: 200 }),
  body("clientName").optional().isString().isLength({ max: 80 }),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const {
      youtubeVideoId,
      paymentAmount,
      paymentId,
      tableNumber,
      clientSessionId,
      clientMessage,
      clientName,
    } = req.body;

    const result = await collaborativePlaylistService.processSuperVote(
      restaurantId,
      youtubeVideoId,
      paymentAmount,
      paymentId,
  tableNumber,
  clientSessionId,
  clientMessage,
  clientName
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message,
      });
    }

    res.json({
      success: true,
      message: result.message,
      data: {
        voteWeight: result.voteWeight,
        paymentAmount,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/add-tracks:
 *   post:
 *     summary: Adicionar músicas à playlist colaborativa
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               videoIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dQw4w9WgXcQ", "kJQP7kiw5Fk"]
 *     responses:
 *       200:
 *         description: Músicas adicionadas com sucesso
 */
router.post(
  "/:restaurantId/add-tracks",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("videoIds")
      .isArray({ min: 1 })
      .withMessage("Lista de vídeos é obrigatória"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { videoIds } = req.body;

    // Buscar playlist do restaurante
    const playlist = await collaborativePlaylistService.getPlaylistStats(
      restaurantId
    );

    if (!playlist.success) {
      return res.status(404).json({
        success: false,
        message: "Playlist não encontrada",
      });
    }

    // Adicionar músicas (assumindo que temos o playlistId)
    // Esta implementação precisa ser ajustada para obter o playlistId

    res.json({
      success: true,
      message: "Funcionalidade em desenvolvimento",
    });
  })
);

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/ranking:
 *   get:
 *     summary: Obter ranking de votação
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: Ranking de votação
 */
router.get(
  "/:restaurantId/ranking",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("limit").optional().isInt({ min: 1, max: 100 }),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { limit = 20 } = req.query as any;

    const result = await collaborativePlaylistService.getVotingRanking(
      restaurantId,
      parseInt(limit)
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message,
      });
    }

    res.json({
      success: true,
      message: result.message,
      data: result.data,
    });
  })
);

/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/reorder:
 *   post:
 *     summary: Reordenar playlist baseada em votos
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist reordenada com sucesso
 */
router.post(
  "/:restaurantId/reorder",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    // Usar o serviço central de reordenação (emite WebSocket e atualiza DB)
    const { playlistReorderService } = await import(
      "../services/PlaylistReorderService"
    );
    const reorderResult = await playlistReorderService.manualReorder(
      restaurantId
    );

    if (!reorderResult.success) {
      return res.status(400).json({
        success: false,
        message: reorderResult.message,
      });
    }

    res.json({
      success: true,
      message: reorderResult.message,
      data: {
        reorderedCount: reorderResult.tracksReordered,
        playlistId: reorderResult.playlistId,
      },
    });
  })
);

export default router;
