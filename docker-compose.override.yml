# Override para desenvolvimento

services:
  # PostgreSQL na porta 8002
  postgres:
    ports:
      - "8002:5432"
    environment:
      POSTGRES_DB: restaurant_playlist
      POSTGRES_USER: restaurant_user
      POSTGRES_PASSWORD: restaurant_pass

  # Redis na porta 8003
  redis:
    ports:
      - "8003:6379"

  # Backend na porta 8001
  backend:
    ports:
      - "8001:5000"
    environment:
      PORT: 5000
      API_URL: http://localhost:8001
      FRONTEND_URL: http://localhost:8000
      DATABASE_URL: **********************************************************/restaurant_playlist?sslmode=disable
      REDIS_URL: redis://:redis_password@redis:6379
      CORS_ORIGINS: http://localhost:8000,http://localhost:8001

  # Frontend na porta 8000
  frontend:
    ports:
      - "8000:3000"
    environment:
      VITE_API_URL: http://localhost:8001
      VITE_WS_URL: http://localhost:8001

  # Adminer na porta 8004
  adminer:
    ports:
      - "8004:8080"
