import{u as re,b as x,A as f,c as i,j as e}from"./index-6e2e4ef2.js";import{r as g}from"./vendor-66b0ef43.js";import{R as T,aD as B,j as F,h as ae,m as E,c as se,aE as oe,q as te,f as ie,_ as ne,i as le,A as ce,X as de,u as ue}from"./ui-a5f8f5f0.js";import"./router-f729e475.js";import"./utils-08f61814.js";const fe=({onGenreSelect:j,selectedGenres:$=[],mode:G="manage"})=>{const{restaurantId:N}=re(),[v,k]=g.useState({}),[O,D]=g.useState(!0),[y,I]=g.useState(""),[w,M]=g.useState("all"),[L,p]=g.useState(!1),[m,C]=g.useState(null),[t,c]=g.useState({name:"",displayName:"",description:"",category:"music",color:"#3B82F6",icon:"",priority:0}),A={all:"Todos",music:"Gêneros Musicais",mood:"Humor",energy:"Energia",time:"Horário",custom:"Personalizado"},H=["Music","Music2","Music3","Music4","Mic","Heart","Zap","Volume2","Headphones","Sun","Moon","Coffee","Smile","CloudRain","Waves","Activity","Star","Disc","Radio","Speaker"];g.useEffect(()=>{h()},[]);const h=async()=>{try{D(!0),console.log("🎵 Carregando gêneros para restaurante:",N),await J();const r=x(`${f.ENDPOINTS.GENRES}`);console.log("🎵 URL da API:",r);const s=localStorage.getItem("authToken"),a={"Content-Type":"application/json"};s&&(a.Authorization=`Bearer ${s}`);const o=await fetch(r,{headers:a});if(o.ok){const n=await o.json();console.log("🎵 Gêneros do sistema carregados:",n),k(u=>({...n.genres,...u})),i.success("Gêneros carregados com sucesso!")}else{const n=await o.text().catch(()=>"");console.error("🎵 Erro na resposta:",o.status,o.statusText,n),o.status===500?(console.log("🎵 Carregando gêneros de exemplo devido ao erro 500"),S(),i("Carregados gêneros de exemplo (erro no servidor)",{icon:"⚠️",duration:4e3})):i.error("Erro ao carregar gêneros")}}catch(r){console.error("🎵 Erro ao carregar gêneros:",r),S(),i.error("Erro ao carregar gêneros. Carregando dados de exemplo.")}finally{D(!1)}},J=async()=>{try{console.log("🎵 Analisando playlist para extrair gêneros...");const r=await fetch(x(`/playlists/restaurant/${N}`));if(!r.ok){console.log("🎵 Não foi possível carregar playlists para análise de gêneros");return}const a=(await r.json()).playlists||[];if(a.length===0){console.log("🎵 Nenhuma playlist encontrada para análise");return}const o={},n=new Map;for(const l of a)if(l.isActive){const d=await fetch(x(`/playlists/${l.id}`));if(d.ok){const P=(await d.json()).tracks||[];console.log(`🎵 Analisando ${P.length} músicas da playlist "${l.name}"`);for(const Y of P)q(Y.title,"").forEach(R=>{const ee=n.get(R)||0;n.set(R,ee+1)})}}let u=1;n.forEach((l,d)=>{l>=2&&(o.music=o.music||[],o.music.push({id:`playlist-${d}`,name:d.toLowerCase(),displayName:d,color:K(d),isActive:!0,category:"music",priority:u++,description:`Gênero identificado na playlist (${l} músicas)`,isDefault:!1,usageCount:l}))}),o.music&&o.music.length>0&&(console.log("🎵 Gêneros extraídos da playlist:",o),k(l=>({...l,music:[...l.music||[],...o.music]})),i.success(`${o.music.length} gêneros identificados na playlist!`))}catch(r){console.error("🎵 Erro ao analisar playlist para gêneros:",r)}},S=()=>{k({music:[{id:"1",name:"rock",displayName:"Rock",color:"#e74c3c",isActive:!0,category:"music",priority:1,isDefault:!0,usageCount:0},{id:"2",name:"pop",displayName:"Pop",color:"#3498db",isActive:!0,category:"music",priority:2,isDefault:!0,usageCount:0},{id:"3",name:"jazz",displayName:"Jazz",color:"#f39c12",isActive:!0,category:"music",priority:3,isDefault:!1,usageCount:0},{id:"4",name:"classical",displayName:"Clássica",color:"#9b59b6",isActive:!0,category:"music",priority:4,isDefault:!1,usageCount:0},{id:"5",name:"electronic",displayName:"Eletrônica",color:"#1abc9c",isActive:!0,category:"music",priority:5,isDefault:!1,usageCount:0},{id:"6",name:"reggae",displayName:"Reggae",color:"#27ae60",isActive:!0,category:"music",priority:6,isDefault:!1,usageCount:0},{id:"7",name:"blues",displayName:"Blues",color:"#34495e",isActive:!0,category:"music",priority:7,isDefault:!1,usageCount:0},{id:"8",name:"country",displayName:"Country",color:"#d35400",isActive:!0,category:"music",priority:8,isDefault:!1,usageCount:0}]})},U=async()=>{try{console.log("🎵 Criando gêneros padrão para restaurante:",N);const r=x(`${f.ENDPOINTS.GENRES}/seed`),s=await fetch(r,{method:"POST"});if(s.ok){const a=await s.json();i.success(a.message||"Gêneros padrão criados com sucesso!"),h()}else{const a=await s.json().catch(()=>({}));i.error(a.message||"Erro ao criar gêneros padrão")}}catch(r){console.error("🎵 Erro ao criar gêneros padrão:",r),i.error("Erro ao criar gêneros padrão")}},q=(r,s)=>{const a=[],o=`${r} ${s}`.toLowerCase();return Object.entries({Rock:["rock","metal","punk","grunge","alternative","hard rock","soft rock"],Pop:["pop","mainstream","hit","chart","top","billboard"],Jazz:["jazz","swing","blues","smooth","bossa nova"],Eletronica:["electronic","edm","techno","house","dance","remix","mix","dj"],"Hip Hop":["hip hop","rap","trap","beats","freestyle","mc"],Reggae:["reggae","ska","dub","bob marley"],Country:["country","folk","acoustic","bluegrass"],Classica:["classical","orchestra","symphony","piano","violin","opera"],Latina:["latin","salsa","bachata","reggaeton","spanish","latino","merengue"],"R&B":["r&b","soul","funk","motown","rhythm"],Indie:["indie","independent","alternative","underground"],Sertanejo:["sertanejo","caipira","modao","universitario","raiz"],MPB:["mpb","bossa nova","brazilian","brasil","caetano","gilberto"],Forro:["forro","nordestino","sanfona","zabumba","triangulo"],Samba:["samba","pagode","carnaval","escola de samba","batucada"],Funk:["funk","baile funk","mc","beat","pancadao"],Gospel:["gospel","religioso","igreja","louvor","adoracao","jesus"],Internacional:["english","international","worldwide","global"]}).forEach(([u,l])=>{l.some(d=>o.includes(d))&&a.push(u)}),a},K=r=>({Rock:"#DC2626",Pop:"#EC4899",Jazz:"#F59E0B",Eletronica:"#10B981","Hip Hop":"#8B5CF6",Reggae:"#059669",Country:"#D97706",Classica:"#7C3AED",Latina:"#EF4444","R&B":"#F97316",Indie:"#6366F1",Sertanejo:"#84CC16",MPB:"#06B6D4",Forro:"#F59E0B",Samba:"#EF4444",Funk:"#9333EA",Gospel:"#0EA5E9",Internacional:"#64748B"})[r]||"#6B7280",Q=async()=>{try{console.log("🎵 Criando novo gênero:",t);const r=x(`${f.ENDPOINTS.GENRES}`),s=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok){const a=await s.json();console.log("🎵 Gênero criado:",a),i.success("Gênero criado com sucesso!"),p(!1),b(),h()}else{const a=await s.json().catch(()=>({}));console.error("🎵 Erro ao criar gênero:",a),i.error(a.message||"Erro ao criar gênero")}}catch(r){console.error("🎵 Erro ao criar gênero:",r),i.error("Erro ao criar gênero")}},_=async()=>{if(m)try{console.log("🎵 Atualizando gênero:",m.id,t);const r=x(`${f.ENDPOINTS.GENRES}/${m.id}`),s=await fetch(r,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok){const a=await s.json();console.log("🎵 Gênero atualizado:",a),i.success("Gênero atualizado com sucesso!"),C(null),b(),h()}else{const a=await s.json().catch(()=>({}));console.error("🎵 Erro ao atualizar gênero:",a),i.error(a.message||"Erro ao atualizar gênero")}}catch(r){console.error("🎵 Erro ao atualizar gênero:",r),i.error("Erro ao atualizar gênero")}},V=async r=>{if(r.isDefault){i.error("Não é possível deletar gêneros padrão do sistema");return}if(confirm(`Tem certeza que deseja deletar o gênero "${r.displayName}"?`))try{console.log("🎵 Deletando gênero:",r.id);const s=x(`${f.ENDPOINTS.GENRES}/${r.id}`),a=await fetch(s,{method:"DELETE"});if(a.ok)console.log("🎵 Gênero deletado com sucesso"),i.success("Gênero deletado com sucesso!"),h();else{const o=await a.json().catch(()=>({message:"Erro desconhecido"}));console.error("🎵 Erro ao deletar gênero:",o),a.status===400?i.error(o.message||"Não é possível deletar este gênero"):a.status===404?i.error("Gênero não encontrado"):i.error(o.message||"Erro ao deletar gênero")}}catch(s){console.error("🎵 Erro ao deletar gênero:",s),i.error("Erro ao deletar gênero")}},W=r=>{C(r),c({name:r.name,displayName:r.displayName,description:r.description||"",category:r.category,color:r.color,icon:r.icon||"",priority:r.priority}),p(!0)},b=()=>{c({name:"",displayName:"",description:"",category:"music",color:"#3B82F6",icon:"",priority:0}),C(null)},z=Object.entries(v).reduce((r,[s,a])=>{if(w!=="all"&&s!==w)return r;const n=(Array.isArray(a)?a:[]).filter(u=>u.displayName.toLowerCase().includes(y.toLowerCase())||u.name.toLowerCase().includes(y.toLowerCase()));return n.length>0&&(r[s]=n),r},{}),X=Object.values(v).flat().length,Z=Object.values(v).flat().filter(r=>r.isActive).length;return O?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(T,{className:"w-6 h-6 animate-spin text-blue-600"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Carregando gêneros..."})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg",children:e.jsx(B,{className:"w-6 h-6 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Gerenciamento de Gêneros"}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[X," gêneros • ",Z," ativos"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:U,className:"flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[e.jsx(T,{className:"w-4 h-4"}),e.jsx("span",{children:"Gêneros Padrão"})]}),e.jsxs("button",{onClick:()=>p(!0),className:"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[e.jsx(F,{className:"w-4 h-4"}),e.jsx("span",{children:"Novo Gênero"})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ae,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx("input",{type:"text",placeholder:"Buscar gêneros...",value:y,onChange:r=>I(r.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})}),e.jsx("select",{value:w,onChange:r=>M(r.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:Object.entries(A).map(([r,s])=>e.jsx("option",{value:r,children:s},r))})]}),e.jsx("div",{className:"space-y-6",children:Object.entries(z).map(([r,s])=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center space-x-2",children:[e.jsx("span",{children:A[r]}),e.jsx("span",{className:"text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full",children:s.length})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:s.map(a=>e.jsxs(E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:`p-4 rounded-lg border-2 transition-all cursor-pointer ${$.includes(a.id)?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"}`,onClick:()=>G==="select"&&(j==null?void 0:j(a)),children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium",style:{backgroundColor:a.color},children:a.icon?e.jsx(se,{className:"w-4 h-4"}):a.displayName.charAt(0).toUpperCase()}),G==="manage"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:o=>{o.stopPropagation(),W(a)},className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",children:e.jsx(oe,{className:"w-3 h-3"})}),!a.isDefault&&e.jsx("button",{onClick:o=>{o.stopPropagation(),V(a)},className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:e.jsx(te,{className:"w-3 h-3"})})]})]}),e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white text-sm mb-1",children:a.displayName}),a.description&&e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-2",children:a.description}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[e.jsxs("span",{className:"flex items-center space-x-1",children:[e.jsx(ie,{className:"w-3 h-3"}),e.jsx("span",{children:a.usageCount})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[a.isDefault&&e.jsx(ne,{className:"w-3 h-3 text-green-500"}),!a.isActive&&e.jsx(le,{className:"w-3 h-3 text-red-500"})]})]})]},a.id))})]},r))}),Object.keys(z).length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(B,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Nenhum gênero encontrado"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:y?"Tente uma busca diferente":"Crie seu primeiro gênero"}),!y&&e.jsxs("button",{onClick:()=>p(!0),className:"inline-flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[e.jsx(F,{className:"w-4 h-4"}),e.jsx("span",{children:"Criar Gênero"})]})]}),e.jsx(ce,{children:L&&e.jsx(E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:()=>{p(!1),b()},children:e.jsxs(E.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md",onClick:r=>r.stopPropagation(),children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:m?"Editar Gênero":"Novo Gênero"}),e.jsx("button",{onClick:()=>{p(!1),b()},className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e.jsx(de,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome (ID)"}),e.jsx("input",{type:"text",value:t.name,onChange:r=>c({...t,name:r.target.value}),disabled:!!m,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50",placeholder:"rock, pop, sertanejo..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome de Exibição"}),e.jsx("input",{type:"text",value:t.displayName,onChange:r=>c({...t,displayName:r.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Rock, Pop, Sertanejo..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:t.description,onChange:r=>c({...t,description:r.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",rows:2,placeholder:"Descrição do gênero..."})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Categoria"}),e.jsxs("select",{value:t.category,onChange:r=>c({...t,category:r.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[e.jsx("option",{value:"music",children:"Gênero Musical"}),e.jsx("option",{value:"mood",children:"Humor"}),e.jsx("option",{value:"energy",children:"Energia"}),e.jsx("option",{value:"time",children:"Horário"}),e.jsx("option",{value:"custom",children:"Personalizado"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Cor"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"color",value:t.color,onChange:r=>c({...t,color:r.target.value}),className:"w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"}),e.jsx("input",{type:"text",value:t.color,onChange:r=>c({...t,color:r.target.value}),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"#3B82F6"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Ícone"}),e.jsxs("select",{value:t.icon,onChange:r=>c({...t,icon:r.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Sem ícone"}),H.map(r=>e.jsx("option",{value:r,children:r},r))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Prioridade"}),e.jsx("input",{type:"number",value:t.priority,onChange:r=>c({...t,priority:parseInt(r.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",min:"0"})]})]})]}),e.jsxs("div",{className:"flex items-center justify-end space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>{p(!1),b()},className:"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:"Cancelar"}),e.jsxs("button",{onClick:m?_:Q,className:"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[e.jsx(ue,{className:"w-4 h-4"}),e.jsx("span",{children:m?"Atualizar":"Criar"})]})]})]})})})]})};export{fe as default};
//# sourceMappingURL=GenreManager-f5564d47.js.map
