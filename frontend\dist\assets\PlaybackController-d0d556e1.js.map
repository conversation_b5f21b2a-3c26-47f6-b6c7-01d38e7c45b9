{"version": 3, "file": "PlaybackController-d0d556e1.js", "sources": ["../../src/components/restaurant/PlaybackController.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useRestaurantContext } from \"./RestaurantDashboard\";\r\nimport { buildApiUrl } from \"../../config/api\";\r\nimport { useWebSocket, wsService } from \"@/services/websocket\";\r\nimport {\r\n  Play,\r\n  Pause,\r\n  SkipForward,\r\n  Volume2,\r\n  VolumeX,\r\n  Music,\r\n  Clock,\r\n  Users,\r\n  TrendingUp,\r\n  AlertTriangle,\r\n  RefreshCw,\r\n  Loader2,\r\n  X,\r\n  ChevronUp,\r\n  Shuffle,\r\n  Repeat,\r\n  Settings2,\r\n  ArrowUp,\r\n  ArrowDown,\r\n  Lock,\r\n  Unlock,\r\n  Trash2,\r\n  ExternalLink,\r\n  Activity,\r\n  DollarSign,\r\n  Gift,\r\n  Zap,\r\n  ThumbsUp,\r\n} from \"lucide-react\";\r\nimport { toast, Toaster } from \"react-hot-toast\";\r\n\r\ninterface Track {\r\n  id: string;\r\n  title: string;\r\n  artist: string;\r\n  youtubeVideoId: string;\r\n  duration: number;\r\n  thumbnailUrl: string;\r\n  upvotes: number;\r\n  downvotes: number;\r\n  score: number;\r\n  suggestedBy?: string;\r\n  createdAt: Date;\r\n}\r\n\r\n// Itens de ranking do sistema colaborativo\r\ninterface RankingItem {\r\n  youtubeVideoId: string;\r\n  title?: string;\r\n  artist?: string;\r\n  voteCount: number;\r\n  superVoteCount: number;\r\n  normalVoteCount: number;\r\n  totalRevenue: number;\r\n  isPaid: boolean;\r\n  paymentAmount: number;\r\n  tableNumber?: number;\r\n}\r\n\r\ninterface PlaybackState {\r\n  currentTrack: Track | null;\r\n  isPlaying: boolean;\r\n  currentTime: number;\r\n  volume: number;\r\n  queue: Track[];\r\n  priorityQueue: Track[];\r\n  normalQueue: Track[];\r\n  history: Track[];\r\n  estimatedWaitTime?: number;\r\n  connectionStatus?: \"connected\" | \"disconnected\" | \"reconnecting\";\r\n}\r\n\r\n// Cache Manager para armazenamento local\r\nclass PlaybackCache {\r\n  private static CACHE_PREFIX = \"playback_controller_\";\r\n\r\n  static setCache(key: string, data: any, ttl: number = 2 * 60 * 1000) {\r\n    const cacheData = {\r\n      data,\r\n      timestamp: Date.now(),\r\n      ttl,\r\n    };\r\n    localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\r\n  }\r\n\r\n  static getCache(key: string) {\r\n    try {\r\n      const cached = localStorage.getItem(this.CACHE_PREFIX + key);\r\n      if (!cached) return null;\r\n\r\n      const { data, timestamp, ttl } = JSON.parse(cached);\r\n      if (Date.now() - timestamp > ttl) {\r\n        localStorage.removeItem(this.CACHE_PREFIX + key);\r\n        return null;\r\n      }\r\n\r\n      return data;\r\n    } catch {\r\n      return null;\r\n    }\r\n  }\r\n}\r\n\r\nconst PlaybackController: React.FC = () => {\r\n  const { restaurantId: paramRestaurantId } = useParams<{\r\n    restaurantId: string;\r\n  }>();\r\n  const { restaurantId: contextRestaurantId, isConnected } = useRestaurantContext();\r\n  const restaurantId = paramRestaurantId || contextRestaurantId;\r\n  const { on, off, emit } = useWebSocket();\r\n\r\n  const [playbackState, setPlaybackState] = useState<PlaybackState | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [actionLoading, setActionLoading] = useState<string | null>(null);\r\n  const [priorityQueue, setPriorityQueue] = useState<Track[]>([]);\r\n  const [normalQueue, setNormalQueue] = useState<Track[]>([]);\r\n  const [queueStats, setQueueStats] = useState({\r\n    totalItems: 0,\r\n    paidItems: 0,\r\n    freeItems: 0,\r\n    estimatedWaitTime: 0,\r\n  });\r\n  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());\r\n  const [retryCount, setRetryCount] = useState(0);\r\n\r\n  // Estado para Votações (Colaborativa)\r\n  const [rankingPaid, setRankingPaid] = useState<RankingItem[]>([]);\r\n  const [rankingFree, setRankingFree] = useState<RankingItem[]>([]);\r\n  const [nextReorderAt, setNextReorderAt] = useState<Date | null>(null);\r\n  const [countdown, setCountdown] = useState<number>(0);\r\n  const [autoReorder, setAutoReorder] = useState<boolean>(false);\r\n  const [autoPreview, setAutoPreview] = useState<RankingItem[]>([]);\r\n  const [totalVotes, setTotalVotes] = useState<number>(0);\r\n  const [reorderHistory, setReorderHistory] = useState<any[]>([]);\r\n  const [collabStats, setCollabStats] = useState<any>(null);\r\n  const [voteAggregates, setVoteAggregates] = useState({\r\n    totalSuperVotes: 0,\r\n    totalNormalVotes: 0,\r\n    paidItems: 0,\r\n    freeItems: 0,\r\n  });\r\n  const [computedRevenue, setComputedRevenue] = useState<number>(0);\r\n\r\n  // Configurações locais (apenas UI por enquanto)\r\n  const [localSettings, setLocalSettings] = useState({\r\n    shuffle: false,\r\n    repeat: false,\r\n    autoPlay: false,\r\n    crossfade: 0,\r\n    lockVoting: false,\r\n  });\r\n\r\n  // Adição manual\r\n  const [manualVideoInput, setManualVideoInput] = useState<string>(\"\");\r\n  const [manualIsPaid, setManualIsPaid] = useState<boolean>(false);\r\n\r\n  // Refs for cleanup and caching\r\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const rankingIntervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // WebSocket setup for real-time updates\r\n  useEffect(() => {\r\n    if (!restaurantId || !isConnected) return;\r\n\r\n    const handlePlaybackUpdate = (data: Partial<PlaybackState>) => {\r\n      setPlaybackState((prev) => (prev ? { ...prev, ...data } : null));\r\n      setLastUpdateTime(new Date());\r\n      PlaybackCache.setCache(`playback_${restaurantId}`, data);\r\n    };\r\n\r\n  const handleQueueUpdate = (data: { priorityQueue: Track[]; normalQueue: Track[] }) => {\r\n      setPriorityQueue(data.priorityQueue || []);\r\n      setNormalQueue(data.normalQueue || []);\r\n\r\n      const estimatedWait = calculateEstimatedWaitTime([...data.priorityQueue, ...data.normalQueue]);\r\n      setQueueStats((prev) => ({ ...prev, estimatedWaitTime: estimatedWait }));\r\n\r\n      PlaybackCache.setCache(`queue_${restaurantId}`, data);\r\n    };\r\n\r\n    // Atualizar status via serviço\r\n  const unsubscribeStatus = wsService.onConnectionStatusChange((status) => {\r\n      setPlaybackState((prev) =>\r\n    prev ? { ...prev, connectionStatus: (status as any) } : null\r\n      );\r\n    });\r\n\r\n    // Register WebSocket listeners\r\n  on(\"playback-state-update\" as any, handlePlaybackUpdate as any);\r\n  on(\"queue-update\" as any, handleQueueUpdate as any);\r\n\r\n    // Reagir à reordenação automática (websocket backend)\r\n    const handlePlaylistReordered = (payload: any) => {\r\n      toast.success(payload?.message || \"Playlist reordenada por votos\");\r\n      // Registrar histórico de reordenação\r\n      setReorderHistory((prev) => [\r\n        {\r\n          time: new Date().toISOString(),\r\n          playlistName: payload?.playlist?.name || payload?.playlistName,\r\n          count: payload?.playlist?.tracksReordered || payload?.tracksReordered || 0,\r\n          details: (payload?.topTracks || []).slice(0, 5),\r\n        },\r\n        ...prev.slice(0, 19),\r\n      ]);\r\n      // Recarregar dados críticos\r\n      loadQueues();\r\n      loadPlaybackState();\r\n      loadVotingRanking();\r\n      loadCollaborativeStats();\r\n      // Reinciar countdown de 5min, se disponível\r\n      const target = new Date(Date.now() + 5 * 60 * 1000);\r\n      setNextReorderAt(target);\r\n      startCountdown(target);\r\n    };\r\n    on(\"playlistReordered\", handlePlaylistReordered);\r\n\r\n    // Join restaurant room\r\n    emit(\"join-restaurant-playback\", { restaurantId });\r\n\r\n    return () => {\r\n  off(\"playback-state-update\" as any, handlePlaybackUpdate as any);\r\n  off(\"queue-update\" as any, handleQueueUpdate as any);\r\n  unsubscribeStatus?.();\r\n      off(\"playlistReordered\", handlePlaylistReordered);\r\n      emit(\"leave-restaurant-playback\", { restaurantId });\r\n    };\r\n  }, [restaurantId, isConnected, on, off, emit]);\r\n\r\n  // Initial data load with cache fallback\r\n  useEffect(() => {\r\n    loadInitialData();\r\n\r\n    // Fallback polling para quando WebSocket não estiver disponível\r\n    if (!isConnected) {\r\n      intervalRef.current = setInterval(() => {\r\n        loadPlaybackState();\r\n        loadQueues();\r\n      }, 10000); // 10 segundos quando offline\r\n    }\r\n\r\n    // Iniciar carga e atualização periódica do ranking colaborativo + stats\r\n    loadVotingRanking();\r\n    loadCollaborativeStats();\r\n    if (rankingIntervalRef.current) clearInterval(rankingIntervalRef.current);\r\n    rankingIntervalRef.current = setInterval(() => {\r\n      loadVotingRanking();\r\n      loadCollaborativeStats();\r\n    }, 15000); // a cada 15s\r\n\r\n    // Iniciar (ou reiniciar) contador de 5 minutos, se ainda não existir\r\n    if (!nextReorderAt) {\r\n      const target = new Date(Date.now() + 5 * 60 * 1000);\r\n      setNextReorderAt(target);\r\n      startCountdown(target);\r\n    }\r\n\r\n    return () => {\r\n      if (intervalRef.current) {\r\n        clearInterval(intervalRef.current);\r\n      }\r\n      if (retryTimeoutRef.current) {\r\n        clearTimeout(retryTimeoutRef.current);\r\n      }\r\n      if (rankingIntervalRef.current) {\r\n        clearInterval(rankingIntervalRef.current);\r\n      }\r\n      if (countdownIntervalRef.current) {\r\n        clearInterval(countdownIntervalRef.current);\r\n      }\r\n    };\r\n  }, [restaurantId, isConnected]);\r\n\r\n  const loadInitialData = async () => {\r\n    // Tentar carregar do cache primeiro\r\n    const cachedPlayback = PlaybackCache.getCache(`playback_${restaurantId}`);\r\n    const cachedQueue = PlaybackCache.getCache(`queue_${restaurantId}`);\r\n\r\n    if (cachedPlayback) {\r\n      setPlaybackState(cachedPlayback);\r\n  toast(\"Carregando dados em cache...\", { duration: 2000, icon: \"ℹ️\" });\r\n    }\r\n\r\n    if (cachedQueue) {\r\n      setPriorityQueue(cachedQueue.priorityQueue || []);\r\n      setNormalQueue(cachedQueue.normalQueue || []);\r\n    }\r\n\r\n    // Carregar dados atuais\r\n    await Promise.all([loadPlaybackState(), loadQueues()]);\r\n  };\r\n\r\n  // Carregar ranking de votações (Colaborativa)\r\n  const loadVotingRanking = async () => {\r\n    if (!restaurantId) return;\r\n\r\n    try {\r\n      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`, { limit: \"50\" });\r\n      const res = await fetch(url);\r\n      if (!res.ok) throw new Error(`Falha ao carregar ranking (${res.status})`);\r\n      const json = await res.json();\r\n      const items: RankingItem[] = json?.data || [];\r\n\r\n  const coerce = (x: any) => ({ ...x, paymentAmount: Number(x.paymentAmount) || 0, voteCount: Number(x.voteCount) || 0 });\r\n  const normalized = items.map(coerce);\r\n  const paid = normalized.filter((i) => i.isPaid).sort((a, b) => b.voteCount - a.voteCount);\r\n  const free = normalized.filter((i) => !i.isPaid).sort((a, b) => b.voteCount - a.voteCount);\r\n\r\n      setRankingPaid(paid);\r\n      setRankingFree(free);\r\n    } catch (e) {\r\n      console.warn(\"Não foi possível obter ranking colaborativo:\", e);\r\n    }\r\n  };\r\n\r\n  // Carregar estatísticas agregadas (Colaborativa)\r\n  const loadCollaborativeStats = async () => {\r\n    if (!restaurantId) return;\r\n    try {\r\n      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/stats`);\r\n      const res = await fetch(url);\r\n      if (!res.ok) throw new Error(`Falha ao carregar stats (${res.status})`);\r\n      const json = await res.json();\r\n      // Aceitar tanto data quanto root\r\n      setCollabStats(json?.data ?? json ?? null);\r\n    } catch (e) {\r\n      console.warn(\"Não foi possível obter stats colaborativas:\", e);\r\n    }\r\n  };\r\n\r\n  // Recalcular prévia automática e total de votos quando o ranking muda\r\n  useEffect(() => {\r\n    const combined = [...rankingPaid, ...rankingFree].sort((a, b) => {\r\n      // Paga primeiro, depois maior valor pago, depois mais votos\r\n      const paidDiff = Number(b.isPaid) - Number(a.isPaid);\r\n      if (paidDiff !== 0) return paidDiff;\r\n      const payDiff = toNumber(b.paymentAmount) - toNumber(a.paymentAmount);\r\n      if (payDiff !== 0) return payDiff;\r\n      return toNumber(b.voteCount) - toNumber(a.voteCount);\r\n    });\r\n    setAutoPreview(combined);\r\n    setTotalVotes(combined.reduce((sum, it) => sum + toNumber(it.voteCount), 0));\r\n    // Aggregates for header chips\r\n    const totalSuperVotes = combined.reduce((sum, it) => sum + toNumber(it.superVoteCount), 0);\r\n    const totalNormalVotes = combined.reduce((sum, it) => sum + toNumber(it.normalVoteCount), 0);\r\n    setVoteAggregates({\r\n      totalSuperVotes,\r\n      totalNormalVotes,\r\n      paidItems: rankingPaid.length,\r\n      freeItems: rankingFree.length,\r\n    });\r\n  // Compute revenue fallback from paid items\r\n  const revenue = rankingPaid.reduce((sum, it) => sum + toNumber((it as any).paymentAmount ?? 0), 0);\r\n  setComputedRevenue(revenue);\r\n  }, [rankingPaid, rankingFree]);\r\n\r\n  // Iniciar contador regressivo até próxima reordenação\r\n  const startCountdown = (target: Date) => {\r\n    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);\r\n\r\n    const tick = async () => {\r\n      const diff = Math.max(0, Math.floor((target.getTime() - Date.now()) / 1000));\r\n      setCountdown(diff);\r\n      if (diff <= 0) {\r\n        clearInterval(countdownIntervalRef.current as NodeJS.Timeout);\r\n        countdownIntervalRef.current = null;\r\n        if (autoReorder) {\r\n          await handleReorderByVotes();\r\n        }\r\n      }\r\n    };\r\n\r\n    tick();\r\n    countdownIntervalRef.current = setInterval(tick, 1000);\r\n  };\r\n\r\n  // Disparar reordenação no servidor\r\n  const handleReorderByVotes = async () => {\r\n    if (!restaurantId) return;\r\n    try {\r\n      const res = await fetch(buildApiUrl(`/collaborative-playlist/${restaurantId}/reorder`), {\r\n        method: \"POST\",\r\n      });\r\n      if (!res.ok) throw new Error(`Falha ao reordenar (${res.status})`);\r\n      toast.success(\"Playlist reordenada por votos\");\r\n      // Atualizar filas e ranking após reordenar\r\n      await Promise.all([loadQueues(), loadVotingRanking(), loadPlaybackState()]);\r\n    } catch (e) {\r\n      console.error(\"Erro ao reordenar por votos:\", e);\r\n      toast.error(\"Erro ao reordenar por votos\");\r\n    } finally {\r\n      const target = new Date(Date.now() + 5 * 60 * 1000);\r\n      setNextReorderAt(target);\r\n      startCountdown(target);\r\n    }\r\n  };\r\n\r\n  const calculateEstimatedWaitTime = useCallback((queue: Track[]) => {\r\n    return queue.reduce((total, track, index) => {\r\n      if (index === 0) return 0; // Música atual\r\n      return total + (track.duration || 180); // 3min padrão\r\n    }, 0);\r\n  }, []);\r\n\r\n  // ===== Ações de Voto (Admin no Controller) =====\r\n  const voteNormalFromController = async (youtubeVideoId: string) => {\r\n    if (!restaurantId) return;\r\n    try {\r\n      const res = await fetch(\r\n        buildApiUrl(`/collaborative-playlist/${restaurantId}/vote`),\r\n        {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ youtubeVideoId, clientSessionId: \"admin_panel\" }),\r\n        }\r\n      );\r\n      if (!res.ok) {\r\n        const t = await res.text().catch(() => \"\");\r\n        throw new Error(`Erro ao votar: ${res.status} ${t}`);\r\n      }\r\n      toast.success(\"Voto registrado (normal)\");\r\n      await Promise.all([loadVotingRanking(), loadQueues()]);\r\n    } catch (e) {\r\n      console.error(e);\r\n      toast.error(\"Falha ao registrar voto (normal)\");\r\n    }\r\n  };\r\n\r\n  const superVoteFromController = async (\r\n    youtubeVideoId: string,\r\n    paymentAmount: 5 | 20 | 50\r\n  ) => {\r\n    if (!restaurantId) return;\r\n    try {\r\n      const res = await fetch(\r\n        buildApiUrl(`/collaborative-playlist/${restaurantId}/supervote`),\r\n        {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({\r\n            youtubeVideoId,\r\n            paymentAmount,\r\n            paymentId: `admin_test_${Date.now()}`,\r\n            clientSessionId: \"admin_panel\",\r\n          }),\r\n        }\r\n      );\r\n      if (!res.ok) {\r\n        const t = await res.text().catch(() => \"\");\r\n        throw new Error(`Erro no supervoto: ${res.status} ${t}`);\r\n      }\r\n      toast.success(`Supervoto R$ ${paymentAmount} registrado`);\r\n      await Promise.all([loadVotingRanking(), loadQueues()]);\r\n    } catch (e) {\r\n      console.error(e);\r\n      toast.error(\"Falha ao registrar supervoto\");\r\n    }\r\n  };\r\n\r\n  const makeApiCall = async (url: string, options?: RequestInit, retries = 3): Promise<any> => {\r\n    for (let attempt = 0; attempt < retries; attempt++) {\r\n      try {\r\n        const response = await fetch(url, {\r\n          ...options,\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"authToken\")}`,\r\n            ...options?.headers,\r\n          },\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        setRetryCount(0); // Reset retry count on success\r\n        return data;\r\n      } catch (error) {\r\n        console.error(`API call failed (attempt ${attempt + 1}):`, error);\r\n\r\n        if (attempt === retries - 1) {\r\n          setRetryCount((prev) => prev + 1);\r\n          toast.error(`Falha na conexão. Tentativa ${retryCount + 1}`);\r\n          return undefined;\r\n        }\r\n\r\n        // Exponential backoff\r\n        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));\r\n      }\r\n    }\r\n    return undefined;\r\n  };\r\n\r\n  const loadPlaybackState = async () => {\r\n    if (!restaurantId) {\r\n      console.error(\"Restaurant ID não encontrado\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const data = await makeApiCall(buildApiUrl(`/playback/${restaurantId}/state`));\r\n\r\n      if (data?.state) {\r\n        // Ensure createdAt is a Date object\r\n        if (data.state.currentTrack && data.state.currentTrack.createdAt) {\r\n          data.state.currentTrack.createdAt = new Date(data.state.currentTrack.createdAt);\r\n        }\r\n        setPlaybackState(data.state);\r\n        PlaybackCache.setCache(`playback_${restaurantId}`, data.state);\r\n      } else {\r\n        // Estado padrão se não houver dados\r\n        const defaultState: PlaybackState = {\r\n          isPlaying: false,\r\n          currentTrack: null,\r\n          volume: 50,\r\n          currentTime: 0,\r\n          queue: [],\r\n          priorityQueue: [],\r\n          normalQueue: [],\r\n          history: [],\r\n          connectionStatus: isConnected ? \"connected\" : \"disconnected\",\r\n        };\r\n        setPlaybackState(defaultState);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar estado de reprodução:\", error);\r\n\r\n      // Usar cache em caso de erro\r\n      const cached = PlaybackCache.getCache(`playback_${restaurantId}`);\r\n      if (cached) {\r\n        setPlaybackState(cached);\r\n  toast(\"Usando dados em cache devido à falha de conexão\", { icon: \"ℹ️\" });\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n      setLastUpdateTime(new Date());\r\n    }\r\n  };\r\n\r\n  const loadQueues = async () => {\r\n    if (!restaurantId) return;\r\n\r\n    try {\r\n      // 🎯 USAR LIVEPLAYLISTSERVICE COMO PADRÃO\r\n      console.log(\"🎵 Carregando filas via LivePlaylistService...\");\r\n\r\n      // 1. Obter estado da playlist ao vivo\r\n      const livePlaylistData = await makeApiCall(\r\n        buildApiUrl(`/live-playlist/${restaurantId}/state`)\r\n      );\r\n\r\n      if (livePlaylistData?.success && livePlaylistData?.data) {\r\n        const state = livePlaylistData.data;\r\n\r\n        // 2. Buscar sugestões pagas (fila prioritária)\r\n        const paidSuggestions = await makeApiCall(\r\n          buildApiUrl(`/suggestions/${restaurantId}?isPaid=true&status=approved`)\r\n        );\r\n\r\n        // 3. Buscar sugestões gratuitas (fila normal)\r\n        const freeSuggestions = await makeApiCall(\r\n          buildApiUrl(`/suggestions/${restaurantId}?isPaid=false&status=approved`)\r\n        );\r\n\r\n        const priority = (paidSuggestions?.suggestions || []).map((track: any) => ({\r\n          ...track,\r\n          createdAt: new Date(track.createdAt),\r\n          isPaid: true,\r\n        }));\r\n\r\n        const normal = (freeSuggestions?.suggestions || []).map((track: any) => ({\r\n          ...track,\r\n          createdAt: new Date(track.createdAt),\r\n          isPaid: false,\r\n        }));\r\n\r\n        setPriorityQueue(priority);\r\n        setNormalQueue(normal);\r\n\r\n        const estimatedWait = calculateEstimatedWaitTime([...priority, ...normal]);\r\n\r\n        // 4. Usar estatísticas do LivePlaylistService\r\n        setQueueStats({\r\n          totalItems: state.paidSuggestionsCount + state.freeSuggestionsCount,\r\n          paidItems: state.paidSuggestionsCount,\r\n          freeItems: state.freeSuggestionsCount,\r\n          estimatedWaitTime: estimatedWait,\r\n        });\r\n\r\n        PlaybackCache.setCache(`queue_${restaurantId}`, { priorityQueue: priority, normalQueue: normal });\r\n\r\n        console.log(\r\n          `🎵 LivePlaylist: ${state.paidSuggestionsCount} prioritárias, ${state.freeSuggestionsCount} normais, tempo estimado: ${Math.round(\r\n            estimatedWait / 60\r\n          )}min`\r\n        );\r\n\r\n        return; // Sucesso com LivePlaylistService\r\n      }\r\n    } catch (liveError) {\r\n      console.warn(\"⚠️ LivePlaylistService falhou, usando fallback:\", liveError);\r\n    }\r\n\r\n    // 🔄 FALLBACK PARA SISTEMA ANTIGO\r\n    try {\r\n      console.log(\"🔄 Usando sistema de filas antigo como fallback...\");\r\n      const data = await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}`));\r\n\r\n      // Separar filas por tipo de pagamento\r\n      const allQueue = (data.queue || []).map((track: any) => ({\r\n        ...track,\r\n        createdAt: new Date(track.createdAt), // Ensure createdAt is a Date\r\n      }));\r\n      const priority = allQueue.filter((track: any) => track.isPaid);\r\n      const normal = allQueue.filter((track: any) => !track.isPaid);\r\n\r\n      setPriorityQueue(priority);\r\n      setNormalQueue(normal);\r\n\r\n      const estimatedWait = calculateEstimatedWaitTime(allQueue);\r\n\r\n      setQueueStats({\r\n        totalItems: allQueue.length,\r\n        paidItems: priority.length,\r\n        freeItems: normal.length,\r\n        estimatedWaitTime: estimatedWait,\r\n      });\r\n\r\n      PlaybackCache.setCache(`queue_${restaurantId}`, { priorityQueue: priority, normalQueue: normal });\r\n\r\n      console.log(\r\n        `🎵 Fallback: ${priority.length} prioritárias, ${normal.length} normais, tempo estimado: ${Math.round(\r\n          estimatedWait / 60\r\n        )}min`\r\n      );\r\n    } catch (error) {\r\n      console.error(\"❌ Erro ao carregar filas (fallback também falhou):\", error);\r\n\r\n      // Usar cache em caso de erro\r\n      const cached = PlaybackCache.getCache(`queue_${restaurantId}`);\r\n      if (cached) {\r\n        setPriorityQueue(cached.priorityQueue || []);\r\n        setNormalQueue(cached.normalQueue || []);\r\n  toast(\"Usando dados de fila em cache\", { icon: \"ℹ️\" });\r\n      }\r\n    }\r\n  };\r\n\r\n  // ===== Helpers e Ações somente Front =====\r\n  const extractYouTubeId = (input: string): string | null => {\r\n    try {\r\n      if (!input) return null;\r\n      // Caso já seja um ID simples\r\n      if (/^[a-zA-Z0-9_-]{6,}$/.test(input)) return input;\r\n      const url = new URL(input);\r\n      if (url.hostname.includes(\"youtu.be\")) {\r\n        return url.pathname.replace(\"/\", \"\");\r\n      }\r\n      if (url.hostname.includes(\"youtube.com\")) {\r\n        return url.searchParams.get(\"v\");\r\n      }\r\n      return null;\r\n    } catch {\r\n      // Não é URL, talvez seja um ID simples\r\n      return /^[a-zA-Z0-9_-]{6,}$/.test(input) ? input : null;\r\n    }\r\n  };\r\n\r\n  const handleManualAdd = (toPaid: boolean) => {\r\n    const id = extractYouTubeId(manualVideoInput.trim());\r\n    if (!id) {\r\n      toast.error(\"Informe um link ou ID válido do YouTube\");\r\n      return;\r\n    }\r\n    const newTrack: Track = {\r\n      id: `local_${Date.now()}`,\r\n      youtubeVideoId: id,\r\n      title: `Música (${id})`,\r\n      artist: \"Artista Desconhecido\",\r\n      duration: 180,\r\n      thumbnailUrl: `https://img.youtube.com/vi/${id}/mqdefault.jpg`,\r\n      upvotes: 0,\r\n      downvotes: 0,\r\n      score: 0,\r\n      createdAt: new Date(),\r\n    };\r\n    if (toPaid) {\r\n      setPriorityQueue((prev) => [newTrack, ...prev]);\r\n      toast.success(\"Adicionada à fila prioritária (somente UI)\");\r\n    } else {\r\n      setNormalQueue((prev) => [newTrack, ...prev]);\r\n      toast.success(\"Adicionada à fila normal (somente UI)\");\r\n    }\r\n    setManualVideoInput(\"\");\r\n  };\r\n\r\n  const handleClearQueues = () => {\r\n    setPriorityQueue([]);\r\n    setNormalQueue([]);\r\n    setQueueStats((s) => ({ ...s, totalItems: 0, paidItems: 0, freeItems: 0 }));\r\n    toast.success(\"Filas limpas (somente UI)\");\r\n  };\r\n\r\n  const handleMoveInQueue = (\r\n    which: \"priority\" | \"normal\",\r\n    trackId: string,\r\n    direction: \"up\" | \"down\"\r\n  ) => {\r\n    const list = which === \"priority\" ? [...priorityQueue] : [...normalQueue];\r\n    const index = list.findIndex((t) => t.id === trackId);\r\n    if (index < 0) return;\r\n    const targetIndex = direction === \"up\" ? index - 1 : index + 1;\r\n    if (targetIndex < 0 || targetIndex >= list.length) return;\r\n    const temp = list[targetIndex];\r\n    list[targetIndex] = list[index];\r\n    list[index] = temp;\r\n    if (which === \"priority\") setPriorityQueue(list);\r\n    else setNormalQueue(list);\r\n  };\r\n\r\n  const handleDemoteToNormal = (trackId: string) => {\r\n    const idx = priorityQueue.findIndex((t) => t.id === trackId);\r\n    if (idx >= 0) {\r\n      const item = priorityQueue[idx];\r\n      const nextPrio = [...priorityQueue];\r\n      nextPrio.splice(idx, 1);\r\n      setPriorityQueue(nextPrio);\r\n      setNormalQueue((prev) => [item, ...prev]);\r\n      toast(\"Movida para fila normal (somente UI)\");\r\n    }\r\n  };\r\n\r\n  const toggleLocal = (key: keyof typeof localSettings) => {\r\n    setLocalSettings((prev) => ({ ...prev, [key]: !prev[key] }));\r\n  };\r\n\r\n  const handlePlayPause = async () => {\r\n    if (!playbackState || !restaurantId) return;\r\n\r\n    setActionLoading(\"playpause\");\r\n\r\n    try {\r\n      const endpoint = playbackState.isPlaying ? \"pause\" : \"resume\";\r\n      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/${endpoint}`), {\r\n        method: \"POST\",\r\n      });\r\n\r\n      const newState = { ...playbackState, isPlaying: !playbackState.isPlaying };\r\n      setPlaybackState(newState);\r\n      PlaybackCache.setCache(`playback_${restaurantId}`, newState);\r\n\r\n      toast.success(playbackState.isPlaying ? \"Reprodução pausada\" : \"Reprodução retomada\");\r\n\r\n      // Registro de analytics\r\n      logUserAction(\"playback_toggle\", {\r\n        action: endpoint,\r\n        trackId: playbackState.currentTrack?.id,\r\n      });\r\n    } catch (error) {\r\n      toast.error(\"Erro ao controlar reprodução\");\r\n    } finally {\r\n      setActionLoading(null);\r\n    }\r\n  };\r\n\r\n  const handleSkip = async () => {\r\n    if (!restaurantId) return;\r\n\r\n    setActionLoading(\"skip\");\r\n\r\n    try {\r\n      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/skip`), { method: \"POST\" });\r\n\r\n      toast.success(\"Música pulada\");\r\n\r\n      // Registro de analytics\r\n      logUserAction(\"track_skip\", {\r\n        trackId: playbackState?.currentTrack?.id,\r\n        skipTime: playbackState?.currentTime,\r\n      });\r\n\r\n      // Recarregar estado após skip\r\n      await loadPlaybackState();\r\n      await loadQueues();\r\n    } catch (error) {\r\n      toast.error(\"Erro ao pular música\");\r\n    } finally {\r\n      setActionLoading(null);\r\n    }\r\n  };\r\n\r\n  const handleStartNext = async () => {\r\n    if (!restaurantId) return;\r\n\r\n    setActionLoading(\"start\");\r\n\r\n    // Lógica de prioridade: fila paga > fila normal\r\n    let nextTrack = null;\r\n    let queueType = \"\";\r\n\r\n    if (priorityQueue.length > 0) {\r\n      nextTrack = priorityQueue[0];\r\n      queueType = \"priority\";\r\n      console.log(`🔥 Tocando da fila prioritária: ${nextTrack.title}`);\r\n      toast.success(`Tocando da fila prioritária: ${nextTrack.title}`);\r\n    } else if (normalQueue.length > 0) {\r\n      // Ordenar fila normal por votos\r\n      const sortedNormal = [...normalQueue].sort(\r\n        (a, b) => b.upvotes - b.downvotes - (a.upvotes - a.downvotes)\r\n      );\r\n      nextTrack = sortedNormal[0];\r\n      queueType = \"normal\";\r\n      console.log(`📋 Tocando da fila normal: ${nextTrack.title}`);\r\n      toast.success(`Tocando da fila normal: ${nextTrack.title}`);\r\n    }\r\n\r\n    if (!nextTrack) {\r\n      toast.error(\"Nenhuma música na fila\");\r\n      setActionLoading(null);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/play`), {\r\n        method: \"POST\",\r\n        body: JSON.stringify({ songId: nextTrack.id }),\r\n      });\r\n\r\n      toast.success(`Tocando: ${nextTrack.title}`);\r\n\r\n      // Registro de analytics\r\n      logUserAction(\"track_start\", {\r\n        trackId: nextTrack.id,\r\n        queueType,\r\n        trackTitle: nextTrack.title,\r\n      });\r\n\r\n      // Recarregar dados\r\n      await Promise.all([loadPlaybackState(), loadQueues()]);\r\n    } catch (error) {\r\n      console.error(\"Erro ao iniciar próxima música:\", error);\r\n      toast.error(\"Erro ao iniciar próxima música\");\r\n    } finally {\r\n      setActionLoading(null);\r\n    }\r\n  };\r\n\r\n  const handleVolumeChange = async (newVolume: number) => {\r\n    if (!restaurantId || !playbackState) return;\r\n\r\n    try {\r\n      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/volume`), {\r\n        method: \"POST\",\r\n        body: JSON.stringify({ volume: newVolume }),\r\n      });\r\n\r\n      const newState = { ...playbackState, volume: newVolume };\r\n      setPlaybackState(newState);\r\n      PlaybackCache.setCache(`playback_${restaurantId}`, newState);\r\n\r\n      // Registro de analytics\r\n      logUserAction(\"volume_change\", {\r\n        previousVolume: playbackState.volume,\r\n        newVolume,\r\n      });\r\n    } catch (error) {\r\n      toast.error(\"Erro ao ajustar volume\");\r\n    }\r\n  };\r\n\r\n  const toggleMute = () => {\r\n    if (!playbackState) return;\r\n\r\n    const newVolume = playbackState.volume === 0 ? 50 : 0;\r\n    handleVolumeChange(newVolume);\r\n\r\n    // Registro de analytics\r\n    logUserAction(\"volume_mute_toggle\", {\r\n      action: newVolume === 0 ? \"mute\" : \"unmute\",\r\n    });\r\n  };\r\n\r\n  const handleRemoveFromQueue = async (trackId: string) => {\r\n    if (!restaurantId) return;\r\n\r\n    setActionLoading(`remove-priority-${trackId}`);\r\n\r\n    try {\r\n      await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}/remove`), {\r\n        method: \"POST\",\r\n        body: JSON.stringify({ trackId }),\r\n      });\r\n\r\n      toast.success(\"Música removida da fila\");\r\n\r\n      // Registro de analytics\r\n      logUserAction(\"track_remove\", { trackId });\r\n\r\n      // Recarregar filas\r\n      await loadQueues();\r\n    } catch (error) {\r\n      toast.error(\"Erro ao remover música da fila\");\r\n    } finally {\r\n      setActionLoading(null);\r\n    }\r\n  };\r\n\r\n  const handlePromoteTrack = async (trackId: string) => {\r\n    if (!restaurantId) return;\r\n\r\n    setActionLoading(`promote-${trackId}`);\r\n\r\n    try {\r\n      await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}/promote`), {\r\n        method: \"POST\",\r\n        body: JSON.stringify({ trackId }),\r\n      });\r\n\r\n      toast.success(\"Música promovida para fila prioritária\");\r\n\r\n      // Registro de analytics\r\n      logUserAction(\"track_promote\", { trackId });\r\n\r\n      // Recarregar filas\r\n      await loadQueues();\r\n    } catch (error) {\r\n      toast.error(\"Erro ao promover música\");\r\n    } finally {\r\n      setActionLoading(null);\r\n    }\r\n  };\r\n\r\n  // Função para registrar ações do usuário para analytics\r\n  const logUserAction = (action: string, data: any) => {\r\n    try {\r\n      // Enviar para analytics (implementar conforme necessário)\r\n      console.log(`📊 Analytics: ${action}`, data);\r\n\r\n      // Opcional: salvar no localStorage para análise posterior\r\n      const analyticsData = {\r\n        timestamp: new Date().toISOString(),\r\n        restaurantId,\r\n        action,\r\n        data,\r\n      };\r\n\r\n      const existingLogs = JSON.parse(localStorage.getItem(\"playback_analytics\") || \"[]\");\r\n      existingLogs.push(analyticsData);\r\n\r\n      // Manter apenas os últimos 100 registros\r\n      if (existingLogs.length > 100) {\r\n        existingLogs.splice(0, existingLogs.length - 100);\r\n      }\r\n\r\n      localStorage.setItem(\"playback_analytics\", JSON.stringify(existingLogs));\r\n    } catch (error) {\r\n      console.error(\"Erro ao registrar analytics:\", error);\r\n    }\r\n  };\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, \"0\")}`;\r\n  };\r\n\r\n  const formatEstimatedTime = (seconds: number) => {\r\n    if (seconds < 60) return `${seconds}s`;\r\n    if (seconds < 3600) return `${Math.round(seconds / 60)}min`;\r\n    return `${Math.round(seconds / 3600)}h`;\r\n  };\r\n\r\n  // Helpers para valores monetários (evita erro de toFixed quando vier string/null)\r\n  const toNumber = (v: any): number => {\r\n    const n = Number(v);\r\n    return Number.isFinite(n) ? n : 0;\r\n  };\r\n  const formatBRL = (v: any): string =>\r\n    toNumber(v).toLocaleString(\"pt-BR\", { minimumFractionDigits: 2, maximumFractionDigits: 2 });\r\n\r\n  const getProgressPercentage = () => {\r\n    if (!playbackState?.currentTrack) return 0;\r\n    return (playbackState.currentTime / playbackState.currentTrack.duration) * 100;\r\n  };\r\n\r\n  // Conexão: não exibir status visual (solicitado para remover da UI)\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <div className=\"flex justify-center items-center h-32\">\r\n          <RefreshCw className=\"w-8 h-8 animate-spin text-blue-600\" />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n  <Toaster position=\"top-left\" />\r\n\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Controle da Playlist (Colaborativa)</h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">Gerencie a reprodução e acompanhe votos, supervotos e reordenações automáticas.</p>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <label className=\"flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-3 py-1.5 rounded-lg\">\r\n            <input type=\"checkbox\" checked={autoReorder} onChange={(e) => setAutoReorder(e.target.checked)} />\r\n            Auto-reordenar\r\n          </label>\r\n          <button\r\n            onClick={handleReorderByVotes}\r\n            className=\"px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm\"\r\n          >\r\n            Reordenar agora\r\n          </button>\r\n          <button\r\n            onClick={loadInitialData}\r\n            disabled={actionLoading === \"refresh\"}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\r\n            aria-label=\"Atualizar dados de reprodução\"\r\n          >\r\n            {actionLoading === \"refresh\" ? <Loader2 className=\"w-4 h-4 animate-spin\" /> : <RefreshCw className=\"w-4 h-4\" />}\r\n            Atualizar\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Próxima reordenação</p>\r\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{formatEstimatedTime(countdown)}</p>\r\n            </div>\r\n            <RefreshCw className=\"w-7 h-7 text-indigo-500\" />\r\n          </div>\r\n        </div>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total de votos</p>\r\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{collabStats?.totalVotes ?? totalVotes}</p>\r\n            </div>\r\n            <TrendingUp className=\"w-7 h-7 text-green-500\" />\r\n          </div>\r\n        </div>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Itens pagos</p>\r\n              <p className=\"text-2xl font-bold text-yellow-700 dark:text-yellow-300\">{voteAggregates.paidItems}</p>\r\n            </div>\r\n            <Users className=\"w-7 h-7 text-yellow-500\" />\r\n          </div>\r\n        </div>\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Itens grátis</p>\r\n              <p className=\"text-2xl font-bold text-green-700 dark:text-green-300\">{voteAggregates.freeItems}</p>\r\n            </div>\r\n            <Users className=\"w-7 h-7 text-green-500\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Receita (link para Analytics) */}\r\n      <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/10 rounded-lg p-4 border border-indigo-200 dark:border-indigo-700 flex items-center justify-between\">\r\n        <div>\r\n          <p className=\"text-sm text-indigo-700 dark:text-indigo-300\">Receita total acumulada</p>\r\n          <p className=\"text-2xl font-bold text-indigo-900 dark:text-indigo-100\">R$ {formatBRL(collabStats?.totalRevenue ?? computedRevenue)}</p>\r\n        </div>\r\n        <a\r\n          href={`/restaurant/${restaurantId}/dashboard/analytics`}\r\n          className=\"inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm\"\r\n        >\r\n          <ExternalLink className=\"w-4 h-4\" /> Ver Analytics\r\n        </a>\r\n      </div>\r\n\r\n      {/* Player Principal */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        {playbackState?.currentTrack ? (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            className=\"space-y-4\"\r\n          >\r\n            {/* Informações da música atual */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              <img\r\n                src={playbackState.currentTrack.thumbnailUrl}\r\n                alt={playbackState.currentTrack.title}\r\n                className=\"w-16 h-12 object-cover rounded-lg shadow-sm\"\r\n              />\r\n              <div className=\"flex-1\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\r\n                  {playbackState.currentTrack.title}\r\n                </h3>\r\n                <p className=\"text-gray-600 dark:text-gray-400\">\r\n                  {playbackState.currentTrack.artist}\r\n                </p>\r\n                <div className=\"flex items-center space-x-4 mt-1\">\r\n                  <div className=\"flex items-center space-x-1 text-green-600\">\r\n                    <TrendingUp className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                    <span className=\"text-sm\">{playbackState.currentTrack.upvotes}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1 text-red-600\">\r\n                    <AlertTriangle className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                    <span className=\"text-sm\">{playbackState.currentTrack.downvotes}</span>\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-500\">\r\n                    Score: {playbackState.currentTrack.score}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Barra de progresso */}\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex justify-between text-sm text-gray-500\">\r\n                <span>{formatTime(playbackState.currentTime)}</span>\r\n                <span>{formatTime(playbackState.currentTrack.duration)}</span>\r\n              </div>\r\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n                <motion.div\r\n                  className=\"bg-blue-600 h-2 rounded-full\"\r\n                  initial={{ width: 0 }}\r\n                  animate={{ width: `${getProgressPercentage()}%` }}\r\n                  transition={{ duration: 1 }}\r\n                  role=\"progressbar\"\r\n                  aria-valuenow={getProgressPercentage()}\r\n                  aria-valuemin={0}\r\n                  aria-valuemax={100}\r\n                  aria-label=\"Progresso da música\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Controles */}\r\n            <div className=\"flex items-center justify-center space-x-4\">\r\n              <button\r\n                onClick={handlePlayPause}\r\n                disabled={actionLoading === \"playpause\"}\r\n                className=\"flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                aria-label={playbackState.isPlaying ? \"Pausar\" : \"Reproduzir\"}\r\n              >\r\n                {actionLoading === \"playpause\" ? (\r\n                  <Loader2 className=\"w-6 h-6 animate-spin\" />\r\n                ) : playbackState.isPlaying ? (\r\n                  <Pause className=\"w-6 h-6\" />\r\n                ) : (\r\n                  <Play className=\"w-6 h-6 ml-1\" />\r\n                )}\r\n              </button>\r\n\r\n              <button\r\n                onClick={handleSkip}\r\n                disabled={actionLoading === \"skip\"}\r\n                className=\"flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                aria-label=\"Pular música\"\r\n              >\r\n                {actionLoading === \"skip\" ? (\r\n                  <Loader2 className=\"w-5 h-5 animate-spin\" />\r\n                ) : (\r\n                  <SkipForward className=\"w-5 h-5\" />\r\n                )}\r\n              </button>\r\n\r\n              {/* Controle de volume */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                <button\r\n                  onClick={toggleMute}\r\n                  aria-label={playbackState.volume === 0 ? \"Ativar som\" : \"Silenciar\"}\r\n                >\r\n                  {playbackState.volume === 0 ? (\r\n                    <VolumeX className=\"w-5 h-5 text-gray-600\" />\r\n                  ) : (\r\n                    <Volume2 className=\"w-5 h-5 text-gray-600\" />\r\n                  )}\r\n                </button>\r\n                <input\r\n                  type=\"range\"\r\n                  min=\"0\"\r\n                  max=\"100\"\r\n                  value={playbackState.volume}\r\n                  onChange={(e) => handleVolumeChange(parseInt(e.target.value))}\r\n                  className=\"w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\r\n                  aria-label=\"Controle de volume\"\r\n                />\r\n                <span className=\"text-sm text-gray-500 w-8\">{playbackState.volume}</span>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        ) : (\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-start gap-4\">\r\n              <div className=\"w-16 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg\" />\r\n              <div className=\"flex-1\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Sem reprodução</h3>\r\n                <p className=\"text-gray-600 dark:text-gray-400 text-sm\">Quando a próxima música começar, os controles ficam ativos.</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Ações Rápidas (somente UI) */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <div className=\"flex items-center gap-2 text-gray-800 dark:text-gray-200\">\r\n                  <Settings2 className=\"w-4 h-4\" />\r\n                  <span className=\"font-medium text-sm\">Ações Rápidas</span>\r\n                </div>\r\n                <button onClick={handleClearQueues} className=\"flex items-center gap-1 text-red-600 hover:text-red-700 text-sm\">\r\n                  <Trash2 className=\"w-4 h-4\" /> Limpar Filas\r\n                </button>\r\n              </div>\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                <button onClick={loadInitialData} className=\"px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\">Atualizar</button>\r\n                <button onClick={handleReorderByVotes} className=\"px-3 py-1.5 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm\">Reordenar por Votos</button>\r\n                <button onClick={() => toggleLocal('shuffle')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${localSettings.shuffle ? 'bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}>\r\n                  <Shuffle className=\"w-4 h-4\" /> Shuffle\r\n                </button>\r\n                <button onClick={() => toggleLocal('repeat')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${localSettings.repeat ? 'bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}>\r\n                  <Repeat className=\"w-4 h-4\" /> Repeat\r\n                </button>\r\n                <button onClick={() => toggleLocal('lockVoting')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${localSettings.lockVoting ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}>\r\n                  {localSettings.lockVoting ? <Lock className=\"w-4 h-4\" /> : <Unlock className=\"w-4 h-4\" />} {localSettings.lockVoting ? 'Votação Travada' : 'Votação Liberada'}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Adicionar Manualmente (somente UI) */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-3\">\r\n                <ExternalLink className=\"w-4 h-4\" />\r\n                <span className=\"font-medium text-sm\">Adicionar música manualmente</span>\r\n              </div>\r\n              <div className=\"flex flex-col sm:flex-row gap-2\">\r\n                <input\r\n                  value={manualVideoInput}\r\n                  onChange={(e) => setManualVideoInput(e.target.value)}\r\n                  placeholder=\"Cole o link ou ID do YouTube\"\r\n                  className=\"flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\r\n                />\r\n                <div className=\"flex gap-2\">\r\n                  <button onClick={() => handleManualAdd(false)} className=\"px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm\">Adicionar na Fila Normal</button>\r\n                  <button onClick={() => handleManualAdd(true)} className=\"px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm\">Adicionar na Fila Prioritária</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Estatísticas das Filas + Histórico de Reordenação */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\r\n        {/* Estatísticas */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total na Fila</p>\r\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n                {queueStats.totalItems}\r\n              </p>\r\n            </div>\r\n            <Music className=\"w-8 h-8 text-blue-500\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-yellow-200 dark:border-yellow-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-yellow-600 dark:text-yellow-400\">Fila Prioritária</p>\r\n              <p className=\"text-2xl font-bold text-yellow-700 dark:text-yellow-300\">\r\n                {queueStats.paidItems}\r\n              </p>\r\n            </div>\r\n            <TrendingUp className=\"w-8 h-8 text-yellow-500\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-green-200 dark:border-green-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm text-green-600 dark:text-green-400\">Fila Normal</p>\r\n              <p className=\"text-2xl font-bold text-green-700 dark:text-green-300\">\r\n                {queueStats.freeItems}\r\n              </p>\r\n            </div>\r\n            <Users className=\"w-8 h-8 text-green-500\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Histórico de Reordenação */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-indigo-200 dark:border-indigo-700 mb-6\">\r\n        <div className=\"flex items-center justify-between mb-3\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Histórico de Reordenação</h3>\r\n          <span className=\"text-xs px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200\">\r\n            {reorderHistory.length} eventos\r\n          </span>\r\n        </div>\r\n        {reorderHistory.length === 0 ? (\r\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Nenhum evento de reordenação ainda</div>\r\n        ) : (\r\n          <div className=\"space-y-2 max-h-64 overflow-y-auto\">\r\n            {reorderHistory.map((evt, idx) => (\r\n              <div key={idx} className=\"p-3 rounded border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20\">\r\n                <div className=\"flex items-center justify-between text-sm\">\r\n                  <div className=\"font-medium text-gray-800 dark:text-gray-200\">\r\n                    {new Date(evt.time).toLocaleString(\"pt-BR\")} • {evt.playlistName || \"Playlist\"}\r\n                  </div>\r\n                  <div className=\"text-xs text-indigo-700 dark:text-indigo-300\">{evt.count} músicas impactadas</div>\r\n                </div>\r\n                <div className=\"mt-2 text-xs text-gray-700 dark:text-gray-300\">\r\n                  Top 5:\r\n                  <ul className=\"list-disc list-inside\">\r\n                    {(evt.details || []).map((t: any, i: number) => (\r\n                      <li key={i} className=\"truncate\">\r\n                        {t.title || t.videoId} {t.isPaid ? \"(Paga)\" : \"\"} — votos: {t.voteCount ?? \"?\"}\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Filas Separadas */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Fila Prioritária */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-yellow-200 dark:border-yellow-700\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\r\n              🔥 Fila Prioritária ({priorityQueue.length})\r\n            </h3>\r\n            <div className=\"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium\">\r\n              SuperVoto (R$ 5, 20, 50)\r\n            </div>\r\n          </div>\r\n          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"space-y-3\">\r\n                {[...Array(3)].map((_, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: i * 0.1 }}\r\n                    className=\"flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg animate-pulse\"\r\n                  >\r\n                    <div className=\"w-8 h-8 bg-yellow-200 dark:bg-yellow-700 rounded-full\" />\r\n                    <div className=\"flex-1 space-y-2\">\r\n                      <div className=\"h-4 bg-yellow-200 dark:bg-yellow-700 rounded w-3/4\" />\r\n                      <div className=\"h-3 bg-yellow-200 dark:bg-yellow-700 rounded w-1/2\" />\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            ) : priorityQueue.length > 0 ? (\r\n              <AnimatePresence>\r\n                {priorityQueue.map((track, index) => (\r\n                  <motion.div\r\n                    key={track.id}\r\n                    layout\r\n                    initial={{ opacity: 0, x: -20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 20 }}\r\n                    transition={{ duration: 0.2 }}\r\n                    className=\"flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700\"\r\n                  >\r\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-xs flex items-center justify-center font-bold\">\r\n                      {index + 1}\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\r\n                        {track.title}\r\n                      </h4>\r\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400 truncate\">\r\n                        {track.artist}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <div className=\"text-xs text-gray-500\">{formatTime(track.duration)}</div>\r\n                      <button\r\n                        onClick={() => handleMoveInQueue('priority', track.id, 'up')}\r\n                        className=\"p-1 text-gray-600 hover:text-gray-800\"\r\n                        title=\"Mover para cima\"\r\n                        aria-label=\"Mover para cima\"\r\n                      >\r\n                        <ArrowUp className=\"w-3 h-3\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleMoveInQueue('priority', track.id, 'down')}\r\n                        className=\"p-1 text-gray-600 hover:text-gray-800\"\r\n                        title=\"Mover para baixo\"\r\n                        aria-label=\"Mover para baixo\"\r\n                      >\r\n                        <ArrowDown className=\"w-3 h-3\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDemoteToNormal(track.id)}\r\n                        className=\"p-1 text-blue-600 hover:text-blue-800\"\r\n                        title=\"Mover para fila normal\"\r\n                        aria-label=\"Mover para fila normal\"\r\n                      >\r\n                        <ChevronUp className=\"w-3 h-3 rotate-180\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleRemoveFromQueue(track.id)}\r\n                        disabled={actionLoading === `remove-priority-${track.id}`}\r\n                        className=\"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        aria-label=\"Remover da fila prioritária\"\r\n                        title=\"Remover\"\r\n                      >\r\n                        {actionLoading === `remove-priority-${track.id}` ? (\r\n                          <Loader2 className=\"w-3 h-3 animate-spin\" />\r\n                        ) : (\r\n                          <X className=\"w-3 h-3\" />\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </AnimatePresence>\r\n            ) : (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"text-center py-8\"\r\n              >\r\n                <div className=\"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <TrendingUp className=\"w-6 h-6 text-yellow-600\" />\r\n                </div>\r\n                <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\r\n                  Nenhuma música na fila prioritária\r\n                </p>\r\n              </motion.div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Fila Normal */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-green-200 dark:border-green-700\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\r\n              📋 Fila Normal ({normalQueue.length})\r\n            </h3>\r\n            <div className=\"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs font-medium\">\r\n              Gratuito\r\n            </div>\r\n          </div>\r\n          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"space-y-3\">\r\n                {[...Array(5)].map((_, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: i * 0.1 }}\r\n                    className=\"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg animate-pulse\"\r\n                  >\r\n                    <div className=\"w-6 h-6 bg-green-200 dark:bg-green-700 rounded\" />\r\n                    <div className=\"flex-1 space-y-2\">\r\n                      <div className=\"h-4 bg-green-200 dark:bg-green-700 rounded w-3/4\" />\r\n                      <div className=\"h-3 bg-green-200 dark:bg-green-700 rounded w-1/2\" />\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            ) : normalQueue.length > 0 ? (\r\n              <AnimatePresence>\r\n                {normalQueue\r\n                  .slice()\r\n                  .sort((a, b) => b.upvotes - b.downvotes - (a.upvotes - a.downvotes))\r\n                  .map((track, index) => (\r\n                    <motion.div\r\n                      key={track.id}\r\n                      layout\r\n                      initial={{ opacity: 0, x: -20 }}\r\n                      animate={{ opacity: 1, x: 0 }}\r\n                      exit={{ opacity: 0, x: 20 }}\r\n                      transition={{ duration: 0.2 }}\r\n                      className=\"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700\"\r\n                    >\r\n                      <div className=\"w-6 h-6 bg-green-600 text-white rounded text-xs flex items-center justify-center font-medium\">\r\n                        {index + 1}\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\r\n                          {track.title}\r\n                        </h4>\r\n                        <p className=\"text-xs text-gray-600 dark:text-gray-400 truncate\">\r\n                          {track.artist}\r\n                        </p>\r\n                        <div className=\"flex items-center space-x-3 mt-1\">\r\n                          <div className=\"flex items-center space-x-1\">\r\n                            <TrendingUp className=\"w-3 h-3 text-green-600\" aria-hidden=\"true\" />\r\n                            <span className=\"text-xs text-green-600\">{track.upvotes}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-1\">\r\n                            <AlertTriangle className=\"w-3 h-3 text-red-600\" aria-hidden=\"true\" />\r\n                            <span className=\"text-xs text-red-600\">{track.downvotes}</span>\r\n                          </div>\r\n                          <div className=\"text-xs text-gray-500\">\r\n                            Score: {track.score || track.upvotes - track.downvotes}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <div className=\"text-xs text-gray-500\">{formatTime(track.duration)}</div>\r\n                        <button\r\n                          onClick={() => handleMoveInQueue('normal', track.id, 'up')}\r\n                          className=\"p-1 text-gray-600 hover:text-gray-800\"\r\n                          title=\"Mover para cima\"\r\n                          aria-label=\"Mover para cima\"\r\n                        >\r\n                          <ArrowUp className=\"w-3 h-3\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleMoveInQueue('normal', track.id, 'down')}\r\n                          className=\"p-1 text-gray-600 hover:text-gray-800\"\r\n                          title=\"Mover para baixo\"\r\n                          aria-label=\"Mover para baixo\"\r\n                        >\r\n                          <ArrowDown className=\"w-3 h-3\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handlePromoteTrack(track.id)}\r\n                          disabled={actionLoading === `promote-${track.id}`}\r\n                          className=\"p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                          aria-label=\"Promover para fila prioritária\"\r\n                          title=\"Promover\"\r\n                        >\r\n                          {actionLoading === `promote-${track.id}` ? (\r\n                            <Loader2 className=\"w-3 h-3 animate-spin\" />\r\n                          ) : (\r\n                            <ChevronUp className=\"w-3 h-3\" />\r\n                          )}\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleRemoveFromQueue(track.id)}\r\n                          disabled={actionLoading === `remove-normal-${track.id}`}\r\n                          className=\"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                          aria-label=\"Remover da fila normal\"\r\n                          title=\"Remover\"\r\n                        >\r\n                          {actionLoading === `remove-normal-${track.id}` ? (\r\n                            <Loader2 className=\"w-3 h-3 animate-spin\" />\r\n                          ) : (\r\n                            <X className=\"w-3 h-3\" />\r\n                          )}\r\n                        </button>\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n              </AnimatePresence>\r\n            ) : (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"text-center py-8\"\r\n              >\r\n                <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <Users className=\"w-6 h-6 text-green-600\" />\r\n                </div>\r\n                <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\r\n                  Nenhuma música na fila normal\r\n                </p>\r\n              </motion.div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Analytics Tracking */}\r\n      <motion.div \r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.8 }}\r\n        className=\"mt-6\"\r\n      >\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Activity className=\"w-4 h-4 text-blue-500\" />\r\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Sistema ativo - Última atualização: {new Date().toLocaleTimeString('pt-BR')}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-1\">\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\r\n              <span className=\"text-xs text-green-600 dark:text-green-400\">Online</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Votações (Colaborativa) */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Votações (Colaborativa)</h3>\r\n            <span className=\"inline-flex items-center gap-1 text-xs px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200\">\r\n              <RefreshCw className=\"w-3 h-3\" /> {formatEstimatedTime(countdown)}\r\n            </span>\r\n          </div>\r\n          <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\r\n            Reordenação automática baseada em votos\r\n          </p>\r\n\r\n          {/* Mini-cards grid */}\r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 mt-3\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-xs text-gray-600 dark:text-gray-400\">Próx. reordenação</span>\r\n                <Clock className=\"w-4 h-4 text-indigo-500\" />\r\n              </div>\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">{formatEstimatedTime(countdown)}</div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-xs text-gray-600 dark:text-gray-400\">Total de votos</span>\r\n                <ThumbsUp className=\"w-4 h-4 text-green-500\" />\r\n              </div>\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">{collabStats?.totalVotes ?? totalVotes}</div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-xs text-gray-600 dark:text-gray-400\">Receita total</span>\r\n                <DollarSign className=\"w-4 h-4 text-yellow-500\" />\r\n              </div>\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">R$ {formatBRL(collabStats?.totalRevenue ?? computedRevenue)}</div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-xs text-gray-600 dark:text-gray-400\">Pagas/Grátis</span>\r\n                <Gift className=\"w-4 h-4 text-purple-500\" />\r\n              </div>\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">{voteAggregates.paidItems}/{voteAggregates.freeItems}</div>\r\n              <div className=\"mt-1 text-xs text-gray-600 dark:text-gray-400\">SV: {voteAggregates.totalSuperVotes} • VN: {voteAggregates.totalNormalVotes}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n          {/* SuperVotos (Pagas) */}\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-yellow-200 dark:border-yellow-700\">\r\n            <div className=\"flex items-center justify-between mb-3\">\r\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">SuperVotos (Pagas)</h4>\r\n              <span className=\"text-xs px-2 py-1 rounded bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200\">\r\n                {rankingPaid.length} itens\r\n              </span>\r\n            </div>\r\n            <div className=\"space-y-2 max-h-72 overflow-y-auto\">\r\n              {rankingPaid.length === 0 ? (\r\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Nenhum supervoto no momento</div>\r\n              ) : (\r\n                rankingPaid.map((item, idx) => (\r\n                  <div key={item.youtubeVideoId + idx} className=\"flex items-center gap-3 p-3 rounded border border-yellow-200 dark:border-yellow-700 bg-yellow-50/60 dark:bg-yellow-900/20\">\r\n                    <div className=\"w-7 h-7 bg-yellow-500 text-white rounded flex items-center justify-center text-xs font-bold\">{idx + 1}</div>\r\n                    <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className=\"w-10 h-8 object-cover rounded\" />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">{item.title || item.youtubeVideoId}</div>\r\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 truncate\">{item.artist || \"—\"}</div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <div className=\"text-xs text-gray-700 dark:text-gray-300 mr-1\">Votos: {item.voteCount}</div>\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <button\r\n                          onClick={() => voteNormalFromController(item.youtubeVideoId)}\r\n                          className=\"px-2 py-0.5 border border-gray-300 dark:border-gray-600 rounded text-xs hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                          title=\"Voto normal\"\r\n                        >+1</button>\r\n                        <button\r\n                          onClick={() => superVoteFromController(item.youtubeVideoId, 5)}\r\n                          className=\"px-2 py-0.5 border border-yellow-400 dark:border-yellow-700 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/20\"\r\n                          title=\"Supervoto R$5\"\r\n                        >5</button>\r\n                        <button\r\n                          onClick={() => superVoteFromController(item.youtubeVideoId, 20)}\r\n                          className=\"px-2 py-0.5 border border-yellow-500 dark:border-yellow-800 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/30\"\r\n                          title=\"Supervoto R$20\"\r\n                        >20</button>\r\n                        <button\r\n                          onClick={() => superVoteFromController(item.youtubeVideoId, 50)}\r\n                          className=\"px-2 py-0.5 border border-yellow-600 dark:border-yellow-900 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/40\"\r\n                          title=\"Supervoto R$50\"\r\n                        >50</button>\r\n                      </div>\r\n                      <div className=\"text-xs text-green-700 dark:text-green-300 ml-1\">R$ {formatBRL(item.paymentAmount)}</div>\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Votos (Grátis) */}\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-green-200 dark:border-green-700\">\r\n            <div className=\"flex items-center justify-between mb-3\">\r\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Votos (Grátis)</h4>\r\n              <span className=\"text-xs px-2 py-1 rounded bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200\">\r\n                {rankingFree.length} itens\r\n              </span>\r\n            </div>\r\n            <div className=\"space-y-2 max-h-72 overflow-y-auto\">\r\n              {rankingFree.length === 0 ? (\r\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Nenhum voto gratuito no momento</div>\r\n              ) : (\r\n                rankingFree.map((item, idx) => (\r\n                  <div key={item.youtubeVideoId + idx} className=\"flex items-center gap-3 p-3 rounded border border-green-200 dark:border-green-700 bg-green-50/60 dark:bg-green-900/20\">\r\n                    <div className=\"w-7 h-7 bg-green-600 text-white rounded flex items-center justify-center text-xs font-bold\">{idx + 1}</div>\r\n                    <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className=\"w-10 h-8 object-cover rounded\" />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">{item.title || item.youtubeVideoId}</div>\r\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 truncate\">{item.artist || \"—\"}</div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <div className=\"text-xs text-gray-700 dark:text-gray-300 mr-1\">Votos: {item.voteCount}</div>\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <button\r\n                          onClick={() => voteNormalFromController(item.youtubeVideoId)}\r\n                          className=\"px-2 py-0.5 border border-gray-300 dark:border-gray-600 rounded text-xs hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                          title=\"Voto normal\"\r\n                        >+1</button>\r\n                        <button\r\n                          onClick={() => superVoteFromController(item.youtubeVideoId, 5)}\r\n                          className=\"px-2 py-0.5 border border-yellow-400 dark:border-yellow-700 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/20\"\r\n                          title=\"Supervoto R$5\"\r\n                        >5</button>\r\n                        <button\r\n                          onClick={() => superVoteFromController(item.youtubeVideoId, 20)}\r\n                          className=\"px-2 py-0.5 border border-yellow-500 dark:border-yellow-800 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/30\"\r\n                          title=\"Supervoto R$20\"\r\n                        >20</button>\r\n                        <button\r\n                          onClick={() => superVoteFromController(item.youtubeVideoId, 50)}\r\n                          className=\"px-2 py-0.5 border border-yellow-600 dark:border-yellow-900 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/40\"\r\n                          title=\"Supervoto R$50\"\r\n                        >50</button>\r\n                      </div>\r\n                      <div className=\"text-xs text-gray-500 ml-1\">Mesa: {item.tableNumber ?? \"—\"}</div>\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {/* Prévia da Fila Automática de Reordenação */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Fila Automática (Prévia)</h4>\r\n            <span className=\"text-xs text-gray-600 dark:text-gray-300\">Aplicada quando o contador chegar a 0</span>\r\n          </div>\r\n          <div className=\"space-y-2 max-h-80 overflow-y-auto\">\r\n            {autoPreview.length === 0 ? (\r\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">Sem itens suficientes para prévia</div>\r\n            ) : (\r\n              autoPreview.slice(0, 20).map((item, idx) => (\r\n                <div key={`${item.youtubeVideoId}-${idx}`} className=\"flex items-center gap-3 p-3 rounded border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/30\">\r\n                  <div className={`w-7 h-7 rounded text-white flex items-center justify-center text-xs font-bold ${item.isPaid ? 'bg-yellow-600' : 'bg-green-600'}`}>{idx + 1}</div>\r\n                  <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className=\"w-10 h-8 object-cover rounded\" />\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">{item.title || item.youtubeVideoId}</div>\r\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400 truncate\">{item.artist || '—'}</div>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-3 text-right\">\r\n                    <span className={`text-xs px-2 py-0.5 rounded ${item.isPaid ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'}`}>\r\n                      {item.isPaid ? 'Paga' : 'Grátis'}\r\n                    </span>\r\n                    <div className=\"text-xs text-gray-700 dark:text-gray-300\">Votos: {item.voteCount}</div>\r\n                    {item.isPaid && (\r\n                      <div className=\"text-xs text-green-700 dark:text-green-300\">R$ {formatBRL(item.paymentAmount)}</div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              ))\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PlaybackController;"], "names": ["Playback<PERSON><PERSON>", "key", "data", "ttl", "cacheData", "cached", "timestamp", "__publicField", "PlaybackController", "paramRestaurantId", "useParams", "contextRestaurantId", "isConnected", "useRestaurantContext", "restaurantId", "on", "off", "emit", "useWebSocket", "playbackState", "setPlaybackState", "useState", "loading", "setLoading", "actionLoading", "setActionLoading", "priorityQueue", "setPriorityQueue", "normalQueue", "setNormalQueue", "queueStats", "setQueueStats", "lastUpdateTime", "setLastUpdateTime", "retryCount", "setRetryCount", "rankingPaid", "setRankingPaid", "rankingFree", "setRankingFree", "nextReorderAt", "setNextReorderAt", "countdown", "setCountdown", "autoReorder", "setAutoReorder", "autoPreview", "setAutoPreview", "totalVotes", "setTotalVotes", "reorderHistory", "setReorderHistory", "collabStats", "setCollabStats", "voteAggregates", "setVoteAggregates", "computedRevenue", "setComputedRevenue", "localSettings", "setLocalSettings", "manualVideoInput", "setManualVideoInput", "intervalRef", "useRef", "retryTimeoutRef", "rankingIntervalRef", "countdownIntervalRef", "useEffect", "handlePlaybackUpdate", "prev", "handleQueueUpdate", "estimatedWait", "calculateEstimatedWaitTime", "unsubscribeStatus", "wsService", "status", "handlePlaylistReordered", "payload", "toast", "_a", "_b", "loadQueues", "loadPlaybackState", "loadVotingRanking", "loadCollaborativeStats", "target", "startCountdown", "loadInitialData", "cachedPlayback", "cachedQueue", "url", "buildApiUrl", "res", "json", "items", "coerce", "x", "normalized", "paid", "i", "a", "b", "free", "e", "combined", "paidDiff", "payDiff", "toNumber", "sum", "it", "totalSuperVotes", "totalNormalVotes", "revenue", "tick", "diff", "handleReorderByVotes", "useCallback", "queue", "total", "track", "index", "voteNormalFromController", "youtubeVideoId", "t", "superVoteFromController", "paymentAmount", "makeApiCall", "options", "retries", "attempt", "response", "error", "resolve", "livePlaylistData", "state", "paidSuggestions", "freeSuggestions", "priority", "normal", "liveError", "allQueue", "extractYouTubeId", "input", "handleManualAdd", "toPaid", "id", "newTrack", "handleClearQueues", "s", "handleMoveInQueue", "which", "trackId", "direction", "list", "targetIndex", "temp", "handleDemoteToNormal", "idx", "item", "nextPrio", "toggleLocal", "handlePlayPause", "endpoint", "newState", "logUserAction", "handleSkip", "handleVolumeChange", "newVolume", "toggleMute", "handleRemoveFromQueue", "handlePromoteTrack", "action", "analyticsData", "existingLogs", "formatTime", "seconds", "mins", "secs", "formatEstimatedTime", "v", "n", "formatBRL", "getProgressPercentage", "jsx", "RefreshCw", "jsxs", "Toaster", "Loader2", "TrendingUp", "Users", "ExternalLink", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pause", "Play", "SkipForward", "VolumeX", "Volume2", "Settings2", "Trash2", "Shuffle", "Repeat", "Lock", "Unlock", "Music", "evt", "_", "AnimatePresence", "ArrowUp", "ArrowDown", "ChevronUp", "X", "Activity", "Clock", "ThumbsUp", "DollarSign", "Gift"], "mappings": "2nBAgFA,MAAMA,CAAc,CAGlB,OAAO,SAASC,EAAaC,EAAWC,EAAc,EAAI,GAAK,IAAM,CACnE,MAAMC,EAAY,CAChB,KAAAF,EACA,UAAW,KAAK,IAAI,EACpB,IAAAC,CAAA,EAEF,aAAa,QAAQ,KAAK,aAAeF,EAAK,KAAK,UAAUG,CAAS,CAAC,CACzE,CAEA,OAAO,SAASH,EAAa,CACvB,GAAA,CACF,MAAMI,EAAS,aAAa,QAAQ,KAAK,aAAeJ,CAAG,EAC3D,GAAI,CAACI,EAAe,OAAA,KAEpB,KAAM,CAAE,KAAAH,EAAM,UAAAI,EAAW,IAAAH,CAAQ,EAAA,KAAK,MAAME,CAAM,EAClD,OAAI,KAAK,MAAQC,EAAYH,GACd,aAAA,WAAW,KAAK,aAAeF,CAAG,EACxC,MAGFC,CAAA,MACD,CACC,OAAA,IACT,CACF,CACF,CA3BEK,GADIP,EACW,eAAe,wBA6BhC,MAAMQ,GAA+B,IAAM,CACzC,KAAM,CAAE,aAAcC,CAAkB,EAAIC,GAEzC,EACG,CAAE,aAAcC,EAAqB,YAAAC,GAAgBC,GAAqB,EAC1EC,EAAeL,GAAqBE,EACpC,CAAE,GAAAI,EAAI,IAAAC,EAAK,KAAAC,GAASC,GAAa,EAEjC,CAACC,EAAeC,CAAgB,EAAIC,WAA+B,IAAI,EACvE,CAACC,EAASC,EAAU,EAAIF,WAAS,EAAI,EACrC,CAACG,EAAeC,CAAgB,EAAIJ,WAAwB,IAAI,EAChE,CAACK,EAAeC,CAAgB,EAAIN,EAAA,SAAkB,CAAE,CAAA,EACxD,CAACO,EAAaC,CAAc,EAAIR,EAAA,SAAkB,CAAE,CAAA,EACpD,CAACS,EAAYC,CAAa,EAAIV,WAAS,CAC3C,WAAY,EACZ,UAAW,EACX,UAAW,EACX,kBAAmB,CAAA,CACpB,EACK,CAACW,GAAgBC,EAAiB,EAAIZ,EAAAA,SAAe,IAAI,IAAM,EAC/D,CAACa,GAAYC,EAAa,EAAId,WAAS,CAAC,EAGxC,CAACe,EAAaC,EAAc,EAAIhB,EAAA,SAAwB,CAAE,CAAA,EAC1D,CAACiB,EAAaC,EAAc,EAAIlB,EAAA,SAAwB,CAAE,CAAA,EAC1D,CAACmB,GAAeC,EAAgB,EAAIpB,WAAsB,IAAI,EAC9D,CAACqB,GAAWC,EAAY,EAAItB,WAAiB,CAAC,EAC9C,CAACuB,GAAaC,EAAc,EAAIxB,WAAkB,EAAK,EACvD,CAACyB,GAAaC,EAAc,EAAI1B,EAAA,SAAwB,CAAE,CAAA,EAC1D,CAAC2B,GAAYC,EAAa,EAAI5B,WAAiB,CAAC,EAChD,CAAC6B,GAAgBC,EAAiB,EAAI9B,EAAA,SAAgB,CAAE,CAAA,EACxD,CAAC+B,EAAaC,EAAc,EAAIhC,WAAc,IAAI,EAClD,CAACiC,EAAgBC,EAAiB,EAAIlC,WAAS,CACnD,gBAAiB,EACjB,iBAAkB,EAClB,UAAW,EACX,UAAW,CAAA,CACZ,EACK,CAACmC,GAAiBC,EAAkB,EAAIpC,WAAiB,CAAC,EAG1D,CAACqC,EAAeC,EAAgB,EAAItC,WAAS,CACjD,QAAS,GACT,OAAQ,GACR,SAAU,GACV,UAAW,EACX,WAAY,EAAA,CACb,EAGK,CAACuC,GAAkBC,EAAmB,EAAIxC,WAAiB,EAAE,EAC3BA,EAAAA,SAAkB,EAAK,EAGzD,MAAAyC,GAAcC,SAA8B,IAAI,EAChDC,GAAkBD,SAA8B,IAAI,EACpDE,EAAqBF,SAA8B,IAAI,EACvDG,EAAuBH,SAA8B,IAAI,EAG/DI,EAAAA,UAAU,IAAM,CACV,GAAA,CAACrD,GAAgB,CAACF,EAAa,OAE7B,MAAAwD,EAAwBlE,GAAiC,CAC5CkB,EAACiD,GAAUA,EAAO,CAAE,GAAGA,EAAM,GAAGnE,GAAS,IAAK,EAC7C+B,GAAA,IAAI,IAAM,EAC5BjC,EAAc,SAAS,YAAYc,CAAY,GAAIZ,CAAI,CAAA,EAGrDoE,EAAqBpE,GAA2D,CACjEyB,EAAAzB,EAAK,eAAiB,CAAA,CAAE,EAC1B2B,EAAA3B,EAAK,aAAe,CAAA,CAAE,EAE/B,MAAAqE,EAAgBC,GAA2B,CAAC,GAAGtE,EAAK,cAAe,GAAGA,EAAK,WAAW,CAAC,EAC7F6B,EAAesC,IAAU,CAAE,GAAGA,EAAM,kBAAmBE,CAAgB,EAAA,EAEvEvE,EAAc,SAAS,SAASc,CAAY,GAAIZ,CAAI,CAAA,EAIlDuE,EAAoBC,GAAU,yBAA0BC,GAAW,CACrEvD,EAAkBiD,GACpBA,EAAO,CAAE,GAAGA,EAAM,iBAAmBM,GAAmB,IAAA,CACtD,CACD,EAGH5D,EAAG,wBAAgCqD,CAA2B,EAC9DrD,EAAG,eAAuBuD,CAAwB,EAG1C,MAAAM,EAA2BC,GAAiB,CAC1CC,EAAA,SAAQD,GAAA,YAAAA,EAAS,UAAW,+BAA+B,EAEjE1B,GAAmBkB,GAAS,SAAA,OAC1B,CACE,KAAM,IAAI,KAAK,EAAE,YAAY,EAC7B,eAAcU,EAAAF,GAAA,YAAAA,EAAS,WAAT,YAAAE,EAAmB,QAAQF,GAAA,YAAAA,EAAS,cAClD,QAAOG,EAAAH,GAAA,YAAAA,EAAS,WAAT,YAAAG,EAAmB,mBAAmBH,GAAA,YAAAA,EAAS,kBAAmB,EACzE,UAAUA,GAAA,YAAAA,EAAS,YAAa,CAAI,GAAA,MAAM,EAAG,CAAC,CAChD,EACA,GAAGR,EAAK,MAAM,EAAG,EAAE,CAAA,EACpB,EAEUY,IACOC,IACAC,IACKC,KAEjB,MAAAC,EAAS,IAAI,KAAK,KAAK,MAAQ,EAAI,GAAK,GAAI,EAClD5C,GAAiB4C,CAAM,EACvBC,GAAeD,CAAM,CAAA,EAEvB,OAAAtE,EAAG,oBAAqB6D,CAAuB,EAG1C3D,EAAA,2BAA4B,CAAE,aAAAH,CAAA,CAAc,EAE1C,IAAM,CACfE,EAAI,wBAAgCoD,CAA2B,EAC/DpD,EAAI,eAAuBsD,CAAwB,EAC/BG,GAAA,MAAAA,IAChBzD,EAAI,oBAAqB4D,CAAuB,EAC3C3D,EAAA,4BAA6B,CAAE,aAAAH,CAAA,CAAc,CAAA,CACpD,EACC,CAACA,EAAcF,EAAaG,EAAIC,EAAKC,CAAI,CAAC,EAG7CkD,EAAAA,UAAU,IAAM,CAqBd,GApBgBoB,KAGX3E,IACSkD,GAAA,QAAU,YAAY,IAAM,CACpBoB,IACPD,KACV,GAAK,GAIQE,IACKC,KACnBnB,EAAmB,SAAS,cAAcA,EAAmB,OAAO,EACrDA,EAAA,QAAU,YAAY,IAAM,CAC3BkB,IACKC,MACtB,IAAK,EAGJ,CAAC5C,GAAe,CACZ,MAAA6C,EAAS,IAAI,KAAK,KAAK,MAAQ,GAAa,EAClD5C,GAAiB4C,CAAM,EACvBC,GAAeD,CAAM,CACvB,CAEA,MAAO,IAAM,CACPvB,GAAY,SACd,cAAcA,GAAY,OAAO,EAE/BE,GAAgB,SAClB,aAAaA,GAAgB,OAAO,EAElCC,EAAmB,SACrB,cAAcA,EAAmB,OAAO,EAEtCC,EAAqB,SACvB,cAAcA,EAAqB,OAAO,CAC5C,CACF,EACC,CAACpD,EAAcF,CAAW,CAAC,EAE9B,MAAM2E,GAAkB,SAAY,CAElC,MAAMC,EAAiBxF,EAAc,SAAS,YAAYc,CAAY,EAAE,EAClE2E,EAAczF,EAAc,SAAS,SAASc,CAAY,EAAE,EAE9D0E,IACFpE,EAAiBoE,CAAc,EACnCV,EAAM,+BAAgC,CAAE,SAAU,IAAM,KAAM,KAAM,GAG9DW,IACe9D,EAAA8D,EAAY,eAAiB,CAAA,CAAE,EACjC5D,EAAA4D,EAAY,aAAe,CAAA,CAAE,GAI9C,MAAM,QAAQ,IAAI,CAACP,IAAqBD,EAAY,CAAA,CAAC,CAAA,EAIjDE,EAAoB,SAAY,CACpC,GAAKrE,EAED,GAAA,CACI,MAAA4E,EAAMC,EAAY,2BAA2B7E,CAAY,WAAY,CAAE,MAAO,KAAM,EACpF8E,EAAM,MAAM,MAAMF,CAAG,EAC3B,GAAI,CAACE,EAAI,GAAI,MAAM,IAAI,MAAM,8BAA8BA,EAAI,MAAM,GAAG,EAClE,MAAAC,EAAO,MAAMD,EAAI,OACjBE,GAAuBD,GAAA,YAAAA,EAAM,OAAQ,GAEzCE,EAAUC,IAAY,CAAE,GAAGA,EAAG,cAAe,OAAOA,EAAE,aAAa,GAAK,EAAG,UAAW,OAAOA,EAAE,SAAS,GAAK,CAAE,GAC/GC,EAAaH,EAAM,IAAIC,CAAM,EAC7BG,EAAOD,EAAW,OAAQE,GAAMA,EAAE,MAAM,EAAE,KAAK,CAACC,EAAGC,KAAMA,GAAE,UAAYD,EAAE,SAAS,EAClFE,EAAOL,EAAW,OAAQE,GAAM,CAACA,EAAE,MAAM,EAAE,KAAK,CAACC,EAAGC,KAAMA,GAAE,UAAYD,EAAE,SAAS,EAErF/D,GAAe6D,CAAI,EACnB3D,GAAe+D,CAAI,QACZC,EAAG,CACF,QAAA,KAAK,+CAAgDA,CAAC,CAChE,CAAA,EAIInB,GAAyB,SAAY,CACzC,GAAKtE,EACD,GAAA,CACF,MAAM4E,EAAMC,EAAY,2BAA2B7E,CAAY,QAAQ,EACjE8E,EAAM,MAAM,MAAMF,CAAG,EAC3B,GAAI,CAACE,EAAI,GAAI,MAAM,IAAI,MAAM,4BAA4BA,EAAI,MAAM,GAAG,EAChE,MAAAC,EAAO,MAAMD,EAAI,OAERvC,IAAAwC,GAAA,YAAAA,EAAM,OAAQA,GAAQ,IAAI,QAClCU,EAAG,CACF,QAAA,KAAK,8CAA+CA,CAAC,CAC/D,CAAA,EAIFpC,EAAAA,UAAU,IAAM,CACR,MAAAqC,EAAW,CAAC,GAAGpE,EAAa,GAAGE,CAAW,EAAE,KAAK,CAAC8D,EAAGC,IAAM,CAE/D,MAAMI,EAAW,OAAOJ,EAAE,MAAM,EAAI,OAAOD,EAAE,MAAM,EACnD,GAAIK,IAAa,EAAU,OAAAA,EAC3B,MAAMC,EAAUC,EAASN,EAAE,aAAa,EAAIM,EAASP,EAAE,aAAa,EACpE,OAAIM,IAAY,EAAUA,EACnBC,EAASN,EAAE,SAAS,EAAIM,EAASP,EAAE,SAAS,CAAA,CACpD,EACDrD,GAAeyD,CAAQ,EACTvD,GAAAuD,EAAS,OAAO,CAACI,EAAKC,IAAOD,EAAMD,EAASE,EAAG,SAAS,EAAG,CAAC,CAAC,EAErE,MAAAC,EAAkBN,EAAS,OAAO,CAACI,EAAKC,IAAOD,EAAMD,EAASE,EAAG,cAAc,EAAG,CAAC,EACnFE,EAAmBP,EAAS,OAAO,CAACI,EAAKC,IAAOD,EAAMD,EAASE,EAAG,eAAe,EAAG,CAAC,EACzEtD,GAAA,CAChB,gBAAAuD,EACA,iBAAAC,EACA,UAAW3E,EAAY,OACvB,UAAWE,EAAY,MAAA,CACxB,EAEH,MAAM0E,EAAU5E,EAAY,OAAO,CAACwE,EAAKC,IAAOD,EAAMD,EAAUE,EAAW,eAAiB,CAAC,EAAG,CAAC,EACjGpD,GAAmBuD,CAAO,CAAA,EACvB,CAAC5E,EAAaE,CAAW,CAAC,EAGvB,MAAAgD,GAAkBD,GAAiB,CACnCnB,EAAqB,SAAS,cAAcA,EAAqB,OAAO,EAE5E,MAAM+C,EAAO,SAAY,CACvB,MAAMC,EAAO,KAAK,IAAI,EAAG,KAAK,OAAO7B,EAAO,QAAY,EAAA,KAAK,IAAI,GAAK,GAAI,CAAC,EAC3E1C,GAAauE,CAAI,EACbA,GAAQ,IACV,cAAchD,EAAqB,OAAyB,EAC5DA,EAAqB,QAAU,KAC3BtB,IACF,MAAMuE,GAAqB,EAE/B,EAGGF,IACgB/C,EAAA,QAAU,YAAY+C,EAAM,GAAI,CAAA,EAIjDE,GAAuB,SAAY,CACvC,GAAKrG,EACD,GAAA,CACF,MAAM8E,EAAM,MAAM,MAAMD,EAAY,2BAA2B7E,CAAY,UAAU,EAAG,CACtF,OAAQ,MAAA,CACT,EACD,GAAI,CAAC8E,EAAI,GAAI,MAAM,IAAI,MAAM,uBAAuBA,EAAI,MAAM,GAAG,EACjEd,EAAM,QAAQ,+BAA+B,EAEvC,MAAA,QAAQ,IAAI,CAACG,EAAA,EAAcE,IAAqBD,EAAmB,CAAA,CAAC,QACnEqB,EAAG,CACF,QAAA,MAAM,+BAAgCA,CAAC,EAC/CzB,EAAM,MAAM,6BAA6B,CAAA,QACzC,CACM,MAAAO,EAAS,IAAI,KAAK,KAAK,MAAQ,GAAa,EAClD5C,GAAiB4C,CAAM,EACvBC,GAAeD,CAAM,CACvB,CAAA,EAGIb,GAA6B4C,cAAaC,GACvCA,EAAM,OAAO,CAACC,EAAOC,EAAOC,IAC7BA,IAAU,EAAU,EACjBF,GAASC,EAAM,UAAY,KACjC,CAAC,EACH,CAAE,CAAA,EAGCE,GAA2B,MAAOC,GAA2B,CACjE,GAAK5G,EACD,GAAA,CACF,MAAM8E,EAAM,MAAM,MAChBD,EAAY,2BAA2B7E,CAAY,OAAO,EAC1D,CACE,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,UAAU,CAAE,eAAA4G,EAAgB,gBAAiB,cAAe,CACzE,CAAA,EAEE,GAAA,CAAC9B,EAAI,GAAI,CACX,MAAM+B,EAAI,MAAM/B,EAAI,KAAO,EAAA,MAAM,IAAM,EAAE,EACzC,MAAM,IAAI,MAAM,kBAAkBA,EAAI,MAAM,IAAI+B,CAAC,EAAE,CACrD,CACA7C,EAAM,QAAQ,0BAA0B,EACxC,MAAM,QAAQ,IAAI,CAACK,IAAqBF,EAAY,CAAA,CAAC,QAC9CsB,EAAG,CACV,QAAQ,MAAMA,CAAC,EACfzB,EAAM,MAAM,kCAAkC,CAChD,CAAA,EAGI8C,EAA0B,MAC9BF,EACAG,IACG,CACH,GAAK/G,EACD,GAAA,CACF,MAAM8E,EAAM,MAAM,MAChBD,EAAY,2BAA2B7E,CAAY,YAAY,EAC/D,CACE,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,UAAU,CACnB,eAAA4G,EACA,cAAAG,EACA,UAAW,cAAc,KAAK,IAAK,CAAA,GACnC,gBAAiB,aAAA,CAClB,CACH,CAAA,EAEE,GAAA,CAACjC,EAAI,GAAI,CACX,MAAM+B,EAAI,MAAM/B,EAAI,KAAO,EAAA,MAAM,IAAM,EAAE,EACzC,MAAM,IAAI,MAAM,sBAAsBA,EAAI,MAAM,IAAI+B,CAAC,EAAE,CACzD,CACM7C,EAAA,QAAQ,gBAAgB+C,CAAa,aAAa,EACxD,MAAM,QAAQ,IAAI,CAAC1C,IAAqBF,EAAY,CAAA,CAAC,QAC9CsB,EAAG,CACV,QAAQ,MAAMA,CAAC,EACfzB,EAAM,MAAM,8BAA8B,CAC5C,CAAA,EAGIgD,EAAc,MAAOpC,EAAaqC,EAAuBC,EAAU,IAAoB,CAC3F,QAASC,EAAU,EAAGA,EAAUD,EAASC,IACnC,GAAA,CACI,MAAAC,EAAW,MAAM,MAAMxC,EAAK,CAChC,GAAGqC,EACH,QAAS,CACP,eAAgB,mBAChB,cAAe,UAAU,aAAa,QAAQ,WAAW,CAAC,GAC1D,GAAGA,GAAA,YAAAA,EAAS,OACd,CAAA,CACD,EAEG,GAAA,CAACG,EAAS,GACN,MAAA,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKA,EAAS,UAAU,EAAE,EAG7D,MAAAhI,EAAO,MAAMgI,EAAS,OAC5B,OAAA/F,GAAc,CAAC,EACRjC,QACAiI,EAAO,CAGV,GAFJ,QAAQ,MAAM,4BAA4BF,EAAU,CAAC,KAAME,CAAK,EAE5DF,IAAYD,EAAU,EAAG,CACb7F,GAACkC,GAASA,EAAO,CAAC,EAChCS,EAAM,MAAM,+BAA+B5C,GAAa,CAAC,EAAE,EACpD,MACT,CAGA,MAAM,IAAI,QAASkG,GAAY,WAAWA,EAAS,KAAK,IAAI,EAAGH,CAAO,EAAI,GAAI,CAAC,CACjF,CAEK,EAGH/C,EAAoB,SAAY,CACpC,GAAI,CAACpE,EAAc,CACjB,QAAQ,MAAM,8BAA8B,EAC5C,MACF,CAEI,GAAA,CACF,MAAMZ,EAAO,MAAM4H,EAAYnC,EAAY,aAAa7E,CAAY,QAAQ,CAAC,EAEzEZ,GAAA,MAAAA,EAAM,OAEJA,EAAK,MAAM,cAAgBA,EAAK,MAAM,aAAa,YAChDA,EAAA,MAAM,aAAa,UAAY,IAAI,KAAKA,EAAK,MAAM,aAAa,SAAS,GAEhFkB,EAAiBlB,EAAK,KAAK,EAC3BF,EAAc,SAAS,YAAYc,CAAY,GAAIZ,EAAK,KAAK,GAc7DkB,EAXoC,CAClC,UAAW,GACX,aAAc,KACd,OAAQ,GACR,YAAa,EACb,MAAO,CAAC,EACR,cAAe,CAAC,EAChB,YAAa,CAAC,EACd,QAAS,CAAC,EACV,iBAAkBR,EAAc,YAAc,cAAA,CAEnB,QAExBuH,EAAO,CACN,QAAA,MAAM,yCAA0CA,CAAK,EAG7D,MAAM9H,EAASL,EAAc,SAAS,YAAYc,CAAY,EAAE,EAC5DT,IACFe,EAAiBf,CAAM,EAC7ByE,EAAM,kDAAmD,CAAE,KAAM,IAAM,CAAA,EACnE,QACA,CACAvD,GAAW,EAAK,EACEU,GAAA,IAAI,IAAM,CAC9B,CAAA,EAGIgD,EAAa,SAAY,CAC7B,GAAKnE,EAED,IAAA,CAEF,QAAQ,IAAI,gDAAgD,EAG5D,MAAMuH,EAAmB,MAAMP,EAC7BnC,EAAY,kBAAkB7E,CAAY,QAAQ,CAAA,EAGhD,GAAAuH,GAAA,MAAAA,EAAkB,UAAWA,GAAA,MAAAA,EAAkB,MAAM,CACvD,MAAMC,EAAQD,EAAiB,KAGzBE,EAAkB,MAAMT,EAC5BnC,EAAY,gBAAgB7E,CAAY,8BAA8B,CAAA,EAIlE0H,EAAkB,MAAMV,EAC5BnC,EAAY,gBAAgB7E,CAAY,+BAA+B,CAAA,EAGnE2H,IAAYF,GAAA,YAAAA,EAAiB,cAAe,CAAA,GAAI,IAAKhB,IAAgB,CACzE,GAAGA,EACH,UAAW,IAAI,KAAKA,EAAM,SAAS,EACnC,OAAQ,EACR,EAAA,EAEImB,IAAUF,GAAA,YAAAA,EAAiB,cAAe,CAAA,GAAI,IAAKjB,IAAgB,CACvE,GAAGA,EACH,UAAW,IAAI,KAAKA,EAAM,SAAS,EACnC,OAAQ,EACR,EAAA,EAEF5F,EAAiB8G,CAAQ,EACzB5G,EAAe6G,CAAM,EAErB,MAAMnE,EAAgBC,GAA2B,CAAC,GAAGiE,EAAU,GAAGC,CAAM,CAAC,EAG3D3G,EAAA,CACZ,WAAYuG,EAAM,qBAAuBA,EAAM,qBAC/C,UAAWA,EAAM,qBACjB,UAAWA,EAAM,qBACjB,kBAAmB/D,CAAA,CACpB,EAEavE,EAAA,SAAS,SAASc,CAAY,GAAI,CAAE,cAAe2H,EAAU,YAAaC,CAAQ,CAAA,EAExF,QAAA,IACN,oBAAoBJ,EAAM,oBAAoB,kBAAkBA,EAAM,oBAAoB,6BAA6B,KAAK,MAC1H/D,EAAgB,EACjB,CAAA,KAAA,EAGH,MACF,QACOoE,EAAW,CACV,QAAA,KAAK,kDAAmDA,CAAS,CAC3E,CAGI,GAAA,CACF,QAAQ,IAAI,oDAAoD,EAIhE,MAAMC,IAHO,MAAMd,EAAYnC,EAAY,mBAAmB7E,CAAY,EAAE,CAAC,GAGtD,OAAS,CAAA,GAAI,IAAKyG,IAAgB,CACvD,GAAGA,EACH,UAAW,IAAI,KAAKA,EAAM,SAAS,CACnC,EAAA,EACIkB,EAAWG,EAAS,OAAQrB,GAAeA,EAAM,MAAM,EACvDmB,EAASE,EAAS,OAAQrB,GAAe,CAACA,EAAM,MAAM,EAE5D5F,EAAiB8G,CAAQ,EACzB5G,EAAe6G,CAAM,EAEf,MAAAnE,EAAgBC,GAA2BoE,CAAQ,EAE3C7G,EAAA,CACZ,WAAY6G,EAAS,OACrB,UAAWH,EAAS,OACpB,UAAWC,EAAO,OAClB,kBAAmBnE,CAAA,CACpB,EAEavE,EAAA,SAAS,SAASc,CAAY,GAAI,CAAE,cAAe2H,EAAU,YAAaC,CAAQ,CAAA,EAExF,QAAA,IACN,gBAAgBD,EAAS,MAAM,kBAAkBC,EAAO,MAAM,6BAA6B,KAAK,MAC9FnE,EAAgB,EACjB,CAAA,KAAA,QAEI4D,EAAO,CACN,QAAA,MAAM,qDAAsDA,CAAK,EAGzE,MAAM9H,EAASL,EAAc,SAAS,SAASc,CAAY,EAAE,EACzDT,IACesB,EAAAtB,EAAO,eAAiB,CAAA,CAAE,EAC5BwB,EAAAxB,EAAO,aAAe,CAAA,CAAE,EAC7CyE,EAAM,gCAAiC,CAAE,KAAM,IAAM,CAAA,EAEnD,EAAA,EAII+D,GAAoBC,GAAiC,CACrD,GAAA,CACF,GAAI,CAACA,EAAc,OAAA,KAEf,GAAA,sBAAsB,KAAKA,CAAK,EAAU,OAAAA,EACxC,MAAApD,EAAM,IAAI,IAAIoD,CAAK,EACzB,OAAIpD,EAAI,SAAS,SAAS,UAAU,EAC3BA,EAAI,SAAS,QAAQ,IAAK,EAAE,EAEjCA,EAAI,SAAS,SAAS,aAAa,EAC9BA,EAAI,aAAa,IAAI,GAAG,EAE1B,IAAA,MACD,CAEN,MAAO,sBAAsB,KAAKoD,CAAK,EAAIA,EAAQ,IACrD,CAAA,EAGIC,GAAmBC,GAAoB,CAC3C,MAAMC,EAAKJ,GAAiBjF,GAAiB,KAAM,CAAA,EACnD,GAAI,CAACqF,EAAI,CACPnE,EAAM,MAAM,yCAAyC,EACrD,MACF,CACA,MAAMoE,EAAkB,CACtB,GAAI,SAAS,KAAK,IAAK,CAAA,GACvB,eAAgBD,EAChB,MAAO,WAAWA,CAAE,IACpB,OAAQ,uBACR,SAAU,IACV,aAAc,8BAA8BA,CAAE,iBAC9C,QAAS,EACT,UAAW,EACX,MAAO,EACP,cAAe,IAAK,EAElBD,GACFrH,EAAkB0C,GAAS,CAAC6E,EAAU,GAAG7E,CAAI,CAAC,EAC9CS,EAAM,QAAQ,4CAA4C,IAE1DjD,EAAgBwC,GAAS,CAAC6E,EAAU,GAAG7E,CAAI,CAAC,EAC5CS,EAAM,QAAQ,uCAAuC,GAEvDjB,GAAoB,EAAE,CAAA,EAGlBsF,GAAoB,IAAM,CAC9BxH,EAAiB,CAAE,CAAA,EACnBE,EAAe,CAAE,CAAA,EACHE,EAACqH,IAAO,CAAE,GAAGA,EAAG,WAAY,EAAG,UAAW,EAAG,UAAW,CAAA,EAAI,EAC1EtE,EAAM,QAAQ,2BAA2B,CAAA,EAGrCuE,EAAoB,CACxBC,EACAC,EACAC,IACG,CACG,MAAAC,EAAOH,IAAU,WAAa,CAAC,GAAG5H,CAAa,EAAI,CAAC,GAAGE,CAAW,EAClE4F,EAAQiC,EAAK,UAAW9B,GAAMA,EAAE,KAAO4B,CAAO,EACpD,GAAI/B,EAAQ,EAAG,OACf,MAAMkC,EAAcF,IAAc,KAAOhC,EAAQ,EAAIA,EAAQ,EACzD,GAAAkC,EAAc,GAAKA,GAAeD,EAAK,OAAQ,OAC7C,MAAAE,EAAOF,EAAKC,CAAW,EACxBD,EAAAC,CAAW,EAAID,EAAKjC,CAAK,EAC9BiC,EAAKjC,CAAK,EAAImC,EACVL,IAAU,WAAY3H,EAAiB8H,CAAI,EAC1C5H,EAAe4H,CAAI,CAAA,EAGpBG,GAAwBL,GAAoB,CAChD,MAAMM,EAAMnI,EAAc,UAAWiG,GAAMA,EAAE,KAAO4B,CAAO,EAC3D,GAAIM,GAAO,EAAG,CACN,MAAAC,EAAOpI,EAAcmI,CAAG,EACxBE,EAAW,CAAC,GAAGrI,CAAa,EACzBqI,EAAA,OAAOF,EAAK,CAAC,EACtBlI,EAAiBoI,CAAQ,EACzBlI,EAAgBwC,GAAS,CAACyF,EAAM,GAAGzF,CAAI,CAAC,EACxCS,EAAM,sCAAsC,CAC9C,CAAA,EAGIkF,GAAe/J,GAAoC,CACvD0D,GAAkBU,IAAU,CAAE,GAAGA,EAAM,CAACpE,CAAG,EAAG,CAACoE,EAAKpE,CAAG,CAAI,EAAA,CAAA,EAGvDgK,GAAkB,SAAY,OAC9B,GAAA,GAAC9I,GAAiB,CAACL,GAEvB,CAAAW,EAAiB,WAAW,EAExB,GAAA,CACI,MAAAyI,EAAW/I,EAAc,UAAY,QAAU,SACrD,MAAM2G,EAAYnC,EAAY,aAAa7E,CAAY,IAAIoJ,CAAQ,EAAE,EAAG,CACtE,OAAQ,MAAA,CACT,EAED,MAAMC,EAAW,CAAE,GAAGhJ,EAAe,UAAW,CAACA,EAAc,WAC/DC,EAAiB+I,CAAQ,EACzBnK,EAAc,SAAS,YAAYc,CAAY,GAAIqJ,CAAQ,EAE3DrF,EAAM,QAAQ3D,EAAc,UAAY,qBAAuB,qBAAqB,EAGpFiJ,EAAc,kBAAmB,CAC/B,OAAQF,EACR,SAASnF,EAAA5D,EAAc,eAAd,YAAA4D,EAA4B,EAAA,CACtC,OACa,CACdD,EAAM,MAAM,8BAA8B,CAAA,QAC1C,CACArD,EAAiB,IAAI,CACvB,EAAA,EAGI4I,GAAa,SAAY,OAC7B,GAAKvJ,EAEL,CAAAW,EAAiB,MAAM,EAEnB,GAAA,CACI,MAAAqG,EAAYnC,EAAY,aAAa7E,CAAY,OAAO,EAAG,CAAE,OAAQ,MAAA,CAAQ,EAEnFgE,EAAM,QAAQ,eAAe,EAG7BsF,EAAc,aAAc,CAC1B,SAASrF,EAAA5D,GAAA,YAAAA,EAAe,eAAf,YAAA4D,EAA6B,GACtC,SAAU5D,GAAA,YAAAA,EAAe,WAAA,CAC1B,EAGD,MAAM+D,EAAkB,EACxB,MAAMD,EAAW,OACH,CACdH,EAAM,MAAM,sBAAsB,CAAA,QAClC,CACArD,EAAiB,IAAI,CACvB,EAAA,EA2DI6I,GAAqB,MAAOC,GAAsB,CAClD,GAAA,GAACzJ,GAAgB,CAACK,GAElB,GAAA,CACF,MAAM2G,EAAYnC,EAAY,aAAa7E,CAAY,SAAS,EAAG,CACjE,OAAQ,OACR,KAAM,KAAK,UAAU,CAAE,OAAQyJ,EAAW,CAAA,CAC3C,EAED,MAAMJ,EAAW,CAAE,GAAGhJ,EAAe,OAAQoJ,CAAU,EACvDnJ,EAAiB+I,CAAQ,EACzBnK,EAAc,SAAS,YAAYc,CAAY,GAAIqJ,CAAQ,EAG3DC,EAAc,gBAAiB,CAC7B,eAAgBjJ,EAAc,OAC9B,UAAAoJ,CAAA,CACD,OACa,CACdzF,EAAM,MAAM,wBAAwB,CACtC,CAAA,EAGI0F,GAAa,IAAM,CACvB,GAAI,CAACrJ,EAAe,OAEpB,MAAMoJ,EAAYpJ,EAAc,SAAW,EAAI,GAAK,EACpDmJ,GAAmBC,CAAS,EAG5BH,EAAc,qBAAsB,CAClC,OAAQG,IAAc,EAAI,OAAS,QAAA,CACpC,CAAA,EAGGE,GAAwB,MAAOlB,GAAoB,CACvD,GAAKzI,EAEY,CAAAW,EAAA,mBAAmB8H,CAAO,EAAE,EAEzC,GAAA,CACF,MAAMzB,EAAYnC,EAAY,mBAAmB7E,CAAY,SAAS,EAAG,CACvE,OAAQ,OACR,KAAM,KAAK,UAAU,CAAE,QAAAyI,EAAS,CAAA,CACjC,EAEDzE,EAAM,QAAQ,yBAAyB,EAGzBsF,EAAA,eAAgB,CAAE,QAAAb,CAAA,CAAS,EAGzC,MAAMtE,EAAW,OACH,CACdH,EAAM,MAAM,gCAAgC,CAAA,QAC5C,CACArD,EAAiB,IAAI,CACvB,EAAA,EAGIiJ,GAAqB,MAAOnB,GAAoB,CACpD,GAAKzI,EAEY,CAAAW,EAAA,WAAW8H,CAAO,EAAE,EAEjC,GAAA,CACF,MAAMzB,EAAYnC,EAAY,mBAAmB7E,CAAY,UAAU,EAAG,CACxE,OAAQ,OACR,KAAM,KAAK,UAAU,CAAE,QAAAyI,EAAS,CAAA,CACjC,EAEDzE,EAAM,QAAQ,wCAAwC,EAGxCsF,EAAA,gBAAiB,CAAE,QAAAb,CAAA,CAAS,EAG1C,MAAMtE,EAAW,OACH,CACdH,EAAM,MAAM,yBAAyB,CAAA,QACrC,CACArD,EAAiB,IAAI,CACvB,EAAA,EAII2I,EAAgB,CAACO,EAAgBzK,IAAc,CAC/C,GAAA,CAEF,QAAQ,IAAI,iBAAiByK,CAAM,GAAIzK,CAAI,EAG3C,MAAM0K,EAAgB,CACpB,UAAW,IAAI,KAAK,EAAE,YAAY,EAClC,aAAA9J,EACA,OAAA6J,EACA,KAAAzK,CAAA,EAGI2K,EAAe,KAAK,MAAM,aAAa,QAAQ,oBAAoB,GAAK,IAAI,EAClFA,EAAa,KAAKD,CAAa,EAG3BC,EAAa,OAAS,KACxBA,EAAa,OAAO,EAAGA,EAAa,OAAS,GAAG,EAGlD,aAAa,QAAQ,qBAAsB,KAAK,UAAUA,CAAY,CAAC,QAChE1C,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CACrD,CAAA,EAGI2C,EAAcC,GAAoB,CACtC,MAAMC,EAAO,KAAK,MAAMD,EAAU,EAAE,EAC9BE,EAAOF,EAAU,GAChB,MAAA,GAAGC,CAAI,IAAIC,EAAK,WAAW,SAAS,EAAG,GAAG,CAAC,EAAA,EAG9CC,GAAuBH,GACvBA,EAAU,GAAW,GAAGA,CAAO,IAC/BA,EAAU,KAAa,GAAG,KAAK,MAAMA,EAAU,EAAE,CAAC,MAC/C,GAAG,KAAK,MAAMA,EAAU,IAAI,CAAC,IAIhCpE,EAAYwE,GAAmB,CAC7B,MAAAC,EAAI,OAAOD,CAAC,EAClB,OAAO,OAAO,SAASC,CAAC,EAAIA,EAAI,CAAA,EAE5BC,EAAaF,GACjBxE,EAASwE,CAAC,EAAE,eAAe,QAAS,CAAE,sBAAuB,EAAG,sBAAuB,CAAG,CAAA,EAEtFG,GAAwB,IACvBnK,GAAA,MAAAA,EAAe,aACZA,EAAc,YAAcA,EAAc,aAAa,SAAY,IADlC,EAM3C,OAAIG,EAECiK,EAAA,IAAA,MAAA,CAAI,UAAU,qDACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,wCACb,SAACA,EAAA,IAAAC,EAAA,CAAU,UAAU,oCAAqC,CAAA,EAC5D,CACF,CAAA,EAKFC,EAAA,KAAC,MAAI,CAAA,UAAU,YACjB,SAAA,CAACF,EAAAA,IAAAG,GAAA,CAAQ,SAAS,UAAW,CAAA,EAGzBD,EAAAA,KAAC,MAAI,CAAA,UAAU,qEACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAAmC,sCAAA,EACnGA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAA+E,kFAAA,CAAA,EACtI,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,uHACf,SAAA,CAAAF,EAAAA,IAAC,QAAM,CAAA,KAAK,WAAW,QAAS3I,GAAa,SAAW2D,GAAM1D,GAAe0D,EAAE,OAAO,OAAO,CAAG,CAAA,EAAE,gBAAA,EAEpG,EACAgF,EAAA,IAAC,SAAA,CACC,QAASpE,GACT,UAAU,4EACX,SAAA,iBAAA,CAED,EACAsE,EAAA,KAAC,SAAA,CACC,QAASlG,GACT,SAAU/D,IAAkB,UAC5B,UAAU,gJACV,aAAW,gCAEV,SAAA,CAAkBA,IAAA,gBAAamK,EAAQ,CAAA,UAAU,uBAAuB,EAAKJ,EAAA,IAACC,EAAU,CAAA,UAAU,SAAU,CAAA,EAAG,WAAA,CAAA,CAElH,CAAA,EACF,CAAA,EACF,EAGAC,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAF,EAAAA,IAAC,OAAI,UAAU,iGACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAAmB,sBAAA,QAC1E,IAAE,CAAA,UAAU,mDAAoD,SAAAL,GAAoBxI,EAAS,EAAE,CAAA,EAClG,EACA6I,EAAAA,IAACC,EAAU,CAAA,UAAU,yBAA0B,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,QACC,MAAI,CAAA,UAAU,iGACb,SAACC,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAAc,iBAAA,QACrE,IAAE,CAAA,UAAU,mDAAoD,UAAAnI,GAAA,YAAAA,EAAa,aAAcJ,GAAW,CAAA,EACzG,EACAuI,EAAAA,IAACK,EAAW,CAAA,UAAU,wBAAyB,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,QACC,MAAI,CAAA,UAAU,iGACb,SAACH,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAAW,cAAA,EAClEA,EAAA,IAAA,IAAA,CAAE,UAAU,0DAA2D,WAAe,UAAU,CAAA,EACnG,EACAA,EAAAA,IAACM,EAAM,CAAA,UAAU,yBAA0B,CAAA,CAAA,CAAA,CAC7C,CACF,CAAA,QACC,MAAI,CAAA,UAAU,iGACb,SAACJ,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAAY,eAAA,EACnEA,EAAA,IAAA,IAAA,CAAE,UAAU,wDAAyD,WAAe,UAAU,CAAA,EACjG,EACAA,EAAAA,IAACM,EAAM,CAAA,UAAU,wBAAyB,CAAA,CAAA,CAAA,CAC5C,CACF,CAAA,CAAA,EACF,EAGAJ,EAAAA,KAAC,MAAI,CAAA,UAAU,8LACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,+CAA+C,SAAuB,0BAAA,EACnFE,EAAAA,KAAC,IAAE,CAAA,UAAU,0DAA0D,SAAA,CAAA,MAAIJ,GAAUjI,GAAA,YAAAA,EAAa,eAAgBI,EAAe,CAAA,EAAE,CAAA,EACrI,EACAiI,EAAA,KAAC,IAAA,CACC,KAAM,eAAe3K,CAAY,uBACjC,UAAU,2GAEV,SAAA,CAACyK,EAAAA,IAAAO,GAAA,CAAa,UAAU,SAAU,CAAA,EAAE,gBAAA,CAAA,CACtC,CAAA,EACF,EAGCP,EAAA,IAAA,MAAA,CAAI,UAAU,qDACZ,oBAAe,aACdE,EAAA,KAACM,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAU,YAGV,SAAA,CAACN,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,EAAA,IAAC,MAAA,CACC,IAAKpK,EAAc,aAAa,aAChC,IAAKA,EAAc,aAAa,MAChC,UAAU,6CAAA,CACZ,EACAsK,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAF,MAAC,KAAG,CAAA,UAAU,sDACX,SAAApK,EAAc,aAAa,MAC9B,QACC,IAAE,CAAA,UAAU,mCACV,SAAAA,EAAc,aAAa,OAC9B,EACAsK,EAAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAAAF,EAAA,IAACK,EAAW,CAAA,UAAU,UAAU,cAAY,OAAO,QAClD,OAAK,CAAA,UAAU,UAAW,SAAAzK,EAAc,aAAa,QAAQ,CAAA,EAChE,EACAsK,EAAAA,KAAC,MAAI,CAAA,UAAU,2CACb,SAAA,CAAAF,EAAA,IAACS,GAAc,CAAA,UAAU,UAAU,cAAY,OAAO,QACrD,OAAK,CAAA,UAAU,UAAW,SAAA7K,EAAc,aAAa,UAAU,CAAA,EAClE,EACAsK,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAAwB,SAAA,CAAA,UAC7BtK,EAAc,aAAa,KAAA,EACrC,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGAsK,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAAAF,EAAA,IAAC,OAAM,CAAA,SAAAT,EAAW3J,EAAc,WAAW,EAAE,QAC5C,OAAM,CAAA,SAAA2J,EAAW3J,EAAc,aAAa,QAAQ,EAAE,CAAA,EACzD,EACAoK,EAAAA,IAAC,MAAI,CAAA,UAAU,uDACb,SAAAA,EAAA,IAACQ,EAAO,IAAP,CACC,UAAU,+BACV,QAAS,CAAE,MAAO,CAAE,EACpB,QAAS,CAAE,MAAO,GAAGT,GAAuB,CAAA,GAAI,EAChD,WAAY,CAAE,SAAU,CAAE,EAC1B,KAAK,cACL,gBAAeA,GAAsB,EACrC,gBAAe,EACf,gBAAe,IACf,aAAW,qBAAA,CAAA,EAEf,CAAA,EACF,EAGAG,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAAStB,GACT,SAAUzI,IAAkB,YAC5B,UAAU,qKACV,aAAYL,EAAc,UAAY,SAAW,aAEhD,aAAkB,YACjBoK,MAACI,EAAQ,CAAA,UAAU,uBAAuB,EACxCxK,EAAc,UAChBoK,EAAAA,IAACU,IAAM,UAAU,SAAU,CAAA,EAE1BV,EAAA,IAAAW,GAAA,CAAK,UAAU,eAAe,CAAA,CAEnC,EAEAX,EAAA,IAAC,SAAA,CACC,QAASlB,GACT,SAAU7I,IAAkB,OAC5B,UAAU,mOACV,aAAW,eAEV,SAAAA,IAAkB,OACjB+J,EAAAA,IAACI,EAAQ,CAAA,UAAU,sBAAuB,CAAA,EAE1CJ,EAAAA,IAACY,GAAY,CAAA,UAAU,SAAU,CAAA,CAAA,CAErC,EAGAV,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAASf,GACT,aAAYrJ,EAAc,SAAW,EAAI,aAAe,YAEvD,SAAAA,EAAc,SAAW,EACvBoK,EAAA,IAAAa,GAAA,CAAQ,UAAU,uBAAwB,CAAA,EAE3Cb,EAAAA,IAACc,GAAQ,CAAA,UAAU,uBAAwB,CAAA,CAAA,CAE/C,EACAd,EAAA,IAAC,QAAA,CACC,KAAK,QACL,IAAI,IACJ,IAAI,MACJ,MAAOpK,EAAc,OACrB,SAAWoF,GAAM+D,GAAmB,SAAS/D,EAAE,OAAO,KAAK,CAAC,EAC5D,UAAU,iEACV,aAAW,oBAAA,CACb,EACCgF,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA6B,WAAc,OAAO,CAAA,EACpE,CAAA,EACF,CAAA,CAAA,CAGF,EAAAE,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACF,EAAAA,IAAA,MAAA,CAAI,UAAU,mDAAoD,CAAA,EACnEE,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAc,iBAAA,EACjFA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAA2D,8DAAA,CAAA,EACrH,CAAA,EACF,EAGAE,EAAAA,KAAC,MAAI,CAAA,UAAU,4FACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2DACb,SAAA,CAACF,EAAAA,IAAAe,GAAA,CAAU,UAAU,SAAU,CAAA,EAC9Bf,EAAA,IAAA,OAAA,CAAK,UAAU,sBAAsB,SAAa,gBAAA,CAAA,EACrD,EACCE,EAAA,KAAA,SAAA,CAAO,QAAStC,GAAmB,UAAU,kEAC5C,SAAA,CAACoC,EAAAA,IAAAgB,GAAA,CAAO,UAAU,SAAU,CAAA,EAAE,eAAA,EAChC,CAAA,EACF,EACAd,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAAAF,MAAC,SAAO,CAAA,QAAShG,GAAiB,UAAU,uEAAuE,SAAS,YAAA,QAC3H,SAAO,CAAA,QAAS4B,GAAsB,UAAU,2EAA2E,SAAmB,sBAAA,EAC9IsE,EAAA,KAAA,SAAA,CAAO,QAAS,IAAMzB,GAAY,SAAS,EAAG,UAAW,uDAAuDtG,EAAc,QAAU,mEAAqE,+DAA+D,GAC3Q,SAAA,CAAC6H,EAAAA,IAAAiB,GAAA,CAAQ,UAAU,SAAU,CAAA,EAAE,UAAA,EACjC,EACCf,EAAA,KAAA,SAAA,CAAO,QAAS,IAAMzB,GAAY,QAAQ,EAAG,UAAW,uDAAuDtG,EAAc,OAAS,mEAAqE,+DAA+D,GACzQ,SAAA,CAAC6H,EAAAA,IAAAkB,GAAA,CAAO,UAAU,SAAU,CAAA,EAAE,SAAA,EAChC,EACChB,EAAA,KAAA,SAAA,CAAO,QAAS,IAAMzB,GAAY,YAAY,EAAG,UAAW,uDAAuDtG,EAAc,WAAa,2EAA6E,+DAA+D,GACxR,SAAA,CAAcA,EAAA,iBAAcgJ,GAAK,CAAA,UAAU,UAAU,EAAKnB,EAAA,IAACoB,GAAO,CAAA,UAAU,SAAU,CAAA,EAAG,IAAEjJ,EAAc,WAAa,kBAAoB,kBAAA,EAC7I,CAAA,EACF,CAAA,EACF,EAGA+H,EAAAA,KAAC,MAAI,CAAA,UAAU,4FACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gEACb,SAAA,CAACF,EAAAA,IAAAO,GAAA,CAAa,UAAU,SAAU,CAAA,EACjCP,EAAA,IAAA,OAAA,CAAK,UAAU,sBAAsB,SAA4B,+BAAA,CAAA,EACpE,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,MAAO3H,GACP,SAAW2C,GAAM1C,GAAoB0C,EAAE,OAAO,KAAK,EACnD,YAAY,+BACZ,UAAU,sIAAA,CACZ,EACAkF,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACF,EAAAA,IAAA,SAAA,CAAO,QAAS,IAAMxC,GAAgB,EAAK,EAAG,UAAU,uEAAuE,SAAwB,0BAAA,CAAA,EACxJwC,EAAAA,IAAC,UAAO,QAAS,IAAMxC,GAAgB,EAAI,EAAG,UAAU,yEAAyE,SAA6B,+BAAA,CAAA,CAAA,EAChK,CAAA,EACF,CAAA,EACF,CAAA,CAAA,CACF,CAEJ,CAAA,EAGA0C,EAAAA,KAAC,MAAI,CAAA,UAAU,6CAEb,SAAA,CAAAF,EAAAA,IAAC,OAAI,UAAU,iGACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAAa,gBAAA,EACpEA,EAAA,IAAA,IAAA,CAAE,UAAU,mDACV,WAAW,WACd,CAAA,EACF,EACAA,EAAAA,IAACqB,GAAM,CAAA,UAAU,uBAAwB,CAAA,CAAA,CAAA,CAC3C,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,qGACb,SAACnB,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,+CAA+C,SAAgB,mBAAA,EAC3EA,EAAA,IAAA,IAAA,CAAE,UAAU,0DACV,WAAW,UACd,CAAA,EACF,EACAA,EAAAA,IAACK,EAAW,CAAA,UAAU,yBAA0B,CAAA,CAAA,CAAA,CAClD,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,mGACb,SAACH,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,IAAA,CAAE,UAAU,6CAA6C,SAAW,cAAA,EACpEA,EAAA,IAAA,IAAA,CAAE,UAAU,wDACV,WAAW,UACd,CAAA,EACF,EACAA,EAAAA,IAACM,EAAM,CAAA,UAAU,wBAAyB,CAAA,CAAA,CAAA,CAC5C,CACF,CAAA,CAAA,EACF,EAGAJ,EAAAA,KAAC,MAAI,CAAA,UAAU,0GACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAwB,2BAAA,EAC5FE,EAAAA,KAAC,OAAK,CAAA,UAAU,qGACb,SAAA,CAAevI,GAAA,OAAO,UAAA,EACzB,CAAA,EACF,EACCA,GAAe,SAAW,EACzBqI,EAAAA,IAAC,OAAI,UAAU,2CAA2C,8CAAkC,EAE5FA,EAAA,IAAC,OAAI,UAAU,qCACZ,YAAe,IAAI,CAACsB,EAAKhD,IACxB4B,EAAAA,KAAC,MAAc,CAAA,UAAU,oGACvB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,4CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,+CACZ,SAAA,CAAA,IAAI,KAAKoB,EAAI,IAAI,EAAE,eAAe,OAAO,EAAE,MAAIA,EAAI,cAAgB,UAAA,EACtE,EACApB,EAAAA,KAAC,MAAI,CAAA,UAAU,+CAAgD,SAAA,CAAIoB,EAAA,MAAM,qBAAA,EAAmB,CAAA,EAC9F,EACApB,EAAAA,KAAC,MAAI,CAAA,UAAU,gDAAgD,SAAA,CAAA,SAE5DF,EAAA,IAAA,KAAA,CAAG,UAAU,wBACV,YAAI,SAAW,CAAA,GAAI,IAAI,CAAC5D,EAAQxB,IAC/BsF,EAAA,KAAA,KAAA,CAAW,UAAU,WACnB,SAAA,CAAA9D,EAAE,OAASA,EAAE,QAAQ,IAAEA,EAAE,OAAS,SAAW,GAAG,aAAWA,EAAE,WAAa,GAAA,GADpExB,CAET,CACD,EACH,CAAA,EACF,CAAA,GAhBQ0D,CAiBV,CACD,EACH,CAAA,EAEJ,EAGA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,wEAAwE,SAAA,CAAA,wBAC9D/J,EAAc,OAAO,GAAA,EAC7C,EACC6J,EAAA,IAAA,MAAA,CAAI,UAAU,iHAAiH,SAEhI,2BAAA,CAAA,EACF,QACC,MAAI,CAAA,UAAU,qCACZ,SACCjK,EAAAiK,MAAC,OAAI,UAAU,YACZ,SAAC,CAAA,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAACuB,EAAG3G,IACrBsF,EAAA,KAACM,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAO5F,EAAI,EAAI,EAC7B,UAAU,8FAEV,SAAA,CAACoF,EAAAA,IAAA,MAAA,CAAI,UAAU,uDAAwD,CAAA,EACvEE,EAAAA,KAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAACF,EAAAA,IAAA,MAAA,CAAI,UAAU,oDAAqD,CAAA,EACpEA,EAAAA,IAAC,MAAI,CAAA,UAAU,oDAAqD,CAAA,CAAA,EACtE,CAAA,CAAA,EAVKpF,CAYR,CAAA,CACH,CAAA,EACEzE,EAAc,OAAS,EACxB6J,EAAAA,IAAAwB,GAAA,CACE,SAAcrL,EAAA,IAAI,CAAC6F,EAAOC,IACzBiE,EAAA,KAACM,EAAO,IAAP,CAEC,OAAM,GACN,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,EAAG,EAC1B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,gIAEV,SAAA,CAAAR,EAAA,IAAC,MAAI,CAAA,UAAU,oIACZ,SAAA/D,EAAQ,EACX,EACAiE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,EAAA,IAAC,KAAG,CAAA,UAAU,6DACX,SAAAhE,EAAM,MACT,EACCgE,EAAA,IAAA,IAAA,CAAE,UAAU,oDACV,WAAM,OACT,CAAA,EACF,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAF,MAAC,OAAI,UAAU,wBAAyB,SAAWT,EAAAvD,EAAM,QAAQ,EAAE,EACnEgE,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlC,EAAkB,WAAY9B,EAAM,GAAI,IAAI,EAC3D,UAAU,wCACV,MAAM,kBACN,aAAW,kBAEX,SAAAgE,EAAAA,IAACyB,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAC/B,EACAzB,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlC,EAAkB,WAAY9B,EAAM,GAAI,MAAM,EAC7D,UAAU,wCACV,MAAM,mBACN,aAAW,mBAEX,SAAAgE,EAAAA,IAAC0B,GAAU,CAAA,UAAU,SAAU,CAAA,CAAA,CACjC,EACA1B,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3B,GAAqBrC,EAAM,EAAE,EAC5C,UAAU,wCACV,MAAM,yBACN,aAAW,yBAEX,SAAAgE,EAAAA,IAAC2B,GAAU,CAAA,UAAU,oBAAqB,CAAA,CAAA,CAC5C,EACA3B,EAAA,IAAC,SAAA,CACC,QAAS,IAAMd,GAAsBlD,EAAM,EAAE,EAC7C,SAAU/F,IAAkB,mBAAmB+F,EAAM,EAAE,GACvD,UAAU,wGACV,aAAW,8BACX,MAAM,UAEL,SAAkB/F,IAAA,mBAAmB+F,EAAM,EAAE,GAC5CgE,MAACI,EAAQ,CAAA,UAAU,sBAAuB,CAAA,EAEzCJ,EAAAA,IAAA4B,GAAA,CAAE,UAAU,UAAU,CAAA,CAE3B,CAAA,EACF,CAAA,CAAA,EA1DK5F,EAAM,EAAA,CA4Dd,EACH,EAEAkE,EAAA,KAACM,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAU,mBAEV,SAAA,CAAAR,EAAAA,IAAC,OAAI,UAAU,2GACb,eAACK,EAAW,CAAA,UAAU,0BAA0B,CAClD,CAAA,EACCL,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,qCAAA,CAAA,CAAA,CAAA,EAGN,CAAA,EACF,EAGAE,EAAAA,KAAC,MAAI,CAAA,UAAU,mGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,wEAAwE,SAAA,CAAA,mBACnE7J,EAAY,OAAO,GAAA,EACtC,EACC2J,EAAA,IAAA,MAAA,CAAI,UAAU,6GAA6G,SAE5H,WAAA,CAAA,EACF,QACC,MAAI,CAAA,UAAU,qCACZ,SACCjK,EAAAiK,MAAC,OAAI,UAAU,YACZ,SAAC,CAAA,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAACuB,EAAG3G,IACrBsF,EAAA,KAACM,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAO5F,EAAI,EAAI,EAC7B,UAAU,4FAEV,SAAA,CAACoF,EAAAA,IAAA,MAAA,CAAI,UAAU,gDAAiD,CAAA,EAChEE,EAAAA,KAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAACF,EAAAA,IAAA,MAAA,CAAI,UAAU,kDAAmD,CAAA,EAClEA,EAAAA,IAAC,MAAI,CAAA,UAAU,kDAAmD,CAAA,CAAA,EACpE,CAAA,CAAA,EAVKpF,CAYR,CAAA,CACH,CAAA,EACEvE,EAAY,OAAS,EACvB2J,EAAA,IAACwB,GACE,CAAA,SAAAnL,EACE,MAAM,EACN,KAAK,CAACwE,EAAGC,IAAMA,EAAE,QAAUA,EAAE,WAAaD,EAAE,QAAUA,EAAE,UAAU,EAClE,IAAI,CAACmB,EAAOC,IACXiE,EAAA,KAACM,EAAO,IAAP,CAEC,OAAM,GACN,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,EAAG,EAC1B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,4HAEV,SAAA,CAAAR,EAAA,IAAC,MAAI,CAAA,UAAU,+FACZ,SAAA/D,EAAQ,EACX,EACAiE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,EAAA,IAAC,KAAG,CAAA,UAAU,6DACX,SAAAhE,EAAM,MACT,EACCgE,EAAA,IAAA,IAAA,CAAE,UAAU,oDACV,WAAM,OACT,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,EAAA,IAACK,EAAW,CAAA,UAAU,yBAAyB,cAAY,OAAO,EACjEL,EAAA,IAAA,OAAA,CAAK,UAAU,yBAA0B,WAAM,QAAQ,CAAA,EAC1D,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAF,EAAA,IAACS,GAAc,CAAA,UAAU,uBAAuB,cAAY,OAAO,EAClET,EAAA,IAAA,OAAA,CAAK,UAAU,uBAAwB,WAAM,UAAU,CAAA,EAC1D,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAAwB,SAAA,CAAA,UAC7BlE,EAAM,OAASA,EAAM,QAAUA,EAAM,SAAA,EAC/C,CAAA,EACF,CAAA,EACF,EACAkE,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAF,MAAC,OAAI,UAAU,wBAAyB,SAAWT,EAAAvD,EAAM,QAAQ,EAAE,EACnEgE,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlC,EAAkB,SAAU9B,EAAM,GAAI,IAAI,EACzD,UAAU,wCACV,MAAM,kBACN,aAAW,kBAEX,SAAAgE,EAAAA,IAACyB,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAC/B,EACAzB,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlC,EAAkB,SAAU9B,EAAM,GAAI,MAAM,EAC3D,UAAU,wCACV,MAAM,mBACN,aAAW,mBAEX,SAAAgE,EAAAA,IAAC0B,GAAU,CAAA,UAAU,SAAU,CAAA,CAAA,CACjC,EACA1B,EAAA,IAAC,SAAA,CACC,QAAS,IAAMb,GAAmBnD,EAAM,EAAE,EAC1C,SAAU/F,IAAkB,WAAW+F,EAAM,EAAE,GAC/C,UAAU,0GACV,aAAW,iCACX,MAAM,WAEL,SAAkB/F,IAAA,WAAW+F,EAAM,EAAE,GACpCgE,MAACI,EAAQ,CAAA,UAAU,sBAAuB,CAAA,EAEzCJ,EAAAA,IAAA2B,GAAA,CAAU,UAAU,UAAU,CAAA,CAEnC,EACA3B,EAAA,IAAC,SAAA,CACC,QAAS,IAAMd,GAAsBlD,EAAM,EAAE,EAC7C,SAAU/F,IAAkB,iBAAiB+F,EAAM,EAAE,GACrD,UAAU,wGACV,aAAW,yBACX,MAAM,UAEL,SAAkB/F,IAAA,iBAAiB+F,EAAM,EAAE,GAC1CgE,MAACI,EAAQ,CAAA,UAAU,sBAAuB,CAAA,EAEzCJ,EAAAA,IAAA4B,GAAA,CAAE,UAAU,UAAU,CAAA,CAE3B,CAAA,EACF,CAAA,CAAA,EA5EK5F,EAAM,EAAA,CA8Ed,EACL,EAEAkE,EAAA,KAACM,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAU,mBAEV,SAAA,CAAAR,EAAAA,IAAC,OAAI,UAAU,yGACb,eAACM,EAAM,CAAA,UAAU,yBAAyB,CAC5C,CAAA,EACCN,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,gCAAA,CAAA,CAAA,CAAA,EAGN,CAAA,EACF,CAAA,EACF,EAGAA,EAAA,IAACQ,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAO,EAAI,EACzB,UAAU,OAEV,eAAC,MAAI,CAAA,UAAU,iGACb,SAACN,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACF,EAAAA,IAAA6B,GAAA,CAAS,UAAU,uBAAwB,CAAA,EAC5C3B,EAAAA,KAAC,OAAK,CAAA,UAAU,2CAA2C,SAAA,CAAA,uCAChB,IAAA,KAAA,EAAO,mBAAmB,OAAO,CAAA,EAC5E,CAAA,EACF,EACAA,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACF,EAAAA,IAAA,MAAA,CAAI,UAAU,iDAAkD,CAAA,EAChEA,EAAA,IAAA,OAAA,CAAK,UAAU,6CAA6C,SAAM,SAAA,CAAA,EACrE,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,EAGAE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAuB,0BAAA,EAC3FE,EAAAA,KAAC,OAAK,CAAA,UAAU,oIACd,SAAA,CAACF,EAAAA,IAAAC,EAAA,CAAU,UAAU,SAAU,CAAA,EAAE,IAAEN,GAAoBxI,EAAS,CAAA,EAClE,CAAA,EACF,EACC6I,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,0CAAA,EAGAE,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACF,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAAiB,oBAAA,EAC5EA,EAAAA,IAAC8B,GAAM,CAAA,UAAU,yBAA0B,CAAA,CAAA,EAC7C,QACC,MAAI,CAAA,UAAU,sDAAuD,SAAAnC,GAAoBxI,EAAS,EAAE,CAAA,EACvG,EACA+I,EAAAA,KAAC,MAAI,CAAA,UAAU,oFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACF,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAAc,iBAAA,EACzEA,EAAAA,IAAC+B,GAAS,CAAA,UAAU,wBAAyB,CAAA,CAAA,EAC/C,QACC,MAAI,CAAA,UAAU,sDAAuD,UAAAlK,GAAA,YAAAA,EAAa,aAAcJ,GAAW,CAAA,EAC9G,EACAyI,EAAAA,KAAC,MAAI,CAAA,UAAU,oFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACF,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAAa,gBAAA,EACxEA,EAAAA,IAACgC,GAAW,CAAA,UAAU,yBAA0B,CAAA,CAAA,EAClD,EACA9B,EAAAA,KAAC,MAAI,CAAA,UAAU,sDAAsD,SAAA,CAAA,MAAIJ,GAAUjI,GAAA,YAAAA,EAAa,eAAgBI,EAAe,CAAA,EAAE,CAAA,EACnI,EACAiI,EAAAA,KAAC,MAAI,CAAA,UAAU,oFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACF,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAAY,eAAA,EACvEA,EAAAA,IAACiC,GAAK,CAAA,UAAU,yBAA0B,CAAA,CAAA,EAC5C,EACA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,sDAAuD,SAAA,CAAenI,EAAA,UAAU,IAAEA,EAAe,SAAA,EAAU,EAC1HmI,EAAAA,KAAC,MAAI,CAAA,UAAU,gDAAgD,SAAA,CAAA,OAAKnI,EAAe,gBAAgB,UAAQA,EAAe,gBAAA,EAAiB,CAAA,EAC7I,CAAA,EACF,CAAA,EACF,EAEAmI,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAkB,qBAAA,EACtFE,EAAAA,KAAC,OAAK,CAAA,UAAU,qGACb,SAAA,CAAYrJ,EAAA,OAAO,QAAA,EACtB,CAAA,EACF,EACAmJ,EAAAA,IAAC,OAAI,UAAU,qCACZ,WAAY,SAAW,QACrB,MAAI,CAAA,UAAU,2CAA2C,SAA2B,6BAAA,CAAA,EAErFnJ,EAAY,IAAI,CAAC0H,EAAMD,IACrB4B,EAAAA,KAAC,MAAoC,CAAA,UAAU,4HAC7C,SAAA,CAAAF,EAAA,IAAC,MAAI,CAAA,UAAU,8FAA+F,SAAA1B,EAAM,EAAE,EACrH0B,EAAA,IAAA,MAAA,CAAI,IAAK,8BAA8BzB,EAAK,cAAc,iBAAkB,IAAKA,EAAK,OAASA,EAAK,eAAgB,UAAU,gCAAgC,EAC/J2B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,MAAC,OAAI,UAAU,6DAA8D,SAAKzB,EAAA,OAASA,EAAK,eAAe,QAC9G,MAAI,CAAA,UAAU,oDAAqD,SAAAA,EAAK,QAAU,IAAI,CAAA,EACzF,EACA2B,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDAAgD,SAAA,CAAA,UAAQ3B,EAAK,SAAA,EAAU,EACtF2B,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAAS,IAAM9D,GAAyBqC,EAAK,cAAc,EAC3D,UAAU,mHACV,MAAM,cACP,SAAA,IAAA,CAAE,EACHyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3D,EAAwBkC,EAAK,eAAgB,CAAC,EAC7D,UAAU,6HACV,MAAM,gBACP,SAAA,GAAA,CAAC,EACFyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3D,EAAwBkC,EAAK,eAAgB,EAAE,EAC9D,UAAU,6HACV,MAAM,iBACP,SAAA,IAAA,CAAE,EACHyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3D,EAAwBkC,EAAK,eAAgB,EAAE,EAC9D,UAAU,6HACV,MAAM,iBACP,SAAA,IAAA,CAAE,CAAA,EACL,EACA2B,EAAAA,KAAC,MAAI,CAAA,UAAU,kDAAkD,SAAA,CAAA,MAAIJ,EAAUvB,EAAK,aAAa,CAAA,EAAE,CAAA,EACrG,CAAA,CAAA,EAhCQA,EAAK,eAAiBD,CAiChC,CACD,CAEL,CAAA,CAAA,EACF,EAGA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,mGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAc,iBAAA,EAClFE,EAAAA,KAAC,OAAK,CAAA,UAAU,iGACb,SAAA,CAAYnJ,EAAA,OAAO,QAAA,EACtB,CAAA,EACF,EACAiJ,EAAAA,IAAC,OAAI,UAAU,qCACZ,WAAY,SAAW,QACrB,MAAI,CAAA,UAAU,2CAA2C,SAA+B,iCAAA,CAAA,EAEzFjJ,EAAY,IAAI,CAACwH,EAAMD,IACrB4B,EAAAA,KAAC,MAAoC,CAAA,UAAU,wHAC7C,SAAA,CAAAF,EAAA,IAAC,MAAI,CAAA,UAAU,6FAA8F,SAAA1B,EAAM,EAAE,EACpH0B,EAAA,IAAA,MAAA,CAAI,IAAK,8BAA8BzB,EAAK,cAAc,iBAAkB,IAAKA,EAAK,OAASA,EAAK,eAAgB,UAAU,gCAAgC,EAC/J2B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,MAAC,OAAI,UAAU,6DAA8D,SAAKzB,EAAA,OAASA,EAAK,eAAe,QAC9G,MAAI,CAAA,UAAU,oDAAqD,SAAAA,EAAK,QAAU,IAAI,CAAA,EACzF,EACA2B,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDAAgD,SAAA,CAAA,UAAQ3B,EAAK,SAAA,EAAU,EACtF2B,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAAS,IAAM9D,GAAyBqC,EAAK,cAAc,EAC3D,UAAU,mHACV,MAAM,cACP,SAAA,IAAA,CAAE,EACHyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3D,EAAwBkC,EAAK,eAAgB,CAAC,EAC7D,UAAU,6HACV,MAAM,gBACP,SAAA,GAAA,CAAC,EACFyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3D,EAAwBkC,EAAK,eAAgB,EAAE,EAC9D,UAAU,6HACV,MAAM,iBACP,SAAA,IAAA,CAAE,EACHyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3D,EAAwBkC,EAAK,eAAgB,EAAE,EAC9D,UAAU,6HACV,MAAM,iBACP,SAAA,IAAA,CAAE,CAAA,EACL,EACA2B,EAAAA,KAAC,MAAI,CAAA,UAAU,6BAA6B,SAAA,CAAA,SAAO3B,EAAK,aAAe,GAAA,EAAI,CAAA,EAC7E,CAAA,CAAA,EAhCQA,EAAK,eAAiBD,CAiChC,CACD,CAEL,CAAA,CAAA,EACF,CAAA,EACF,EAEA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAwB,2BAAA,EAC3FA,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAAqC,wCAAA,CAAA,EAClG,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qCACZ,SAAAzI,GAAY,SAAW,EACtByI,EAAA,IAAC,MAAI,CAAA,UAAU,2CAA2C,SAAA,oCAAiC,EAE3FzI,GAAY,MAAM,EAAG,EAAE,EAAE,IAAI,CAACgH,EAAMD,IAClC4B,EAAA,KAAC,MAA0C,CAAA,UAAU,iHACnD,SAAA,CAACF,EAAAA,IAAA,MAAA,CAAI,UAAW,iFAAiFzB,EAAK,OAAS,gBAAkB,cAAc,GAAK,SAAAD,EAAM,CAAE,CAAA,EAC3J0B,EAAA,IAAA,MAAA,CAAI,IAAK,8BAA8BzB,EAAK,cAAc,iBAAkB,IAAKA,EAAK,OAASA,EAAK,eAAgB,UAAU,gCAAgC,EAC/J2B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,MAAC,OAAI,UAAU,6DAA8D,SAAKzB,EAAA,OAASA,EAAK,eAAe,QAC9G,MAAI,CAAA,UAAU,oDAAqD,SAAAA,EAAK,QAAU,IAAI,CAAA,EACzF,EACA2B,EAAAA,KAAC,MAAI,CAAA,UAAU,qCACb,SAAA,CAACF,EAAA,IAAA,OAAA,CAAK,UAAW,+BAA+BzB,EAAK,OAAS,2EAA6E,sEAAsE,GAC9M,SAAAA,EAAK,OAAS,OAAS,SAC1B,EACA2B,EAAAA,KAAC,MAAI,CAAA,UAAU,2CAA2C,SAAA,CAAA,UAAQ3B,EAAK,SAAA,EAAU,EAChFA,EAAK,QACH2B,OAAA,MAAA,CAAI,UAAU,6CAA6C,SAAA,CAAA,MAAIJ,EAAUvB,EAAK,aAAa,CAAA,EAAE,CAAA,EAElG,CAAA,GAfQ,GAAGA,EAAK,cAAc,IAAID,CAAG,EAgBvC,CACD,EAEL,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,CAEJ"}