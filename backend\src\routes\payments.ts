import { Router } from "express";
import { body, param, query, checkValidationErrors } from "../utils/validation";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";
import { v4 as uuidv4 } from "uuid";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { logger } from "../utils/logger";
import * as QRCode from "qrcode";
import { MercadoPagoClient } from "../services/MercadoPagoClient";

const router = Router();

/**
 * Gera QR Code de forma segura com fallback
 */
async function generateQRCodeSafely(pixCode: string): Promise<string> {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(pixCode, {
      errorCorrectionLevel: "M",
      type: "image/png",
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    logger.info("✅ QR Code PIX gerado com sucesso");
    return qrCodeDataURL;
  } catch (error) {
    logger.error("❌ Erro ao gerar QR Code PIX:", error);

    // Fallback: gerar QR Code simples usando API externa
    try {
      const fallbackUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(
        pixCode
      )}&size=256x256&margin=10`;
      const response = await fetch(fallbackUrl);

      if (response.ok) {
        const buffer = await response.arrayBuffer();
        const base64 = Buffer.from(buffer).toString("base64");
        const dataUrl = `data:image/png;base64,${base64}`;

        logger.info("✅ QR Code gerado via fallback API");
        return dataUrl;
      }
    } catch (fallbackError) {
      logger.error("❌ Erro no fallback do QR Code:", fallbackError);
    }

    // Último recurso: QR Code placeholder
    logger.warn("⚠️ Usando QR Code placeholder");
    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
  }
}

// Rota de teste
router.get("/test", (req, res) => {
  console.log("🧪 Rota de teste de pagamentos chamada");
  res.json({ message: "Rota de pagamentos funcionando!" });
});

/**
 * @swagger
 * /api/v1/payments/pix/suggestion:
 *   post:
 *     summary: Criar pagamento PIX para sugestão prioritária
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - youtubeId
 *               - title
 *             properties:
 *               restaurantId:
 *                 type: string
 *               youtubeId:
 *                 type: string
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableNumber:
 *                 type: integer
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Pagamento PIX criado com sucesso
 *       400:
 *         description: Dados inválidos
 *       404:
 *         description: Restaurante não encontrado
 */
router.post(
  "/pix/suggestion",
  [
    body("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    body("youtubeId").notEmpty().withMessage("YoutubeId é obrigatório"),
    body("title").notEmpty().withMessage("Title é obrigatório"),
    body("amount")
      .optional()
      .isInt({ min: 100 })
      .withMessage("Amount deve ser em centavos (mínimo 100)"),
  ],
  asyncHandler(async (req, res, next) => {
    checkValidationErrors(req, res, next);

    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
      amount: rawAmountCents,
    } = req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({ where: { id: restaurantId } });
    if (!restaurant) throw new NotFoundError("Restaurante não encontrado");

    const amountCents = Number(rawAmountCents) && Number(rawAmountCents) > 0 ? Number(rawAmountCents) : 500;
    const amount = parseFloat((amountCents / 100).toFixed(2));

    // Inicializa cliente Mercado Pago
    const accessToken = process.env.MERCADO_PAGO_ACCESS_TOKEN || process.env.MP_ACCESS_TOKEN;
    if (!accessToken) {
      logger.error("❌ Configuração de Mercado Pago ausente", {
        MERCADO_PAGO_ACCESS_TOKEN: !!process.env.MERCADO_PAGO_ACCESS_TOKEN,
        MP_ACCESS_TOKEN: !!process.env.MP_ACCESS_TOKEN
      });
      throw new ValidationError("Configuração de Mercado Pago ausente (MERCADO_PAGO_ACCESS_TOKEN)");
    }
    const integratorId = process.env.MP_INTEGRATOR_ID;
    const mp = new MercadoPagoClient(accessToken, integratorId);

    logger.info("💳 Criando pagamento PIX", { restaurantId, amount, title });

    // notification_url deve ser público (defina via env em produção)
    const baseUrl = process.env.PUBLIC_BASE_URL || req.headers.origin || `${req.protocol}://${req.get("host")}`;
    const notificationUrl = `${baseUrl.replace(/\/$/, "")}/api/v1/payments/webhook`;

    // Cria cobrança PIX real
    const mpPayment = await mp.createPixPayment({
      amount,
      description: `Sugestão prioritária: ${title}`,
      notificationUrl,
      payer: clientName ? { first_name: clientName } : undefined,
      metadata: {
        restaurantId,
        youtubeId,
        title,
        artist,
        sessionId,
        tableNumber,
        source: "supervote",
      },
    });

    const qr = mpPayment.point_of_interaction?.transaction_data || {};
    const pixCode = qr.qr_code || "";
    const qrCodeBase64 = qr.qr_code_base64 || (pixCode ? await generateQRCodeSafely(pixCode) : "");

    res.json({
      success: true,
      payment: {
        id: mpPayment.id,
        amount: mpPayment.transaction_amount,
        status: mpPayment.status,
        pixCode,
        qrCodeData: qrCodeBase64,
        ticketUrl: qr.ticket_url || null,
        description: `Sugestão prioritária: ${title}`,
        createdAt: mpPayment.date_created,
        restaurantId,
        youtubeId,
        title,
        artist,
      },
      message: "Código PIX gerado com sucesso!",
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/:paymentId/status:
 *   get:
 *     summary: Verificar status do pagamento PIX
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status do pagamento
 *       400:
 *         description: PaymentId inválido
 */
router.get(
  "/:paymentId/status",
  [param("paymentId").notEmpty().withMessage("PaymentId é obrigatório")],
  asyncHandler(async (req, res, next) => {
    checkValidationErrors(req, res, next);

    const { paymentId } = req.params;
    const accessToken = process.env.MP_ACCESS_TOKEN || process.env.MERCADO_PAGO_ACCESS_TOKEN;
    if (!accessToken) throw new ValidationError("Configuração de Mercado Pago ausente (MP_ACCESS_TOKEN)");
    const integratorId = process.env.MP_INTEGRATOR_ID;
    const mp = new MercadoPagoClient(accessToken, integratorId);

    const payment = await mp.getPayment(paymentId);
    const status = payment.status;

    res.json({
      success: true,
      payment: {
        id: payment.id,
        status: status === "approved" ? "paid" : status,
        paidAt: payment.date_approved || null,
        amount: payment.transaction_amount,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/:paymentId/confirm:
 *   post:
 *     summary: Confirmar pagamento e criar sugestão prioritária
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - youtubeId
 *               - title
 *             properties:
 *               restaurantId:
 *                 type: string
 *               youtubeId:
 *                 type: string
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableNumber:
 *                 type: integer
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Sugestão prioritária criada com sucesso
 *       400:
 *         description: Pagamento não aprovado ou dados inválidos
 */
router.post(
  "/:paymentId/confirm",
  [
    param("paymentId").notEmpty().withMessage("PaymentId é obrigatório"),
    body("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    body("youtubeId").notEmpty().withMessage("YoutubeId é obrigatório"),
    body("title").notEmpty().withMessage("Title é obrigatório"),
  ],
  asyncHandler(async (req, res, next) => {
    checkValidationErrors(req, res, next);

    const { paymentId } = req.params;
    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
    } = req.body;

    // Verificar se o pagamento foi aprovado
    // Em produção, consultar o gateway de pagamento (Mercado Pago)
    const timestampFromId = paymentId.split("-")[0];
    const createdTime = parseInt(timestampFromId, 16) || Date.now() - 60000;
    const now = Date.now();
    const paymentStatus = now - createdTime > 30000 ? "paid" : "pending";

    if (paymentStatus !== "paid") {
      return res.status(400).json({
        success: false,
        error: "Pagamento não foi aprovado ainda",
      });
    }

    // Criar sugestão prioritária
    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        error: "Restaurante não encontrado",
      });
    }

    const suggestion = suggestionRepository.create({
      youtubeVideoId: youtubeId,
      title,
      artist: artist || "Artista Desconhecido",
      clientName: clientName || "Anônimo",
      tableNumber: tableNumber || null,
      clientSessionId: sessionId || null,
      status: SuggestionStatus.APPROVED,
      isPriority: true,
      paymentId,
      paymentAmount: 2.0,
      paymentStatus: "paid",
      restaurant: restaurant,
    });

    const savedSuggestion = await suggestionRepository.save(suggestion);
    const suggestionId = savedSuggestion.id;

    logger.info(
      `Sugestão prioritária criada para restaurante ${restaurantId}:`,
      {
        suggestionId,
        paymentId,
        title,
      }
    );

    res.json({
      success: true,
      suggestion: savedSuggestion,
      message: "Sugestão prioritária criada com sucesso!",
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/history:
 *   get:
 *     summary: Obter histórico de pagamentos de um restaurante
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *     responses:
 *       200:
 *         description: Histórico de pagamentos
 */
router.get(
  "/history",
  [
    query("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    query("limit").optional().isInt({ min: 1, max: 100 }),
  ],
  asyncHandler(async (req, res, next) => {
    checkValidationErrors(req, res, next);

    const { restaurantId, limit = 50 } = req.query;

    // Buscar sugestões pagas do restaurante
    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const paidSuggestions = await suggestionRepository.find({
      where: {
        restaurant: { id: restaurantId as string },
        paymentStatus: "paid",
        isPriority: true,
      },
      order: { createdAt: "DESC" },
      take: Number(limit),
    });

    const payments = paidSuggestions.map((suggestion) => ({
      id: suggestion.paymentId || suggestion.id,
      amount: suggestion.paymentAmount || 2.0,
      title: suggestion.title,
      artist: suggestion.artist,
      clientName: suggestion.clientName,
      tableNumber: suggestion.tableNumber,
      status: suggestion.paymentStatus || "paid",
      createdAt: suggestion.createdAt,
    }));

    res.json({
      success: true,
      payments,
      total: payments.length,
      restaurantId,
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/webhook:
 *   post:
 *     summary: Webhook do Mercado Pago (placeholder)
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processado
 */
router.post(
  "/webhook",
  asyncHandler(async (req, res) => {
    // Webhook do Mercado Pago: recebemos tópico e id do recurso
    // Documentação: https://www.mercadopago.com.br/developers/pt/docs/your-integrations/notifications/webhooks
    try {
      const topic = (req.query.topic || req.query.type || req.body?.type) as string | undefined;
      const dataId = (req.query.id || req.body?.data?.id || req.body?.resource?.id) as string | undefined;

      logger.info("🔔 Webhook recebido", { topic, dataId, body: req.body });

      if (!dataId) {
        return res.status(200).json({ success: true });
      }

      const accessToken = process.env.MP_ACCESS_TOKEN || process.env.MERCADO_PAGO_ACCESS_TOKEN;
      const integratorId = process.env.MP_INTEGRATOR_ID;
      if (!accessToken) {
        logger.warn("Webhook recebido, mas MP_ACCESS_TOKEN ausente");
        return res.status(200).json({ success: true });
      }

      const mp = new MercadoPagoClient(accessToken, integratorId);
      const payment = await mp.getPayment(dataId);

      // Mapear status e, se aprovado, podemos emitir evento ou atualizar banco
      const normalized = {
        id: payment.id,
        status: payment.status === "approved" ? "paid" : payment.status,
        amount: payment.transaction_amount,
        date_approved: payment.date_approved || null,
        metadata: (payment as any).metadata || {},
      };

      logger.info("📩 Webhook processado", normalized);

      // TODO: opcional - integrar com serviço que marca Suggestion como paga com base no metadata
      // e dispara eventos via WebSocketService

      return res.status(200).json({ success: true });
    } catch (err) {
      logger.error("❌ Erro ao processar webhook:", err);
      return res.status(200).json({ success: true });
    }
  })
);

/**
 * @swagger
 * /api/v1/payments/test:
 *   post:
 *     summary: Criar pagamento de teste
 *     tags: [Payments]
 *     responses:
 *       201:
 *         description: Pagamento de teste criado
 */
router.post(
  "/test",
  asyncHandler(async (req, res) => {
    // Criar um pagamento de teste com dados mock
    const pixCode =
      "00020126580014BR.GOV.BCB.PIX0136123e4567-e12b-12d1-a456-426614174000520400005303986540502.005802BR5913Fulano de Tal6008BRASILIA62070503***63041D3D";

    const testPayment = {
      id: `test_${Date.now()}`,
      amount: 2.0,
      status: "pending",
      pixCode,
      qrCodeData: await QRCode.toDataURL(pixCode, {
        errorCorrectionLevel: "M",
        type: "image/png",
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
        width: 256,
      }),
      description: "Pagamento de teste",
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
    };

    res.status(201).json({
      success: true,
      message: "Pagamento de teste criado",
      payment: testPayment,
      note: "Este é um pagamento de teste. Use apenas para desenvolvimento.",
    });
  })
);

export default router;
