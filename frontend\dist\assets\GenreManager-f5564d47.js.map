{"version": 3, "file": "GenreManager-f5564d47.js", "sources": ["../../src/components/restaurant/GenreManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  Music,\r\n  Plus,\r\n  Edit3,\r\n  Trash2,\r\n  Save,\r\n  X,\r\n  Search,\r\n  Filter,\r\n  Palette,\r\n  Tag,\r\n  <PERSON><PERSON>hart3,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>freshC<PERSON>,\r\n  CheckCircle,\r\n  AlertCircle,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { useRestaurantContext } from \"./RestaurantDashboard\";\r\nimport { buildApiUrl, API_CONFIG } from \"../../config/api\";\r\n\r\ninterface Genre {\r\n  id: string;\r\n  name: string;\r\n  displayName: string;\r\n  description?: string;\r\n  category: \"music\" | \"mood\" | \"energy\" | \"time\" | \"custom\";\r\n  color: string;\r\n  icon?: string;\r\n  priority: number;\r\n  isActive: boolean;\r\n  isDefault: boolean;\r\n  usageCount: number;\r\n  lastUsedAt?: string;\r\n}\r\n\r\ninterface GenreManagerProps {\r\n  onGenreSelect?: (genre: Genre) => void;\r\n  selectedGenres?: string[];\r\n  mode?: \"select\" | \"manage\";\r\n}\r\n\r\nconst GenreManager: React.FC<GenreManagerProps> = ({\r\n  onGenreSelect,\r\n  selectedGenres = [],\r\n  mode = \"manage\",\r\n}) => {\r\n  const { restaurantId } = useRestaurantContext();\r\n\r\n  const [genres, setGenres] = useState<Record<string, Genre[]>>({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [selectedCategory, setSelectedCategory] = useState<string>(\"all\");\r\n  const [showCreateForm, setShowCreateForm] = useState(false);\r\n  const [editingGenre, setEditingGenre] = useState<Genre | null>(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    displayName: \"\",\r\n    description: \"\",\r\n    category: \"music\" as const,\r\n    color: \"#3B82F6\",\r\n    icon: \"\",\r\n    priority: 0,\r\n  });\r\n\r\n  const categories = {\r\n    all: \"Todos\",\r\n    music: \"Gêneros Musicais\",\r\n    mood: \"Humor\",\r\n    energy: \"Energia\",\r\n    time: \"Horário\",\r\n    custom: \"Personalizado\",\r\n  };\r\n\r\n  const iconOptions = [\r\n    \"Music\",\r\n    \"Music2\",\r\n    \"Music3\",\r\n    \"Music4\",\r\n    \"Mic\",\r\n    \"Heart\",\r\n    \"Zap\",\r\n    \"Volume2\",\r\n    \"Headphones\",\r\n    \"Sun\",\r\n    \"Moon\",\r\n    \"Coffee\",\r\n    \"Smile\",\r\n    \"CloudRain\",\r\n    \"Waves\",\r\n    \"Activity\",\r\n    \"Star\",\r\n    \"Disc\",\r\n    \"Radio\",\r\n    \"Speaker\",\r\n  ];\r\n\r\n  useEffect(() => {\r\n    loadGenres();\r\n  }, []);\r\n\r\n  const loadGenres = async () => {\r\n    try {\r\n      setLoading(true);\r\n      console.log(\"🎵 Carregando gêneros para restaurante:\", restaurantId);\r\n\r\n      // Primeiro, tentar carregar gêneros baseados na playlist do restaurante\r\n      await loadPlaylistBasedGenres();\r\n\r\n      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}`);\r\n      console.log(\"🎵 URL da API:\", url);\r\n\r\n      // Obter token de autenticação\r\n      const authToken = localStorage.getItem(\"authToken\");\r\n      const headers: Record<string, string> = {\r\n        \"Content-Type\": \"application/json\",\r\n      };\r\n\r\n      if (authToken) {\r\n        headers.Authorization = `Bearer ${authToken}`;\r\n      }\r\n\r\n      const response = await fetch(url, { headers });\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🎵 Gêneros do sistema carregados:\", data);\r\n\r\n        // Mesclar gêneros do sistema com os da playlist\r\n        setGenres((prevGenres) => ({\r\n          ...data.genres,\r\n          ...prevGenres, // Priorizar gêneros da playlist\r\n        }));\r\n\r\n        toast.success(\"Gêneros carregados com sucesso!\");\r\n      } else {\r\n        const errorText = await response.text().catch(() => \"\");\r\n        console.error(\r\n          \"🎵 Erro na resposta:\",\r\n          response.status,\r\n          response.statusText,\r\n          errorText\r\n        );\r\n\r\n        // Se for erro 500, carregar dados de exemplo\r\n        if (response.status === 500) {\r\n          console.log(\"🎵 Carregando gêneros de exemplo devido ao erro 500\");\r\n          loadExampleGenres();\r\n          toast(\"Carregados gêneros de exemplo (erro no servidor)\", {\r\n            icon: \"⚠️\",\r\n            duration: 4000,\r\n          });\r\n        } else {\r\n          toast.error(\"Erro ao carregar gêneros\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🎵 Erro ao carregar gêneros:\", error);\r\n      loadExampleGenres();\r\n      toast.error(\"Erro ao carregar gêneros. Carregando dados de exemplo.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Nova função para carregar gêneros baseados na playlist do restaurante\r\n  const loadPlaylistBasedGenres = async () => {\r\n    try {\r\n      console.log(\"🎵 Analisando playlist para extrair gêneros...\");\r\n\r\n      // Carregar playlists do restaurante\r\n      const playlistsResponse = await fetch(\r\n        buildApiUrl(`/playlists/restaurant/${restaurantId}`)\r\n      );\r\n\r\n      if (!playlistsResponse.ok) {\r\n        console.log(\r\n          \"🎵 Não foi possível carregar playlists para análise de gêneros\"\r\n        );\r\n        return;\r\n      }\r\n\r\n      const playlistsData = await playlistsResponse.json();\r\n      const playlists = playlistsData.playlists || [];\r\n\r\n      if (playlists.length === 0) {\r\n        console.log(\"🎵 Nenhuma playlist encontrada para análise\");\r\n        return;\r\n      }\r\n\r\n      // Analisar as músicas das playlists para extrair gêneros\r\n  const extractedGenres: Record<string, Genre[]> = { } as any;\r\n      const genreMap = new Map<string, number>();\r\n\r\n      for (const playlist of playlists) {\r\n        if (playlist.isActive) {\r\n          // Carregar detalhes da playlist com tracks\r\n          const playlistDetailResponse = await fetch(\r\n            buildApiUrl(`/playlists/${playlist.id}`)\r\n          );\r\n\r\n          if (playlistDetailResponse.ok) {\r\n            const playlistDetail = await playlistDetailResponse.json();\r\n            const tracks = playlistDetail.tracks || [];\r\n\r\n            console.log(\r\n              `🎵 Analisando ${tracks.length} músicas da playlist \"${playlist.name}\"`\r\n            );\r\n\r\n            for (const track of tracks) {\r\n              // Analisar título para identificar possíveis gêneros\r\n              const genres = analyzeTrackGenres(track.title, \"\");\r\n              genres.forEach((genre) => {\r\n                const count = genreMap.get(genre) || 0;\r\n                genreMap.set(genre, count + 1);\r\n              });\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Converter para formato de gêneros\r\n      let priority = 1;\r\n      genreMap.forEach((count, genreName) => {\r\n        if (count >= 2) {\r\n          // Só incluir gêneros que aparecem pelo menos 2 vezes\r\n          (extractedGenres as any).music = (extractedGenres as any).music || [];\r\n          (extractedGenres as any).music.push({\r\n            id: `playlist-${genreName}`,\r\n            name: genreName.toLowerCase(),\r\n            displayName: genreName,\r\n            color: getGenreColor(genreName),\r\n            isActive: true,\r\n            category: \"music\",\r\n            priority: priority++,\r\n            description: `Gênero identificado na playlist (${count} músicas)`,\r\n            isDefault: false,\r\n            usageCount: count,\r\n          });\r\n        }\r\n      });\r\n\r\n      if ((extractedGenres as any).music && (extractedGenres as any).music.length > 0) {\r\n        console.log(\"🎵 Gêneros extraídos da playlist:\", extractedGenres);\r\n        setGenres((prevGenres) => ({\r\n          ...prevGenres,\r\n          music: [\r\n            ...((prevGenres.music as any) || []),\r\n            ...((extractedGenres as any).music as Genre[]),\r\n          ],\r\n        }));\r\n        toast.success(\r\n          `${(extractedGenres as any).music.length} gêneros identificados na playlist!`\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🎵 Erro ao analisar playlist para gêneros:\", error);\r\n    }\r\n  };\r\n\r\n  // Carregar gêneros de exemplo\r\n  const loadExampleGenres = () => {\r\n  const exampleGenres: Record<string, Genre[]> = {\r\n      music: [\r\n        {\r\n          id: \"1\",\r\n          name: \"rock\",\r\n          displayName: \"Rock\",\r\n          color: \"#e74c3c\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 1,\r\n      isDefault: true,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"2\",\r\n          name: \"pop\",\r\n          displayName: \"Pop\",\r\n          color: \"#3498db\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 2,\r\n      isDefault: true,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"3\",\r\n          name: \"jazz\",\r\n          displayName: \"Jazz\",\r\n          color: \"#f39c12\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 3,\r\n      isDefault: false,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"4\",\r\n          name: \"classical\",\r\n          displayName: \"Clássica\",\r\n          color: \"#9b59b6\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 4,\r\n      isDefault: false,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"5\",\r\n          name: \"electronic\",\r\n          displayName: \"Eletrônica\",\r\n          color: \"#1abc9c\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 5,\r\n      isDefault: false,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"6\",\r\n          name: \"reggae\",\r\n          displayName: \"Reggae\",\r\n          color: \"#27ae60\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 6,\r\n      isDefault: false,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"7\",\r\n          name: \"blues\",\r\n          displayName: \"Blues\",\r\n          color: \"#34495e\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 7,\r\n      isDefault: false,\r\n      usageCount: 0,\r\n        },\r\n        {\r\n          id: \"8\",\r\n          name: \"country\",\r\n          displayName: \"Country\",\r\n          color: \"#d35400\",\r\n          isActive: true,\r\n          category: \"music\",\r\n          priority: 8,\r\n      isDefault: false,\r\n      usageCount: 0,\r\n        },\r\n      ],\r\n    };\r\n    setGenres(exampleGenres);\r\n  };\r\n\r\n  const seedDefaultGenres = async () => {\r\n    try {\r\n      console.log(\"🎵 Criando gêneros padrão para restaurante:\", restaurantId);\r\n\r\n      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}/seed`);\r\n      const response = await fetch(url, {\r\n        method: \"POST\",\r\n      });\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        toast.success(data.message || \"Gêneros padrão criados com sucesso!\");\r\n        loadGenres();\r\n      } else {\r\n        const errorData = await response.json().catch(() => ({}));\r\n        toast.error(errorData.message || \"Erro ao criar gêneros padrão\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🎵 Erro ao criar gêneros padrão:\", error);\r\n      toast.error(\"Erro ao criar gêneros padrão\");\r\n    }\r\n  };\r\n\r\n  // Função para analisar uma música e identificar possíveis gêneros\r\n  const analyzeTrackGenres = (title: string, artist: string): string[] => {\r\n    const genres: string[] = [];\r\n    const text = `${title} ${artist}`.toLowerCase();\r\n\r\n    // Mapeamento de palavras-chave para gêneros (sem acentos para evitar problemas de encoding)\r\n    const genreKeywords = {\r\n      Rock: [\r\n        \"rock\",\r\n        \"metal\",\r\n        \"punk\",\r\n        \"grunge\",\r\n        \"alternative\",\r\n        \"hard rock\",\r\n        \"soft rock\",\r\n      ],\r\n      Pop: [\"pop\", \"mainstream\", \"hit\", \"chart\", \"top\", \"billboard\"],\r\n      Jazz: [\"jazz\", \"swing\", \"blues\", \"smooth\", \"bossa nova\"],\r\n      Eletronica: [\r\n        \"electronic\",\r\n        \"edm\",\r\n        \"techno\",\r\n        \"house\",\r\n        \"dance\",\r\n        \"remix\",\r\n        \"mix\",\r\n        \"dj\",\r\n      ],\r\n      \"Hip Hop\": [\"hip hop\", \"rap\", \"trap\", \"beats\", \"freestyle\", \"mc\"],\r\n      Reggae: [\"reggae\", \"ska\", \"dub\", \"bob marley\"],\r\n      Country: [\"country\", \"folk\", \"acoustic\", \"bluegrass\"],\r\n      Classica: [\r\n        \"classical\",\r\n        \"orchestra\",\r\n        \"symphony\",\r\n        \"piano\",\r\n        \"violin\",\r\n        \"opera\",\r\n      ],\r\n      Latina: [\r\n        \"latin\",\r\n        \"salsa\",\r\n        \"bachata\",\r\n        \"reggaeton\",\r\n        \"spanish\",\r\n        \"latino\",\r\n        \"merengue\",\r\n      ],\r\n      \"R&B\": [\"r&b\", \"soul\", \"funk\", \"motown\", \"rhythm\"],\r\n      Indie: [\"indie\", \"independent\", \"alternative\", \"underground\"],\r\n      Sertanejo: [\"sertanejo\", \"caipira\", \"modao\", \"universitario\", \"raiz\"],\r\n      MPB: [\"mpb\", \"bossa nova\", \"brazilian\", \"brasil\", \"caetano\", \"gilberto\"],\r\n      Forro: [\"forro\", \"nordestino\", \"sanfona\", \"zabumba\", \"triangulo\"],\r\n      Samba: [\"samba\", \"pagode\", \"carnaval\", \"escola de samba\", \"batucada\"],\r\n      Funk: [\"funk\", \"baile funk\", \"mc\", \"beat\", \"pancadao\"],\r\n      Gospel: [\"gospel\", \"religioso\", \"igreja\", \"louvor\", \"adoracao\", \"jesus\"],\r\n      Internacional: [\"english\", \"international\", \"worldwide\", \"global\"],\r\n    };\r\n\r\n    Object.entries(genreKeywords).forEach(([genre, keywords]) => {\r\n      if (keywords.some((keyword) => text.includes(keyword))) {\r\n        genres.push(genre);\r\n      }\r\n    });\r\n\r\n    return genres;\r\n  };\r\n\r\n  // Função para obter cor do gênero\r\n  const getGenreColor = (genreName: string): string => {\r\n    const colors: Record<string, string> = {\r\n      Rock: \"#DC2626\",\r\n      Pop: \"#EC4899\",\r\n      Jazz: \"#F59E0B\",\r\n      Eletronica: \"#10B981\",\r\n      \"Hip Hop\": \"#8B5CF6\",\r\n      Reggae: \"#059669\",\r\n      Country: \"#D97706\",\r\n      Classica: \"#7C3AED\",\r\n      Latina: \"#EF4444\",\r\n      \"R&B\": \"#F97316\",\r\n      Indie: \"#6366F1\",\r\n      Sertanejo: \"#84CC16\",\r\n      MPB: \"#06B6D4\",\r\n      Forro: \"#F59E0B\",\r\n      Samba: \"#EF4444\",\r\n      Funk: \"#9333EA\",\r\n      Gospel: \"#0EA5E9\",\r\n      Internacional: \"#64748B\",\r\n    };\r\n\r\n    return colors[genreName] || \"#6B7280\";\r\n  };\r\n\r\n  const handleCreateGenre = async () => {\r\n    try {\r\n      console.log(\"🎵 Criando novo gênero:\", formData);\r\n\r\n      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}`);\r\n      const response = await fetch(url, {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify(formData),\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🎵 Gênero criado:\", data);\r\n        toast.success(\"Gênero criado com sucesso!\");\r\n        setShowCreateForm(false);\r\n        resetForm();\r\n        loadGenres();\r\n      } else {\r\n        const error = await response.json().catch(() => ({}));\r\n        console.error(\"🎵 Erro ao criar gênero:\", error);\r\n        toast.error(error.message || \"Erro ao criar gênero\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🎵 Erro ao criar gênero:\", error);\r\n      toast.error(\"Erro ao criar gênero\");\r\n    }\r\n  };\r\n\r\n  const handleUpdateGenre = async () => {\r\n    if (!editingGenre) return;\r\n\r\n    try {\r\n      console.log(\"🎵 Atualizando gênero:\", editingGenre.id, formData);\r\n\r\n      const url = buildApiUrl(\r\n        `${API_CONFIG.ENDPOINTS.GENRES}/${editingGenre.id}`\r\n      );\r\n      const response = await fetch(url, {\r\n        method: \"PUT\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify(formData),\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🎵 Gênero atualizado:\", data);\r\n        toast.success(\"Gênero atualizado com sucesso!\");\r\n        setEditingGenre(null);\r\n        resetForm();\r\n        loadGenres();\r\n      } else {\r\n        const error = await response.json().catch(() => ({}));\r\n        console.error(\"🎵 Erro ao atualizar gênero:\", error);\r\n        toast.error(error.message || \"Erro ao atualizar gênero\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🎵 Erro ao atualizar gênero:\", error);\r\n      toast.error(\"Erro ao atualizar gênero\");\r\n    }\r\n  };\r\n\r\n  const handleDeleteGenre = async (genre: Genre) => {\r\n    if (genre.isDefault) {\r\n      toast.error(\"Não é possível deletar gêneros padrão do sistema\");\r\n      return;\r\n    }\r\n\r\n    if (\r\n      !confirm(\r\n        `Tem certeza que deseja deletar o gênero \"${genre.displayName}\"?`\r\n      )\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log(\"🎵 Deletando gênero:\", genre.id);\r\n\r\n      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}/${genre.id}`);\r\n      const response = await fetch(url, {\r\n        method: \"DELETE\",\r\n      });\r\n\r\n      if (response.ok) {\r\n        console.log(\"🎵 Gênero deletado com sucesso\");\r\n        toast.success(\"Gênero deletado com sucesso!\");\r\n        loadGenres();\r\n      } else {\r\n        const error = await response\r\n          .json()\r\n          .catch(() => ({ message: \"Erro desconhecido\" }));\r\n        console.error(\"🎵 Erro ao deletar gênero:\", error);\r\n\r\n        // Tratar diferentes tipos de erro\r\n        if (response.status === 400) {\r\n          toast.error(error.message || \"Não é possível deletar este gênero\");\r\n        } else if (response.status === 404) {\r\n          toast.error(\"Gênero não encontrado\");\r\n        } else {\r\n          toast.error(error.message || \"Erro ao deletar gênero\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🎵 Erro ao deletar gênero:\", error);\r\n      toast.error(\"Erro ao deletar gênero\");\r\n    }\r\n  };\r\n\r\n  const startEdit = (genre: Genre) => {\r\n    setEditingGenre(genre);\r\n    setFormData({\r\n      name: genre.name,\r\n      displayName: genre.displayName,\r\n      description: genre.description || \"\",\r\n  category: (genre.category as any),\r\n      color: genre.color,\r\n      icon: genre.icon || \"\",\r\n      priority: genre.priority,\r\n    });\r\n    setShowCreateForm(true);\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      name: \"\",\r\n      displayName: \"\",\r\n      description: \"\",\r\n      category: \"music\",\r\n      color: \"#3B82F6\",\r\n      icon: \"\",\r\n      priority: 0,\r\n    });\r\n    setEditingGenre(null);\r\n  };\r\n\r\n  const filteredGenres = Object.entries(genres).reduce(\r\n    (acc, [category, categoryGenres]) => {\r\n      if (selectedCategory !== \"all\" && category !== selectedCategory) {\r\n        return acc;\r\n      }\r\n\r\n      // Garantir que categoryGenres seja um array\r\n      const genresArray = Array.isArray(categoryGenres) ? categoryGenres : [];\r\n\r\n      const filtered = genresArray.filter(\r\n        (genre) =>\r\n          genre.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n          genre.name.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n\r\n      if (filtered.length > 0) {\r\n        acc[category] = filtered;\r\n      }\r\n\r\n      return acc;\r\n    },\r\n    {} as Record<string, Genre[]>\r\n  );\r\n\r\n  const totalGenres = Object.values(genres).flat().length;\r\n  const activeGenres = Object.values(genres)\r\n    .flat()\r\n    .filter((g) => g.isActive).length;\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <RefreshCw className=\"w-6 h-6 animate-spin text-blue-600\" />\r\n        <span className=\"ml-2 text-gray-600\">Carregando gêneros...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div className=\"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg\">\r\n            <Tag className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\r\n              Gerenciamento de Gêneros\r\n            </h2>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              {totalGenres} gêneros • {activeGenres} ativos\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={seedDefaultGenres}\r\n            className=\"flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\r\n          >\r\n            <RefreshCw className=\"w-4 h-4\" />\r\n            <span>Gêneros Padrão</span>\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setShowCreateForm(true)}\r\n            className=\"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\r\n          >\r\n            <Plus className=\"w-4 h-4\" />\r\n            <span>Novo Gênero</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"flex flex-col sm:flex-row gap-4\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Buscar gêneros...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <select\r\n          value={selectedCategory}\r\n          onChange={(e) => setSelectedCategory(e.target.value)}\r\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n        >\r\n          {Object.entries(categories).map(([key, label]) => (\r\n            <option key={key} value={key}>\r\n              {label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n      </div>\r\n\r\n      {/* Genres Grid */}\r\n      <div className=\"space-y-6\">\r\n        {Object.entries(filteredGenres).map(([category, categoryGenres]) => (\r\n          <div\r\n            key={category}\r\n            className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\"\r\n          >\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center space-x-2\">\r\n              <span>{categories[category as keyof typeof categories]}</span>\r\n              <span className=\"text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full\">\r\n                {categoryGenres.length}\r\n              </span>\r\n            </h3>\r\n\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\r\n              {categoryGenres.map((genre) => (\r\n                <motion.div\r\n                  key={genre.id}\r\n                  initial={{ opacity: 0, y: 10 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${\r\n                    selectedGenres.includes(genre.id)\r\n                      ? \"border-purple-500 bg-purple-50 dark:bg-purple-900/20\"\r\n                      : \"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500\"\r\n                  }`}\r\n                  onClick={() => mode === \"select\" && onGenreSelect?.(genre)}\r\n                >\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <div\r\n                      className=\"w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium\"\r\n                      style={{ backgroundColor: genre.color }}\r\n                    >\r\n                      {genre.icon ? (\r\n                        <Music className=\"w-4 h-4\" />\r\n                      ) : (\r\n                        genre.displayName.charAt(0).toUpperCase()\r\n                      )}\r\n                    </div>\r\n\r\n                    {mode === \"manage\" && (\r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <button\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            startEdit(genre);\r\n                          }}\r\n                          className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\r\n                        >\r\n                          <Edit3 className=\"w-3 h-3\" />\r\n                        </button>\r\n\r\n                        {!genre.isDefault && (\r\n                          <button\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              handleDeleteGenre(genre);\r\n                            }}\r\n                            className=\"p-1 text-gray-400 hover:text-red-600 transition-colors\"\r\n                          >\r\n                            <Trash2 className=\"w-3 h-3\" />\r\n                          </button>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-white text-sm mb-1\">\r\n                    {genre.displayName}\r\n                  </h4>\r\n\r\n                  {genre.description && (\r\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 mb-2\">\r\n                      {genre.description}\r\n                    </p>\r\n                  )}\r\n\r\n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\r\n                    <span className=\"flex items-center space-x-1\">\r\n                      <BarChart3 className=\"w-3 h-3\" />\r\n                      <span>{genre.usageCount}</span>\r\n                    </span>\r\n\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      {genre.isDefault && (\r\n                        <CheckCircle className=\"w-3 h-3 text-green-500\" />\r\n                      )}\r\n                      {!genre.isActive && (\r\n                        <AlertCircle className=\"w-3 h-3 text-red-500\" />\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {Object.keys(filteredGenres).length === 0 && (\r\n        <div className=\"text-center py-12\">\r\n          <Tag className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n            Nenhum gênero encontrado\r\n          </h3>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\r\n            {searchQuery\r\n              ? \"Tente uma busca diferente\"\r\n              : \"Crie seu primeiro gênero\"}\r\n          </p>\r\n          {!searchQuery && (\r\n            <button\r\n              onClick={() => setShowCreateForm(true)}\r\n              className=\"inline-flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\r\n            >\r\n              <Plus className=\"w-4 h-4\" />\r\n              <span>Criar Gênero</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Create/Edit Form Modal */}\r\n      <AnimatePresence>\r\n        {showCreateForm && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\"\r\n            onClick={() => {\r\n              setShowCreateForm(false);\r\n              resetForm();\r\n            }}\r\n          >\r\n            <motion.div\r\n              initial={{ scale: 0.95, opacity: 0 }}\r\n              animate={{ scale: 1, opacity: 1 }}\r\n              exit={{ scale: 0.95, opacity: 0 }}\r\n              className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md\"\r\n              onClick={(e) => e.stopPropagation()}\r\n            >\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\r\n                  {editingGenre ? \"Editar Gênero\" : \"Novo Gênero\"}\r\n                </h3>\r\n                <button\r\n                  onClick={() => {\r\n                    setShowCreateForm(false);\r\n                    resetForm();\r\n                  }}\r\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n                >\r\n                  <X className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                    Nome (ID)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.name}\r\n                    onChange={(e) =>\r\n                      setFormData({ ...formData, name: e.target.value })\r\n                    }\r\n                    disabled={!!editingGenre}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50\"\r\n                    placeholder=\"rock, pop, sertanejo...\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                    Nome de Exibição\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.displayName}\r\n                    onChange={(e) =>\r\n                      setFormData({ ...formData, displayName: e.target.value })\r\n                    }\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                    placeholder=\"Rock, Pop, Sertanejo...\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                    Descrição\r\n                  </label>\r\n                  <textarea\r\n                    value={formData.description}\r\n                    onChange={(e) =>\r\n                      setFormData({ ...formData, description: e.target.value })\r\n                    }\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                    rows={2}\r\n                    placeholder=\"Descrição do gênero...\"\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                      Categoria\r\n                    </label>\r\n                    <select\r\n                      value={formData.category}\r\n                      onChange={(e) =>\r\n                        setFormData({\r\n                          ...formData,\r\n                          category: e.target.value as any,\r\n                        })\r\n                      }\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                    >\r\n                      <option value=\"music\">Gênero Musical</option>\r\n                      <option value=\"mood\">Humor</option>\r\n                      <option value=\"energy\">Energia</option>\r\n                      <option value=\"time\">Horário</option>\r\n                      <option value=\"custom\">Personalizado</option>\r\n                    </select>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                      Cor\r\n                    </label>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <input\r\n                        type=\"color\"\r\n                        value={formData.color}\r\n                        onChange={(e) =>\r\n                          setFormData({ ...formData, color: e.target.value })\r\n                        }\r\n                        className=\"w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer\"\r\n                      />\r\n                      <input\r\n                        type=\"text\"\r\n                        value={formData.color}\r\n                        onChange={(e) =>\r\n                          setFormData({ ...formData, color: e.target.value })\r\n                        }\r\n                        className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                        placeholder=\"#3B82F6\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                      Ícone\r\n                    </label>\r\n                    <select\r\n                      value={formData.icon}\r\n                      onChange={(e) =>\r\n                        setFormData({ ...formData, icon: e.target.value })\r\n                      }\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                    >\r\n                      <option value=\"\">Sem ícone</option>\r\n                      {iconOptions.map((icon) => (\r\n                        <option key={icon} value={icon}>\r\n                          {icon}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                      Prioridade\r\n                    </label>\r\n                    <input\r\n                      type=\"number\"\r\n                      value={formData.priority}\r\n                      onChange={(e) =>\r\n                        setFormData({\r\n                          ...formData,\r\n                          priority: parseInt(e.target.value) || 0,\r\n                        })\r\n                      }\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n                      min=\"0\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-center justify-end space-x-3 mt-6\">\r\n                <button\r\n                  onClick={() => {\r\n                    setShowCreateForm(false);\r\n                    resetForm();\r\n                  }}\r\n                  className=\"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\r\n                >\r\n                  Cancelar\r\n                </button>\r\n                <button\r\n                  onClick={editingGenre ? handleUpdateGenre : handleCreateGenre}\r\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\r\n                >\r\n                  <Save className=\"w-4 h-4\" />\r\n                  <span>{editingGenre ? \"Atualizar\" : \"Criar\"}</span>\r\n                </button>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GenreManager;\r\n"], "names": ["GenreManager", "onGenreSelect", "selected<PERSON><PERSON><PERSON>", "mode", "restaurantId", "useRestaurantContext", "genres", "setGenres", "useState", "loading", "setLoading", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showCreateForm", "setShowCreateForm", "editingGenre", "setEditingGenre", "formData", "setFormData", "categories", "iconOptions", "useEffect", "loadGenres", "loadPlaylistBasedGenres", "url", "buildApiUrl", "API_CONFIG", "authToken", "headers", "response", "data", "prevGenres", "toast", "errorText", "loadExampleGenres", "error", "playlistsResponse", "playlists", "extractedGenres", "genreMap", "playlist", "playlistDetailResponse", "tracks", "track", "analyzeTrackGenres", "genre", "count", "priority", "genreName", "getGenreColor", "seedDefaultGenres", "errorData", "title", "artist", "text", "keywords", "keyword", "handleCreateGenre", "resetForm", "handleUpdateGenre", "handleDeleteGenre", "startEdit", "filteredGenres", "acc", "category", "categoryGenres", "filtered", "totalGenres", "activeGenres", "g", "jsxs", "jsx", "RefreshCw", "Tag", "Plus", "Search", "e", "key", "label", "motion", "Music", "Edit3", "Trash2", "BarChart3", "CheckCircle", "AlertCircle", "AnimatePresence", "X", "icon", "Save"], "mappings": "mTA4CA,MAAMA,GAA4C,CAAC,CACjD,cAAAC,EACA,eAAAC,EAAiB,CAAC,EAClB,KAAAC,EAAO,QACT,IAAM,CACE,KAAA,CAAE,aAAAC,GAAiBC,KAEnB,CAACC,EAAQC,CAAS,EAAIC,EAAA,SAAkC,CAAE,CAAA,EAC1D,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAI,EACrC,CAACG,EAAaC,CAAc,EAAIJ,WAAS,EAAE,EAC3C,CAACK,EAAkBC,CAAmB,EAAIN,WAAiB,KAAK,EAChE,CAACO,EAAgBC,CAAiB,EAAIR,WAAS,EAAK,EACpD,CAACS,EAAcC,CAAe,EAAIV,WAAuB,IAAI,EAC7D,CAACW,EAAUC,CAAW,EAAIZ,WAAS,CACvC,KAAM,GACN,YAAa,GACb,YAAa,GACb,SAAU,QACV,MAAO,UACP,KAAM,GACN,SAAU,CAAA,CACX,EAEKa,EAAa,CACjB,IAAK,QACL,MAAO,mBACP,KAAM,QACN,OAAQ,UACR,KAAM,UACN,OAAQ,eAAA,EAGJC,EAAc,CAClB,QACA,SACA,SACA,SACA,MACA,QACA,MACA,UACA,aACA,MACA,OACA,SACA,QACA,YACA,QACA,WACA,OACA,OACA,QACA,SAAA,EAGFC,EAAAA,UAAU,IAAM,CACHC,GACb,EAAG,CAAE,CAAA,EAEL,MAAMA,EAAa,SAAY,CACzB,GAAA,CACFd,EAAW,EAAI,EACP,QAAA,IAAI,0CAA2CN,CAAY,EAGnE,MAAMqB,EAAwB,EAE9B,MAAMC,EAAMC,EAAY,GAAGC,EAAW,UAAU,MAAM,EAAE,EAChD,QAAA,IAAI,iBAAkBF,CAAG,EAG3B,MAAAG,EAAY,aAAa,QAAQ,WAAW,EAC5CC,EAAkC,CACtC,eAAgB,kBAAA,EAGdD,IACMC,EAAA,cAAgB,UAAUD,CAAS,IAG7C,MAAME,EAAW,MAAM,MAAML,EAAK,CAAE,QAAAI,CAAS,CAAA,EAC7C,GAAIC,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OACpB,QAAA,IAAI,oCAAqCC,CAAI,EAGrDzB,EAAW0B,IAAgB,CACzB,GAAGD,EAAK,OACR,GAAGC,CACH,EAAA,EAEFC,EAAM,QAAQ,iCAAiC,CAAA,KAC1C,CACL,MAAMC,EAAY,MAAMJ,EAAS,KAAO,EAAA,MAAM,IAAM,EAAE,EAC9C,QAAA,MACN,uBACAA,EAAS,OACTA,EAAS,WACTI,CAAA,EAIEJ,EAAS,SAAW,KACtB,QAAQ,IAAI,qDAAqD,EAC/CK,IAClBF,EAAM,mDAAoD,CACxD,KAAM,KACN,SAAU,GAAA,CACX,GAEDA,EAAM,MAAM,0BAA0B,CAE1C,QACOG,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,EACjCD,IAClBF,EAAM,MAAM,wDAAwD,CAAA,QACpE,CACAxB,EAAW,EAAK,CAClB,CAAA,EAIIe,EAA0B,SAAY,CACtC,GAAA,CACF,QAAQ,IAAI,gDAAgD,EAG5D,MAAMa,EAAoB,MAAM,MAC9BX,EAAY,yBAAyBvB,CAAY,EAAE,CAAA,EAGjD,GAAA,CAACkC,EAAkB,GAAI,CACjB,QAAA,IACN,gEAAA,EAEF,MACF,CAGM,MAAAC,GADgB,MAAMD,EAAkB,QACd,WAAa,GAEzC,GAAAC,EAAU,SAAW,EAAG,CAC1B,QAAQ,IAAI,6CAA6C,EACzD,MACF,CAGJ,MAAMC,EAA2C,CAAA,EACvCC,MAAe,IAErB,UAAWC,KAAYH,EACrB,GAAIG,EAAS,SAAU,CAErB,MAAMC,EAAyB,MAAM,MACnChB,EAAY,cAAce,EAAS,EAAE,EAAE,CAAA,EAGzC,GAAIC,EAAuB,GAAI,CAEvB,MAAAC,GADiB,MAAMD,EAAuB,QACtB,QAAU,GAEhC,QAAA,IACN,iBAAiBC,EAAO,MAAM,yBAAyBF,EAAS,IAAI,GAAA,EAGtE,UAAWG,KAASD,EAEHE,EAAmBD,EAAM,MAAO,EAAE,EAC1C,QAASE,GAAU,CACxB,MAAMC,GAAQP,EAAS,IAAIM,CAAK,GAAK,EAC5BN,EAAA,IAAIM,EAAOC,GAAQ,CAAC,CAAA,CAC9B,CAEL,CACF,CAIF,IAAIC,EAAW,EACNR,EAAA,QAAQ,CAACO,EAAOE,IAAc,CACjCF,GAAS,IAEVR,EAAwB,MAASA,EAAwB,OAAS,CAAA,EAClEA,EAAwB,MAAM,KAAK,CAClC,GAAI,YAAYU,CAAS,GACzB,KAAMA,EAAU,YAAY,EAC5B,YAAaA,EACb,MAAOC,EAAcD,CAAS,EAC9B,SAAU,GACV,SAAU,QACV,SAAUD,IACV,YAAa,oCAAoCD,CAAK,YACtD,UAAW,GACX,WAAYA,CAAA,CACb,EACH,CACD,EAEIR,EAAwB,OAAUA,EAAwB,MAAM,OAAS,IACpE,QAAA,IAAI,oCAAqCA,CAAe,EAChEjC,EAAW0B,IAAgB,CACzB,GAAGA,EACH,MAAO,CACL,GAAKA,EAAW,OAAiB,CAAC,EAClC,GAAKO,EAAwB,KAC/B,CACA,EAAA,EACIN,EAAA,QACJ,GAAIM,EAAwB,MAAM,MAAM,qCAAA,SAGrCH,EAAO,CACN,QAAA,MAAM,6CAA8CA,CAAK,CACnE,CAAA,EAIID,EAAoB,IAAM,CA6F9B7B,EA5F6C,CAC3C,MAAO,CACL,CACE,GAAI,IACJ,KAAM,OACN,YAAa,OACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,MACN,YAAa,MACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,OACN,YAAa,OACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,YACN,YAAa,WACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,aACN,YAAa,aACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,SACN,YAAa,SACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,QACN,YAAa,QACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,EACA,CACE,GAAI,IACJ,KAAM,UACN,YAAa,UACb,MAAO,UACP,SAAU,GACV,SAAU,QACV,SAAU,EACd,UAAW,GACX,WAAY,CACV,CACF,CAAA,CAEqB,CAAA,EAGnB6C,EAAoB,SAAY,CAChC,GAAA,CACM,QAAA,IAAI,8CAA+ChD,CAAY,EAEvE,MAAMsB,EAAMC,EAAY,GAAGC,EAAW,UAAU,MAAM,OAAO,EACvDG,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,MAAA,CACT,EACD,GAAIK,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OACtBG,EAAA,QAAQF,EAAK,SAAW,qCAAqC,EACxDR,GAAA,KACN,CACC,MAAA6B,EAAY,MAAMtB,EAAS,OAAO,MAAM,KAAO,CAAG,EAAA,EAClDG,EAAA,MAAMmB,EAAU,SAAW,8BAA8B,CACjE,QACOhB,EAAO,CACN,QAAA,MAAM,mCAAoCA,CAAK,EACvDH,EAAM,MAAM,8BAA8B,CAC5C,CAAA,EAIIY,EAAqB,CAACQ,EAAeC,IAA6B,CACtE,MAAMjD,EAAmB,CAAA,EACnBkD,EAAO,GAAGF,CAAK,IAAIC,CAAM,GAAG,cAwD3B,cAAA,QArDe,CACpB,KAAM,CACJ,OACA,QACA,OACA,SACA,cACA,YACA,WACF,EACA,IAAK,CAAC,MAAO,aAAc,MAAO,QAAS,MAAO,WAAW,EAC7D,KAAM,CAAC,OAAQ,QAAS,QAAS,SAAU,YAAY,EACvD,WAAY,CACV,aACA,MACA,SACA,QACA,QACA,QACA,MACA,IACF,EACA,UAAW,CAAC,UAAW,MAAO,OAAQ,QAAS,YAAa,IAAI,EAChE,OAAQ,CAAC,SAAU,MAAO,MAAO,YAAY,EAC7C,QAAS,CAAC,UAAW,OAAQ,WAAY,WAAW,EACpD,SAAU,CACR,YACA,YACA,WACA,QACA,SACA,OACF,EACA,OAAQ,CACN,QACA,QACA,UACA,YACA,UACA,SACA,UACF,EACA,MAAO,CAAC,MAAO,OAAQ,OAAQ,SAAU,QAAQ,EACjD,MAAO,CAAC,QAAS,cAAe,cAAe,aAAa,EAC5D,UAAW,CAAC,YAAa,UAAW,QAAS,gBAAiB,MAAM,EACpE,IAAK,CAAC,MAAO,aAAc,YAAa,SAAU,UAAW,UAAU,EACvE,MAAO,CAAC,QAAS,aAAc,UAAW,UAAW,WAAW,EAChE,MAAO,CAAC,QAAS,SAAU,WAAY,kBAAmB,UAAU,EACpE,KAAM,CAAC,OAAQ,aAAc,KAAM,OAAQ,UAAU,EACrD,OAAQ,CAAC,SAAU,YAAa,SAAU,SAAU,WAAY,OAAO,EACvE,cAAe,CAAC,UAAW,gBAAiB,YAAa,QAAQ,CAAA,CAGvC,EAAE,QAAQ,CAAC,CAACR,EAAOU,CAAQ,IAAM,CACvDA,EAAS,KAAMC,GAAYF,EAAK,SAASE,CAAO,CAAC,GACnDpD,EAAO,KAAKyC,CAAK,CACnB,CACD,EAEMzC,CAAA,EAIH6C,EAAiBD,IACkB,CACrC,KAAM,UACN,IAAK,UACL,KAAM,UACN,WAAY,UACZ,UAAW,UACX,OAAQ,UACR,QAAS,UACT,SAAU,UACV,OAAQ,UACR,MAAO,UACP,MAAO,UACP,UAAW,UACX,IAAK,UACL,MAAO,UACP,MAAO,UACP,KAAM,UACN,OAAQ,UACR,cAAe,SAAA,GAGHA,CAAS,GAAK,UAGxBS,EAAoB,SAAY,CAChC,GAAA,CACM,QAAA,IAAI,0BAA2BxC,CAAQ,EAE/C,MAAMO,EAAMC,EAAY,GAAGC,EAAW,UAAU,MAAM,EAAE,EAClDG,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,UAAUP,CAAQ,CAAA,CAC9B,EAED,GAAIY,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OACpB,QAAA,IAAI,oBAAqBC,CAAI,EACrCE,EAAM,QAAQ,4BAA4B,EAC1ClB,EAAkB,EAAK,EACb4C,IACCpC,GAAA,KACN,CACC,MAAAa,EAAQ,MAAMN,EAAS,OAAO,MAAM,KAAO,CAAG,EAAA,EAC5C,QAAA,MAAM,2BAA4BM,CAAK,EACzCH,EAAA,MAAMG,EAAM,SAAW,sBAAsB,CACrD,QACOA,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,EAC/CH,EAAM,MAAM,sBAAsB,CACpC,CAAA,EAGI2B,EAAoB,SAAY,CACpC,GAAK5C,EAED,GAAA,CACF,QAAQ,IAAI,yBAA0BA,EAAa,GAAIE,CAAQ,EAE/D,MAAMO,EAAMC,EACV,GAAGC,EAAW,UAAU,MAAM,IAAIX,EAAa,EAAE,EAAA,EAE7Cc,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,MACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,UAAUP,CAAQ,CAAA,CAC9B,EAED,GAAIY,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OACpB,QAAA,IAAI,wBAAyBC,CAAI,EACzCE,EAAM,QAAQ,gCAAgC,EAC9ChB,EAAgB,IAAI,EACV0C,IACCpC,GAAA,KACN,CACC,MAAAa,EAAQ,MAAMN,EAAS,OAAO,MAAM,KAAO,CAAG,EAAA,EAC5C,QAAA,MAAM,+BAAgCM,CAAK,EAC7CH,EAAA,MAAMG,EAAM,SAAW,0BAA0B,CACzD,QACOA,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,EACnDH,EAAM,MAAM,0BAA0B,CACxC,CAAA,EAGI4B,EAAoB,MAAOf,GAAiB,CAChD,GAAIA,EAAM,UAAW,CACnBb,EAAM,MAAM,kDAAkD,EAC9D,MACF,CAEA,GACG,QACC,4CAA4Ca,EAAM,WAAW,IAAA,EAM7D,GAAA,CACM,QAAA,IAAI,uBAAwBA,EAAM,EAAE,EAEtC,MAAArB,EAAMC,EAAY,GAAGC,EAAW,UAAU,MAAM,IAAImB,EAAM,EAAE,EAAE,EAC9DhB,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,QAAA,CACT,EAED,GAAIK,EAAS,GACX,QAAQ,IAAI,gCAAgC,EAC5CG,EAAM,QAAQ,8BAA8B,EACjCV,QACN,CACC,MAAAa,EAAQ,MAAMN,EACjB,KAAK,EACL,MAAM,KAAO,CAAE,QAAS,mBAAsB,EAAA,EACzC,QAAA,MAAM,6BAA8BM,CAAK,EAG7CN,EAAS,SAAW,IAChBG,EAAA,MAAMG,EAAM,SAAW,oCAAoC,EACxDN,EAAS,SAAW,IAC7BG,EAAM,MAAM,uBAAuB,EAE7BA,EAAA,MAAMG,EAAM,SAAW,wBAAwB,CAEzD,QACOA,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjDH,EAAM,MAAM,wBAAwB,CACtC,CAAA,EAGI6B,EAAahB,GAAiB,CAClC7B,EAAgB6B,CAAK,EACT3B,EAAA,CACV,KAAM2B,EAAM,KACZ,YAAaA,EAAM,YACnB,YAAaA,EAAM,aAAe,GACtC,SAAWA,EAAM,SACb,MAAOA,EAAM,MACb,KAAMA,EAAM,MAAQ,GACpB,SAAUA,EAAM,QAAA,CACjB,EACD/B,EAAkB,EAAI,CAAA,EAGlB4C,EAAY,IAAM,CACVxC,EAAA,CACV,KAAM,GACN,YAAa,GACb,YAAa,GACb,SAAU,QACV,MAAO,UACP,KAAM,GACN,SAAU,CAAA,CACX,EACDF,EAAgB,IAAI,CAAA,EAGhB8C,EAAiB,OAAO,QAAQ1D,CAAM,EAAE,OAC5C,CAAC2D,EAAK,CAACC,EAAUC,CAAc,IAAM,CAC/B,GAAAtD,IAAqB,OAASqD,IAAarD,EACtC,OAAAoD,EAMT,MAAMG,GAFc,MAAM,QAAQD,CAAc,EAAIA,EAAiB,IAExC,OAC1BpB,GACCA,EAAM,YAAY,YAAY,EAAE,SAASpC,EAAY,YAAa,CAAA,GAClEoC,EAAM,KAAK,YAAA,EAAc,SAASpC,EAAY,aAAa,CAAA,EAG3D,OAAAyD,EAAS,OAAS,IACpBH,EAAIC,CAAQ,EAAIE,GAGXH,CACT,EACA,CAAC,CAAA,EAGGI,EAAc,OAAO,OAAO/D,CAAM,EAAE,KAAO,EAAA,OAC3CgE,EAAe,OAAO,OAAOhE,CAAM,EACtC,KAAA,EACA,OAAQiE,GAAMA,EAAE,QAAQ,EAAE,OAE7B,OAAI9D,EAEA+D,EAAA,KAAC,MAAI,CAAA,UAAU,uCACb,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAU,UAAU,oCAAqC,CAAA,EACzDD,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAqB,SAAqB,wBAAA,CAC5D,CAAA,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qDACb,eAACE,EAAI,CAAA,UAAU,+CAA+C,CAChE,CAAA,SACC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAEpE,2BAAA,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,2CACV,SAAA,CAAAH,EAAY,cAAYC,EAAa,SAAA,EACxC,CAAA,EACF,CAAA,EACF,EAEAE,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAASpB,EACT,UAAU,4KAEV,SAAA,CAACqB,EAAAA,IAAAC,EAAA,CAAU,UAAU,SAAU,CAAA,EAC/BD,EAAAA,IAAC,QAAK,SAAc,gBAAA,CAAA,CAAA,CAAA,CACtB,EAEAD,EAAA,KAAC,SAAA,CACC,QAAS,IAAMxD,EAAkB,EAAI,EACrC,UAAU,kHAEV,SAAA,CAACyD,EAAAA,IAAAG,EAAA,CAAK,UAAU,SAAU,CAAA,EAC1BH,EAAAA,IAAC,QAAK,SAAW,aAAA,CAAA,CAAA,CAAA,CACnB,CAAA,EACF,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,SACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACC,EAAAA,IAAAI,GAAA,CAAO,UAAU,0EAA2E,CAAA,EAC7FJ,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,oBACZ,MAAO9D,EACP,SAAWmE,GAAMlE,EAAekE,EAAE,OAAO,KAAK,EAC9C,UAAU,mMAAA,CACZ,CAAA,CAAA,CACF,CACF,CAAA,EAEAL,EAAA,IAAC,SAAA,CACC,MAAO5D,EACP,SAAWiE,GAAMhE,EAAoBgE,EAAE,OAAO,KAAK,EACnD,UAAU,uLAET,gBAAO,QAAQzD,CAAU,EAAE,IAAI,CAAC,CAAC0D,EAAKC,CAAK,UACzC,SAAiB,CAAA,MAAOD,EACtB,SAAAC,CAAA,EADUD,CAEb,CACD,CAAA,CACH,CAAA,EACF,EAGCN,EAAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAO,OAAA,QAAQT,CAAc,EAAE,IAAI,CAAC,CAACE,EAAUC,CAAc,IAC5DK,EAAA,KAAC,MAAA,CAEC,UAAU,uFAEV,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,qFACZ,SAAA,CAACC,EAAA,IAAA,OAAA,CAAM,SAAWpD,EAAA6C,CAAmC,CAAE,CAAA,EACtDO,EAAA,IAAA,OAAA,CAAK,UAAU,4EACb,WAAe,OAClB,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,sEACZ,SAAeN,EAAA,IAAKpB,GACnByB,EAAA,KAACS,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAW,yDACT/E,EAAe,SAAS6C,EAAM,EAAE,EAC5B,uDACA,uFACN,GACA,QAAS,IAAM5C,IAAS,WAAYF,GAAA,YAAAA,EAAgB8C,IAEpD,SAAA,CAACyB,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,UAAU,qFACV,MAAO,CAAE,gBAAiB1B,EAAM,KAAM,EAErC,SAAMA,EAAA,KACJ0B,EAAAA,IAAAS,GAAA,CAAM,UAAU,SAAA,CAAU,EAE3BnC,EAAM,YAAY,OAAO,CAAC,EAAE,YAAY,CAAA,CAE5C,EAEC5C,IAAS,UACPqE,OAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAUK,GAAM,CACdA,EAAE,gBAAgB,EAClBf,EAAUhB,CAAK,CACjB,EACA,UAAU,0DAEV,SAAA0B,EAAAA,IAACU,GAAM,CAAA,UAAU,SAAU,CAAA,CAAA,CAC7B,EAEC,CAACpC,EAAM,WACN0B,EAAA,IAAC,SAAA,CACC,QAAUK,GAAM,CACdA,EAAE,gBAAgB,EAClBhB,EAAkBf,CAAK,CACzB,EACA,UAAU,yDAEV,SAAA0B,EAAAA,IAACW,GAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAC9B,CAAA,EAEJ,CAAA,EAEJ,EAECX,EAAA,IAAA,KAAA,CAAG,UAAU,yDACX,WAAM,YACT,EAEC1B,EAAM,aACL0B,EAAA,IAAC,KAAE,UAAU,gDACV,WAAM,YACT,EAGFD,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,8BACd,SAAA,CAACC,EAAAA,IAAAY,GAAA,CAAU,UAAU,SAAU,CAAA,EAC/BZ,EAAAA,IAAC,OAAM,CAAA,SAAA1B,EAAM,UAAW,CAAA,CAAA,EAC1B,EAEAyB,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CAAAzB,EAAM,WACL0B,EAAA,IAACa,GAAY,CAAA,UAAU,yBAAyB,EAEjD,CAACvC,EAAM,UACL0B,EAAA,IAAAc,GAAA,CAAY,UAAU,uBAAuB,CAAA,EAElD,CAAA,EACF,CAAA,CAAA,EAzEKxC,EAAM,EA2Ed,CAAA,EACH,CAAA,CAAA,EAzFKmB,CA2FR,CAAA,EACH,EAEC,OAAO,KAAKF,CAAc,EAAE,SAAW,GACtCQ,EAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAI,UAAU,sCAAuC,CAAA,EACrDF,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,2BAAA,QACC,IAAE,CAAA,UAAU,wCACV,SAAA9D,EACG,4BACA,2BACN,EACC,CAACA,GACA6D,EAAA,KAAC,SAAA,CACC,QAAS,IAAMxD,EAAkB,EAAI,EACrC,UAAU,yHAEV,SAAA,CAACyD,EAAAA,IAAAG,EAAA,CAAK,UAAU,SAAU,CAAA,EAC1BH,EAAAA,IAAC,QAAK,SAAY,cAAA,CAAA,CAAA,CAAA,CACpB,CAAA,EAEJ,EAIFA,EAAAA,IAACe,IACE,SACCzE,GAAA0D,EAAA,IAACQ,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,CAAE,EACtB,QAAS,CAAE,QAAS,CAAE,EACtB,KAAM,CAAE,QAAS,CAAE,EACnB,UAAU,iFACV,QAAS,IAAM,CACbjE,EAAkB,EAAK,EACb4C,GACZ,EAEA,SAAAY,EAAA,KAACS,EAAO,IAAP,CACC,QAAS,CAAE,MAAO,IAAM,QAAS,CAAE,EACnC,QAAS,CAAE,MAAO,EAAG,QAAS,CAAE,EAChC,KAAM,CAAE,MAAO,IAAM,QAAS,CAAE,EAChC,UAAU,2DACV,QAAUH,GAAMA,EAAE,gBAAgB,EAElC,SAAA,CAACN,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,sDACX,SAAAxD,EAAe,gBAAkB,cACpC,EACAwD,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,CACbzD,EAAkB,EAAK,EACb4C,GACZ,EACA,UAAU,6DAEV,SAAAa,EAAAA,IAACgB,GAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,CAAA,EACF,EAEAjB,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,YAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOtD,EAAS,KAChB,SAAW2D,GACT1D,EAAY,CAAE,GAAGD,EAAU,KAAM2D,EAAE,OAAO,MAAO,EAEnD,SAAU,CAAC,CAAC7D,EACZ,UAAU,kNACV,YAAY,yBAAA,CACd,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACwD,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,mBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOtD,EAAS,YAChB,SAAW2D,GACT1D,EAAY,CAAE,GAAGD,EAAU,YAAa2D,EAAE,OAAO,MAAO,EAE1D,UAAU,8LACV,YAAY,yBAAA,CACd,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACL,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,YAAA,EACAA,EAAA,IAAC,WAAA,CACC,MAAOtD,EAAS,YAChB,SAAW2D,GACT1D,EAAY,CAAE,GAAGD,EAAU,YAAa2D,EAAE,OAAO,MAAO,EAE1D,UAAU,8LACV,KAAM,EACN,YAAY,wBAAA,CACd,CAAA,EACF,EAEAN,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,YAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAOrD,EAAS,SAChB,SAAW2D,GACT1D,EAAY,CACV,GAAGD,EACH,SAAU2D,EAAE,OAAO,KAAA,CACpB,EAEH,UAAU,8LAEV,SAAA,CAACL,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAc,iBAAA,EACnCA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAK,QAAA,EACzBA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAO,UAAA,EAC7BA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAO,UAAA,EAC3BA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAa,gBAAA,CAAA,CAAA,CACtC,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,MAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,QACL,MAAOtD,EAAS,MAChB,SAAW2D,GACT1D,EAAY,CAAE,GAAGD,EAAU,MAAO2D,EAAE,OAAO,MAAO,EAEpD,UAAU,iFAAA,CACZ,EACAL,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOtD,EAAS,MAChB,SAAW2D,GACT1D,EAAY,CAAE,GAAGD,EAAU,MAAO2D,EAAE,OAAO,MAAO,EAEpD,UAAU,8LACV,YAAY,SAAA,CACd,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAEAN,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,QAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAOrD,EAAS,KAChB,SAAW2D,GACT1D,EAAY,CAAE,GAAGD,EAAU,KAAM2D,EAAE,OAAO,MAAO,EAEnD,UAAU,8LAEV,SAAA,CAACL,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAS,YAAA,EACzBnD,EAAY,IAAKoE,GAChBjB,EAAA,IAAC,UAAkB,MAAOiB,EACvB,SADUA,CAAA,EAAAA,CAEb,CACD,CAAA,CAAA,CACH,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACjB,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,aAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOtD,EAAS,SAChB,SAAW2D,GACT1D,EAAY,CACV,GAAGD,EACH,SAAU,SAAS2D,EAAE,OAAO,KAAK,GAAK,CAAA,CACvC,EAEH,UAAU,8LACV,IAAI,GAAA,CACN,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAEAN,EAAAA,KAAC,MAAI,CAAA,UAAU,+CACb,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,CACbzD,EAAkB,EAAK,EACb4C,GACZ,EACA,UAAU,mHACX,SAAA,UAAA,CAED,EACAY,EAAA,KAAC,SAAA,CACC,QAASvD,EAAe4C,EAAoBF,EAC5C,UAAU,kHAEV,SAAA,CAACc,EAAAA,IAAAkB,GAAA,CAAK,UAAU,SAAU,CAAA,EACzBlB,EAAA,IAAA,OAAA,CAAM,SAAexD,EAAA,YAAc,QAAQ,CAAA,CAAA,CAC9C,CAAA,EACF,CAAA,CAAA,CACF,CAAA,CAAA,EAGN,CACF,CAAA,CAAA,CAEJ"}