import{e as m,c as s,j as a}from"./index-6e2e4ef2.js";import{r as t}from"./vendor-66b0ef43.js";import{c as V}from"./router-f729e475.js";import{af as F,R as p,aJ as Y,k as S,j as H,A as Z,m as C,aK as W,U as X,B as q,Q as ee,Z as ae,D as re,q as se}from"./ui-a5f8f5f0.js";import"./utils-08f61814.js";const h="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",ce=t.memo(()=>{const{restaurantId:M}=V(),[f,T]=t.useState([]),[d,i]=t.useState(!1),[N,y]=t.useState({}),[z,c]=t.useState(!1),[x,Q]=t.useState(""),[w,v]=t.useState(""),[g,K]=t.useState(5),[E,A]=t.useState({}),l=M||"demo-restaurant",D=t.useCallback(()=>{const e=`/couvert/${l}`;window.open(e,"_blank","noopener,noreferrer")},[l]),o=t.useCallback(async()=>{var e;i(!0);try{console.log("🔍 QR: Carregando QR codes para restaurante:",l);const r=await m.client.get(`/qrcode/${l}`);console.log("🔍 QR: Dados recebidos:",r.data);const n=(r.data.qrCodes||[]).filter(b=>b&&b.id&&b.qrCodeDataURL);n.length<(r.data.qrCodes||[]).length&&console.warn("🔍 QR: Alguns QR codes foram filtrados por dados inválidos"),A({}),T(n);const u=n.filter(b=>b.qrCodeDataURL===h).length;u>0?(console.warn(`🔍 QR: ${u} QR codes são imagens placeholder`),s&&s(`${u} QR codes precisam ser regenerados`,{icon:"⚠️"})):(e=s)!=null&&e.success&&s.success(`${n.length} QR codes carregados`)}catch(r){console.error("🔍 QR: Erro ao carregar QR codes:",r),s.error("Erro ao carregar QR codes")}finally{i(!1)}},[l]);t.useEffect(()=>{o()},[o]);const U=t.useCallback(e=>{console.error(`🔍 QR: Erro ao carregar imagem para QR code ID: ${e}`),A(r=>({...r,[e]:!0}))},[]),B=t.useCallback(async()=>{var e;if(!x.trim()){s.error("Número da mesa é obrigatório");return}i(!0);try{console.log("🔍 QR: Gerando QR code para mesa:",x);const r=await m.client.post("/qrcode/table",{restaurantId:l,tableNumber:x.trim(),tableName:w.trim()||`Mesa ${x}`});console.log("🔍 QR: Resposta da geração:",r.data),r.data.success?((e=r.data.qrCode)==null?void 0:e.qrCodeDataURL)===h?s("QR Code gerado como placeholder, tente novamente",{icon:"⚠️"}):(s.success("QR Code gerado com sucesso!"),Q(""),v(""),c(!1),await o()):s.error(r.data.error||"Erro ao gerar QR Code")}catch(r){console.error("Erro ao gerar QR code:",r),s.error("Erro ao gerar QR Code")}finally{i(!1)}},[l,x,w,o]),P=t.useCallback(async()=>{if(g<1||g>50){s.error("Número de mesas deve estar entre 1 e 50");return}i(!0);try{const e=await m.client.post("/qrcode/bulk-tables",{restaurantId:l,tableCount:g,tablePrefix:"Mesa"});console.log("🔍 QR: Resposta da geração em lote:",e.data),e.data.success?(s.success(`${e.data.totalGenerated} QR Codes gerados com sucesso!`),c(!1),await o()):s.error(e.data.error||"Erro ao gerar QR Codes")}catch(e){console.error("Erro ao gerar QR codes em lote:",e),s.error("Erro ao gerar QR Codes")}finally{i(!1)}},[l,g,o]),J=t.useCallback(async()=>{var e;i(!0);try{const r=await m.client.post("/qrcode/restaurant",{restaurantId:l,restaurantName:"Restaurante Demo"});console.log("🔍 QR: Resposta da geração do QR do restaurante:",r.data),r.data.success?((e=r.data.qrCode)==null?void 0:e.qrCodeDataURL)===h?s("QR Code gerado como placeholder, tente novamente",{icon:"⚠️"}):(s.success("QR Code do restaurante gerado com sucesso!"),c(!1),await o()):s.error(r.data.error||"Erro ao gerar QR Code")}catch(r){console.error("Erro ao gerar QR code do restaurante:",r),s.error("Erro ao gerar QR Code")}finally{i(!1)}},[l,o]);t.useCallback(async e=>{y(r=>({...r,[e.id]:!0}));try{let r;e.type==="table"?r=await m.client.post("/qrcode/regenerate/table",{qrCodeId:e.id,restaurantId:l,tableNumber:e.tableNumber||"1",tableName:e.name}):r=await m.client.post("/qrcode/regenerate/restaurant",{qrCodeId:e.id,restaurantId:l,restaurantName:e.name}),console.log("🔍 QR: Resposta da regeneração:",r.data),r.data.success?(s.success("QR Code regenerado com sucesso!"),await o()):s.error(r.data.error||"Erro ao regenerar QR Code")}catch(r){console.error("Erro ao regenerar QR code:",r),s.error("Erro ao regenerar QR Code")}finally{y(r=>({...r,[e.id]:!1}))}},[l,o]);const L=t.useCallback(async e=>{try{y(k=>({...k,[e.id]:!0}));const r=encodeURIComponent(e.url),n=300,u=`https://api.qrserver.com/v1/create-qr-code/?data=${r}&size=${n}x${n}&margin=10`,O=await(await fetch(u)).blob(),j=new FileReader;j.readAsDataURL(O),j.onloadend=async()=>{const k=j.result;try{(await m.client.post("/qrcode/update-image",{qrCodeId:e.id,qrCodeDataURL:k})).data.success?(s.success("QR Code regenerado com sucesso!"),await o()):s.error("Erro ao atualizar QR Code no servidor")}catch(R){console.error("Erro ao atualizar QR code no servidor:",R),s.error("Erro ao atualizar QR Code")}y(R=>({...R,[e.id]:!1}))}}catch(r){console.error("Erro ao gerar QR code localmente:",r),s.error("Erro ao gerar QR Code localmente"),y(n=>({...n,[e.id]:!1}))}},[o]),$=t.useCallback(e=>{navigator.clipboard.writeText(e),s.success("URL copiada para a área de transferência!")},[]),G=t.useCallback(async e=>{if(window.confirm("Tem certeza que deseja deletar este QR Code?"))try{const r=await m.client.delete(`/qrcode/${e}`);r.data.success?(s.success("QR Code deletado com sucesso!"),await o()):s.error(r.data.error||"Erro ao deletar QR Code")}catch(r){console.error("Erro ao deletar QR code:",r),s.error("Erro ao deletar QR Code")}},[o]),I=t.useCallback(e=>{if(!e.qrCodeDataURL||e.qrCodeDataURL===h){s.error("Este QR Code precisa ser regenerado antes de baixar");return}try{const r=document.createElement("a");r.href=e.qrCodeDataURL,r.download=`${e.name}-${e.id}.png`,document.body.appendChild(r),r.click(),document.body.removeChild(r),s.success("QR Code baixado com sucesso!")}catch(r){console.error("Erro ao fazer download do QR code:",r),s.error("Erro ao baixar QR Code")}},[]),_=t.useCallback(e=>{const r=E[e.id],n=e.qrCodeDataURL===h,u=N[e.id];return r||!e.qrCodeDataURL||n?a.jsx("div",{className:"w-32 h-32 mx-auto flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg mb-3",children:a.jsxs("div",{className:"text-center",children:[a.jsx(F,{className:"w-8 h-8 text-yellow-500 mx-auto mb-2"}),a.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"QR indisponível"}),a.jsxs("button",{onClick:()=>L(e),disabled:u,className:"mt-2 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-1 mx-auto",children:[u?a.jsx(p,{className:"w-3 h-3 animate-spin"}):a.jsx(Y,{className:"w-3 h-3"}),a.jsx("span",{children:"Regenerar"})]})]})}):a.jsx("img",{src:e.qrCodeDataURL,alt:`QR Code para ${e.name}`,className:"w-32 h-32 mx-auto rounded-lg mb-3",loading:"lazy",onError:()=>U(e.id)})},[E,N,U,L]);return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"QR Codes"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie os QR Codes para acesso dos clientes"})]}),a.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[a.jsxs("button",{onClick:D,onKeyDown:e=>e.key==="Enter"&&D(),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Abrir Couvert do restaurante em nova aba",children:[a.jsx(S,{className:"w-4 h-4"}),a.jsx("span",{className:"uppercase",children:"COUVERT"})]}),a.jsxs("button",{onClick:()=>c(!0),onKeyDown:e=>e.key==="Enter"&&c(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Gerar novo QR Code",children:[a.jsx(H,{className:"w-4 h-4"}),a.jsx("span",{children:"Gerar QR Code"})]})]})]}),a.jsx(Z,{children:z&&a.jsx(C.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:a.jsxs(C.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},transition:{duration:.2},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Gerar QR Code"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:"Mesa Individual"}),a.jsxs("div",{className:"space-y-3",children:[a.jsx("input",{type:"text",value:x,onChange:e=>Q(e.target.value),placeholder:"Número da mesa (ex: 1, 2, 3...)",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Número da mesa"}),a.jsx("input",{type:"text",value:w,onChange:e=>v(e.target.value),placeholder:"Nome da mesa (opcional)",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Nome da mesa"}),a.jsxs("button",{onClick:B,disabled:d||!x.trim(),className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors","aria-label":"Gerar QR Code para mesa individual",children:[d?a.jsx(p,{className:"w-4 h-4 animate-spin"}):a.jsx(W,{className:"w-4 h-4"}),a.jsx("span",{children:"Gerar Mesa"})]})]})]}),a.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:"Múltiplas Mesas"}),a.jsxs("div",{className:"space-y-3",children:[a.jsx("input",{type:"number",value:g,onChange:e=>K(Math.max(1,Math.min(50,parseInt(e.target.value)||1))),min:"1",max:"50",placeholder:"Quantidade de mesas",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Quantidade de mesas"}),a.jsxs("button",{onClick:P,disabled:d,className:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors","aria-label":`Gerar ${g} QR Codes para mesas`,children:[d?a.jsx(p,{className:"w-4 h-4 animate-spin"}):a.jsx(X,{className:"w-4 h-4"}),a.jsxs("span",{children:["Gerar ",g," Mesas"]})]})]})]}),a.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:"QR Code Geral do Restaurante"}),a.jsxs("button",{onClick:J,disabled:d,className:"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors","aria-label":"Gerar QR Code geral do restaurante",children:[d?a.jsx(p,{className:"w-4 h-4 animate-spin"}):a.jsx(q,{className:"w-4 h-4"}),a.jsx("span",{children:"Gerar QR Geral"})]})]})]}),a.jsx("div",{className:"flex space-x-3 mt-6",children:a.jsx("button",{onClick:()=>c(!1),onKeyDown:e=>e.key==="Enter"&&c(!1),className:"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors","aria-label":"Cancelar geração de QR Code",children:"Cancelar"})})]})})}),a.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[a.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"QR Codes Criados"}),a.jsxs("button",{onClick:o,onKeyDown:e=>e.key==="Enter"&&o(),className:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1 transition-colors","aria-label":"Atualizar lista de QR Codes",children:[a.jsx(p,{className:`w-4 h-4 ${d?"animate-spin":""}`}),a.jsx("span",{children:d?"Carregando...":"Atualizar"})]})]})}),a.jsx("div",{className:"p-6",children:d&&f.length===0?a.jsxs("div",{className:"text-center py-12",children:[a.jsx(p,{className:"w-12 h-12 text-gray-400 mx-auto mb-4 animate-spin"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Carregando QR Codes..."})]}):f.length===0?a.jsxs("div",{className:"text-center py-12",children:[a.jsx(ee,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Nenhum QR Code criado"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Crie QR Codes para que os clientes possam acessar o sistema"}),a.jsx("button",{onClick:()=>c(!0),onKeyDown:e=>e.key==="Enter"&&c(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Criar primeiro QR Code",children:"Criar Primeiro QR Code"})]}):a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>a.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:[a.jsxs("div",{className:"text-center mb-4",children:[_(e),a.jsx("h4",{className:"font-medium text-gray-900 dark:text-white truncate",children:e.name}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.type==="table"?"Mesa":"Restaurante"})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 break-all",children:e.url}),a.jsxs("div",{className:"flex space-x-2",children:[a.jsxs("button",{onClick:()=>window.open(e.url,"_blank"),onKeyDown:r=>r.key==="Enter"&&window.open(e.url,"_blank"),className:"flex-1 px-2 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs hover:bg-blue-200 dark:hover:bg-blue-900/30 flex items-center justify-center space-x-1 transition-colors","aria-label":`Testar QR Code ${e.name}`,children:[a.jsx(S,{className:"w-3 h-3"}),a.jsx("span",{children:"Testar"})]}),a.jsxs("button",{onClick:()=>$(e.url),onKeyDown:r=>r.key==="Enter"&&$(e.url),className:"flex-1 px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 rounded text-xs hover:bg-green-200 dark:hover:bg-green-900/30 flex items-center justify-center space-x-1 transition-colors","aria-label":`Copiar URL do QR Code ${e.name}`,children:[a.jsx(ae,{className:"w-3 h-3"}),a.jsx("span",{children:"Copiar"})]}),a.jsxs("button",{onClick:()=>I(e),onKeyDown:r=>r.key==="Enter"&&I(e),className:"flex-1 px-2 py-1 bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 rounded text-xs hover:bg-purple-200 dark:hover:bg-purple-900/30 flex items-center justify-center space-x-1 transition-colors","aria-label":`Baixar QR Code ${e.name}`,disabled:e.qrCodeDataURL===h,children:[a.jsx(re,{className:"w-3 h-3"}),a.jsx("span",{children:"Baixar"})]}),a.jsx("button",{onClick:()=>G(e.id),onKeyDown:r=>r.key==="Enter"&&G(e.id),className:"px-2 py-1 bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400 rounded text-xs hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors","aria-label":`Deletar QR Code ${e.name}`,children:a.jsx(se,{className:"w-3 h-3"})})]})]})]},e.id))})})]})]})});export{ce as default};
//# sourceMappingURL=QRCodeManager-a9b0104f.js.map
