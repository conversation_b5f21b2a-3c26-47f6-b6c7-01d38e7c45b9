import React, {
  createContext,
  useContext,
  lazy,
  Suspense,
  useEffect,
  useState,
  useCallback,
} from "react";
import {
  Routes,
  Route,
  Navigate,
  Link,
  useNavigate,
  useLocation,
  useParams,
} from "react-router-dom";
import { motion } from "framer-motion";
import {
  BarChart3,
  Music,
  Users,
  Settings,
  PlayCircle,
  Clock,
  Star,
  ThumbsUp,
  Play,
  LogOut,
  User,
  QrCode,
  AlertCircle,
  Wifi,
  WifiOff,
  RefreshCw,
} from "lucide-react";
import { useQuery, useQueryClient } from "react-query";
import { toast } from "react-hot-toast";
import { useAuth } from "@/store";
import apiService from "@/services/api";
import { useWebSocket } from "@/services/websocket";

// Lazy-loaded components
const PlaylistManager = lazy(
  () => import("@/components/restaurant/PlaylistManager")
);
const MusicPlayer = lazy(() => import("@/components/restaurant/MusicPlayer"));
const PlaybackController = lazy(() => import("@/components/restaurant/PlaybackController"));
const UnifiedAnalytics = lazy(
  () => import("@/components/restaurant/UnifiedAnalytics")
);
const GenreManager = lazy(() => import("@/components/restaurant/GenreManager"));
const RestaurantProfile = lazy(
  () => import("@/components/restaurant/RestaurantProfile")
);
const QRCodeManager = lazy(
  () => import("@/components/restaurant/QRCodeManager")
);
const ProblematicTracksAlert = lazy(
  () => import("@/components/restaurant/ProblematicTracksAlert")
);
const AdvancedModeration = lazy(
  () => import("@/components/admin/AdvancedModeration")
);
const RestaurantSettings = lazy(
  () => import("@/components/admin/RestaurantSettings")
);
const EnhancedRestaurantProfile = lazy(
  () => import("@/components/restaurant/EnhancedRestaurantProfile")
);
// (removido) const CollaborativePlaylistManager

// Types
interface RestaurantContextType {
  restaurantId: string;
  isConnected: boolean;
  lastUpdate: Date;
}

interface DashboardStats {
  totalSuggestions: number;
  totalVotes: number;
  pendingSuggestions: number;
  dailyStats: { suggestions: number; votes: number };
  totalPlays: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
  estimatedWaitTime?: number;
  currentlyPlaying?: {
    title: string;
    artist: string;
    remainingTime?: number;
  };
}

interface Activity {
  id: string;
  title: string;
  artist: string;
  createdAt: string;
  upvotes?: number;
  type?: "suggestion" | "vote" | "play";
  priority?: "normal" | "premium";
}

interface QueueItem {
  id: string;
  title: string;
  artist: string;
  upvotes: number;
  downvotes: number;
  duration?: number;
  priority?: "normal" | "premium";
  estimatedPlayTime?: Date;
}

// Restaurant Context
const RestaurantContext = createContext<RestaurantContextType>({
  restaurantId: "",
  isConnected: false,
  lastUpdate: new Date(),
});

export const useRestaurantContext = () => useContext(RestaurantContext);

// Cache Manager for Local Storage
class CacheManager {
  private static CACHE_PREFIX = "restaurant_dashboard_";

  static setCache(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    const cacheData = {
      data,
      timestamp: Date.now(),
      ttl,
    };
    localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));
  }

  static getCache(key: string) {
    try {
      const cached = localStorage.getItem(this.CACHE_PREFIX + key);
      if (!cached) return null;

      const { data, timestamp, ttl } = JSON.parse(cached);
      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(this.CACHE_PREFIX + key);
        return null;
      }

      return data;
    } catch {
      return null;
    }
  }

  static clearCache() {
    Object.keys(localStorage)
      .filter((key) => key.startsWith(this.CACHE_PREFIX))
      .forEach((key) => localStorage.removeItem(key));
  }
}

// Skeleton Component for Loading
const SkeletonCard = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse">
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
  </div>
);

// Stat Card Component
const StatCard: React.FC<{
  title: string;
  value: string;
  change: string;
  icon: React.FC<{ className?: string }>;
  color: string;
  bgColor: string;
  description: string;
}> = React.memo(
  ({ title, value, change, icon: Icon, color, bgColor, description }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {title}
            </p>
            <div
              className={`w-10 h-10 rounded-lg ${bgColor} flex items-center justify-center`}
            >
              <Icon className={`w-5 h-5 ${color}`} />
            </div>
          </div>
          <p className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
            {value}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
            {description}
          </p>
          <div className="flex items-center">
            <span
              className={`text-sm font-medium ${
                change.startsWith("+")
                  ? "text-green-600 dark:text-green-400"
                  : "text-gray-600 dark:text-gray-400"
              }`}
            >
              {change}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
              hoje
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  )
);

// Dashboard Home Component
const DashboardHome: React.FC = () => {
  const { restaurantId, isConnected } = useRestaurantContext();
  const queryClient = useQueryClient();
  const { on, off, emit } = useWebSocket();

  // Local state for real-time updates
  const [realtimeStats, setRealtimeStats] = useState<Partial<DashboardStats>>(
    {}
  );
  const [realtimeActivity, setRealtimeActivity] = useState<Activity[]>([]);
  const [realtimeQueue, setRealtimeQueue] = useState<QueueItem[]>([]);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());

  // Queries with cache fallback
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
  } = useQuery(["analytics", restaurantId], async () => {
      try {
        const data = await apiService.getAnalytics(restaurantId);
        CacheManager.setCache(`stats_${restaurantId}`, data);
        return data;
      } catch (error) {
        const cached = CacheManager.getCache(`stats_${restaurantId}`);
        if (cached) {
          toast("Usando dados em cache devido à falha de conexão", { icon: "ℹ️" });
          return cached;
        }
        throw error;
      }
    }, {
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const { data: suggestionsData, isLoading: suggestionsLoading } = useQuery(["suggestions", restaurantId], async () => {
      try {
        const data = await apiService.getSuggestions(restaurantId, {
          limit: 10,
        });
        CacheManager.setCache(`suggestions_${restaurantId}`, data);
        return data;
      } catch (error) {
        const cached = CacheManager.getCache(`suggestions_${restaurantId}`);
        if (cached) return cached;
        throw error;
      }
    }, { staleTime: 30 * 1000, retry: 2 });

  const { data: queueData, isLoading: queueLoading } = useQuery(["queue", restaurantId], async () => {
      try {
        const data = await apiService.getPlayQueue(restaurantId);
        CacheManager.setCache(`queue_${restaurantId}`, data);
        return data;
      } catch (error) {
        const cached = CacheManager.getCache(`queue_${restaurantId}`);
        if (cached) return cached;
        throw error;
      }
    }, { staleTime: 15 * 1000, retry: 2 });

  // Calculate estimated wait time
  const calculateWaitTime = useCallback((queue: QueueItem[]) => {
    return queue.reduce((total, item, index) => {
      if (index === 0) return 0; // Currently playing
      return total + (item.duration || 180); // Default 3min per song
    }, 0);
  }, []);

  // WebSocket event handlers
  useEffect(() => {
    if (!restaurantId || !isConnected) return;

    const handleStatsUpdate = (data: Partial<DashboardStats>) => {
      setRealtimeStats((prev) => ({ ...prev, ...data }));
      setLastUpdateTime(new Date());

      // Update query cache
      queryClient.setQueryData(["analytics", restaurantId], (old: any) => ({
        ...old,
        summary: { ...old?.summary, ...data },
      }));
    };

    const handleNewSuggestion = (suggestion: Activity) => {
      setRealtimeActivity((prev) => [
        { ...suggestion, type: "suggestion" },
        ...prev.slice(0, 9),
      ]);

      // Update stats
      setRealtimeStats((prev) => ({
        ...prev,
        totalSuggestions: (prev.totalSuggestions || 0) + 1,
        dailyStats: {
          ...prev.dailyStats,
          suggestions: (prev.dailyStats?.suggestions || 0) + 1,
          votes: prev.dailyStats?.votes || 0,
        },
      }));

      toast.success(`🎵 Nova sugestão: ${suggestion.title}`, {
        duration: 3000,
        position: "bottom-right",
      });
    };

    const handleQueueUpdate = (queue: QueueItem[]) => {
      setRealtimeQueue(queue);

      // Calculate estimated wait time
      const waitTime = calculateWaitTime(queue);
      setRealtimeStats((prev) => ({ ...prev, estimatedWaitTime: waitTime }));

      // Update query cache
      queryClient.setQueryData(["queue", restaurantId], { queue });
    };

    const handleVoteUpdate = (data: {
      suggestionId: string;
      upvotes: number;
      downvotes: number;
    }) => {
      setRealtimeStats((prev) => ({
        ...prev,
        totalVotes: (prev.totalVotes || 0) + 1,
        dailyStats: {
          suggestions: prev.dailyStats?.suggestions || 0,
          votes: (prev.dailyStats?.votes || 0) + 1,
        },
      }));
    };

    const handleCurrentTrackUpdate = (track: any) => {
      setRealtimeStats((prev) => ({
        ...prev,
        currentlyPlaying: track
          ? {
              title: track.title,
              artist: track.artist,
              remainingTime: track.remainingTime,
            }
          : undefined,
      }));
    };

  // Register event listeners (usar nomes tipados)
  // on("dashboard-stats-update", handleStatsUpdate); // não tipado
  on("new-suggestion", handleNewSuggestion as any);
  on("queue-update", handleQueueUpdate as any);
  on("vote-update", handleVoteUpdate as any);
  on("now-playing", handleCurrentTrackUpdate as any);

    // Join restaurant room for real-time updates
    emit("join-restaurant", { restaurantId });

    return () => {
  // off("dashboard-stats-update", handleStatsUpdate);
  off("new-suggestion", handleNewSuggestion as any);
  off("queue-update", handleQueueUpdate as any);
  off("vote-update", handleVoteUpdate as any);
  off("now-playing", handleCurrentTrackUpdate as any);
      emit("leave-restaurant", { restaurantId });
    };
  }, [
    restaurantId,
    isConnected,
    on,
    off,
    emit,
    queryClient,
    calculateWaitTime,
  ]);

  // Merge real-time data with query data
  const stats = React.useMemo(() => {
    const baseStats = statsData?.summary || {};
    return {
      totalSuggestions:
        realtimeStats.totalSuggestions ?? baseStats.totalSuggestions ?? 0,
      totalVotes: realtimeStats.totalVotes ?? baseStats.totalVotes ?? 0,
      pendingSuggestions:
        realtimeStats.pendingSuggestions ?? baseStats.pendingSuggestions ?? 0,
      dailyStats: {
        suggestions:
          realtimeStats.dailyStats?.suggestions ??
          baseStats.dailySuggestions ??
          0,
        votes: realtimeStats.dailyStats?.votes ?? baseStats.dailyVotes ?? 0,
      },
      totalPlays: realtimeStats.totalPlays ?? baseStats.totalPlays ?? 0,
      activeUsers: realtimeStats.activeUsers ?? baseStats.activeUsers ?? 0,
      averageRating:
        realtimeStats.averageRating ?? baseStats.averageRating ?? 0,
      growthRate: realtimeStats.growthRate ?? baseStats.growthRate ?? 0,
      peakHour: realtimeStats.peakHour ?? baseStats.peakHour ?? "0:00",
      topGenre: realtimeStats.topGenre ?? baseStats.topGenre ?? "N/A",
      estimatedWaitTime: realtimeStats.estimatedWaitTime,
      currentlyPlaying: realtimeStats.currentlyPlaying,
    };
  }, [statsData, realtimeStats]);

  const recentActivity = React.useMemo(() => {
    const baseActivity = suggestionsData?.suggestions || [];
    const combinedActivity = [
      ...realtimeActivity,
      ...baseActivity.map((item: any) => ({
        id: item.id || `suggestion-${Date.now()}-${Math.random()}`,
        title: item.title || "Título não disponível",
        artist: item.artist || "Artista desconhecido",
        createdAt: item.createdAt || new Date().toISOString(),
        upvotes: item.upvotes || 0,
        type: "suggestion" as const,
      })),
    ];

    // Remove duplicates and limit to 10
    const uniqueActivity = combinedActivity
      .filter(
        (item, index, arr) => arr.findIndex((a) => a.id === item.id) === index
      )
      .slice(0, 10);

    return uniqueActivity;
  }, [suggestionsData, realtimeActivity]);

  const currentQueue = React.useMemo(() => {
    const baseQueue = queueData?.queue || [];
    const activeQueue = realtimeQueue.length > 0 ? realtimeQueue : baseQueue;

    return activeQueue.slice(0, 10).map((item: any, index: number) => {
      const estimatedStart = new Date();
      estimatedStart.setSeconds(estimatedStart.getSeconds() + index * 180); // 3min average

      return {
        id: item.id || `queue-${Date.now()}-${Math.random()}`,
        title: item.title || "Título não disponível",
        artist: item.artist || "Artista desconhecido",
        upvotes: item.upvotes || 0,
        downvotes: item.downvotes || 0,
        duration: item.duration || 180,
        priority: item.priority || "normal",
        estimatedPlayTime: estimatedStart,
      };
    });
  }, [queueData, realtimeQueue]);

  const dashboardStats = React.useMemo(
    () => [
      {
        title: "Sugestões Hoje",
        value: stats.dailyStats.suggestions.toString(),
        change:
          stats.growthRate > 0 ? `+${stats.growthRate.toFixed(1)}%` : "0%",
        icon: Music,
        color: "text-blue-600 dark:text-blue-400",
        bgColor: "bg-blue-100 dark:bg-blue-900/20",
        description: "Novas sugestões recebidas hoje",
        trend: stats.growthRate > 0 ? "up" : "stable",
      },
      {
        title: "Total de Votos",
        value: stats.totalVotes.toString(),
        change: `+${stats.dailyStats.votes}`,
        icon: ThumbsUp,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-green-100 dark:bg-green-900/20",
        description: "Votos acumulados de clientes",
        trend: stats.dailyStats.votes > 0 ? "up" : "stable",
      },
      {
        title: "Fila de Espera",
        value: currentQueue.length.toString(),
        change: stats.estimatedWaitTime
          ? `${Math.round(stats.estimatedWaitTime / 60)}min`
          : "0min",
        icon: Clock,
        color: "text-yellow-600 dark:text-yellow-400",
        bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
        description: stats.estimatedWaitTime
          ? "Tempo estimado de espera"
          : "Fila vazia",
        trend:
          currentQueue.length > 5
            ? "up"
            : currentQueue.length > 0
            ? "stable"
            : "down",
      },
      {
        title: "Usuários Ativos",
        value: stats.activeUsers.toString(),
        change: "+0.2",
        icon: Users,
        color: "text-purple-600 dark:text-purple-400",
        bgColor: "bg-purple-100 dark:bg-purple-900/20",
        description: "Clientes interagindo agora",
        trend: stats.activeUsers > 0 ? "up" : "stable",
      },
    ],
    [stats, currentQueue.length]
  );

  const isLoading = statsLoading || suggestionsLoading || queueLoading;

  // Error state with retry
  if (statsError && !statsData) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-8 h-8 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
                Erro ao carregar dados
              </h3>
              <p className="text-red-700 dark:text-red-300 mt-1">
                Não foi possível conectar ao servidor. Tentando usar dados em
                cache...
              </p>
              <button
                onClick={() => {
                  CacheManager.clearCache();
                  window.location.reload();
                }}
                className="mt-3 flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Tentar Novamente</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg animate-pulse">
          <div className="h-8 bg-white/20 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-white/20 rounded w-3/4"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <SkeletonCard key={index} />
            ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">
              Dashboard do Restaurante
            </h1>
            <p className="text-blue-100 text-lg">
              Visão geral das atividades e estatísticas em tempo real
            </p>
            <div className="flex items-center space-x-6 mt-4">
              <div className="flex items-center space-x-2">
                {isConnected ? (
                  <>
                    <Wifi className="w-4 h-4 text-green-300" />
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-100 font-medium">
                      Sistema Online
                    </span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-4 h-4 text-red-300" />
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    <span className="text-sm text-red-100">Modo Offline</span>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-blue-200" />
                <span className="text-sm text-blue-100">
                  Atualizado:{" "}
                  {lastUpdateTime.toLocaleTimeString("pt-BR", {
                    hour: "2-digit",
                    minute: "2-digit",
                    second: "2-digit",
                  })}
                </span>
              </div>
              {stats.currentlyPlaying && (
                <div className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-1">
                  <Play className="w-4 h-4 text-green-300" />
                  <div className="text-sm">
                    <span className="text-green-100 font-medium">Tocando:</span>
                    <span className="text-blue-100 ml-2">
                      {stats.currentlyPlaying.title} -{" "}
                      {stats.currentlyPlaying.artist}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm relative">
              <BarChart3 className="w-12 h-12 text-white" />
              {!isConnected && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat: any, index: number) => (
          <StatCard key={stat.title} {...stat} />
        ))}
      </div>

      {/* Problematic Tracks Alert */}
      <Suspense fallback={<SkeletonCard />}>
        <ProblematicTracksAlert />
      </Suspense>

      {/* Activity Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Atividade Recente
            </h3>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Ao vivo
              </span>
            </div>
          </div>
          <div className="space-y-3">
              {recentActivity.length > 0 ? (
              recentActivity.map((activity: any) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
                    <Music className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      Nova sugestão: "{activity.title}"
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {activity.artist}
                      </p>
                      <span className="text-xs text-gray-400">•</span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(activity.createdAt).toLocaleTimeString(
                          "pt-BR",
                          {
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <ThumbsUp className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-gray-500">
                      {activity.upvotes || 0}
                    </span>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-8">
                <Music className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Nenhuma sugestão recente
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  As sugestões mais recentes dos clientes aparecerão aqui
                </p>
                <div className="mt-4 space-y-2">
                  <p className="text-xs text-blue-600 dark:text-blue-400">
                    🎵 Compartilhe o QR Code para clientes sugerirem músicas
                  </p>
                  <p className="text-xs text-gray-500">
                    Acesse "QR Code" no menu principal
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Current Queue */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Fila Atual
            </h3>
            <div className="flex items-center space-x-2">
              <Play className="w-4 h-4 text-green-500" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {currentQueue.length} músicas
              </span>
            </div>
          </div>
          <div className="space-y-3">
            {currentQueue.length > 0 ? (
              currentQueue.map((item: any, i: number) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                    i === 0
                      ? "bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700"
                      : "bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full text-xs flex items-center justify-center font-bold ${
                      i === 0
                        ? "bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-sm"
                        : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    {i === 0 ? <Play className="w-3 h-3" /> : i + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p
                      className={`text-sm font-medium truncate ${
                        i === 0
                          ? "text-gray-900 dark:text-white font-semibold"
                          : "text-gray-900 dark:text-white"
                      }`}
                    >
                      {item.title}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {item.artist}
                      </p>
                      <span className="text-xs text-gray-400">•</span>
                      <div className="flex items-center space-x-1">
                        <ThumbsUp className="w-3 h-3 text-green-500" />
                        <span className="text-xs text-gray-500">
                          {item.upvotes - item.downvotes}
                        </span>
                      </div>
                    </div>
                  </div>
                  {i === 0 && (
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                        Tocando
                      </span>
                    </div>
                  )}
                </motion.div>
              ))
            ) : (
              <div className="text-center py-8">
                <Play className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Fila de reprodução vazia
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Aguardando músicas serem aprovadas e adicionadas à fila
                </p>
                <div className="mt-4 space-y-2">
                  <p className="text-xs text-blue-600 dark:text-blue-400">
                    💡 Dica: Use "Controle de Reprodução" para acompanhar fila e votação
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Restaurant Dashboard Component
const RestaurantDashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const { user, setUser, setAuthToken } = useAuth();
  // Corrige: obter status de conexão do WebSocket para o Provider
  const { status } = useWebSocket();

  // Buscar dados do restaurante dinamicamente
  const { data: restaurantData } = useQuery(
    ["restaurant", restaurantId],
    () => apiService.getRestaurant(restaurantId!),
    {
      enabled: !!restaurantId,
      staleTime: 5 * 60 * 1000,
      retry: 2,
    }
  );

  // Nome derivado com fallbacks (API -> store -> ID)
  const restaurantName =
    (restaurantData as any)?.name ||
    (restaurantData as any)?.displayName ||
    (user as any)?.restaurant?.name ||
    (restaurantId ? `Restaurante ${restaurantId}` : "Restaurante");

  if (!restaurantId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Erro de Rota</h1>
          <p className="text-gray-600 mt-2">
            ID do restaurante não fornecido na URL
          </p>
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    setUser(null);
    setAuthToken(null);
    localStorage.removeItem("authToken");
    toast.success("Logout realizado com sucesso!");
    navigate("/");
  };

  const navItems = React.useMemo(
    () => [
      {
        name: "Player",
        icon: PlayCircle,
        path: `/restaurant/${restaurantId}/dashboard/player`,
      },
      {
        name: "Controle de Reprodução",
        icon: Settings,
        path: `/restaurant/${restaurantId}/dashboard/playback-control`,
      },
      {
        name: "Playlists",
        icon: Music,
        path: `/restaurant/${restaurantId}/dashboard/playlists`,
      },
      {
        name: "Gêneros",
        icon: Star,
        path: `/restaurant/${restaurantId}/dashboard/genres`,
      },
      {
        name: "Analytics",
        icon: BarChart3,
        path: `/restaurant/${restaurantId}/dashboard/analytics`,
      },
      {
        name: "QR Code",
        icon: QrCode,
        path: `/restaurant/${restaurantId}/dashboard/qrcode`,
      },
      {
        name: "Moderação",
        icon: AlertCircle,
        path: `/restaurant/${restaurantId}/dashboard/moderation`,
      },
      {
        name: "Configurações",
        icon: Settings,
        path: `/restaurant/${restaurantId}/dashboard/settings`,
      },
    ],
    [restaurantId]
  );

  return (
  <RestaurantContext.Provider value={{ restaurantId, isConnected: status === 'connected', lastUpdate: new Date() }}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link
                to={`/restaurant/${restaurantId}/dashboard`}
                className="flex items-center space-x-3 hover:opacity-80 transition-all duration-200 hover:scale-105 cursor-pointer"
                title="Voltar ao Dashboard Principal"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {restaurantName}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Dashboard de Gerenciamento
                  </p>
                </div>
              </Link>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {restaurantName}
                </div>
                <div className="flex items-center space-x-2">
                  <Link
                    to="profile"
                    className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer"
                    title="Perfil do Restaurante"
                  >
                    <User className="w-4 h-4 text-white" />
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium"
                    title="Sair"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Sair</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation */}
        <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8 overflow-x-auto">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors whitespace-nowrap ${
                    location.pathname === item.path
                      ? "border-blue-500 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
                  }`}
                >
                  <item.icon className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </Link>
              ))}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Suspense fallback={<SkeletonCard />}>
            <Routes>
              <Route
                path={`/restaurant/${restaurantId}/dashboard`}
                element={<DashboardHome />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/player`}
                element={<MusicPlayer />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/playback-control`}
                element={<PlaybackController />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/playlists`}
                element={<PlaylistManager />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/genres`}
                element={<GenreManager />}
              />
              {/* Rota legada: redireciona para Controle de Reprodução */}
              <Route
                path={`/restaurant/${restaurantId}/dashboard/suggestions`}
                element={
                  <Navigate
                    to={`/restaurant/${restaurantId}/dashboard/playback-control`}
                    replace
                  />
                }
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/moderation`}
                element={<AdvancedModeration />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/qrcode`}
                element={<QRCodeManager />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/analytics`}
                element={<UnifiedAnalytics />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/settings`}
                element={<RestaurantSettings />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/profile`}
                element={<RestaurantProfile />}
              />
              <Route
                path={`/restaurant/${restaurantId}/dashboard/enhanced-profile`}
                element={<EnhancedRestaurantProfile />}
              />
              <Route
                path="*"
                element={<Navigate to={`/restaurant/${restaurantId}/dashboard`} replace />}
              />
            </Routes>
          </Suspense>
        </main>
      </div>
    </RestaurantContext.Provider>
  );
};

export default RestaurantDashboard;
