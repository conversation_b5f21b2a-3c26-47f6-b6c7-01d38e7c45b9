var tt=Object.defineProperty;var at=(I,g,x)=>g in I?tt(I,g,{enumerable:!0,configurable:!0,writable:!0,value:x}):I[g]=x;var Ie=(I,g,x)=>(at(I,typeof g!="symbol"?g+"":g,x),x);import{u as rt,d as st,w as lt,c,b as u,j as e,O as ot}from"./index-6e2e4ef2.js";import{r as d}from"./vendor-66b0ef43.js";import{c as it}from"./router-f729e475.js";import{R as G,aw as Q,T as q,U as X,k as $e,m as C,af as Se,n as nt,P as dt,at as ct,a3 as xt,a4 as mt,ax as ut,q as gt,ar as ht,au as yt,a as bt,ay as pt,c as ft,A as Pe,az as Re,aA as Te,a6 as Ve,X as Ae,t as vt,K as wt,J as jt,s as Nt,aB as kt}from"./ui-a5f8f5f0.js";import"./utils-08f61814.js";class f{static setCache(g,x,l=2*60*1e3){const $={data:x,timestamp:Date.now(),ttl:l};localStorage.setItem(this.CACHE_PREFIX+g,JSON.stringify($))}static getCache(g){try{const x=localStorage.getItem(this.CACHE_PREFIX+g);if(!x)return null;const{data:l,timestamp:$,ttl:A}=JSON.parse(x);return Date.now()-$>A?(localStorage.removeItem(this.CACHE_PREFIX+g),null):l}catch{return null}}}Ie(f,"CACHE_PREFIX","playback_controller_");const Vt=()=>{const{restaurantId:I}=it(),{restaurantId:g,isConnected:x}=rt(),l=I||g,{on:$,off:A,emit:Y}=st(),[n,S]=d.useState(null),[Z,Ee]=d.useState(!0),[b,P]=d.useState(null),[R,w]=d.useState([]),[z,j]=d.useState([]),[K,J]=d.useState({totalItems:0,paidItems:0,freeItems:0,estimatedWaitTime:0}),[Ct,me]=d.useState(new Date),[_e,ue]=d.useState(0),[T,De]=d.useState([]),[E,Fe]=d.useState([]),[Me,ee]=d.useState(null),[te,Qe]=d.useState(0),[ge,Ue]=d.useState(!1),[he,Oe]=d.useState([]),[ye,Le]=d.useState(0),[ae,qe]=d.useState([]),[p,ze]=d.useState(null),[_,Je]=d.useState({totalSuperVotes:0,totalNormalVotes:0,paidItems:0,freeItems:0}),[be,We]=d.useState(0),[U,He]=d.useState({shuffle:!1,repeat:!1,autoPlay:!1,crossfade:0,lockVoting:!1}),[pe,fe]=d.useState("");d.useState(!1);const re=d.useRef(null),ve=d.useRef(null),O=d.useRef(null),V=d.useRef(null);d.useEffect(()=>{if(!l||!x)return;const t=r=>{S(i=>i?{...i,...r}:null),me(new Date),f.setCache(`playback_${l}`,r)},a=r=>{w(r.priorityQueue||[]),j(r.normalQueue||[]);const i=ne([...r.priorityQueue,...r.normalQueue]);J(y=>({...y,estimatedWaitTime:i})),f.setCache(`queue_${l}`,r)},s=lt.onConnectionStatusChange(r=>{S(i=>i?{...i,connectionStatus:r}:null)});$("playback-state-update",t),$("queue-update",a);const o=r=>{c.success((r==null?void 0:r.message)||"Playlist reordenada por votos"),qe(y=>{var m,h;return[{time:new Date().toISOString(),playlistName:((m=r==null?void 0:r.playlist)==null?void 0:m.name)||(r==null?void 0:r.playlistName),count:((h=r==null?void 0:r.playlist)==null?void 0:h.tracksReordered)||(r==null?void 0:r.tracksReordered)||0,details:((r==null?void 0:r.topTracks)||[]).slice(0,5)},...y.slice(0,19)]}),N(),L(),D(),le();const i=new Date(Date.now()+5*60*1e3);ee(i),oe(i)};return $("playlistReordered",o),Y("join-restaurant-playback",{restaurantId:l}),()=>{A("playback-state-update",t),A("queue-update",a),s==null||s(),A("playlistReordered",o),Y("leave-restaurant-playback",{restaurantId:l})}},[l,x,$,A,Y]),d.useEffect(()=>{if(se(),x||(re.current=setInterval(()=>{L(),N()},1e4)),D(),le(),O.current&&clearInterval(O.current),O.current=setInterval(()=>{D(),le()},15e3),!Me){const t=new Date(Date.now()+3e5);ee(t),oe(t)}return()=>{re.current&&clearInterval(re.current),ve.current&&clearTimeout(ve.current),O.current&&clearInterval(O.current),V.current&&clearInterval(V.current)}},[l,x]);const se=async()=>{const t=f.getCache(`playback_${l}`),a=f.getCache(`queue_${l}`);t&&(S(t),c("Carregando dados em cache...",{duration:2e3,icon:"ℹ️"})),a&&(w(a.priorityQueue||[]),j(a.normalQueue||[])),await Promise.all([L(),N()])},D=async()=>{if(l)try{const t=u(`/collaborative-playlist/${l}/ranking`,{limit:"50"}),a=await fetch(t);if(!a.ok)throw new Error(`Falha ao carregar ranking (${a.status})`);const s=await a.json(),o=(s==null?void 0:s.data)||[],r=h=>({...h,paymentAmount:Number(h.paymentAmount)||0,voteCount:Number(h.voteCount)||0}),i=o.map(r),y=i.filter(h=>h.isPaid).sort((h,xe)=>xe.voteCount-h.voteCount),m=i.filter(h=>!h.isPaid).sort((h,xe)=>xe.voteCount-h.voteCount);De(y),Fe(m)}catch(t){console.warn("Não foi possível obter ranking colaborativo:",t)}},le=async()=>{if(l)try{const t=u(`/collaborative-playlist/${l}/stats`),a=await fetch(t);if(!a.ok)throw new Error(`Falha ao carregar stats (${a.status})`);const s=await a.json();ze((s==null?void 0:s.data)??s??null)}catch(t){console.warn("Não foi possível obter stats colaborativas:",t)}};d.useEffect(()=>{const t=[...T,...E].sort((r,i)=>{const y=Number(i.isPaid)-Number(r.isPaid);if(y!==0)return y;const m=k(i.paymentAmount)-k(r.paymentAmount);return m!==0?m:k(i.voteCount)-k(r.voteCount)});Oe(t),Le(t.reduce((r,i)=>r+k(i.voteCount),0));const a=t.reduce((r,i)=>r+k(i.superVoteCount),0),s=t.reduce((r,i)=>r+k(i.normalVoteCount),0);Je({totalSuperVotes:a,totalNormalVotes:s,paidItems:T.length,freeItems:E.length});const o=T.reduce((r,i)=>r+k(i.paymentAmount??0),0);We(o)},[T,E]);const oe=t=>{V.current&&clearInterval(V.current);const a=async()=>{const s=Math.max(0,Math.floor((t.getTime()-Date.now())/1e3));Qe(s),s<=0&&(clearInterval(V.current),V.current=null,ge&&await ie())};a(),V.current=setInterval(a,1e3)},ie=async()=>{if(l)try{const t=await fetch(u(`/collaborative-playlist/${l}/reorder`),{method:"POST"});if(!t.ok)throw new Error(`Falha ao reordenar (${t.status})`);c.success("Playlist reordenada por votos"),await Promise.all([N(),D(),L()])}catch(t){console.error("Erro ao reordenar por votos:",t),c.error("Erro ao reordenar por votos")}finally{const t=new Date(Date.now()+3e5);ee(t),oe(t)}},ne=d.useCallback(t=>t.reduce((a,s,o)=>o===0?0:a+(s.duration||180),0),[]),we=async t=>{if(l)try{const a=await fetch(u(`/collaborative-playlist/${l}/vote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:t,clientSessionId:"admin_panel"})});if(!a.ok){const s=await a.text().catch(()=>"");throw new Error(`Erro ao votar: ${a.status} ${s}`)}c.success("Voto registrado (normal)"),await Promise.all([D(),N()])}catch(a){console.error(a),c.error("Falha ao registrar voto (normal)")}},F=async(t,a)=>{if(l)try{const s=await fetch(u(`/collaborative-playlist/${l}/supervote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:t,paymentAmount:a,paymentId:`admin_test_${Date.now()}`,clientSessionId:"admin_panel"})});if(!s.ok){const o=await s.text().catch(()=>"");throw new Error(`Erro no supervoto: ${s.status} ${o}`)}c.success(`Supervoto R$ ${a} registrado`),await Promise.all([D(),N()])}catch(s){console.error(s),c.error("Falha ao registrar supervoto")}},v=async(t,a,s=3)=>{for(let o=0;o<s;o++)try{const r=await fetch(t,{...a,headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("authToken")}`,...a==null?void 0:a.headers}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const i=await r.json();return ue(0),i}catch(r){if(console.error(`API call failed (attempt ${o+1}):`,r),o===s-1){ue(i=>i+1),c.error(`Falha na conexão. Tentativa ${_e+1}`);return}await new Promise(i=>setTimeout(i,Math.pow(2,o)*1e3))}},L=async()=>{if(!l){console.error("Restaurant ID não encontrado");return}try{const t=await v(u(`/playback/${l}/state`));t!=null&&t.state?(t.state.currentTrack&&t.state.currentTrack.createdAt&&(t.state.currentTrack.createdAt=new Date(t.state.currentTrack.createdAt)),S(t.state),f.setCache(`playback_${l}`,t.state)):S({isPlaying:!1,currentTrack:null,volume:50,currentTime:0,queue:[],priorityQueue:[],normalQueue:[],history:[],connectionStatus:x?"connected":"disconnected"})}catch(t){console.error("Erro ao carregar estado de reprodução:",t);const a=f.getCache(`playback_${l}`);a&&(S(a),c("Usando dados em cache devido à falha de conexão",{icon:"ℹ️"}))}finally{Ee(!1),me(new Date)}},N=async()=>{if(l){try{console.log("🎵 Carregando filas via LivePlaylistService...");const t=await v(u(`/live-playlist/${l}/state`));if(t!=null&&t.success&&(t!=null&&t.data)){const a=t.data,s=await v(u(`/suggestions/${l}?isPaid=true&status=approved`)),o=await v(u(`/suggestions/${l}?isPaid=false&status=approved`)),r=((s==null?void 0:s.suggestions)||[]).map(m=>({...m,createdAt:new Date(m.createdAt),isPaid:!0})),i=((o==null?void 0:o.suggestions)||[]).map(m=>({...m,createdAt:new Date(m.createdAt),isPaid:!1}));w(r),j(i);const y=ne([...r,...i]);J({totalItems:a.paidSuggestionsCount+a.freeSuggestionsCount,paidItems:a.paidSuggestionsCount,freeItems:a.freeSuggestionsCount,estimatedWaitTime:y}),f.setCache(`queue_${l}`,{priorityQueue:r,normalQueue:i}),console.log(`🎵 LivePlaylist: ${a.paidSuggestionsCount} prioritárias, ${a.freeSuggestionsCount} normais, tempo estimado: ${Math.round(y/60)}min`);return}}catch(t){console.warn("⚠️ LivePlaylistService falhou, usando fallback:",t)}try{console.log("🔄 Usando sistema de filas antigo como fallback...");const a=((await v(u(`/playback-queue/${l}`))).queue||[]).map(i=>({...i,createdAt:new Date(i.createdAt)})),s=a.filter(i=>i.isPaid),o=a.filter(i=>!i.isPaid);w(s),j(o);const r=ne(a);J({totalItems:a.length,paidItems:s.length,freeItems:o.length,estimatedWaitTime:r}),f.setCache(`queue_${l}`,{priorityQueue:s,normalQueue:o}),console.log(`🎵 Fallback: ${s.length} prioritárias, ${o.length} normais, tempo estimado: ${Math.round(r/60)}min`)}catch(t){console.error("❌ Erro ao carregar filas (fallback também falhou):",t);const a=f.getCache(`queue_${l}`);a&&(w(a.priorityQueue||[]),j(a.normalQueue||[]),c("Usando dados de fila em cache",{icon:"ℹ️"}))}}},Be=t=>{try{if(!t)return null;if(/^[a-zA-Z0-9_-]{6,}$/.test(t))return t;const a=new URL(t);return a.hostname.includes("youtu.be")?a.pathname.replace("/",""):a.hostname.includes("youtube.com")?a.searchParams.get("v"):null}catch{return/^[a-zA-Z0-9_-]{6,}$/.test(t)?t:null}},je=t=>{const a=Be(pe.trim());if(!a){c.error("Informe um link ou ID válido do YouTube");return}const s={id:`local_${Date.now()}`,youtubeVideoId:a,title:`Música (${a})`,artist:"Artista Desconhecido",duration:180,thumbnailUrl:`https://img.youtube.com/vi/${a}/mqdefault.jpg`,upvotes:0,downvotes:0,score:0,createdAt:new Date};t?(w(o=>[s,...o]),c.success("Adicionada à fila prioritária (somente UI)")):(j(o=>[s,...o]),c.success("Adicionada à fila normal (somente UI)")),fe("")},Ge=()=>{w([]),j([]),J(t=>({...t,totalItems:0,paidItems:0,freeItems:0})),c.success("Filas limpas (somente UI)")},W=(t,a,s)=>{const o=t==="priority"?[...R]:[...z],r=o.findIndex(m=>m.id===a);if(r<0)return;const i=s==="up"?r-1:r+1;if(i<0||i>=o.length)return;const y=o[i];o[i]=o[r],o[r]=y,t==="priority"?w(o):j(o)},Xe=t=>{const a=R.findIndex(s=>s.id===t);if(a>=0){const s=R[a],o=[...R];o.splice(a,1),w(o),j(r=>[s,...r]),c("Movida para fila normal (somente UI)")}},de=t=>{He(a=>({...a,[t]:!a[t]}))},Ye=async()=>{var t;if(!(!n||!l)){P("playpause");try{const a=n.isPlaying?"pause":"resume";await v(u(`/playback/${l}/${a}`),{method:"POST"});const s={...n,isPlaying:!n.isPlaying};S(s),f.setCache(`playback_${l}`,s),c.success(n.isPlaying?"Reprodução pausada":"Reprodução retomada"),M("playback_toggle",{action:a,trackId:(t=n.currentTrack)==null?void 0:t.id})}catch{c.error("Erro ao controlar reprodução")}finally{P(null)}}},Ze=async()=>{var t;if(l){P("skip");try{await v(u(`/playback/${l}/skip`),{method:"POST"}),c.success("Música pulada"),M("track_skip",{trackId:(t=n==null?void 0:n.currentTrack)==null?void 0:t.id,skipTime:n==null?void 0:n.currentTime}),await L(),await N()}catch{c.error("Erro ao pular música")}finally{P(null)}}},Ne=async t=>{if(!(!l||!n))try{await v(u(`/playback/${l}/volume`),{method:"POST",body:JSON.stringify({volume:t})});const a={...n,volume:t};S(a),f.setCache(`playback_${l}`,a),M("volume_change",{previousVolume:n.volume,newVolume:t})}catch{c.error("Erro ao ajustar volume")}},Ke=()=>{if(!n)return;const t=n.volume===0?50:0;Ne(t),M("volume_mute_toggle",{action:t===0?"mute":"unmute"})},ke=async t=>{if(l){P(`remove-priority-${t}`);try{await v(u(`/playback-queue/${l}/remove`),{method:"POST",body:JSON.stringify({trackId:t})}),c.success("Música removida da fila"),M("track_remove",{trackId:t}),await N()}catch{c.error("Erro ao remover música da fila")}finally{P(null)}}},et=async t=>{if(l){P(`promote-${t}`);try{await v(u(`/playback-queue/${l}/promote`),{method:"POST",body:JSON.stringify({trackId:t})}),c.success("Música promovida para fila prioritária"),M("track_promote",{trackId:t}),await N()}catch{c.error("Erro ao promover música")}finally{P(null)}}},M=(t,a)=>{try{console.log(`📊 Analytics: ${t}`,a);const s={timestamp:new Date().toISOString(),restaurantId:l,action:t,data:a},o=JSON.parse(localStorage.getItem("playback_analytics")||"[]");o.push(s),o.length>100&&o.splice(0,o.length-100),localStorage.setItem("playback_analytics",JSON.stringify(o))}catch(s){console.error("Erro ao registrar analytics:",s)}},H=t=>{const a=Math.floor(t/60),s=t%60;return`${a}:${s.toString().padStart(2,"0")}`},ce=t=>t<60?`${t}s`:t<3600?`${Math.round(t/60)}min`:`${Math.round(t/3600)}h`,k=t=>{const a=Number(t);return Number.isFinite(a)?a:0},B=t=>k(t).toLocaleString("pt-BR",{minimumFractionDigits:2,maximumFractionDigits:2}),Ce=()=>n!=null&&n.currentTrack?n.currentTime/n.currentTrack.duration*100:0;return Z?e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(G,{className:"w-8 h-8 animate-spin text-blue-600"})})}):e.jsxs("div",{className:"space-y-6",children:[e.jsx(ot,{position:"top-left"}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Controle da Playlist (Colaborativa)"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie a reprodução e acompanhe votos, supervotos e reordenações automáticas."})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-3 py-1.5 rounded-lg",children:[e.jsx("input",{type:"checkbox",checked:ge,onChange:t=>Ue(t.target.checked)}),"Auto-reordenar"]}),e.jsx("button",{onClick:ie,className:"px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm",children:"Reordenar agora"}),e.jsxs("button",{onClick:se,disabled:b==="refresh",className:"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm","aria-label":"Atualizar dados de reprodução",children:[b==="refresh"?e.jsx(Q,{className:"w-4 h-4 animate-spin"}):e.jsx(G,{className:"w-4 h-4"}),"Atualizar"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Próxima reordenação"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:ce(te)})]}),e.jsx(G,{className:"w-7 h-7 text-indigo-500"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total de votos"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(p==null?void 0:p.totalVotes)??ye})]}),e.jsx(q,{className:"w-7 h-7 text-green-500"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Itens pagos"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-700 dark:text-yellow-300",children:_.paidItems})]}),e.jsx(X,{className:"w-7 h-7 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Itens grátis"}),e.jsx("p",{className:"text-2xl font-bold text-green-700 dark:text-green-300",children:_.freeItems})]}),e.jsx(X,{className:"w-7 h-7 text-green-500"})]})})]}),e.jsxs("div",{className:"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/10 rounded-lg p-4 border border-indigo-200 dark:border-indigo-700 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:"Receita total acumulada"}),e.jsxs("p",{className:"text-2xl font-bold text-indigo-900 dark:text-indigo-100",children:["R$ ",B((p==null?void 0:p.totalRevenue)??be)]})]}),e.jsxs("a",{href:`/restaurant/${l}/dashboard/analytics`,className:"inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm",children:[e.jsx($e,{className:"w-4 h-4"})," Ver Analytics"]})]}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:n!=null&&n.currentTrack?e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("img",{src:n.currentTrack.thumbnailUrl,alt:n.currentTrack.title,className:"w-16 h-12 object-cover rounded-lg shadow-sm"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:n.currentTrack.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:n.currentTrack.artist}),e.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-green-600",children:[e.jsx(q,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"text-sm",children:n.currentTrack.upvotes})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-red-600",children:[e.jsx(Se,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"text-sm",children:n.currentTrack.downvotes})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Score: ",n.currentTrack.score]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-500",children:[e.jsx("span",{children:H(n.currentTime)}),e.jsx("span",{children:H(n.currentTrack.duration)})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e.jsx(C.div,{className:"bg-blue-600 h-2 rounded-full",initial:{width:0},animate:{width:`${Ce()}%`},transition:{duration:1},role:"progressbar","aria-valuenow":Ce(),"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Progresso da música"})})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[e.jsx("button",{onClick:Ye,disabled:b==="playpause",className:"flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":n.isPlaying?"Pausar":"Reproduzir",children:b==="playpause"?e.jsx(Q,{className:"w-6 h-6 animate-spin"}):n.isPlaying?e.jsx(nt,{className:"w-6 h-6"}):e.jsx(dt,{className:"w-6 h-6 ml-1"})}),e.jsx("button",{onClick:Ze,disabled:b==="skip",className:"flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Pular música",children:b==="skip"?e.jsx(Q,{className:"w-5 h-5 animate-spin"}):e.jsx(ct,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:Ke,"aria-label":n.volume===0?"Ativar som":"Silenciar",children:n.volume===0?e.jsx(xt,{className:"w-5 h-5 text-gray-600"}):e.jsx(mt,{className:"w-5 h-5 text-gray-600"})}),e.jsx("input",{type:"range",min:"0",max:"100",value:n.volume,onChange:t=>Ne(parseInt(t.target.value)),className:"w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer","aria-label":"Controle de volume"}),e.jsx("span",{className:"text-sm text-gray-500 w-8",children:n.volume})]})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Sem reprodução"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Quando a próxima música começar, os controles ficam ativos."})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-800 dark:text-gray-200",children:[e.jsx(ut,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium text-sm",children:"Ações Rápidas"})]}),e.jsxs("button",{onClick:Ge,className:"flex items-center gap-1 text-red-600 hover:text-red-700 text-sm",children:[e.jsx(gt,{className:"w-4 h-4"})," Limpar Filas"]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx("button",{onClick:se,className:"px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm",children:"Atualizar"}),e.jsx("button",{onClick:ie,className:"px-3 py-1.5 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm",children:"Reordenar por Votos"}),e.jsxs("button",{onClick:()=>de("shuffle"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${U.shuffle?"bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300":"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}`,children:[e.jsx(ht,{className:"w-4 h-4"})," Shuffle"]}),e.jsxs("button",{onClick:()=>de("repeat"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${U.repeat?"bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300":"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}`,children:[e.jsx(yt,{className:"w-4 h-4"})," Repeat"]}),e.jsxs("button",{onClick:()=>de("lockVoting"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${U.lockVoting?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200":"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}`,children:[U.lockVoting?e.jsx(bt,{className:"w-4 h-4"}):e.jsx(pt,{className:"w-4 h-4"})," ",U.lockVoting?"Votação Travada":"Votação Liberada"]})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-3",children:[e.jsx($e,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium text-sm",children:"Adicionar música manualmente"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsx("input",{value:pe,onChange:t=>fe(t.target.value),placeholder:"Cole o link ou ID do YouTube",className:"flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>je(!1),className:"px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm",children:"Adicionar na Fila Normal"}),e.jsx("button",{onClick:()=>je(!0),className:"px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm",children:"Adicionar na Fila Prioritária"})]})]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total na Fila"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:K.totalItems})]}),e.jsx(ft,{className:"w-8 h-8 text-blue-500"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-yellow-200 dark:border-yellow-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-yellow-600 dark:text-yellow-400",children:"Fila Prioritária"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-700 dark:text-yellow-300",children:K.paidItems})]}),e.jsx(q,{className:"w-8 h-8 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-green-200 dark:border-green-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-green-600 dark:text-green-400",children:"Fila Normal"}),e.jsx("p",{className:"text-2xl font-bold text-green-700 dark:text-green-300",children:K.freeItems})]}),e.jsx(X,{className:"w-8 h-8 text-green-500"})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-indigo-200 dark:border-indigo-700 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Histórico de Reordenação"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200",children:[ae.length," eventos"]})]}),ae.length===0?e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhum evento de reordenação ainda"}):e.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:ae.map((t,a)=>e.jsxs("div",{className:"p-3 rounded border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"font-medium text-gray-800 dark:text-gray-200",children:[new Date(t.time).toLocaleString("pt-BR")," • ",t.playlistName||"Playlist"]}),e.jsxs("div",{className:"text-xs text-indigo-700 dark:text-indigo-300",children:[t.count," músicas impactadas"]})]}),e.jsxs("div",{className:"mt-2 text-xs text-gray-700 dark:text-gray-300",children:["Top 5:",e.jsx("ul",{className:"list-disc list-inside",children:(t.details||[]).map((s,o)=>e.jsxs("li",{className:"truncate",children:[s.title||s.videoId," ",s.isPaid?"(Paga)":""," — votos: ",s.voteCount??"?"]},o))})]})]},a))})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-yellow-200 dark:border-yellow-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:["🔥 Fila Prioritária (",R.length,")"]}),e.jsx("div",{className:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium",children:"SuperVoto (R$ 5, 20, 50)"})]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:Z?e.jsx("div",{className:"space-y-3",children:[...Array(3)].map((t,a)=>e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:a*.1},className:"flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg animate-pulse",children:[e.jsx("div",{className:"w-8 h-8 bg-yellow-200 dark:bg-yellow-700 rounded-full"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx("div",{className:"h-4 bg-yellow-200 dark:bg-yellow-700 rounded w-3/4"}),e.jsx("div",{className:"h-3 bg-yellow-200 dark:bg-yellow-700 rounded w-1/2"})]})]},a))}):R.length>0?e.jsx(Pe,{children:R.map((t,a)=>e.jsxs(C.div,{layout:!0,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-xs flex items-center justify-center font-bold",children:a+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:t.title}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 truncate",children:t.artist})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:H(t.duration)}),e.jsx("button",{onClick:()=>W("priority",t.id,"up"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para cima","aria-label":"Mover para cima",children:e.jsx(Re,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>W("priority",t.id,"down"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para baixo","aria-label":"Mover para baixo",children:e.jsx(Te,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>Xe(t.id),className:"p-1 text-blue-600 hover:text-blue-800",title:"Mover para fila normal","aria-label":"Mover para fila normal",children:e.jsx(Ve,{className:"w-3 h-3 rotate-180"})}),e.jsx("button",{onClick:()=>ke(t.id),disabled:b===`remove-priority-${t.id}`,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Remover da fila prioritária",title:"Remover",children:b===`remove-priority-${t.id}`?e.jsx(Q,{className:"w-3 h-3 animate-spin"}):e.jsx(Ae,{className:"w-3 h-3"})})]})]},t.id))}):e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(q,{className:"w-6 h-6 text-yellow-600"})}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Nenhuma música na fila prioritária"})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-green-200 dark:border-green-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:["📋 Fila Normal (",z.length,")"]}),e.jsx("div",{className:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs font-medium",children:"Gratuito"})]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:Z?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((t,a)=>e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:a*.1},className:"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg animate-pulse",children:[e.jsx("div",{className:"w-6 h-6 bg-green-200 dark:bg-green-700 rounded"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx("div",{className:"h-4 bg-green-200 dark:bg-green-700 rounded w-3/4"}),e.jsx("div",{className:"h-3 bg-green-200 dark:bg-green-700 rounded w-1/2"})]})]},a))}):z.length>0?e.jsx(Pe,{children:z.slice().sort((t,a)=>a.upvotes-a.downvotes-(t.upvotes-t.downvotes)).map((t,a)=>e.jsxs(C.div,{layout:!0,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700",children:[e.jsx("div",{className:"w-6 h-6 bg-green-600 text-white rounded text-xs flex items-center justify-center font-medium",children:a+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:t.title}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 truncate",children:t.artist}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(q,{className:"w-3 h-3 text-green-600","aria-hidden":"true"}),e.jsx("span",{className:"text-xs text-green-600",children:t.upvotes})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Se,{className:"w-3 h-3 text-red-600","aria-hidden":"true"}),e.jsx("span",{className:"text-xs text-red-600",children:t.downvotes})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Score: ",t.score||t.upvotes-t.downvotes]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:H(t.duration)}),e.jsx("button",{onClick:()=>W("normal",t.id,"up"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para cima","aria-label":"Mover para cima",children:e.jsx(Re,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>W("normal",t.id,"down"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para baixo","aria-label":"Mover para baixo",children:e.jsx(Te,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>et(t.id),disabled:b===`promote-${t.id}`,className:"p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Promover para fila prioritária",title:"Promover",children:b===`promote-${t.id}`?e.jsx(Q,{className:"w-3 h-3 animate-spin"}):e.jsx(Ve,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>ke(t.id),disabled:b===`remove-normal-${t.id}`,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Remover da fila normal",title:"Remover",children:b===`remove-normal-${t.id}`?e.jsx(Q,{className:"w-3 h-3 animate-spin"}):e.jsx(Ae,{className:"w-3 h-3"})})]})]},t.id))}):e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(X,{className:"w-6 h-6 text-green-600"})}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Nenhuma música na fila normal"})]})})]})]}),e.jsx(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},className:"mt-6",children:e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(vt,{className:"w-4 h-4 text-blue-500"}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Sistema ativo - Última atualização: ",new Date().toLocaleTimeString("pt-BR")]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-green-600 dark:text-green-400",children:"Online"})]})]})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Votações (Colaborativa)"}),e.jsxs("span",{className:"inline-flex items-center gap-1 text-xs px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200",children:[e.jsx(G,{className:"w-3 h-3"})," ",ce(te)]})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Reordenação automática baseada em votos"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mt-3",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Próx. reordenação"}),e.jsx(wt,{className:"w-4 h-4 text-indigo-500"})]}),e.jsx("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:ce(te)})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Total de votos"}),e.jsx(jt,{className:"w-4 h-4 text-green-500"})]}),e.jsx("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:(p==null?void 0:p.totalVotes)??ye})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Receita total"}),e.jsx(Nt,{className:"w-4 h-4 text-yellow-500"})]}),e.jsxs("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:["R$ ",B((p==null?void 0:p.totalRevenue)??be)]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Pagas/Grátis"}),e.jsx(kt,{className:"w-4 h-4 text-purple-500"})]}),e.jsxs("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[_.paidItems,"/",_.freeItems]}),e.jsxs("div",{className:"mt-1 text-xs text-gray-600 dark:text-gray-400",children:["SV: ",_.totalSuperVotes," • VN: ",_.totalNormalVotes]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-yellow-200 dark:border-yellow-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"SuperVotos (Pagas)"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200",children:[T.length," itens"]})]}),e.jsx("div",{className:"space-y-2 max-h-72 overflow-y-auto",children:T.length===0?e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhum supervoto no momento"}):T.map((t,a)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-yellow-200 dark:border-yellow-700 bg-yellow-50/60 dark:bg-yellow-900/20",children:[e.jsx("div",{className:"w-7 h-7 bg-yellow-500 text-white rounded flex items-center justify-center text-xs font-bold",children:a+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${t.youtubeVideoId}/mqdefault.jpg`,alt:t.title||t.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:t.title||t.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate",children:t.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-gray-700 dark:text-gray-300 mr-1",children:["Votos: ",t.voteCount]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>we(t.youtubeVideoId),className:"px-2 py-0.5 border border-gray-300 dark:border-gray-600 rounded text-xs hover:bg-gray-100 dark:hover:bg-gray-700",title:"Voto normal",children:"+1"}),e.jsx("button",{onClick:()=>F(t.youtubeVideoId,5),className:"px-2 py-0.5 border border-yellow-400 dark:border-yellow-700 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/20",title:"Supervoto R$5",children:"5"}),e.jsx("button",{onClick:()=>F(t.youtubeVideoId,20),className:"px-2 py-0.5 border border-yellow-500 dark:border-yellow-800 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/30",title:"Supervoto R$20",children:"20"}),e.jsx("button",{onClick:()=>F(t.youtubeVideoId,50),className:"px-2 py-0.5 border border-yellow-600 dark:border-yellow-900 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/40",title:"Supervoto R$50",children:"50"})]}),e.jsxs("div",{className:"text-xs text-green-700 dark:text-green-300 ml-1",children:["R$ ",B(t.paymentAmount)]})]})]},t.youtubeVideoId+a))})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-green-200 dark:border-green-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Votos (Grátis)"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200",children:[E.length," itens"]})]}),e.jsx("div",{className:"space-y-2 max-h-72 overflow-y-auto",children:E.length===0?e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhum voto gratuito no momento"}):E.map((t,a)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-green-200 dark:border-green-700 bg-green-50/60 dark:bg-green-900/20",children:[e.jsx("div",{className:"w-7 h-7 bg-green-600 text-white rounded flex items-center justify-center text-xs font-bold",children:a+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${t.youtubeVideoId}/mqdefault.jpg`,alt:t.title||t.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:t.title||t.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate",children:t.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-gray-700 dark:text-gray-300 mr-1",children:["Votos: ",t.voteCount]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>we(t.youtubeVideoId),className:"px-2 py-0.5 border border-gray-300 dark:border-gray-600 rounded text-xs hover:bg-gray-100 dark:hover:bg-gray-700",title:"Voto normal",children:"+1"}),e.jsx("button",{onClick:()=>F(t.youtubeVideoId,5),className:"px-2 py-0.5 border border-yellow-400 dark:border-yellow-700 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/20",title:"Supervoto R$5",children:"5"}),e.jsx("button",{onClick:()=>F(t.youtubeVideoId,20),className:"px-2 py-0.5 border border-yellow-500 dark:border-yellow-800 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/30",title:"Supervoto R$20",children:"20"}),e.jsx("button",{onClick:()=>F(t.youtubeVideoId,50),className:"px-2 py-0.5 border border-yellow-600 dark:border-yellow-900 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/40",title:"Supervoto R$50",children:"50"})]}),e.jsxs("div",{className:"text-xs text-gray-500 ml-1",children:["Mesa: ",t.tableNumber??"—"]})]})]},t.youtubeVideoId+a))})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Fila Automática (Prévia)"}),e.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Aplicada quando o contador chegar a 0"})]}),e.jsx("div",{className:"space-y-2 max-h-80 overflow-y-auto",children:he.length===0?e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Sem itens suficientes para prévia"}):he.slice(0,20).map((t,a)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/30",children:[e.jsx("div",{className:`w-7 h-7 rounded text-white flex items-center justify-center text-xs font-bold ${t.isPaid?"bg-yellow-600":"bg-green-600"}`,children:a+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${t.youtubeVideoId}/mqdefault.jpg`,alt:t.title||t.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:t.title||t.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate",children:t.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-3 text-right",children:[e.jsx("span",{className:`text-xs px-2 py-0.5 rounded ${t.isPaid?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200"}`,children:t.isPaid?"Paga":"Grátis"}),e.jsxs("div",{className:"text-xs text-gray-700 dark:text-gray-300",children:["Votos: ",t.voteCount]}),t.isPaid&&e.jsxs("div",{className:"text-xs text-green-700 dark:text-green-300",children:["R$ ",B(t.paymentAmount)]})]})]},`${t.youtubeVideoId}-${a}`))})]})]})]})};export{Vt as default};
//# sourceMappingURL=PlaybackController-d0d556e1.js.map
