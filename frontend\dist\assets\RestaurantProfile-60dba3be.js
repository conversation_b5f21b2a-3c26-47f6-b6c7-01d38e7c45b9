import{j as e,b as Q,A as V,c as f}from"./index-6e2e4ef2.js";import{r as m,a as de}from"./vendor-66b0ef43.js";import{c as ie}from"./router-f729e475.js";import{R as Y,i as ce,X as _,u as ge,l as xe,A as me,m as ue,B as U,K as J,d as K,aF as T,k as W,M as be,aG as he,aH as ye,aI as pe}from"./ui-a5f8f5f0.js";import"./utils-08f61814.js";const je=({icon:x,label:s,id:u,active:N,onClick:$})=>e.jsxs("button",{onClick:()=>$(u),className:`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${N?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 font-medium":"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"}`,"aria-selected":N,role:"tab",id:`tab-${u}`,"aria-controls":`panel-${u}`,children:[de.cloneElement(x,{className:`w-5 h-5 ${N?"text-blue-600 dark:text-blue-400":"text-gray-500 dark:text-gray-400"}`}),e.jsx("span",{children:s})]}),Ce=()=>{const{restaurantId:x}=ie();if(!x)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600",children:"Erro de Rota"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"ID do restaurante não fornecido na URL"})]})});const[s,u]=m.useState(null),[N,$]=m.useState(!0),[l,I]=m.useState(!1),[M,q]=m.useState(!1),[a,b]=m.useState(null),[p,X]=m.useState("basic"),[Z,B]=m.useState(null),P=[{key:"monday",label:"Segunda-feira"},{key:"tuesday",label:"Terça-feira"},{key:"wednesday",label:"Quarta-feira"},{key:"thursday",label:"Quinta-feira"},{key:"friday",label:"Sexta-feira"},{key:"saturday",label:"Sábado"},{key:"sunday",label:"Domingo"}],ee=[{id:"basic",label:"Informações Básicas",icon:e.jsx(U,{})},{id:"hours",label:"Horário de Funcionamento",icon:e.jsx(J,{})},{id:"settings",label:"Configurações",icon:e.jsx(K,{})},{id:"appearance",label:"Aparência",icon:e.jsx(T,{})},{id:"integrations",label:"Integrações",icon:e.jsx(W,{})}],O=m.useCallback(async()=>{var r;try{$(!0),B(null),console.log("🏪 Carregando perfil do restaurante:",x);const t=Q(`${V.ENDPOINTS.RESTAURANTS}/${x}/profile`);console.log("🏪 Fazendo requisição para:",t);const n=new AbortController,d=setTimeout(()=>n.abort(),1e4),o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"},signal:n.signal});if(clearTimeout(d),console.log("🏪 Response status:",o.status),o.ok){const i=await o.json();if(console.log("🏪 Profile loaded:",i),i.success&&i.profile){const c={...i.profile,appearance:i.profile.appearance||{darkMode:!1,primaryColor:"#3b82f6",accentColor:"#10b981",fontFamily:"Inter"},integrations:i.profile.integrations||{youtubeApiEnabled:!0,spotifyConnected:!1,googleAnalytics:!1}};u(c),b(c),f.success("Perfil carregado com sucesso!");return}}else{console.error("🏪 Response não ok:",o.status,o.statusText);const i=await o.text().catch(()=>"");throw console.error("🏪 Error details:",i),new Error(`API returned status ${o.status}`)}}catch(t){console.error("🏪 Erro ao carregar perfil:",t),B(t.message||"Erro ao carregar dados");const n={id:x,name:"Restaurante Demo",email:"<EMAIL>",phone:"(11) 99999-9999",address:"Rua das Flores, 123 - São Paulo, SP",description:"Um restaurante aconchegante com música ambiente personalizada pelos clientes.",businessHours:{monday:{open:"11:00",close:"23:00",isOpen:!0},tuesday:{open:"11:00",close:"23:00",isOpen:!0},wednesday:{open:"11:00",close:"23:00",isOpen:!0},thursday:{open:"11:00",close:"23:00",isOpen:!0},friday:{open:"11:00",close:"24:00",isOpen:!0},saturday:{open:"11:00",close:"24:00",isOpen:!0},sunday:{open:"12:00",close:"22:00",isOpen:!0}},settings:{allowSuggestions:!0,moderationRequired:!0,maxSuggestionsPerUser:3,autoPlayEnabled:!0,autoSkipDisliked:!1},logoUrl:"https://placehold.co/400x400?text=Logo",bannerUrl:"https://placehold.co/1200x300?text=Banner",appearance:{darkMode:!1,primaryColor:"#3b82f6",accentColor:"#10b981",fontFamily:"Inter"},integrations:{youtubeApiEnabled:!0,spotifyConnected:!1,googleAnalytics:!1}};u(n),b(n),t.name==="AbortError"?f.error("Timeout - carregando dados de exemplo"):(r=t.message)!=null&&r.includes("CONNECTION_REFUSED")?f("Servidor offline - usando dados de exemplo",{icon:"ℹ️"}):f("Carregado em modo demo (dados de exemplo)",{icon:"ℹ️"})}finally{$(!1)}},[x]);m.useEffect(()=>{O()},[O]);const ae=async()=>{var r,t;if(a)try{q(!0),console.log("🏪 Salvando perfil do restaurante:",x);const n=Q(`${V.ENDPOINTS.RESTAURANTS}/${x}/profile`);console.log("🏪 Salvando para:",n);const d=await fetch(n,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(d.ok){const o=await d.json();console.log("🏪 Profile saved:",o),u(o.profile||a),I(!1),f.success("Perfil atualizado com sucesso!")}else{console.error("🏪 Erro ao salvar:",d.status,d.statusText);const o=await d.text().catch(()=>"");throw console.error("🏪 Error details:",o),new Error(`API returned status ${d.status}`)}}catch(n){console.error("🏪 Erro ao salvar perfil:",n),u(a),I(!1),(r=n==null?void 0:n.response)!=null&&r.status?f.error(`Erro ${n.response.status}: ${((t=n.response.data)==null?void 0:t.message)||"Erro ao salvar perfil"}`):f("Perfil salvo localmente (sem conexão com servidor)",{icon:"⚠️"})}finally{q(!1)}},se=()=>{b(s),I(!1)},j=(r,t)=>{a&&b({...a,[r]:t})},H=(r,t,n)=>{a&&b({...a,businessHours:{...a.businessHours,[r]:{...a.businessHours[r],[t]:n}}})},v=(r,t)=>{a&&b({...a,settings:{...a.settings,[r]:t}})},R=(r,t)=>{!a||!a.appearance||b({...a,appearance:{...a.appearance,[r]:t}})},D=(r,t)=>{!a||!a.integrations||b({...a,integrations:{...a.integrations,[r]:t}})},re=()=>e.jsxs("div",{className:"space-y-6",role:"tabpanel",id:"panel-basic","aria-labelledby":"tab-basic",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Informações Básicas"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"restaurant-name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome do Restaurante"}),l?e.jsx("input",{id:"restaurant-name",type:"text",value:(a==null?void 0:a.name)||"",onChange:r=>j("name",r.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}):e.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx(U,{className:"w-4 h-4 text-gray-500","aria-hidden":"true"}),e.jsx("span",{children:s==null?void 0:s.name})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),l?e.jsx("input",{id:"email",type:"email",value:(a==null?void 0:a.email)||"",onChange:r=>j("email",r.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}):e.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx(be,{className:"w-4 h-4 text-gray-500","aria-hidden":"true"}),e.jsx("span",{children:s==null?void 0:s.email})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Telefone"}),l?e.jsx("input",{id:"phone",type:"tel",value:(a==null?void 0:a.phone)||"",onChange:r=>j("phone",r.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}):e.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx(he,{className:"w-4 h-4 text-gray-500","aria-hidden":"true"}),e.jsx("span",{children:s==null?void 0:s.phone})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Endereço"}),l?e.jsx("input",{id:"address",type:"text",value:(a==null?void 0:a.address)||"",onChange:r=>j("address",r.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}):e.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx(ye,{className:"w-4 h-4 text-gray-500","aria-hidden":"true"}),e.jsx("span",{children:s==null?void 0:s.address})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),l?e.jsx("textarea",{id:"description",value:(a==null?void 0:a.description)||"",onChange:r=>j("description",r.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}):e.jsx("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:e.jsx("span",{children:s==null?void 0:s.description})})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Imagens do Restaurante"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Logo"}),l?e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex items-center justify-center h-40 w-40 mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-hidden",children:a!=null&&a.logoUrl?e.jsx("img",{src:a.logoUrl,alt:"Logo do Restaurante",className:"max-h-full max-w-full object-contain"}):e.jsxs("div",{className:"text-center p-4",children:[e.jsx(T,{className:"w-8 h-8 text-gray-400 mx-auto mb-2","aria-hidden":"true"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Arraste ou clique para enviar logo"})]})}),e.jsx("input",{type:"text",placeholder:"URL da imagem",value:(a==null?void 0:a.logoUrl)||"",onChange:r=>j("logoUrl",r.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"})]}):e.jsx("div",{className:"flex items-center justify-center h-40 w-40 mx-auto bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden",children:s!=null&&s.logoUrl?e.jsx("img",{src:s.logoUrl,alt:"Logo do Restaurante",className:"max-h-full max-w-full object-contain"}):e.jsx(U,{className:"w-12 h-12 text-gray-400","aria-hidden":"true"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Banner"}),l?e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex items-center justify-center h-32 w-full mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-hidden",children:a!=null&&a.bannerUrl?e.jsx("img",{src:a.bannerUrl,alt:"Banner do Restaurante",className:"max-h-full max-w-full object-cover w-full h-full"}):e.jsxs("div",{className:"text-center p-4",children:[e.jsx(T,{className:"w-8 h-8 text-gray-400 mx-auto mb-2","aria-hidden":"true"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Arraste ou clique para enviar banner"})]})}),e.jsx("input",{type:"text",placeholder:"URL da imagem",value:(a==null?void 0:a.bannerUrl)||"",onChange:r=>j("bannerUrl",r.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"})]}):e.jsx("div",{className:"flex items-center justify-center h-32 w-full mx-auto bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden",children:s!=null&&s.bannerUrl?e.jsx("img",{src:s.bannerUrl,alt:"Banner do Restaurante",className:"max-h-full max-w-full object-cover w-full h-full"}):e.jsx(U,{className:"w-12 h-12 text-gray-400","aria-hidden":"true"})})]})]})]})]}),te=()=>e.jsx("div",{className:"space-y-6",role:"tabpanel",id:"panel-hours","aria-labelledby":"tab-hours",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2",children:[e.jsx(J,{className:"w-5 h-5","aria-hidden":"true"}),e.jsx("span",{children:"Horário de Funcionamento"})]}),e.jsx("div",{className:"space-y-4",children:P.map(r=>{var t,n,d,o,i,c,h;return e.jsxs("div",{className:"flex flex-wrap md:flex-nowrap items-center gap-4 p-3 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg transition-colors",children:[e.jsx("div",{className:"w-full md:w-32",children:e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:r.label})}),l?e.jsxs(e.Fragment,{children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:((t=a==null?void 0:a.businessHours[r.key])==null?void 0:t.isOpen)||!1,onChange:g=>H(r.key,"isOpen",g.target.checked),className:"rounded text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-sm",children:"Aberto"})]}),((n=a==null?void 0:a.businessHours[r.key])==null?void 0:n.isOpen)&&e.jsxs("div",{className:"flex flex-wrap md:flex-nowrap items-center gap-2",children:[e.jsx("label",{className:"text-sm sr-only",htmlFor:`open-${r.key}`,children:"Horário de abertura"}),e.jsx("input",{id:`open-${r.key}`,type:"time",value:((d=a.businessHours[r.key])==null?void 0:d.open)||"",onChange:g=>H(r.key,"open",g.target.value),className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),e.jsx("span",{className:"text-gray-500",children:"às"}),e.jsx("label",{className:"text-sm sr-only",htmlFor:`close-${r.key}`,children:"Horário de fechamento"}),e.jsx("input",{id:`close-${r.key}`,type:"time",value:((o=a.businessHours[r.key])==null?void 0:o.close)||"",onChange:g=>H(r.key,"close",g.target.value),className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}):e.jsx("div",{className:"flex items-center space-x-2",children:(i=s==null?void 0:s.businessHours[r.key])!=null&&i.isOpen?e.jsxs(e.Fragment,{children:[e.jsx(pe,{className:"w-5 h-5 text-green-600","aria-hidden":"true"}),e.jsxs("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:[(c=s.businessHours[r.key])==null?void 0:c.open," às"," ",(h=s.businessHours[r.key])==null?void 0:h.close]})]}):e.jsxs(e.Fragment,{children:[e.jsx(_,{className:"w-5 h-5 text-red-600","aria-hidden":"true"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Fechado"})]})})]},r.key)})})]})}),ne=()=>e.jsx("div",{className:"space-y-6",role:"tabpanel",id:"panel-settings","aria-labelledby":"tab-settings",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2",children:[e.jsx(K,{className:"w-5 h-5","aria-hidden":"true"}),e.jsx("span",{children:"Configurações da Playlist"})]}),e.jsxs("div",{className:"space-y-6 divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Permitir Sugestões dos Clientes"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Clientes podem sugerir músicas via QR code"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"allow-suggestions",checked:(a==null?void 0:a.settings.allowSuggestions)||!1,onChange:r=>v("allowSuggestions",r.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"allow-suggestions",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${a!=null&&a.settings.allowSuggestions?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${a!=null&&a.settings.allowSuggestions?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${s!=null&&s.settings.allowSuggestions?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:s!=null&&s.settings.allowSuggestions?"Ativo":"Inativo"})]})}),e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Moderação Obrigatória"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Sugestões precisam ser aprovadas antes de tocar"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"moderation-required",checked:(a==null?void 0:a.settings.moderationRequired)||!1,onChange:r=>v("moderationRequired",r.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"moderation-required",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${a!=null&&a.settings.moderationRequired?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${a!=null&&a.settings.moderationRequired?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${s!=null&&s.settings.moderationRequired?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"}`,children:s!=null&&s.settings.moderationRequired?"Ativo":"Inativo"})]})}),e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Reprodução Automática"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Próxima música inicia automaticamente"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"auto-play",checked:(a==null?void 0:a.settings.autoPlayEnabled)||!1,onChange:r=>v("autoPlayEnabled",r.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"auto-play",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${a!=null&&a.settings.autoPlayEnabled?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${a!=null&&a.settings.autoPlayEnabled?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${s!=null&&s.settings.autoPlayEnabled?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:s!=null&&s.settings.autoPlayEnabled?"Ativo":"Inativo"})]})}),e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Pular Músicas com Muitos Votos Negativos"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Músicas com muitos dislikes serão puladas automaticamente"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"auto-skip",checked:(a==null?void 0:a.settings.autoSkipDisliked)||!1,onChange:r=>v("autoSkipDisliked",r.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"auto-skip",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${a!=null&&a.settings.autoSkipDisliked?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${a!=null&&a.settings.autoSkipDisliked?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${s!=null&&s.settings.autoSkipDisliked?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:s!=null&&s.settings.autoSkipDisliked?"Ativo":"Inativo"})]})}),e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Máximo de Sugestões por Cliente"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Limite de sugestões por cliente por sessão"})]}),l?e.jsx("input",{type:"number",min:"1",max:"10",value:(a==null?void 0:a.settings.maxSuggestionsPerUser)||3,onChange:r=>v("maxSuggestionsPerUser",parseInt(r.target.value)),className:"w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}):e.jsx("div",{className:"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium",children:s==null?void 0:s.settings.maxSuggestionsPerUser})]})})]})]})}),le=()=>{var r,t,n,d,o,i,c,h,g,w,C,S,A,E,F,y,L,G,z;return e.jsx("div",{className:"space-y-6",role:"tabpanel",id:"panel-appearance","aria-labelledby":"tab-appearance",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2",children:[e.jsx(T,{className:"w-5 h-5","aria-hidden":"true"}),e.jsx("span",{children:"Aparência do Player"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Modo Escuro"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Ativar interface com tema escuro para o player de música"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"dark-mode",checked:((r=a==null?void 0:a.appearance)==null?void 0:r.darkMode)||!1,onChange:k=>R("darkMode",k.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"dark-mode",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${(t=a==null?void 0:a.appearance)!=null&&t.darkMode?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${(n=a==null?void 0:a.appearance)!=null&&n.darkMode?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${(d=s==null?void 0:s.appearance)!=null&&d.darkMode?"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400":"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"}`,children:(o=s==null?void 0:s.appearance)!=null&&o.darkMode?"Escuro":"Claro"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"primary-color",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Cor Primária"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[l?e.jsx("input",{id:"primary-color",type:"color",value:((i=a==null?void 0:a.appearance)==null?void 0:i.primaryColor)||"#3b82f6",onChange:k=>R("primaryColor",k.target.value),className:"h-10 w-20 border-0 p-0 rounded"}):e.jsx("div",{className:"h-6 w-6 rounded-full border border-gray-300 dark:border-gray-600",style:{backgroundColor:((c=s==null?void 0:s.appearance)==null?void 0:c.primaryColor)||"#3b82f6"}}),e.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:((h=s==null?void 0:s.appearance)==null?void 0:h.primaryColor)||"#3b82f6"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"accent-color",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Cor de Destaque"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[l?e.jsx("input",{id:"accent-color",type:"color",value:((g=a==null?void 0:a.appearance)==null?void 0:g.accentColor)||"#10b981",onChange:k=>R("accentColor",k.target.value),className:"h-10 w-20 border-0 p-0 rounded"}):e.jsx("div",{className:"h-6 w-6 rounded-full border border-gray-300 dark:border-gray-600",style:{backgroundColor:((w=s==null?void 0:s.appearance)==null?void 0:w.accentColor)||"#10b981"}}),e.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:((C=s==null?void 0:s.appearance)==null?void 0:C.accentColor)||"#10b981"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"font-family",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Fonte"}),l?e.jsxs("select",{id:"font-family",value:((S=a==null?void 0:a.appearance)==null?void 0:S.fontFamily)||"Inter",onChange:k=>R("fontFamily",k.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"Inter",children:"Inter"}),e.jsx("option",{value:"Roboto",children:"Roboto"}),e.jsx("option",{value:"Poppins",children:"Poppins"}),e.jsx("option",{value:"Montserrat",children:"Montserrat"}),e.jsx("option",{value:"Open Sans",children:"Open Sans"})]}):e.jsx("div",{className:"px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg",children:e.jsx("span",{style:{fontFamily:((A=s==null?void 0:s.appearance)==null?void 0:A.fontFamily)||"Inter"},children:((E=s==null?void 0:s.appearance)==null?void 0:E.fontFamily)||"Inter"})})]}),e.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-750 rounded-lg",children:[e.jsx("h4",{className:"font-medium text-sm text-gray-700 dark:text-gray-300 mb-3",children:"Visualização"}),e.jsxs("div",{className:"h-36 rounded-lg bg-white dark:bg-gray-850 border border-gray-200 dark:border-gray-700 p-3 flex flex-col",style:{backgroundColor:(F=a==null?void 0:a.appearance)!=null&&F.darkMode?"#1f2937":"#ffffff",color:(y=a==null?void 0:a.appearance)!=null&&y.darkMode?"#f3f4f6":"#111827",fontFamily:((L=a==null?void 0:a.appearance)==null?void 0:L.fontFamily)||"Inter"},children:[e.jsxs("div",{className:"text-center mb-2",children:[e.jsx("div",{className:"font-bold",style:{color:(G=a==null?void 0:a.appearance)==null?void 0:G.primaryColor},children:"Agora Tocando"}),e.jsx("div",{className:"text-sm",children:"Nome da Música - Artista"})]}),e.jsx("div",{className:"flex-1 flex items-center justify-center",children:e.jsx("div",{className:"h-2 w-full rounded-full bg-gray-200 dark:bg-gray-600",children:e.jsx("div",{className:"h-2 rounded-full",style:{width:"40%",backgroundColor:((z=a==null?void 0:a.appearance)==null?void 0:z.accentColor)||"#10b981"}})})}),e.jsxs("div",{className:"flex justify-between text-xs mt-2",children:[e.jsx("span",{children:"1:30"}),e.jsx("span",{children:"3:45"})]})]})]})]})]})})},oe=()=>{var r,t,n,d,o,i,c,h,g,w,C,S,A,E,F;return e.jsx("div",{className:"space-y-6",role:"tabpanel",id:"panel-integrations","aria-labelledby":"tab-integrations",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2",children:[e.jsx(W,{className:"w-5 h-5","aria-hidden":"true"}),e.jsx("span",{children:"Integrações"})]}),e.jsxs("div",{className:"space-y-6 divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"API do YouTube"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Usar a API oficial do YouTube para buscar músicas"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"youtube-api",checked:((r=a==null?void 0:a.integrations)==null?void 0:r.youtubeApiEnabled)||!1,onChange:y=>D("youtubeApiEnabled",y.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"youtube-api",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${(t=a==null?void 0:a.integrations)!=null&&t.youtubeApiEnabled?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${(n=a==null?void 0:a.integrations)!=null&&n.youtubeApiEnabled?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${(d=s==null?void 0:s.integrations)!=null&&d.youtubeApiEnabled?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:(o=s==null?void 0:s.integrations)!=null&&o.youtubeApiEnabled?"Conectado":"Desconectado"})]})}),e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Spotify"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Integrar com sua conta Spotify para playlists"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"spotify-connected",checked:((i=a==null?void 0:a.integrations)==null?void 0:i.spotifyConnected)||!1,onChange:y=>D("spotifyConnected",y.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"spotify-connected",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${(c=a==null?void 0:a.integrations)!=null&&c.spotifyConnected?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${(h=a==null?void 0:a.integrations)!=null&&h.spotifyConnected?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${(g=s==null?void 0:s.integrations)!=null&&g.spotifyConnected?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:(w=s==null?void 0:s.integrations)!=null&&w.spotifyConnected?"Conectado":"Desconectado"})]})}),e.jsx("div",{className:"py-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Google Analytics"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Monitorar estatísticas de uso do player"})]}),l?e.jsxs("div",{className:"relative inline-block w-12 mr-2 align-middle select-none",children:[e.jsx("input",{type:"checkbox",id:"google-analytics",checked:((C=a==null?void 0:a.integrations)==null?void 0:C.googleAnalytics)||!1,onChange:y=>D("googleAnalytics",y.target.checked),className:"sr-only"}),e.jsx("label",{htmlFor:"google-analytics",className:`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${(S=a==null?void 0:a.integrations)!=null&&S.googleAnalytics?"bg-blue-600":"bg-gray-300 dark:bg-gray-600"}`,children:e.jsx("span",{className:`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${(A=a==null?void 0:a.integrations)!=null&&A.googleAnalytics?"translate-x-6":"translate-x-0"}`})})]}):e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${(E=s==null?void 0:s.integrations)!=null&&E.googleAnalytics?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:(F=s==null?void 0:s.integrations)!=null&&F.googleAnalytics?"Ativo":"Inativo"})]})})]})]})})};return N?e.jsxs("div",{className:"flex justify-center items-center h-64",children:[e.jsx(Y,{className:"w-8 h-8 animate-spin text-blue-600","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Carregando perfil do restaurante"})]}):s?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Perfil do Restaurante"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie as informações e configurações do seu restaurante"})]}),e.jsx("div",{className:"flex space-x-2",children:l?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:se,disabled:M,className:"flex items-center space-x-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800","aria-label":"Cancelar edição",children:[e.jsx(_,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Cancelar"})]}),e.jsxs("button",{onClick:ae,disabled:M,className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800","aria-label":"Salvar alterações",children:[M?e.jsx(Y,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(ge,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Salvar"})]})]}):e.jsxs("button",{onClick:()=>I(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800","aria-label":"Editar perfil",children:[e.jsx(xe,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Editar"})]})})]}),e.jsxs("div",{className:"flex flex-col space-y-6",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-x-auto",children:e.jsx("div",{className:"flex p-2 gap-1 min-w-max",role:"tablist",children:ee.map(r=>e.jsx(je,{id:r.id,label:r.label,icon:r.icon,active:p===r.id,onClick:X},r.id))})}),e.jsx(me,{mode:"wait",children:e.jsxs(ue.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},children:[p==="basic"&&re(),p==="hours"&&te(),p==="settings"&&ne(),p==="appearance"&&le(),p==="integrations"&&oe()]},p)})]})]}):e.jsxs("div",{className:"text-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg",children:[e.jsx(ce,{className:"w-12 h-12 text-red-400 mx-auto mb-4","aria-hidden":"true"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Erro ao carregar perfil"}),e.jsx("p",{className:"text-red-500 mb-4",children:Z||"Não foi possível carregar os dados do perfil"}),e.jsx("button",{onClick:O,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",children:"Tentar novamente"})]})};export{Ce as default};
//# sourceMappingURL=RestaurantProfile-60dba3be.js.map
