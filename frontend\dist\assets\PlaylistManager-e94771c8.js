import{_ as R,a as tn,u as Ci,b as se,c as $,j as l}from"./index-6e2e4ef2.js";import{a as z,r as I,g as <PERSON>,b as Ii,c as Pi}from"./vendor-66b0ef43.js";import{b as Ei}from"./utils-08f61814.js";import{D as ki,ag as Ai,j as pa,h as ji,ah as Ti,ai as Ri,aj as Bi,c as Ge,ak as Tt,al as zr,am as Xe,R as Fe,i as Oi,A as kr,m as he,X as Br,U as Mi,an as Li,Z as Fi,l as Rt,q as an,_ as Gi,ao as $i}from"./ui-a5f8f5f0.js";import"./router-f729e475.js";function fa(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,a)}return t}function va(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?fa(Object(t),!0).forEach(function(a){Ei(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):fa(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function ie(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var ma=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),pt=function(){return Math.random().toString(36).substring(7).split("").join(".")},ba={INIT:"@@redux/INIT"+pt(),REPLACE:"@@redux/REPLACE"+pt(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+pt()}};function Ui(e){if(typeof e!="object"||e===null)return!1;for(var r=e;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}function nn(e,r,t){var a;if(typeof r=="function"&&typeof t=="function"||typeof t=="function"&&typeof arguments[3]=="function")throw new Error(ie(0));if(typeof r=="function"&&typeof t>"u"&&(t=r,r=void 0),typeof t<"u"){if(typeof t!="function")throw new Error(ie(1));return t(nn)(e,r)}if(typeof e!="function")throw new Error(ie(2));var n=e,i=r,o=[],s=o,d=!1;function g(){s===o&&(s=o.slice())}function p(){if(d)throw new Error(ie(3));return i}function c(h){if(typeof h!="function")throw new Error(ie(4));if(d)throw new Error(ie(5));var y=!0;return g(),s.push(h),function(){if(y){if(d)throw new Error(ie(6));y=!1,g();var w=s.indexOf(h);s.splice(w,1),o=null}}}function u(h){if(!Ui(h))throw new Error(ie(7));if(typeof h.type>"u")throw new Error(ie(8));if(d)throw new Error(ie(9));try{d=!0,i=n(i,h)}finally{d=!1}for(var y=o=s,x=0;x<y.length;x++){var w=y[x];w()}return h}function f(h){if(typeof h!="function")throw new Error(ie(10));n=h,u({type:ba.REPLACE})}function m(){var h,y=c;return h={subscribe:function(w){if(typeof w!="object"||w===null)throw new Error(ie(11));function N(){w.next&&w.next(p())}N();var D=y(N);return{unsubscribe:D}}},h[ma]=function(){return this},h}return u({type:ba.INIT}),a={dispatch:u,subscribe:c,getState:p,replaceReducer:f},a[ma]=m,a}function ha(e,r){return function(){return r(e.apply(this,arguments))}}function ya(e,r){if(typeof e=="function")return ha(e,r);if(typeof e!="object"||e===null)throw new Error(ie(16));var t={};for(var a in e){var n=e[a];typeof n=="function"&&(t[a]=ha(n,r))}return t}function on(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.length===0?function(a){return a}:r.length===1?r[0]:r.reduce(function(a,n){return function(){return a(n.apply(void 0,arguments))}})}function Wi(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(a){return function(){var n=a.apply(void 0,arguments),i=function(){throw new Error(ie(15))},o={getState:n.getState,dispatch:function(){return i.apply(void 0,arguments)}},s=r.map(function(d){return d(o)});return i=on.apply(void 0,s)(n.dispatch),va(va({},n),{},{dispatch:i})}}}var sn=z.createContext(null);function Hi(e){e()}var ln=Hi,Vi=function(r){return ln=r},zi=function(){return ln};function qi(){var e=zi(),r=null,t=null;return{clear:function(){r=null,t=null},notify:function(){e(function(){for(var n=r;n;)n.callback(),n=n.next})},get:function(){for(var n=[],i=r;i;)n.push(i),i=i.next;return n},subscribe:function(n){var i=!0,o=t={callback:n,next:null,prev:t};return o.prev?o.prev.next=o:r=o,function(){!i||r===null||(i=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:r=o.next)}}}}var xa={notify:function(){},get:function(){return[]}};function dn(e,r){var t,a=xa;function n(c){return d(),a.subscribe(c)}function i(){a.notify()}function o(){p.onStateChange&&p.onStateChange()}function s(){return!!t}function d(){t||(t=r?r.addNestedSub(o):e.subscribe(o),a=qi())}function g(){t&&(t(),t=void 0,a.clear(),a=xa)}var p={addNestedSub:n,notifyNestedSubs:i,handleChangeWrapper:o,isSubscribed:s,trySubscribe:d,tryUnsubscribe:g,getListeners:function(){return a}};return p}var cn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?I.useLayoutEffect:I.useEffect;function Yi(e){var r=e.store,t=e.context,a=e.children,n=I.useMemo(function(){var s=dn(r);return{store:r,subscription:s}},[r]),i=I.useMemo(function(){return r.getState()},[r]);cn(function(){var s=n.subscription;return s.onStateChange=s.notifyNestedSubs,s.trySubscribe(),i!==r.getState()&&s.notifyNestedSubs(),function(){s.tryUnsubscribe(),s.onStateChange=null}},[n,i]);var o=t||sn;return z.createElement(o.Provider,{value:n},a)}function Mr(e,r){if(e==null)return{};var t={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(r.indexOf(a)!==-1)continue;t[a]=e[a]}return t}var un={exports:{}},U={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X=typeof Symbol=="function"&&Symbol.for,Bt=X?Symbol.for("react.element"):60103,Ot=X?Symbol.for("react.portal"):60106,qr=X?Symbol.for("react.fragment"):60107,Yr=X?Symbol.for("react.strict_mode"):60108,Jr=X?Symbol.for("react.profiler"):60114,Kr=X?Symbol.for("react.provider"):60109,Xr=X?Symbol.for("react.context"):60110,Mt=X?Symbol.for("react.async_mode"):60111,Qr=X?Symbol.for("react.concurrent_mode"):60111,Zr=X?Symbol.for("react.forward_ref"):60112,_r=X?Symbol.for("react.suspense"):60113,Ji=X?Symbol.for("react.suspense_list"):60120,et=X?Symbol.for("react.memo"):60115,rt=X?Symbol.for("react.lazy"):60116,Ki=X?Symbol.for("react.block"):60121,Xi=X?Symbol.for("react.fundamental"):60117,Qi=X?Symbol.for("react.responder"):60118,Zi=X?Symbol.for("react.scope"):60119;function ue(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case Bt:switch(e=e.type,e){case Mt:case Qr:case qr:case Jr:case Yr:case _r:return e;default:switch(e=e&&e.$$typeof,e){case Xr:case Zr:case rt:case et:case Kr:return e;default:return r}}case Ot:return r}}}function gn(e){return ue(e)===Qr}U.AsyncMode=Mt;U.ConcurrentMode=Qr;U.ContextConsumer=Xr;U.ContextProvider=Kr;U.Element=Bt;U.ForwardRef=Zr;U.Fragment=qr;U.Lazy=rt;U.Memo=et;U.Portal=Ot;U.Profiler=Jr;U.StrictMode=Yr;U.Suspense=_r;U.isAsyncMode=function(e){return gn(e)||ue(e)===Mt};U.isConcurrentMode=gn;U.isContextConsumer=function(e){return ue(e)===Xr};U.isContextProvider=function(e){return ue(e)===Kr};U.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Bt};U.isForwardRef=function(e){return ue(e)===Zr};U.isFragment=function(e){return ue(e)===qr};U.isLazy=function(e){return ue(e)===rt};U.isMemo=function(e){return ue(e)===et};U.isPortal=function(e){return ue(e)===Ot};U.isProfiler=function(e){return ue(e)===Jr};U.isStrictMode=function(e){return ue(e)===Yr};U.isSuspense=function(e){return ue(e)===_r};U.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===qr||e===Qr||e===Jr||e===Yr||e===_r||e===Ji||typeof e=="object"&&e!==null&&(e.$$typeof===rt||e.$$typeof===et||e.$$typeof===Kr||e.$$typeof===Xr||e.$$typeof===Zr||e.$$typeof===Xi||e.$$typeof===Qi||e.$$typeof===Zi||e.$$typeof===Ki)};U.typeOf=ue;un.exports=U;var _i=un.exports,Lt=_i,eo={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},ro={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},to={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},pn={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Ft={};Ft[Lt.ForwardRef]=to;Ft[Lt.Memo]=pn;function wa(e){return Lt.isMemo(e)?pn:Ft[e.$$typeof]||eo}var ao=Object.defineProperty,no=Object.getOwnPropertyNames,Da=Object.getOwnPropertySymbols,io=Object.getOwnPropertyDescriptor,oo=Object.getPrototypeOf,Sa=Object.prototype;function fn(e,r,t){if(typeof r!="string"){if(Sa){var a=oo(r);a&&a!==Sa&&fn(e,a,t)}var n=no(r);Da&&(n=n.concat(Da(r)));for(var i=wa(e),o=wa(r),s=0;s<n.length;++s){var d=n[s];if(!ro[d]&&!(t&&t[d])&&!(o&&o[d])&&!(i&&i[d])){var g=io(r,d);try{ao(e,d,g)}catch{}}}}return e}var so=fn;const Ca=Ni(so);var vn={exports:{}},H={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tt=60103,at=60106,vr=60107,mr=60108,br=60114,hr=60109,yr=60110,xr=60112,wr=60113,Gt=60120,Dr=60115,Sr=60116,mn=60121,bn=60122,hn=60117,yn=60129,xn=60131;if(typeof Symbol=="function"&&Symbol.for){var _=Symbol.for;tt=_("react.element"),at=_("react.portal"),vr=_("react.fragment"),mr=_("react.strict_mode"),br=_("react.profiler"),hr=_("react.provider"),yr=_("react.context"),xr=_("react.forward_ref"),wr=_("react.suspense"),Gt=_("react.suspense_list"),Dr=_("react.memo"),Sr=_("react.lazy"),mn=_("react.block"),bn=_("react.server.block"),hn=_("react.fundamental"),yn=_("react.debug_trace_mode"),xn=_("react.legacy_hidden")}function be(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case tt:switch(e=e.type,e){case vr:case br:case mr:case wr:case Gt:return e;default:switch(e=e&&e.$$typeof,e){case yr:case xr:case Sr:case Dr:case hr:return e;default:return r}}case at:return r}}}var lo=hr,co=tt,uo=xr,go=vr,po=Sr,fo=Dr,vo=at,mo=br,bo=mr,ho=wr;H.ContextConsumer=yr;H.ContextProvider=lo;H.Element=co;H.ForwardRef=uo;H.Fragment=go;H.Lazy=po;H.Memo=fo;H.Portal=vo;H.Profiler=mo;H.StrictMode=bo;H.Suspense=ho;H.isAsyncMode=function(){return!1};H.isConcurrentMode=function(){return!1};H.isContextConsumer=function(e){return be(e)===yr};H.isContextProvider=function(e){return be(e)===hr};H.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===tt};H.isForwardRef=function(e){return be(e)===xr};H.isFragment=function(e){return be(e)===vr};H.isLazy=function(e){return be(e)===Sr};H.isMemo=function(e){return be(e)===Dr};H.isPortal=function(e){return be(e)===at};H.isProfiler=function(e){return be(e)===br};H.isStrictMode=function(e){return be(e)===mr};H.isSuspense=function(e){return be(e)===wr};H.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===vr||e===br||e===yn||e===mr||e===wr||e===Gt||e===xn||typeof e=="object"&&e!==null&&(e.$$typeof===Sr||e.$$typeof===Dr||e.$$typeof===hr||e.$$typeof===yr||e.$$typeof===xr||e.$$typeof===hn||e.$$typeof===mn||e[0]===bn)};H.typeOf=be;vn.exports=H;var yo=vn.exports,xo=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],wo=["reactReduxForwardedRef"],Do=[],So=[null,null];function Co(e,r){var t=e[1];return[r.payload,t+1]}function Na(e,r,t){cn(function(){return e.apply(void 0,r)},t)}function No(e,r,t,a,n,i,o){e.current=a,r.current=n,t.current=!1,i.current&&(i.current=null,o())}function Io(e,r,t,a,n,i,o,s,d,g){if(e){var p=!1,c=null,u=function(){if(!p){var h=r.getState(),y,x;try{y=a(h,n.current)}catch(w){x=w,c=w}x||(c=null),y===i.current?o.current||d():(i.current=y,s.current=y,o.current=!0,g({type:"STORE_UPDATED",payload:{error:x}}))}};t.onStateChange=u,t.trySubscribe(),u();var f=function(){if(p=!0,t.tryUnsubscribe(),t.onStateChange=null,c)throw c};return f}}var Po=function(){return[null,0]};function Eo(e,r){r===void 0&&(r={});var t=r,a=t.getDisplayName,n=a===void 0?function(N){return"ConnectAdvanced("+N+")"}:a,i=t.methodName,o=i===void 0?"connectAdvanced":i,s=t.renderCountProp,d=s===void 0?void 0:s,g=t.shouldHandleStateChanges,p=g===void 0?!0:g,c=t.storeKey,u=c===void 0?"store":c;t.withRef;var f=t.forwardRef,m=f===void 0?!1:f,h=t.context,y=h===void 0?sn:h,x=Mr(t,xo),w=y;return function(D){var j=D.displayName||D.name||"Component",k=n(j),L=R({},x,{getDisplayName:n,methodName:o,renderCountProp:d,shouldHandleStateChanges:p,storeKey:u,displayName:k,wrappedComponentName:j,WrappedComponent:D}),F=x.pure;function M(T){return e(T.dispatch,L)}var E=F?I.useMemo:function(T){return T()};function B(T){var Z=I.useMemo(function(){var Ce=T.reactReduxForwardedRef,Ye=Mr(T,wo);return[T.context,Ce,Ye]},[T]),q=Z[0],ae=Z[1],oe=Z[2],ye=I.useMemo(function(){return q&&q.Consumer&&yo.isContextConsumer(z.createElement(q.Consumer,null))?q:w},[q,w]),V=I.useContext(ye),ne=!!T.store&&!!T.store.getState&&!!T.store.dispatch;V&&V.store;var re=ne?T.store:V.store,Se=I.useMemo(function(){return M(re)},[re]),Ue=I.useMemo(function(){if(!p)return So;var Ce=dn(re,ne?null:V.subscription),Ye=Ce.notifyNestedSubs.bind(Ce);return[Ce,Ye]},[re,ne,V]),ge=Ue[0],je=Ue[1],We=I.useMemo(function(){return ne?V:R({},V,{subscription:ge})},[ne,V,ge]),Te=I.useReducer(Co,Do,Po),He=Te[0],xe=He[0],Ve=Te[1];if(xe&&xe.error)throw xe.error;var Ir=I.useRef(),ar=I.useRef(oe),ze=I.useRef(),Pr=I.useRef(!1),qe=E(function(){return ze.current&&oe===ar.current?ze.current:Se(re.getState(),oe)},[re,xe,oe]);Na(No,[ar,Ir,Pr,oe,qe,ze,je]),Na(Io,[p,re,ge,Se,ar,Ir,Pr,ze,je,Ve],[re,ge,Se]);var Re=I.useMemo(function(){return z.createElement(D,R({},qe,{ref:ae}))},[ae,D,qe]),nr=I.useMemo(function(){return p?z.createElement(ye.Provider,{value:We},Re):Re},[ye,Re,We]);return nr}var Q=F?z.memo(B):B;if(Q.WrappedComponent=D,Q.displayName=B.displayName=k,m){var Y=z.forwardRef(function(Z,q){return z.createElement(Q,R({},Z,{reactReduxForwardedRef:q}))});return Y.displayName=k,Y.WrappedComponent=D,Ca(Y,D)}return Ca(Q,D)}}function Ia(e,r){return e===r?e!==0||r!==0||1/e===1/r:e!==e&&r!==r}function ft(e,r){if(Ia(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;var t=Object.keys(e),a=Object.keys(r);if(t.length!==a.length)return!1;for(var n=0;n<t.length;n++)if(!Object.prototype.hasOwnProperty.call(r,t[n])||!Ia(e[t[n]],r[t[n]]))return!1;return!0}function ko(e,r){var t={},a=function(o){var s=e[o];typeof s=="function"&&(t[o]=function(){return r(s.apply(void 0,arguments))})};for(var n in e)a(n);return t}function $t(e){return function(t,a){var n=e(t,a);function i(){return n}return i.dependsOnOwnProps=!1,i}}function Pa(e){return e.dependsOnOwnProps!==null&&e.dependsOnOwnProps!==void 0?!!e.dependsOnOwnProps:e.length!==1}function wn(e,r){return function(a,n){n.displayName;var i=function(s,d){return i.dependsOnOwnProps?i.mapToProps(s,d):i.mapToProps(s)};return i.dependsOnOwnProps=!0,i.mapToProps=function(s,d){i.mapToProps=e,i.dependsOnOwnProps=Pa(e);var g=i(s,d);return typeof g=="function"&&(i.mapToProps=g,i.dependsOnOwnProps=Pa(g),g=i(s,d)),g},i}}function Ao(e){return typeof e=="function"?wn(e):void 0}function jo(e){return e?void 0:$t(function(r){return{dispatch:r}})}function To(e){return e&&typeof e=="object"?$t(function(r){return ko(e,r)}):void 0}const Ro=[Ao,jo,To];function Bo(e){return typeof e=="function"?wn(e):void 0}function Oo(e){return e?void 0:$t(function(){return{}})}const Mo=[Bo,Oo];function Lo(e,r,t){return R({},t,e,r)}function Fo(e){return function(t,a){a.displayName;var n=a.pure,i=a.areMergedPropsEqual,o=!1,s;return function(g,p,c){var u=e(g,p,c);return o?(!n||!i(u,s))&&(s=u):(o=!0,s=u),s}}}function Go(e){return typeof e=="function"?Fo(e):void 0}function $o(e){return e?void 0:function(){return Lo}}const Uo=[Go,$o];var Wo=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function Ho(e,r,t,a){return function(i,o){return t(e(i,o),r(a,o),o)}}function Vo(e,r,t,a,n){var i=n.areStatesEqual,o=n.areOwnPropsEqual,s=n.areStatePropsEqual,d=!1,g,p,c,u,f;function m(N,D){return g=N,p=D,c=e(g,p),u=r(a,p),f=t(c,u,p),d=!0,f}function h(){return c=e(g,p),r.dependsOnOwnProps&&(u=r(a,p)),f=t(c,u,p),f}function y(){return e.dependsOnOwnProps&&(c=e(g,p)),r.dependsOnOwnProps&&(u=r(a,p)),f=t(c,u,p),f}function x(){var N=e(g,p),D=!s(N,c);return c=N,D&&(f=t(c,u,p)),f}function w(N,D){var j=!o(D,p),k=!i(N,g,D,p);return g=N,p=D,j&&k?h():j?y():k?x():f}return function(D,j){return d?w(D,j):m(D,j)}}function zo(e,r){var t=r.initMapStateToProps,a=r.initMapDispatchToProps,n=r.initMergeProps,i=Mr(r,Wo),o=t(e,i),s=a(e,i),d=n(e,i),g=i.pure?Vo:Ho;return g(o,s,d,e,i)}var qo=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function vt(e,r,t){for(var a=r.length-1;a>=0;a--){var n=r[a](e);if(n)return n}return function(i,o){throw new Error("Invalid value of type "+typeof e+" for "+t+" argument when connecting component "+o.wrappedComponentName+".")}}function Yo(e,r){return e===r}function Jo(e){var r=e===void 0?{}:e,t=r.connectHOC,a=t===void 0?Eo:t,n=r.mapStateToPropsFactories,i=n===void 0?Mo:n,o=r.mapDispatchToPropsFactories,s=o===void 0?Ro:o,d=r.mergePropsFactories,g=d===void 0?Uo:d,p=r.selectorFactory,c=p===void 0?zo:p;return function(f,m,h,y){y===void 0&&(y={});var x=y,w=x.pure,N=w===void 0?!0:w,D=x.areStatesEqual,j=D===void 0?Yo:D,k=x.areOwnPropsEqual,L=k===void 0?ft:k,F=x.areStatePropsEqual,M=F===void 0?ft:F,E=x.areMergedPropsEqual,B=E===void 0?ft:E,Q=Mr(x,qo),Y=vt(f,i,"mapStateToProps"),T=vt(m,s,"mapDispatchToProps"),Z=vt(h,g,"mergeProps");return a(c,R({methodName:"connect",getDisplayName:function(ae){return"Connect("+ae+")"},shouldHandleStateChanges:!!f,initMapStateToProps:Y,initMapDispatchToProps:T,initMergeProps:Z,pure:N,areStatesEqual:j,areOwnPropsEqual:L,areStatePropsEqual:M,areMergedPropsEqual:B},Q))}}const Dn=Jo();Vi(Ii.unstable_batchedUpdates);function Ko(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}function Sn(e,r){var t=I.useState(function(){return{inputs:r,result:e()}})[0],a=I.useRef(!0),n=I.useRef(t),i=a.current||!!(r&&n.current.inputs&&Ko(r,n.current.inputs)),o=i?n.current:{inputs:r,result:e()};return I.useEffect(function(){a.current=!1,n.current=o},[o]),o.result}function Xo(e,r){return Sn(function(){return e},r)}var G=Sn,A=Xo,Qo=!0,mt="Invariant failed";function Zo(e,r){if(!e){if(Qo)throw new Error(mt);var t=typeof r=="function"?r():r,a=t?"".concat(mt,": ").concat(t):mt;throw new Error(a)}}var me=function(r){var t=r.top,a=r.right,n=r.bottom,i=r.left,o=a-i,s=n-t,d={top:t,right:a,bottom:n,left:i,width:o,height:s,x:i,y:t,center:{x:(a+i)/2,y:(n+t)/2}};return d},Ut=function(r,t){return{top:r.top-t.top,left:r.left-t.left,bottom:r.bottom+t.bottom,right:r.right+t.right}},Ea=function(r,t){return{top:r.top+t.top,left:r.left+t.left,bottom:r.bottom-t.bottom,right:r.right-t.right}},_o=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},bt={top:0,right:0,bottom:0,left:0},Wt=function(r){var t=r.borderBox,a=r.margin,n=a===void 0?bt:a,i=r.border,o=i===void 0?bt:i,s=r.padding,d=s===void 0?bt:s,g=me(Ut(t,n)),p=me(Ea(t,o)),c=me(Ea(p,d));return{marginBox:g,borderBox:me(t),paddingBox:p,contentBox:c,margin:n,border:o,padding:d}},pe=function(r){var t=r.slice(0,-2),a=r.slice(-2);if(a!=="px")return 0;var n=Number(t);return isNaN(n)&&Zo(!1),n},es=function(){return{x:window.pageXOffset,y:window.pageYOffset}},Lr=function(r,t){var a=r.borderBox,n=r.border,i=r.margin,o=r.padding,s=_o(a,t);return Wt({borderBox:s,border:n,margin:i,padding:o})},Fr=function(r,t){return t===void 0&&(t=es()),Lr(r,t)},Cn=function(r,t){var a={top:pe(t.marginTop),right:pe(t.marginRight),bottom:pe(t.marginBottom),left:pe(t.marginLeft)},n={top:pe(t.paddingTop),right:pe(t.paddingRight),bottom:pe(t.paddingBottom),left:pe(t.paddingLeft)},i={top:pe(t.borderTopWidth),right:pe(t.borderRightWidth),bottom:pe(t.borderBottomWidth),left:pe(t.borderLeftWidth)};return Wt({borderBox:r,margin:a,padding:n,border:i})},Nn=function(r){var t=r.getBoundingClientRect(),a=window.getComputedStyle(r);return Cn(t,a)},ka=Number.isNaN||function(r){return typeof r=="number"&&r!==r};function rs(e,r){return!!(e===r||ka(e)&&ka(r))}function ts(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(!rs(e[t],r[t]))return!1;return!0}function J(e,r){r===void 0&&(r=ts);var t,a=[],n,i=!1;function o(){for(var s=[],d=0;d<arguments.length;d++)s[d]=arguments[d];return i&&t===this&&r(s,a)||(n=e.apply(this,s),i=!0,t=this,a=s),n}return o}var as=function(r){var t=[],a=null,n=function(){for(var o=arguments.length,s=new Array(o),d=0;d<o;d++)s[d]=arguments[d];t=s,!a&&(a=requestAnimationFrame(function(){a=null,r.apply(void 0,t)}))};return n.cancel=function(){a&&(cancelAnimationFrame(a),a=null)},n};const cr=as;function In(e,r){}In.bind(null,"warn");In.bind(null,"error");function Ie(){}function ns(e,r){return R({},e,{},r)}function fe(e,r,t){var a=r.map(function(n){var i=ns(t,n.options);return e.addEventListener(n.eventName,n.fn,i),function(){e.removeEventListener(n.eventName,n.fn,i)}});return function(){a.forEach(function(i){i()})}}var is="Invariant failed";function Gr(e){this.message=e}Gr.prototype.toString=function(){return this.message};function S(e,r){if(!e)throw new Gr(is)}var os=function(e){tn(r,e);function r(){for(var a,n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return a=e.call.apply(e,[this].concat(i))||this,a.callbacks=null,a.unbind=Ie,a.onWindowError=function(s){var d=a.getCallbacks();d.isDragging()&&d.tryAbort();var g=s.error;g instanceof Gr&&s.preventDefault()},a.getCallbacks=function(){if(!a.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return a.callbacks},a.setCallbacks=function(s){a.callbacks=s},a}var t=r.prototype;return t.componentDidMount=function(){this.unbind=fe(window,[{eventName:"error",fn:this.onWindowError}])},t.componentDidCatch=function(n){if(n instanceof Gr){this.setState({});return}throw n},t.componentWillUnmount=function(){this.unbind()},t.render=function(){return this.props.children(this.setCallbacks)},r}(z.Component),ss=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,$r=function(r){return r+1},ls=function(r){return`
  You have lifted an item in position `+$r(r.source.index)+`
`},Pn=function(r,t){var a=r.droppableId===t.droppableId,n=$r(r.index),i=$r(t.index);return a?`
      You have moved the item from position `+n+`
      to position `+i+`
    `:`
    You have moved the item from position `+n+`
    in list `+r.droppableId+`
    to list `+t.droppableId+`
    in position `+i+`
  `},En=function(r,t,a){var n=t.droppableId===a.droppableId;return n?`
      The item `+r+`
      has been combined with `+a.draggableId:`
      The item `+r+`
      in list `+t.droppableId+`
      has been combined with `+a.draggableId+`
      in list `+a.droppableId+`
    `},ds=function(r){var t=r.destination;if(t)return Pn(r.source,t);var a=r.combine;return a?En(r.draggableId,r.source,a):"You are over an area that cannot be dropped on"},Aa=function(r){return`
  The item has returned to its starting position
  of `+$r(r.index)+`
`},cs=function(r){if(r.reason==="CANCEL")return`
      Movement cancelled.
      `+Aa(r.source)+`
    `;var t=r.destination,a=r.combine;return t?`
      You have dropped the item.
      `+Pn(r.source,t)+`
    `:a?`
      You have dropped the item.
      `+En(r.draggableId,r.source,a)+`
    `:`
    The item has been dropped while not over a drop area.
    `+Aa(r.source)+`
  `},Or={dragHandleUsageInstructions:ss,onDragStart:ls,onDragUpdate:ds,onDragEnd:cs},K={x:0,y:0},ee=function(r,t){return{x:r.x+t.x,y:r.y+t.y}},le=function(r,t){return{x:r.x-t.x,y:r.y-t.y}},Pe=function(r,t){return r.x===t.x&&r.y===t.y},er=function(r){return{x:r.x!==0?-r.x:0,y:r.y!==0?-r.y:0}},$e=function(r,t,a){var n;return a===void 0&&(a=0),n={},n[r]=t,n[r==="x"?"y":"x"]=a,n},ur=function(r,t){return Math.sqrt(Math.pow(t.x-r.x,2)+Math.pow(t.y-r.y,2))},ja=function(r,t){return Math.min.apply(Math,t.map(function(a){return ur(r,a)}))},kn=function(r){return function(t){return{x:r(t.x),y:r(t.y)}}},us=function(e,r){var t=me({top:Math.max(r.top,e.top),right:Math.min(r.right,e.right),bottom:Math.min(r.bottom,e.bottom),left:Math.max(r.left,e.left)});return t.width<=0||t.height<=0?null:t},Cr=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},Ta=function(r){return[{x:r.left,y:r.top},{x:r.right,y:r.top},{x:r.left,y:r.bottom},{x:r.right,y:r.bottom}]},gs={top:0,right:0,bottom:0,left:0},ps=function(r,t){return t?Cr(r,t.scroll.diff.displacement):r},fs=function(r,t,a){if(a&&a.increasedBy){var n;return R({},r,(n={},n[t.end]=r[t.end]+a.increasedBy[t.line],n))}return r},vs=function(r,t){return t&&t.shouldClipSubject?us(t.pageMarginBox,r):me(r)},Qe=function(e){var r=e.page,t=e.withPlaceholder,a=e.axis,n=e.frame,i=ps(r.marginBox,n),o=fs(i,a,t),s=vs(o,n);return{page:r,withPlaceholder:t,active:s}},Ht=function(e,r){e.frame||S(!1);var t=e.frame,a=le(r,t.scroll.initial),n=er(a),i=R({},t,{scroll:{initial:t.scroll.initial,current:r,diff:{value:a,displacement:n},max:t.scroll.max}}),o=Qe({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i}),s=R({},e,{frame:i,subject:o});return s};function Ur(e){return Object.values?Object.values(e):Object.keys(e).map(function(r){return e[r]})}function Vt(e,r){if(e.findIndex)return e.findIndex(r);for(var t=0;t<e.length;t++)if(r(e[t]))return t;return-1}function Ae(e,r){if(e.find)return e.find(r);var t=Vt(e,r);if(t!==-1)return e[t]}function An(e){return Array.prototype.slice.call(e)}var jn=J(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),Tn=J(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),nt=J(function(e){return Ur(e)}),ms=J(function(e){return Ur(e)}),rr=J(function(e,r){var t=ms(r).filter(function(a){return e===a.descriptor.droppableId}).sort(function(a,n){return a.descriptor.index-n.descriptor.index});return t});function zt(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function it(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var ot=J(function(e,r){return r.filter(function(t){return t.descriptor.id!==e.descriptor.id})}),bs=function(e){var r=e.isMovingForward,t=e.draggable,a=e.destination,n=e.insideDestination,i=e.previousImpact;if(!a.isCombineEnabled)return null;var o=zt(i);if(!o)return null;function s(h){var y={type:"COMBINE",combine:{draggableId:h,droppableId:a.descriptor.id}};return R({},i,{at:y})}var d=i.displaced.all,g=d.length?d[0]:null;if(r)return g?s(g):null;var p=ot(t,n);if(!g){if(!p.length)return null;var c=p[p.length-1];return s(c.descriptor.id)}var u=Vt(p,function(h){return h.descriptor.id===g});u===-1&&S(!1);var f=u-1;if(f<0)return null;var m=p[f];return s(m.descriptor.id)},tr=function(e,r){return e.descriptor.droppableId===r.descriptor.id},Rn={point:K,value:0},gr={invisible:{},visible:{},all:[]},hs={displaced:gr,displacedBy:Rn,at:null},ve=function(e,r){return function(t){return e<=t&&t<=r}},Bn=function(e){var r=ve(e.top,e.bottom),t=ve(e.left,e.right);return function(a){var n=r(a.top)&&r(a.bottom)&&t(a.left)&&t(a.right);if(n)return!0;var i=r(a.top)||r(a.bottom),o=t(a.left)||t(a.right),s=i&&o;if(s)return!0;var d=a.top<e.top&&a.bottom>e.bottom,g=a.left<e.left&&a.right>e.right,p=d&&g;if(p)return!0;var c=d&&o||g&&i;return c}},ys=function(e){var r=ve(e.top,e.bottom),t=ve(e.left,e.right);return function(a){var n=r(a.top)&&r(a.bottom)&&t(a.left)&&t(a.right);return n}},qt={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},On={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},xs=function(e){return function(r){var t=ve(r.top,r.bottom),a=ve(r.left,r.right);return function(n){return e===qt?t(n.top)&&t(n.bottom):a(n.left)&&a(n.right)}}},ws=function(r,t){var a=t.frame?t.frame.scroll.diff.displacement:K;return Cr(r,a)},Ds=function(r,t,a){return t.subject.active?a(t.subject.active)(r):!1},Ss=function(r,t,a){return a(t)(r)},Yt=function(r){var t=r.target,a=r.destination,n=r.viewport,i=r.withDroppableDisplacement,o=r.isVisibleThroughFrameFn,s=i?ws(t,a):t;return Ds(s,a,o)&&Ss(s,n,o)},Cs=function(r){return Yt(R({},r,{isVisibleThroughFrameFn:Bn}))},Mn=function(r){return Yt(R({},r,{isVisibleThroughFrameFn:ys}))},Ns=function(r){return Yt(R({},r,{isVisibleThroughFrameFn:xs(r.destination.axis)}))},Is=function(r,t,a){if(typeof a=="boolean")return a;if(!t)return!0;var n=t.invisible,i=t.visible;if(n[r])return!1;var o=i[r];return o?o.shouldAnimate:!0};function Ps(e,r){var t=e.page.marginBox,a={top:r.point.y,right:0,bottom:0,left:r.point.x};return me(Ut(t,a))}function pr(e){var r=e.afterDragging,t=e.destination,a=e.displacedBy,n=e.viewport,i=e.forceShouldAnimate,o=e.last;return r.reduce(function(d,g){var p=Ps(g,a),c=g.descriptor.id;d.all.push(c);var u=Cs({target:p,destination:t,viewport:n,withDroppableDisplacement:!0});if(!u)return d.invisible[g.descriptor.id]=!0,d;var f=Is(c,o,i),m={draggableId:c,shouldAnimate:f};return d.visible[c]=m,d},{all:[],visible:{},invisible:{}})}function Es(e,r){if(!e.length)return 0;var t=e[e.length-1].descriptor.index;return r.inHomeList?t:t+1}function Ra(e){var r=e.insideDestination,t=e.inHomeList,a=e.displacedBy,n=e.destination,i=Es(r,{inHomeList:t});return{displaced:gr,displacedBy:a,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:i}}}}function Wr(e){var r=e.draggable,t=e.insideDestination,a=e.destination,n=e.viewport,i=e.displacedBy,o=e.last,s=e.index,d=e.forceShouldAnimate,g=tr(r,a);if(s==null)return Ra({insideDestination:t,inHomeList:g,displacedBy:i,destination:a});var p=Ae(t,function(h){return h.descriptor.index===s});if(!p)return Ra({insideDestination:t,inHomeList:g,displacedBy:i,destination:a});var c=ot(r,t),u=t.indexOf(p),f=c.slice(u),m=pr({afterDragging:f,destination:a,displacedBy:i,last:o,viewport:n.frame,forceShouldAnimate:d});return{displaced:m,displacedBy:i,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:s}}}}function ke(e,r){return!!r.effected[e]}var ks=function(e){var r=e.isMovingForward,t=e.destination,a=e.draggables,n=e.combine,i=e.afterCritical;if(!t.isCombineEnabled)return null;var o=n.draggableId,s=a[o],d=s.descriptor.index,g=ke(o,i);return g?r?d:d-1:r?d+1:d},As=function(e){var r=e.isMovingForward,t=e.isInHomeList,a=e.insideDestination,n=e.location;if(!a.length)return null;var i=n.index,o=r?i+1:i-1,s=a[0].descriptor.index,d=a[a.length-1].descriptor.index,g=t?d:d+1;return o<s||o>g?null:o},js=function(e){var r=e.isMovingForward,t=e.isInHomeList,a=e.draggable,n=e.draggables,i=e.destination,o=e.insideDestination,s=e.previousImpact,d=e.viewport,g=e.afterCritical,p=s.at;if(p||S(!1),p.type==="REORDER"){var c=As({isMovingForward:r,isInHomeList:t,location:p.destination,insideDestination:o});return c==null?null:Wr({draggable:a,insideDestination:o,destination:i,viewport:d,last:s.displaced,displacedBy:s.displacedBy,index:c})}var u=ks({isMovingForward:r,destination:i,displaced:s.displaced,draggables:n,combine:p.combine,afterCritical:g});return u==null?null:Wr({draggable:a,insideDestination:o,destination:i,viewport:d,last:s.displaced,displacedBy:s.displacedBy,index:u})},Ts=function(e){var r=e.displaced,t=e.afterCritical,a=e.combineWith,n=e.displacedBy,i=!!(r.visible[a]||r.invisible[a]);return ke(a,t)?i?K:er(n.point):i?n.point:K},Rs=function(e){var r=e.afterCritical,t=e.impact,a=e.draggables,n=it(t);n||S(!1);var i=n.draggableId,o=a[i].page.borderBox.center,s=Ts({displaced:t.displaced,afterCritical:r,combineWith:i,displacedBy:t.displacedBy});return ee(o,s)},Ln=function(r,t){return t.margin[r.start]+t.borderBox[r.size]/2},Bs=function(r,t){return t.margin[r.end]+t.borderBox[r.size]/2},Jt=function(r,t,a){return t[r.crossAxisStart]+a.margin[r.crossAxisStart]+a.borderBox[r.crossAxisSize]/2},Ba=function(r){var t=r.axis,a=r.moveRelativeTo,n=r.isMoving;return $e(t.line,a.marginBox[t.end]+Ln(t,n),Jt(t,a.marginBox,n))},Oa=function(r){var t=r.axis,a=r.moveRelativeTo,n=r.isMoving;return $e(t.line,a.marginBox[t.start]-Bs(t,n),Jt(t,a.marginBox,n))},Os=function(r){var t=r.axis,a=r.moveInto,n=r.isMoving;return $e(t.line,a.contentBox[t.start]+Ln(t,n),Jt(t,a.contentBox,n))},Ms=function(e){var r=e.impact,t=e.draggable,a=e.draggables,n=e.droppable,i=e.afterCritical,o=rr(n.descriptor.id,a),s=t.page,d=n.axis;if(!o.length)return Os({axis:d,moveInto:n.page,isMoving:s});var g=r.displaced,p=r.displacedBy,c=g.all[0];if(c){var u=a[c];if(ke(c,i))return Oa({axis:d,moveRelativeTo:u.page,isMoving:s});var f=Lr(u.page,p.point);return Oa({axis:d,moveRelativeTo:f,isMoving:s})}var m=o[o.length-1];if(m.descriptor.id===t.descriptor.id)return s.borderBox.center;if(ke(m.descriptor.id,i)){var h=Lr(m.page,er(i.displacedBy.point));return Ba({axis:d,moveRelativeTo:h,isMoving:s})}return Ba({axis:d,moveRelativeTo:m.page,isMoving:s})},It=function(e,r){var t=e.frame;return t?ee(r,t.scroll.diff.displacement):r},Ls=function(r){var t=r.impact,a=r.draggable,n=r.droppable,i=r.draggables,o=r.afterCritical,s=a.page.borderBox.center,d=t.at;return!n||!d?s:d.type==="REORDER"?Ms({impact:t,draggable:a,draggables:i,droppable:n,afterCritical:o}):Rs({impact:t,draggables:i,afterCritical:o})},st=function(e){var r=Ls(e),t=e.droppable,a=t?It(t,r):r;return a},Fn=function(e,r){var t=le(r,e.scroll.initial),a=er(t),n=me({top:r.y,bottom:r.y+e.frame.height,left:r.x,right:r.x+e.frame.width}),i={frame:n,scroll:{initial:e.scroll.initial,max:e.scroll.max,current:r,diff:{value:t,displacement:a}}};return i};function Ma(e,r){return e.map(function(t){return r[t]})}function Fs(e,r){for(var t=0;t<r.length;t++){var a=r[t].visible[e];if(a)return a}return null}var Gs=function(e){var r=e.impact,t=e.viewport,a=e.destination,n=e.draggables,i=e.maxScrollChange,o=Fn(t,ee(t.scroll.current,i)),s=a.frame?Ht(a,ee(a.frame.scroll.current,i)):a,d=r.displaced,g=pr({afterDragging:Ma(d.all,n),destination:a,displacedBy:r.displacedBy,viewport:o.frame,last:d,forceShouldAnimate:!1}),p=pr({afterDragging:Ma(d.all,n),destination:s,displacedBy:r.displacedBy,viewport:t.frame,last:d,forceShouldAnimate:!1}),c={},u={},f=[d,g,p];d.all.forEach(function(h){var y=Fs(h,f);if(y){u[h]=y;return}c[h]=!0});var m=R({},r,{displaced:{all:d.all,invisible:c,visible:u}});return m},$s=function(e,r){return ee(e.scroll.diff.displacement,r)},Kt=function(e){var r=e.pageBorderBoxCenter,t=e.draggable,a=e.viewport,n=$s(a,r),i=le(n,t.page.borderBox.center);return ee(t.client.borderBox.center,i)},Gn=function(e){var r=e.draggable,t=e.destination,a=e.newPageBorderBoxCenter,n=e.viewport,i=e.withDroppableDisplacement,o=e.onlyOnMainAxis,s=o===void 0?!1:o,d=le(a,r.page.borderBox.center),g=Cr(r.page.borderBox,d),p={target:g,destination:t,withDroppableDisplacement:i,viewport:n};return s?Ns(p):Mn(p)},Us=function(e){var r=e.isMovingForward,t=e.draggable,a=e.destination,n=e.draggables,i=e.previousImpact,o=e.viewport,s=e.previousPageBorderBoxCenter,d=e.previousClientSelection,g=e.afterCritical;if(!a.isEnabled)return null;var p=rr(a.descriptor.id,n),c=tr(t,a),u=bs({isMovingForward:r,draggable:t,destination:a,insideDestination:p,previousImpact:i})||js({isMovingForward:r,isInHomeList:c,draggable:t,draggables:n,destination:a,insideDestination:p,previousImpact:i,viewport:o,afterCritical:g});if(!u)return null;var f=st({impact:u,draggable:t,droppable:a,draggables:n,afterCritical:g}),m=Gn({draggable:t,destination:a,newPageBorderBoxCenter:f,viewport:o.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});if(m){var h=Kt({pageBorderBoxCenter:f,draggable:t,viewport:o});return{clientSelection:h,impact:u,scrollJumpRequest:null}}var y=le(f,s),x=Gs({impact:u,viewport:o,destination:a,draggables:n,maxScrollChange:y});return{clientSelection:d,impact:x,scrollJumpRequest:y}},te=function(r){var t=r.subject.active;return t||S(!1),t},Ws=function(e){var r=e.isMovingForward,t=e.pageBorderBoxCenter,a=e.source,n=e.droppables,i=e.viewport,o=a.subject.active;if(!o)return null;var s=a.axis,d=ve(o[s.start],o[s.end]),g=nt(n).filter(function(c){return c!==a}).filter(function(c){return c.isEnabled}).filter(function(c){return!!c.subject.active}).filter(function(c){return Bn(i.frame)(te(c))}).filter(function(c){var u=te(c);return r?o[s.crossAxisEnd]<u[s.crossAxisEnd]:u[s.crossAxisStart]<o[s.crossAxisStart]}).filter(function(c){var u=te(c),f=ve(u[s.start],u[s.end]);return d(u[s.start])||d(u[s.end])||f(o[s.start])||f(o[s.end])}).sort(function(c,u){var f=te(c)[s.crossAxisStart],m=te(u)[s.crossAxisStart];return r?f-m:m-f}).filter(function(c,u,f){return te(c)[s.crossAxisStart]===te(f[0])[s.crossAxisStart]});if(!g.length)return null;if(g.length===1)return g[0];var p=g.filter(function(c){var u=ve(te(c)[s.start],te(c)[s.end]);return u(t[s.line])});return p.length===1?p[0]:p.length>1?p.sort(function(c,u){return te(c)[s.start]-te(u)[s.start]})[0]:g.sort(function(c,u){var f=ja(t,Ta(te(c))),m=ja(t,Ta(te(u)));return f!==m?f-m:te(c)[s.start]-te(u)[s.start]})[0]},La=function(r,t){var a=r.page.borderBox.center;return ke(r.descriptor.id,t)?le(a,t.displacedBy.point):a},Hs=function(r,t){var a=r.page.borderBox;return ke(r.descriptor.id,t)?Cr(a,er(t.displacedBy.point)):a},Vs=function(e){var r=e.pageBorderBoxCenter,t=e.viewport,a=e.destination,n=e.insideDestination,i=e.afterCritical,o=n.filter(function(s){return Mn({target:Hs(s,i),destination:a,viewport:t.frame,withDroppableDisplacement:!0})}).sort(function(s,d){var g=ur(r,It(a,La(s,i))),p=ur(r,It(a,La(d,i)));return g<p?-1:p<g?1:s.descriptor.index-d.descriptor.index});return o[0]||null},Nr=J(function(r,t){var a=t[r.line];return{value:a,point:$e(r.line,a)}}),zs=function(r,t,a){var n=r.axis;if(r.descriptor.mode==="virtual")return $e(n.line,t[n.line]);var i=r.subject.page.contentBox[n.size],o=rr(r.descriptor.id,a),s=o.reduce(function(p,c){return p+c.client.marginBox[n.size]},0),d=s+t[n.line],g=d-i;return g<=0?null:$e(n.line,g)},$n=function(r,t){return R({},r,{scroll:R({},r.scroll,{max:t})})},Un=function(r,t,a){var n=r.frame;tr(t,r)&&S(!1),r.subject.withPlaceholder&&S(!1);var i=Nr(r.axis,t.displaceBy).point,o=zs(r,i,a),s={placeholderSize:i,increasedBy:o,oldFrameMaxScroll:r.frame?r.frame.scroll.max:null};if(!n){var d=Qe({page:r.subject.page,withPlaceholder:s,axis:r.axis,frame:r.frame});return R({},r,{subject:d})}var g=o?ee(n.scroll.max,o):n.scroll.max,p=$n(n,g),c=Qe({page:r.subject.page,withPlaceholder:s,axis:r.axis,frame:p});return R({},r,{subject:c,frame:p})},qs=function(r){var t=r.subject.withPlaceholder;t||S(!1);var a=r.frame;if(!a){var n=Qe({page:r.subject.page,axis:r.axis,frame:null,withPlaceholder:null});return R({},r,{subject:n})}var i=t.oldFrameMaxScroll;i||S(!1);var o=$n(a,i),s=Qe({page:r.subject.page,axis:r.axis,frame:o,withPlaceholder:null});return R({},r,{subject:s,frame:o})},Ys=function(e){var r=e.previousPageBorderBoxCenter,t=e.moveRelativeTo,a=e.insideDestination,n=e.draggable,i=e.draggables,o=e.destination,s=e.viewport,d=e.afterCritical;if(!t){if(a.length)return null;var g={displaced:gr,displacedBy:Rn,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:0}}},p=st({impact:g,draggable:n,droppable:o,draggables:i,afterCritical:d}),c=tr(n,o)?o:Un(o,n,i),u=Gn({draggable:n,destination:c,newPageBorderBoxCenter:p,viewport:s.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});return u?g:null}var f=r[o.axis.line]<=t.page.borderBox.center[o.axis.line],m=function(){var y=t.descriptor.index;return t.descriptor.id===n.descriptor.id||f?y:y+1}(),h=Nr(o.axis,n.displaceBy);return Wr({draggable:n,insideDestination:a,destination:o,viewport:s,displacedBy:h,last:gr,index:m})},Js=function(e){var r=e.isMovingForward,t=e.previousPageBorderBoxCenter,a=e.draggable,n=e.isOver,i=e.draggables,o=e.droppables,s=e.viewport,d=e.afterCritical,g=Ws({isMovingForward:r,pageBorderBoxCenter:t,source:n,droppables:o,viewport:s});if(!g)return null;var p=rr(g.descriptor.id,i),c=Vs({pageBorderBoxCenter:t,viewport:s,destination:g,insideDestination:p,afterCritical:d}),u=Ys({previousPageBorderBoxCenter:t,destination:g,draggable:a,draggables:i,moveRelativeTo:c,insideDestination:p,viewport:s,afterCritical:d});if(!u)return null;var f=st({impact:u,draggable:a,droppable:g,draggables:i,afterCritical:d}),m=Kt({pageBorderBoxCenter:f,draggable:a,viewport:s});return{clientSelection:m,impact:u,scrollJumpRequest:null}},de=function(e){var r=e.at;return r?r.type==="REORDER"?r.destination.droppableId:r.combine.droppableId:null},Ks=function(r,t){var a=de(r);return a?t[a]:null},Xs=function(e){var r=e.state,t=e.type,a=Ks(r.impact,r.dimensions.droppables),n=!!a,i=r.dimensions.droppables[r.critical.droppable.id],o=a||i,s=o.axis.direction,d=s==="vertical"&&(t==="MOVE_UP"||t==="MOVE_DOWN")||s==="horizontal"&&(t==="MOVE_LEFT"||t==="MOVE_RIGHT");if(d&&!n)return null;var g=t==="MOVE_DOWN"||t==="MOVE_RIGHT",p=r.dimensions.draggables[r.critical.draggable.id],c=r.current.page.borderBoxCenter,u=r.dimensions,f=u.draggables,m=u.droppables;return d?Us({isMovingForward:g,previousPageBorderBoxCenter:c,draggable:p,destination:o,draggables:f,viewport:r.viewport,previousClientSelection:r.current.client.selection,previousImpact:r.impact,afterCritical:r.afterCritical}):Js({isMovingForward:g,previousPageBorderBoxCenter:c,draggable:p,isOver:o,draggables:f,droppables:m,viewport:r.viewport,afterCritical:r.afterCritical})};function Le(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function Wn(e){var r=ve(e.top,e.bottom),t=ve(e.left,e.right);return function(n){return r(n.y)&&t(n.x)}}function Qs(e,r){return e.left<r.right&&e.right>r.left&&e.top<r.bottom&&e.bottom>r.top}function Zs(e){var r=e.pageBorderBox,t=e.draggable,a=e.candidates,n=t.page.borderBox.center,i=a.map(function(o){var s=o.axis,d=$e(o.axis.line,r.center[s.line],o.page.borderBox.center[s.crossAxisLine]);return{id:o.descriptor.id,distance:ur(n,d)}}).sort(function(o,s){return s.distance-o.distance});return i[0]?i[0].id:null}function _s(e){var r=e.pageBorderBox,t=e.draggable,a=e.droppables,n=nt(a).filter(function(i){if(!i.isEnabled)return!1;var o=i.subject.active;if(!o||!Qs(r,o))return!1;if(Wn(o)(r.center))return!0;var s=i.axis,d=o.center[s.crossAxisLine],g=r[s.crossAxisStart],p=r[s.crossAxisEnd],c=ve(o[s.crossAxisStart],o[s.crossAxisEnd]),u=c(g),f=c(p);return!u&&!f?!0:u?g<d:p>d});return n.length?n.length===1?n[0].descriptor.id:Zs({pageBorderBox:r,draggable:t,candidates:n}):null}var Hn=function(r,t){return me(Cr(r,t))},el=function(e,r){var t=e.frame;return t?Hn(r,t.scroll.diff.value):r};function Vn(e){var r=e.displaced,t=e.id;return!!(r.visible[t]||r.invisible[t])}function rl(e){var r=e.draggable,t=e.closest,a=e.inHomeList;return t?a&&t.descriptor.index>r.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}var tl=function(e){var r=e.pageBorderBoxWithDroppableScroll,t=e.draggable,a=e.destination,n=e.insideDestination,i=e.last,o=e.viewport,s=e.afterCritical,d=a.axis,g=Nr(a.axis,t.displaceBy),p=g.value,c=r[d.start],u=r[d.end],f=ot(t,n),m=Ae(f,function(y){var x=y.descriptor.id,w=y.page.borderBox.center[d.line],N=ke(x,s),D=Vn({displaced:i,id:x});return N?D?u<=w:c<w-p:D?u<=w+p:c<w}),h=rl({draggable:t,closest:m,inHomeList:tr(t,a)});return Wr({draggable:t,insideDestination:n,destination:a,viewport:o,last:i,displacedBy:g,index:h})},al=4,nl=function(e){var r=e.draggable,t=e.pageBorderBoxWithDroppableScroll,a=e.previousImpact,n=e.destination,i=e.insideDestination,o=e.afterCritical;if(!n.isCombineEnabled)return null;var s=n.axis,d=Nr(n.axis,r.displaceBy),g=d.value,p=t[s.start],c=t[s.end],u=ot(r,i),f=Ae(u,function(h){var y=h.descriptor.id,x=h.page.borderBox,w=x[s.size],N=w/al,D=ke(y,o),j=Vn({displaced:a.displaced,id:y});return D?j?c>x[s.start]+N&&c<x[s.end]-N:p>x[s.start]-g+N&&p<x[s.end]-g-N:j?c>x[s.start]+g+N&&c<x[s.end]+g-N:p>x[s.start]+N&&p<x[s.end]-N});if(!f)return null;var m={displacedBy:d,displaced:a.displaced,at:{type:"COMBINE",combine:{draggableId:f.descriptor.id,droppableId:n.descriptor.id}}};return m},zn=function(e){var r=e.pageOffset,t=e.draggable,a=e.draggables,n=e.droppables,i=e.previousImpact,o=e.viewport,s=e.afterCritical,d=Hn(t.page.borderBox,r),g=_s({pageBorderBox:d,draggable:t,droppables:n});if(!g)return hs;var p=n[g],c=rr(p.descriptor.id,a),u=el(p,d);return nl({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:i,destination:p,insideDestination:c,afterCritical:s})||tl({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:p,insideDestination:c,last:i.displaced,viewport:o,afterCritical:s})},Xt=function(e,r){var t;return R({},e,(t={},t[r.descriptor.id]=r,t))},il=function(r){var t=r.previousImpact,a=r.impact,n=r.droppables,i=de(t),o=de(a);if(!i||i===o)return n;var s=n[i];if(!s.subject.withPlaceholder)return n;var d=qs(s);return Xt(n,d)},ol=function(e){var r=e.draggable,t=e.draggables,a=e.droppables,n=e.previousImpact,i=e.impact,o=il({previousImpact:n,impact:i,droppables:a}),s=de(i);if(!s)return o;var d=a[s];if(tr(r,d)||d.subject.withPlaceholder)return o;var g=Un(d,r,t);return Xt(o,g)},lr=function(e){var r=e.state,t=e.clientSelection,a=e.dimensions,n=e.viewport,i=e.impact,o=e.scrollJumpRequest,s=n||r.viewport,d=a||r.dimensions,g=t||r.current.client.selection,p=le(g,r.initial.client.selection),c={offset:p,selection:g,borderBoxCenter:ee(r.initial.client.borderBoxCenter,p)},u={selection:ee(c.selection,s.scroll.current),borderBoxCenter:ee(c.borderBoxCenter,s.scroll.current),offset:ee(c.offset,s.scroll.diff.value)},f={client:c,page:u};if(r.phase==="COLLECTING")return R({phase:"COLLECTING"},r,{dimensions:d,viewport:s,current:f});var m=d.draggables[r.critical.draggable.id],h=i||zn({pageOffset:u.offset,draggable:m,draggables:d.draggables,droppables:d.droppables,previousImpact:r.impact,viewport:s,afterCritical:r.afterCritical}),y=ol({draggable:m,impact:h,previousImpact:r.impact,draggables:d.draggables,droppables:d.droppables}),x=R({},r,{current:f,dimensions:{draggables:d.draggables,droppables:y},impact:h,viewport:s,scrollJumpRequest:o||null,forceShouldAnimate:o?!1:null});return x};function sl(e,r){return e.map(function(t){return r[t]})}var qn=function(e){var r=e.impact,t=e.viewport,a=e.draggables,n=e.destination,i=e.forceShouldAnimate,o=r.displaced,s=sl(o.all,a),d=pr({afterDragging:s,destination:n,displacedBy:r.displacedBy,viewport:t.frame,forceShouldAnimate:i,last:o});return R({},r,{displaced:d})},Yn=function(e){var r=e.impact,t=e.draggable,a=e.droppable,n=e.draggables,i=e.viewport,o=e.afterCritical,s=st({impact:r,draggable:t,draggables:n,droppable:a,afterCritical:o});return Kt({pageBorderBoxCenter:s,draggable:t,viewport:i})},Jn=function(e){var r=e.state,t=e.dimensions,a=e.viewport;r.movementMode!=="SNAP"&&S(!1);var n=r.impact,i=a||r.viewport,o=t||r.dimensions,s=o.draggables,d=o.droppables,g=s[r.critical.draggable.id],p=de(n);p||S(!1);var c=d[p],u=qn({impact:n,viewport:i,destination:c,draggables:s}),f=Yn({impact:u,draggable:g,droppable:c,draggables:s,viewport:i,afterCritical:r.afterCritical});return lr({impact:u,clientSelection:f,state:r,dimensions:o,viewport:i})},ll=function(e){return{index:e.index,droppableId:e.droppableId}},Kn=function(e){var r=e.draggable,t=e.home,a=e.draggables,n=e.viewport,i=Nr(t.axis,r.displaceBy),o=rr(t.descriptor.id,a),s=o.indexOf(r);s===-1&&S(!1);var d=o.slice(s+1),g=d.reduce(function(f,m){return f[m.descriptor.id]=!0,f},{}),p={inVirtualList:t.descriptor.mode==="virtual",displacedBy:i,effected:g},c=pr({afterDragging:d,destination:t,displacedBy:i,last:null,viewport:n.frame,forceShouldAnimate:!1}),u={displaced:c,displacedBy:i,at:{type:"REORDER",destination:ll(r.descriptor)}};return{impact:u,afterCritical:p}},dl=function(e,r){return{draggables:e.draggables,droppables:Xt(e.droppables,r)}},cl=function(e){var r=e.draggable,t=e.offset,a=e.initialWindowScroll,n=Lr(r.client,t),i=Fr(n,a),o=R({},r,{placeholder:R({},r.placeholder,{client:n}),client:n,page:i});return o},ul=function(e){var r=e.frame;return r||S(!1),r},gl=function(e){var r=e.additions,t=e.updatedDroppables,a=e.viewport,n=a.scroll.diff.value;return r.map(function(i){var o=i.descriptor.droppableId,s=t[o],d=ul(s),g=d.scroll.diff.value,p=ee(n,g),c=cl({draggable:i,offset:p,initialWindowScroll:a.scroll.initial});return c})},pl=function(e){var r=e.state,t=e.published,a=t.modified.map(function(N){var D=r.dimensions.droppables[N.droppableId],j=Ht(D,N.scroll);return j}),n=R({},r.dimensions.droppables,{},jn(a)),i=Tn(gl({additions:t.additions,updatedDroppables:n,viewport:r.viewport})),o=R({},r.dimensions.draggables,{},i);t.removals.forEach(function(N){delete o[N]});var s={droppables:n,draggables:o},d=de(r.impact),g=d?s.droppables[d]:null,p=s.draggables[r.critical.draggable.id],c=s.droppables[r.critical.droppable.id],u=Kn({draggable:p,home:c,draggables:o,viewport:r.viewport}),f=u.impact,m=u.afterCritical,h=g&&g.isCombineEnabled?r.impact:f,y=zn({pageOffset:r.current.page.offset,draggable:s.draggables[r.critical.draggable.id],draggables:s.draggables,droppables:s.droppables,previousImpact:h,viewport:r.viewport,afterCritical:m}),x=R({phase:"DRAGGING"},r,{phase:"DRAGGING",impact:y,onLiftImpact:f,dimensions:s,afterCritical:m,forceShouldAnimate:!1});if(r.phase==="COLLECTING")return x;var w=R({phase:"DROP_PENDING"},x,{phase:"DROP_PENDING",reason:r.reason,isWaiting:!1});return w},Pt=function(r){return r.movementMode==="SNAP"},ht=function(r,t,a){var n=dl(r.dimensions,t);return!Pt(r)||a?lr({state:r,dimensions:n}):Jn({state:r,dimensions:n})};function yt(e){return e.isDragging&&e.movementMode==="SNAP"?R({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var Fa={phase:"IDLE",completed:null,shouldFlush:!1},fl=function(e,r){if(e===void 0&&(e=Fa),r.type==="FLUSH")return R({},Fa,{shouldFlush:!0});if(r.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&S(!1);var t=r.payload,a=t.critical,n=t.clientSelection,i=t.viewport,o=t.dimensions,s=t.movementMode,d=o.draggables[a.draggable.id],g=o.droppables[a.droppable.id],p={selection:n,borderBoxCenter:d.client.borderBox.center,offset:K},c={client:p,page:{selection:ee(p.selection,i.scroll.initial),borderBoxCenter:ee(p.selection,i.scroll.initial),offset:ee(p.selection,i.scroll.diff.value)}},u=nt(o.droppables).every(function(Ve){return!Ve.isFixedOnPage}),f=Kn({draggable:d,home:g,draggables:o.draggables,viewport:i}),m=f.impact,h=f.afterCritical,y={phase:"DRAGGING",isDragging:!0,critical:a,movementMode:s,dimensions:o,initial:c,current:c,isWindowScrollAllowed:u,impact:m,afterCritical:h,onLiftImpact:m,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null};return y}if(r.type==="COLLECTION_STARTING"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&S(!1);var x=R({phase:"COLLECTING"},e,{phase:"COLLECTING"});return x}if(r.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||S(!1),pl({state:e,published:r.payload});if(r.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;Le(e)||S(!1);var w=r.payload.client;return Pe(w,e.current.client.selection)?e:lr({state:e,clientSelection:w,impact:Pt(e)?e.impact:null})}if(r.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return yt(e);Le(e)||S(!1);var N=r.payload,D=N.id,j=N.newScroll,k=e.dimensions.droppables[D];if(!k)return e;var L=Ht(k,j);return ht(e,L,!1)}if(r.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;Le(e)||S(!1);var F=r.payload,M=F.id,E=F.isEnabled,B=e.dimensions.droppables[M];B||S(!1),B.isEnabled===E&&S(!1);var Q=R({},B,{isEnabled:E});return ht(e,Q,!0)}if(r.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;Le(e)||S(!1);var Y=r.payload,T=Y.id,Z=Y.isCombineEnabled,q=e.dimensions.droppables[T];q||S(!1),q.isCombineEnabled===Z&&S(!1);var ae=R({},q,{isCombineEnabled:Z});return ht(e,ae,!0)}if(r.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;Le(e)||S(!1),e.isWindowScrollAllowed||S(!1);var oe=r.payload.newScroll;if(Pe(e.viewport.scroll.current,oe))return yt(e);var ye=Fn(e.viewport,oe);return Pt(e)?Jn({state:e,viewport:ye}):lr({state:e,viewport:ye})}if(r.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!Le(e))return e;var V=r.payload.maxScroll;if(Pe(V,e.viewport.scroll.max))return e;var ne=R({},e.viewport,{scroll:R({},e.viewport.scroll,{max:V})});return R({phase:"DRAGGING"},e,{viewport:ne})}if(r.type==="MOVE_UP"||r.type==="MOVE_DOWN"||r.type==="MOVE_LEFT"||r.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&S(!1);var re=Xs({state:e,type:r.type});return re?lr({state:e,impact:re.impact,clientSelection:re.clientSelection,scrollJumpRequest:re.scrollJumpRequest}):e}if(r.type==="DROP_PENDING"){var Se=r.payload.reason;e.phase!=="COLLECTING"&&S(!1);var Ue=R({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:Se});return Ue}if(r.type==="DROP_ANIMATE"){var ge=r.payload,je=ge.completed,We=ge.dropDuration,Te=ge.newHomeClientOffset;e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||S(!1);var He={phase:"DROP_ANIMATING",completed:je,dropDuration:We,newHomeClientOffset:Te,dimensions:e.dimensions};return He}if(r.type==="DROP_COMPLETE"){var xe=r.payload.completed;return{phase:"IDLE",completed:xe,shouldFlush:!1}}return e},vl=function(r){return{type:"BEFORE_INITIAL_CAPTURE",payload:r}},ml=function(r){return{type:"LIFT",payload:r}},bl=function(r){return{type:"INITIAL_PUBLISH",payload:r}},hl=function(r){return{type:"PUBLISH_WHILE_DRAGGING",payload:r}},yl=function(){return{type:"COLLECTION_STARTING",payload:null}},xl=function(r){return{type:"UPDATE_DROPPABLE_SCROLL",payload:r}},wl=function(r){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:r}},Dl=function(r){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:r}},Xn=function(r){return{type:"MOVE",payload:r}},Sl=function(r){return{type:"MOVE_BY_WINDOW_SCROLL",payload:r}},Cl=function(r){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:r}},Nl=function(){return{type:"MOVE_UP",payload:null}},Il=function(){return{type:"MOVE_DOWN",payload:null}},Pl=function(){return{type:"MOVE_RIGHT",payload:null}},El=function(){return{type:"MOVE_LEFT",payload:null}},Qt=function(){return{type:"FLUSH",payload:null}},kl=function(r){return{type:"DROP_ANIMATE",payload:r}},Zt=function(r){return{type:"DROP_COMPLETE",payload:r}},Qn=function(r){return{type:"DROP",payload:r}},Al=function(r){return{type:"DROP_PENDING",payload:r}},Zn=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},jl=function(e){return function(r){var t=r.getState,a=r.dispatch;return function(n){return function(i){if(i.type!=="LIFT"){n(i);return}var o=i.payload,s=o.id,d=o.clientSelection,g=o.movementMode,p=t();p.phase==="DROP_ANIMATING"&&a(Zt({completed:p.completed})),t().phase!=="IDLE"&&S(!1),a(Qt()),a(vl({draggableId:s,movementMode:g}));var c={shouldPublishImmediately:g==="SNAP"},u={draggableId:s,scrollOptions:c},f=e.startPublishing(u),m=f.critical,h=f.dimensions,y=f.viewport;a(bl({critical:m,dimensions:h,clientSelection:d,movementMode:g,viewport:y}))}}}},Tl=function(e){return function(){return function(r){return function(t){t.type==="INITIAL_PUBLISH"&&e.dragging(),t.type==="DROP_ANIMATE"&&e.dropping(t.payload.completed.result.reason),(t.type==="FLUSH"||t.type==="DROP_COMPLETE")&&e.resting(),r(t)}}}},_t={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},fr={opacity:{drop:0,combining:.7},scale:{drop:.75}},ea={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Me=ea.outOfTheWay+"s "+_t.outOfTheWay,dr={fluid:"opacity "+Me,snap:"transform "+Me+", opacity "+Me,drop:function(r){var t=r+"s "+_t.drop;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+Me,placeholder:"height "+Me+", width "+Me+", margin "+Me},Ga=function(r){return Pe(r,K)?null:"translate("+r.x+"px, "+r.y+"px)"},Et={moveTo:Ga,drop:function(r,t){var a=Ga(r);return a?t?a+" scale("+fr.scale.drop+")":a:null}},kt=ea.minDropTime,_n=ea.maxDropTime,Rl=_n-kt,$a=1500,Bl=.6,Ol=function(e){var r=e.current,t=e.destination,a=e.reason,n=ur(r,t);if(n<=0)return kt;if(n>=$a)return _n;var i=n/$a,o=kt+Rl*i,s=a==="CANCEL"?o*Bl:o;return Number(s.toFixed(2))},Ml=function(e){var r=e.impact,t=e.draggable,a=e.dimensions,n=e.viewport,i=e.afterCritical,o=a.draggables,s=a.droppables,d=de(r),g=d?s[d]:null,p=s[t.descriptor.droppableId],c=Yn({impact:r,draggable:t,draggables:o,afterCritical:i,droppable:g||p,viewport:n}),u=le(c,t.client.borderBox.center);return u},Ll=function(e){var r=e.draggables,t=e.reason,a=e.lastImpact,n=e.home,i=e.viewport,o=e.onLiftImpact;if(!a.at||t!=="DROP"){var s=qn({draggables:r,impact:o,destination:n,viewport:i,forceShouldAnimate:!0});return{impact:s,didDropInsideDroppable:!1}}if(a.at.type==="REORDER")return{impact:a,didDropInsideDroppable:!0};var d=R({},a,{displaced:gr});return{impact:d,didDropInsideDroppable:!0}},Fl=function(e){var r=e.getState,t=e.dispatch;return function(a){return function(n){if(n.type!=="DROP"){a(n);return}var i=r(),o=n.payload.reason;if(i.phase==="COLLECTING"){t(Al({reason:o}));return}if(i.phase!=="IDLE"){var s=i.phase==="DROP_PENDING"&&i.isWaiting;s&&S(!1),i.phase==="DRAGGING"||i.phase==="DROP_PENDING"||S(!1);var d=i.critical,g=i.dimensions,p=g.draggables[i.critical.draggable.id],c=Ll({reason:o,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),u=c.impact,f=c.didDropInsideDroppable,m=f?zt(u):null,h=f?it(u):null,y={index:d.draggable.index,droppableId:d.droppable.id},x={draggableId:p.descriptor.id,type:p.descriptor.type,source:y,reason:o,mode:i.movementMode,destination:m,combine:h},w=Ml({impact:u,draggable:p,dimensions:g,viewport:i.viewport,afterCritical:i.afterCritical}),N={critical:i.critical,afterCritical:i.afterCritical,result:x,impact:u},D=!Pe(i.current.client.offset,w)||!!x.combine;if(!D){t(Zt({completed:N}));return}var j=Ol({current:i.current.client.offset,destination:w,reason:o}),k={newHomeClientOffset:w,dropDuration:j,completed:N};t(kl(k))}}}},ei=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function Gl(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}function $l(e){var r=e.onWindowScroll;function t(){r(ei())}var a=cr(t),n=Gl(a),i=Ie;function o(){return i!==Ie}function s(){o()&&S(!1),i=fe(window,[n])}function d(){o()||S(!1),a.cancel(),i(),i=Ie}return{start:s,stop:d,isActive:o}}var Ul=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},Wl=function(e){var r=$l({onWindowScroll:function(a){e.dispatch(Sl({newScroll:a}))}});return function(t){return function(a){!r.isActive()&&a.type==="INITIAL_PUBLISH"&&r.start(),r.isActive()&&Ul(a)&&r.stop(),t(a)}}},Hl=function(e){var r=!1,t=!1,a=setTimeout(function(){t=!0}),n=function(o){r||t||(r=!0,e(o),clearTimeout(a))};return n.wasCalled=function(){return r},n},Vl=function(){var e=[],r=function(i){var o=Vt(e,function(g){return g.timerId===i});o===-1&&S(!1);var s=e.splice(o,1),d=s[0];d.callback()},t=function(i){var o=setTimeout(function(){return r(o)}),s={timerId:o,callback:i};e.push(s)},a=function(){if(e.length){var i=[].concat(e);e.length=0,i.forEach(function(o){clearTimeout(o.timerId),o.callback()})}};return{add:t,flush:a}},zl=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.droppableId===t.droppableId&&r.index===t.index},ql=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.draggableId===t.draggableId&&r.droppableId===t.droppableId},Yl=function(r,t){if(r===t)return!0;var a=r.draggable.id===t.draggable.id&&r.draggable.droppableId===t.draggable.droppableId&&r.draggable.type===t.draggable.type&&r.draggable.index===t.draggable.index,n=r.droppable.id===t.droppable.id&&r.droppable.type===t.droppable.type;return a&&n},ir=function(r,t){t()},Ar=function(r,t){return{draggableId:r.draggable.id,type:r.droppable.type,source:{droppableId:r.droppable.id,index:r.draggable.index},mode:t}},xt=function(r,t,a,n){if(!r){a(n(t));return}var i=Hl(a),o={announce:i};r(t,o),i.wasCalled()||a(n(t))},Jl=function(e,r){var t=Vl(),a=null,n=function(u,f){a&&S(!1),ir("onBeforeCapture",function(){var m=e().onBeforeCapture;if(m){var h={draggableId:u,mode:f};m(h)}})},i=function(u,f){a&&S(!1),ir("onBeforeDragStart",function(){var m=e().onBeforeDragStart;m&&m(Ar(u,f))})},o=function(u,f){a&&S(!1);var m=Ar(u,f);a={mode:f,lastCritical:u,lastLocation:m.source,lastCombine:null},t.add(function(){ir("onDragStart",function(){return xt(e().onDragStart,m,r,Or.onDragStart)})})},s=function(u,f){var m=zt(f),h=it(f);a||S(!1);var y=!Yl(u,a.lastCritical);y&&(a.lastCritical=u);var x=!zl(a.lastLocation,m);x&&(a.lastLocation=m);var w=!ql(a.lastCombine,h);if(w&&(a.lastCombine=h),!(!y&&!x&&!w)){var N=R({},Ar(u,a.mode),{combine:h,destination:m});t.add(function(){ir("onDragUpdate",function(){return xt(e().onDragUpdate,N,r,Or.onDragUpdate)})})}},d=function(){a||S(!1),t.flush()},g=function(u){a||S(!1),a=null,ir("onDragEnd",function(){return xt(e().onDragEnd,u,r,Or.onDragEnd)})},p=function(){if(a){var u=R({},Ar(a.lastCritical,a.mode),{combine:null,destination:null,reason:"CANCEL"});g(u)}};return{beforeCapture:n,beforeStart:i,start:o,update:s,flush:d,drop:g,abort:p}},Kl=function(e,r){var t=Jl(e,r);return function(a){return function(n){return function(i){if(i.type==="BEFORE_INITIAL_CAPTURE"){t.beforeCapture(i.payload.draggableId,i.payload.movementMode);return}if(i.type==="INITIAL_PUBLISH"){var o=i.payload.critical;t.beforeStart(o,i.payload.movementMode),n(i),t.start(o,i.payload.movementMode);return}if(i.type==="DROP_COMPLETE"){var s=i.payload.completed.result;t.flush(),n(i),t.drop(s);return}if(n(i),i.type==="FLUSH"){t.abort();return}var d=a.getState();d.phase==="DRAGGING"&&t.update(d.critical,d.impact)}}}},Xl=function(e){return function(r){return function(t){if(t.type!=="DROP_ANIMATION_FINISHED"){r(t);return}var a=e.getState();a.phase!=="DROP_ANIMATING"&&S(!1),e.dispatch(Zt({completed:a.completed}))}}},Ql=function(e){var r=null,t=null;function a(){t&&(cancelAnimationFrame(t),t=null),r&&(r(),r=null)}return function(n){return function(i){if((i.type==="FLUSH"||i.type==="DROP_COMPLETE"||i.type==="DROP_ANIMATION_FINISHED")&&a(),n(i),i.type==="DROP_ANIMATE"){var o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){var d=e.getState();d.phase==="DROP_ANIMATING"&&e.dispatch(Zn())}};t=requestAnimationFrame(function(){t=null,r=fe(window,[o])})}}}},Zl=function(e){return function(){return function(r){return function(t){(t.type==="DROP_COMPLETE"||t.type==="FLUSH"||t.type==="DROP_ANIMATE")&&e.stopPublishing(),r(t)}}}},_l=function(e){var r=!1;return function(){return function(t){return function(a){if(a.type==="INITIAL_PUBLISH"){r=!0,e.tryRecordFocus(a.payload.critical.draggable.id),t(a),e.tryRestoreFocusRecorded();return}if(t(a),!!r){if(a.type==="FLUSH"){r=!1,e.tryRestoreFocusRecorded();return}if(a.type==="DROP_COMPLETE"){r=!1;var n=a.payload.completed.result;n.combine&&e.tryShiftRecord(n.draggableId,n.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}},ed=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},rd=function(e){return function(r){return function(t){return function(a){if(ed(a)){e.stop(),t(a);return}if(a.type==="INITIAL_PUBLISH"){t(a);var n=r.getState();n.phase!=="DRAGGING"&&S(!1),e.start(n);return}t(a),e.scroll(r.getState())}}}},td=function(e){return function(r){return function(t){if(r(t),t.type==="PUBLISH_WHILE_DRAGGING"){var a=e.getState();a.phase==="DROP_PENDING"&&(a.isWaiting||e.dispatch(Qn({reason:a.reason})))}}}},ad=on,nd=function(e){var r=e.dimensionMarshal,t=e.focusMarshal,a=e.styleMarshal,n=e.getResponders,i=e.announce,o=e.autoScroller;return nn(fl,ad(Wi(Tl(a),Zl(r),jl(r),Fl,Xl,Ql,td,rd(o),Wl,_l(t),Kl(n,i))))},wt=function(){return{additions:{},removals:{},modified:{}}};function id(e){var r=e.registry,t=e.callbacks,a=wt(),n=null,i=function(){n||(t.collectionStarting(),n=requestAnimationFrame(function(){n=null;var p=a,c=p.additions,u=p.removals,f=p.modified,m=Object.keys(c).map(function(x){return r.draggable.getById(x).getDimension(K)}).sort(function(x,w){return x.descriptor.index-w.descriptor.index}),h=Object.keys(f).map(function(x){var w=r.droppable.getById(x),N=w.callbacks.getScrollWhileDragging();return{droppableId:x,scroll:N}}),y={additions:m,removals:Object.keys(u),modified:h};a=wt(),t.publish(y)}))},o=function(p){var c=p.descriptor.id;a.additions[c]=p,a.modified[p.descriptor.droppableId]=!0,a.removals[c]&&delete a.removals[c],i()},s=function(p){var c=p.descriptor;a.removals[c.id]=!0,a.modified[c.droppableId]=!0,a.additions[c.id]&&delete a.additions[c.id],i()},d=function(){n&&(cancelAnimationFrame(n),n=null,a=wt())};return{add:o,remove:s,stop:d}}var ri=function(e){var r=e.scrollHeight,t=e.scrollWidth,a=e.height,n=e.width,i=le({x:t,y:r},{x:n,y:a}),o={x:Math.max(0,i.x),y:Math.max(0,i.y)};return o},ti=function(){var e=document.documentElement;return e||S(!1),e},ai=function(){var e=ti(),r=ri({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight});return r},od=function(){var e=ei(),r=ai(),t=e.y,a=e.x,n=ti(),i=n.clientWidth,o=n.clientHeight,s=a+i,d=t+o,g=me({top:t,left:a,right:s,bottom:d}),p={frame:g,scroll:{initial:e,current:e,max:r,diff:{value:K,displacement:K}}};return p},sd=function(e){var r=e.critical,t=e.scrollOptions,a=e.registry,n=od(),i=n.scroll.current,o=r.droppable,s=a.droppable.getAllByType(o.type).map(function(c){return c.callbacks.getDimensionAndWatchScroll(i,t)}),d=a.draggable.getAllByType(r.draggable.type).map(function(c){return c.getDimension(i)}),g={draggables:Tn(d),droppables:jn(s)},p={dimensions:g,critical:r,viewport:n};return p};function Ua(e,r,t){if(t.descriptor.id===r.id||t.descriptor.type!==r.type)return!1;var a=e.droppable.getById(t.descriptor.droppableId);return a.descriptor.mode==="virtual"}var ld=function(e,r){var t=null,a=id({callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:e}),n=function(f,m){e.droppable.exists(f)||S(!1),t&&r.updateDroppableIsEnabled({id:f,isEnabled:m})},i=function(f,m){t&&(e.droppable.exists(f)||S(!1),r.updateDroppableIsCombineEnabled({id:f,isCombineEnabled:m}))},o=function(f,m){t&&(e.droppable.exists(f)||S(!1),r.updateDroppableScroll({id:f,newScroll:m}))},s=function(f,m){t&&e.droppable.getById(f).callbacks.scroll(m)},d=function(){if(t){a.stop();var f=t.critical.droppable;e.droppable.getAllByType(f.type).forEach(function(m){return m.callbacks.dragStopped()}),t.unsubscribe(),t=null}},g=function(f){t||S(!1);var m=t.critical.draggable;f.type==="ADDITION"&&Ua(e,m,f.value)&&a.add(f.value),f.type==="REMOVAL"&&Ua(e,m,f.value)&&a.remove(f.value)},p=function(f){t&&S(!1);var m=e.draggable.getById(f.draggableId),h=e.droppable.getById(m.descriptor.droppableId),y={draggable:m.descriptor,droppable:h.descriptor},x=e.subscribe(g);return t={critical:y,unsubscribe:x},sd({critical:y,registry:e,scrollOptions:f.scrollOptions})},c={updateDroppableIsEnabled:n,updateDroppableIsCombineEnabled:i,scrollDroppable:s,updateDroppableScroll:o,startPublishing:p,stopPublishing:d};return c},ni=function(e,r){return e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===r?!1:e.completed.result.reason==="DROP"},dd=function(e){window.scrollBy(e.x,e.y)},cd=J(function(e){return nt(e).filter(function(r){return!(!r.isEnabled||!r.frame)})}),ud=function(r,t){var a=Ae(cd(t),function(n){return n.frame||S(!1),Wn(n.frame.pageMarginBox)(r)});return a},gd=function(e){var r=e.center,t=e.destination,a=e.droppables;if(t){var n=a[t];return n.frame?n:null}var i=ud(r,a);return i},Ee={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:function(r){return Math.pow(r,2)},durationDampening:{stopDampeningAt:1200,accelerateAt:360}},pd=function(e,r){var t=e[r.size]*Ee.startFromPercentage,a=e[r.size]*Ee.maxScrollAtPercentage,n={startScrollingFrom:t,maxScrollValueAt:a};return n},ii=function(e){var r=e.startOfRange,t=e.endOfRange,a=e.current,n=t-r;if(n===0)return 0;var i=a-r,o=i/n;return o},ra=1,fd=function(e,r){if(e>r.startScrollingFrom)return 0;if(e<=r.maxScrollValueAt)return Ee.maxPixelScroll;if(e===r.startScrollingFrom)return ra;var t=ii({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:e}),a=1-t,n=Ee.maxPixelScroll*Ee.ease(a);return Math.ceil(n)},Wa=Ee.durationDampening.accelerateAt,Ha=Ee.durationDampening.stopDampeningAt,vd=function(e,r){var t=r,a=Ha,n=Date.now(),i=n-t;if(i>=Ha)return e;if(i<Wa)return ra;var o=ii({startOfRange:Wa,endOfRange:a,current:i}),s=e*Ee.ease(o);return Math.ceil(s)},Va=function(e){var r=e.distanceToEdge,t=e.thresholds,a=e.dragStartTime,n=e.shouldUseTimeDampening,i=fd(r,t);return i===0?0:n?Math.max(vd(i,a),ra):i},za=function(e){var r=e.container,t=e.distanceToEdges,a=e.dragStartTime,n=e.axis,i=e.shouldUseTimeDampening,o=pd(r,n),s=t[n.end]<t[n.start];return s?Va({distanceToEdge:t[n.end],thresholds:o,dragStartTime:a,shouldUseTimeDampening:i}):-1*Va({distanceToEdge:t[n.start],thresholds:o,dragStartTime:a,shouldUseTimeDampening:i})},md=function(e){var r=e.container,t=e.subject,a=e.proposedScroll,n=t.height>r.height,i=t.width>r.width;return!i&&!n?a:i&&n?null:{x:i?0:a.x,y:n?0:a.y}},bd=kn(function(e){return e===0?0:e}),oi=function(e){var r=e.dragStartTime,t=e.container,a=e.subject,n=e.center,i=e.shouldUseTimeDampening,o={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},s=za({container:t,distanceToEdges:o,dragStartTime:r,axis:qt,shouldUseTimeDampening:i}),d=za({container:t,distanceToEdges:o,dragStartTime:r,axis:On,shouldUseTimeDampening:i}),g=bd({x:d,y:s});if(Pe(g,K))return null;var p=md({container:t,subject:a,proposedScroll:g});return p?Pe(p,K)?null:p:null},hd=kn(function(e){return e===0?0:e>0?1:-1}),ta=function(){var e=function(t,a){return t<0?t:t>a?t-a:0};return function(r){var t=r.current,a=r.max,n=r.change,i=ee(t,n),o={x:e(i.x,a.x),y:e(i.y,a.y)};return Pe(o,K)?null:o}}(),si=function(r){var t=r.max,a=r.current,n=r.change,i={x:Math.max(a.x,t.x),y:Math.max(a.y,t.y)},o=hd(n),s=ta({max:i,current:a,change:o});return!s||o.x!==0&&s.x===0||o.y!==0&&s.y===0},aa=function(r,t){return si({current:r.scroll.current,max:r.scroll.max,change:t})},yd=function(r,t){if(!aa(r,t))return null;var a=r.scroll.max,n=r.scroll.current;return ta({current:n,max:a,change:t})},na=function(r,t){var a=r.frame;return a?si({current:a.scroll.current,max:a.scroll.max,change:t}):!1},xd=function(r,t){var a=r.frame;return!a||!na(r,t)?null:ta({current:a.scroll.current,max:a.scroll.max,change:t})},wd=function(e){var r=e.viewport,t=e.subject,a=e.center,n=e.dragStartTime,i=e.shouldUseTimeDampening,o=oi({dragStartTime:n,container:r.frame,subject:t,center:a,shouldUseTimeDampening:i});return o&&aa(r,o)?o:null},Dd=function(e){var r=e.droppable,t=e.subject,a=e.center,n=e.dragStartTime,i=e.shouldUseTimeDampening,o=r.frame;if(!o)return null;var s=oi({dragStartTime:n,container:o.pageMarginBox,subject:t,center:a,shouldUseTimeDampening:i});return s&&na(r,s)?s:null},qa=function(e){var r=e.state,t=e.dragStartTime,a=e.shouldUseTimeDampening,n=e.scrollWindow,i=e.scrollDroppable,o=r.current.page.borderBoxCenter,s=r.dimensions.draggables[r.critical.draggable.id],d=s.page.marginBox;if(r.isWindowScrollAllowed){var g=r.viewport,p=wd({dragStartTime:t,viewport:g,subject:d,center:o,shouldUseTimeDampening:a});if(p){n(p);return}}var c=gd({center:o,destination:de(r.impact),droppables:r.dimensions.droppables});if(c){var u=Dd({dragStartTime:t,droppable:c,subject:d,center:o,shouldUseTimeDampening:a});u&&i(c.descriptor.id,u)}},Sd=function(e){var r=e.scrollWindow,t=e.scrollDroppable,a=cr(r),n=cr(t),i=null,o=function(p){i||S(!1);var c=i,u=c.shouldUseTimeDampening,f=c.dragStartTime;qa({state:p,scrollWindow:a,scrollDroppable:n,dragStartTime:f,shouldUseTimeDampening:u})},s=function(p){i&&S(!1);var c=Date.now(),u=!1,f=function(){u=!0};qa({state:p,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:f,scrollDroppable:f}),i={dragStartTime:c,shouldUseTimeDampening:u},u&&o(p)},d=function(){i&&(a.cancel(),n.cancel(),i=null)};return{start:s,stop:d,scroll:o}},Cd=function(e){var r=e.move,t=e.scrollDroppable,a=e.scrollWindow,n=function(g,p){var c=ee(g.current.client.selection,p);r({client:c})},i=function(g,p){if(!na(g,p))return p;var c=xd(g,p);if(!c)return t(g.descriptor.id,p),null;var u=le(p,c);t(g.descriptor.id,u);var f=le(p,u);return f},o=function(g,p,c){if(!g||!aa(p,c))return c;var u=yd(p,c);if(!u)return a(c),null;var f=le(c,u);a(f);var m=le(c,f);return m},s=function(g){var p=g.scrollJumpRequest;if(p){var c=de(g.impact);c||S(!1);var u=i(g.dimensions.droppables[c],p);if(u){var f=g.viewport,m=o(g.isWindowScrollAllowed,f,u);m&&n(g,m)}}};return s},Nd=function(e){var r=e.scrollDroppable,t=e.scrollWindow,a=e.move,n=Sd({scrollWindow:t,scrollDroppable:r}),i=Cd({move:a,scrollWindow:t,scrollDroppable:r}),o=function(g){if(g.phase==="DRAGGING"){if(g.movementMode==="FLUID"){n.scroll(g);return}g.scrollJumpRequest&&i(g)}},s={scroll:o,start:n.start,stop:n.stop};return s},Ze="data-rbd",_e=function(){var e=Ze+"-drag-handle";return{base:e,draggableId:e+"-draggable-id",contextId:e+"-context-id"}}(),At=function(){var e=Ze+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Id=function(){var e=Ze+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Ya={contextId:Ze+"-scroll-container-context-id"},Pd=function(r){return function(t){return"["+t+'="'+r+'"]'}},or=function(r,t){return r.map(function(a){var n=a.styles[t];return n?a.selector+" { "+n+" }":""}).join(" ")},Ed="pointer-events: none;",kd=function(e){var r=Pd(e),t=function(){var s=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:r(_e.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:s,dragging:Ed,dropAnimating:s}}}(),a=function(){var s=`
      transition: `+dr.outOfTheWay+`;
    `;return{selector:r(At.contextId),styles:{dragging:s,dropAnimating:s,userCancel:s}}}(),n={selector:r(Id.contextId),styles:{always:"overflow-anchor: none;"}},i={selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}},o=[a,t,n,i];return{always:or(o,"always"),resting:or(o,"resting"),dragging:or(o,"dragging"),dropAnimating:or(o,"dropAnimating"),userCancel:or(o,"userCancel")}},ce=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?I.useLayoutEffect:I.useEffect,Dt=function(){var r=document.querySelector("head");return r||S(!1),r},Ja=function(r){var t=document.createElement("style");return r&&t.setAttribute("nonce",r),t.type="text/css",t};function Ad(e,r){var t=G(function(){return kd(e)},[e]),a=I.useRef(null),n=I.useRef(null),i=A(J(function(c){var u=n.current;u||S(!1),u.textContent=c}),[]),o=A(function(c){var u=a.current;u||S(!1),u.textContent=c},[]);ce(function(){!a.current&&!n.current||S(!1);var c=Ja(r),u=Ja(r);return a.current=c,n.current=u,c.setAttribute(Ze+"-always",e),u.setAttribute(Ze+"-dynamic",e),Dt().appendChild(c),Dt().appendChild(u),o(t.always),i(t.resting),function(){var f=function(h){var y=h.current;y||S(!1),Dt().removeChild(y),h.current=null};f(a),f(n)}},[r,o,i,t.always,t.resting,e]);var s=A(function(){return i(t.dragging)},[i,t.dragging]),d=A(function(c){if(c==="DROP"){i(t.dropAnimating);return}i(t.userCancel)},[i,t.dropAnimating,t.userCancel]),g=A(function(){n.current&&i(t.resting)},[i,t.resting]),p=G(function(){return{dragging:s,dropping:d,resting:g}},[s,d,g]);return p}var li=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function lt(e){return e instanceof li(e).HTMLElement}function jd(e,r){var t="["+_e.contextId+'="'+e+'"]',a=An(document.querySelectorAll(t));if(!a.length)return null;var n=Ae(a,function(i){return i.getAttribute(_e.draggableId)===r});return!n||!lt(n)?null:n}function Td(e){var r=I.useRef({}),t=I.useRef(null),a=I.useRef(null),n=I.useRef(!1),i=A(function(u,f){var m={id:u,focus:f};return r.current[u]=m,function(){var y=r.current,x=y[u];x!==m&&delete y[u]}},[]),o=A(function(u){var f=jd(e,u);f&&f!==document.activeElement&&f.focus()},[e]),s=A(function(u,f){t.current===u&&(t.current=f)},[]),d=A(function(){a.current||n.current&&(a.current=requestAnimationFrame(function(){a.current=null;var u=t.current;u&&o(u)}))},[o]),g=A(function(u){t.current=null;var f=document.activeElement;f&&f.getAttribute(_e.draggableId)===u&&(t.current=u)},[]);ce(function(){return n.current=!0,function(){n.current=!1;var u=a.current;u&&cancelAnimationFrame(u)}},[]);var p=G(function(){return{register:i,tryRecordFocus:g,tryRestoreFocusRecorded:d,tryShiftRecord:s}},[i,g,d,s]);return p}function Rd(){var e={draggables:{},droppables:{}},r=[];function t(c){return r.push(c),function(){var f=r.indexOf(c);f!==-1&&r.splice(f,1)}}function a(c){r.length&&r.forEach(function(u){return u(c)})}function n(c){return e.draggables[c]||null}function i(c){var u=n(c);return u||S(!1),u}var o={register:function(u){e.draggables[u.descriptor.id]=u,a({type:"ADDITION",value:u})},update:function(u,f){var m=e.draggables[f.descriptor.id];m&&m.uniqueId===u.uniqueId&&(delete e.draggables[f.descriptor.id],e.draggables[u.descriptor.id]=u)},unregister:function(u){var f=u.descriptor.id,m=n(f);m&&u.uniqueId===m.uniqueId&&(delete e.draggables[f],a({type:"REMOVAL",value:u}))},getById:i,findById:n,exists:function(u){return!!n(u)},getAllByType:function(u){return Ur(e.draggables).filter(function(f){return f.descriptor.type===u})}};function s(c){return e.droppables[c]||null}function d(c){var u=s(c);return u||S(!1),u}var g={register:function(u){e.droppables[u.descriptor.id]=u},unregister:function(u){var f=s(u.descriptor.id);f&&u.uniqueId===f.uniqueId&&delete e.droppables[u.descriptor.id]},getById:d,findById:s,exists:function(u){return!!s(u)},getAllByType:function(u){return Ur(e.droppables).filter(function(f){return f.descriptor.type===u})}};function p(){e.draggables={},e.droppables={},r.length=0}return{draggable:o,droppable:g,subscribe:t,clean:p}}function Bd(){var e=G(Rd,[]);return I.useEffect(function(){return function(){requestAnimationFrame(e.clean)}},[e]),e}var ia=z.createContext(null),Hr=function(){var e=document.body;return e||S(!1),e},Od={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},Md=function(r){return"rbd-announcement-"+r};function Ld(e){var r=G(function(){return Md(e)},[e]),t=I.useRef(null);I.useEffect(function(){var i=document.createElement("div");return t.current=i,i.id=r,i.setAttribute("aria-live","assertive"),i.setAttribute("aria-atomic","true"),R(i.style,Od),Hr().appendChild(i),function(){setTimeout(function(){var d=Hr();d.contains(i)&&d.removeChild(i),i===t.current&&(t.current=null)})}},[r]);var a=A(function(n){var i=t.current;if(i){i.textContent=n;return}},[]);return a}var Fd=0,Gd={separator:"::"};function oa(e,r){return r===void 0&&(r=Gd),G(function(){return""+e+r.separator+Fd++},[r.separator,e])}function $d(e){var r=e.contextId,t=e.uniqueId;return"rbd-hidden-text-"+r+"-"+t}function Ud(e){var r=e.contextId,t=e.text,a=oa("hidden-text",{separator:"-"}),n=G(function(){return $d({contextId:r,uniqueId:a})},[a,r]);return I.useEffect(function(){var o=document.createElement("div");return o.id=n,o.textContent=t,o.style.display="none",Hr().appendChild(o),function(){var d=Hr();d.contains(o)&&d.removeChild(o)}},[n,t]),n}var dt=z.createContext(null);function di(e){var r=I.useRef(e);return I.useEffect(function(){r.current=e}),r}function Wd(){var e=null;function r(){return!!e}function t(o){return o===e}function a(o){e&&S(!1);var s={abandon:o};return e=s,s}function n(){e||S(!1),e=null}function i(){e&&(e.abandon(),n())}return{isClaimed:r,isActive:t,claim:a,release:n,tryAbandon:i}}var Hd=9,Vd=13,sa=27,ci=32,zd=33,qd=34,Yd=35,Jd=36,Kd=37,Xd=38,Qd=39,Zd=40,jr,_d=(jr={},jr[Vd]=!0,jr[Hd]=!0,jr),ui=function(e){_d[e.keyCode]&&e.preventDefault()},ct=function(){var e="visibilitychange";if(typeof document>"u")return e;var r=[e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],t=Ae(r,function(a){return"on"+a in document});return t||e}(),gi=0,Ka=5;function ec(e,r){return Math.abs(r.x-e.x)>=Ka||Math.abs(r.y-e.y)>=Ka}var Xa={type:"IDLE"};function rc(e){var r=e.cancel,t=e.completed,a=e.getPhase,n=e.setPhase;return[{eventName:"mousemove",fn:function(o){var s=o.button,d=o.clientX,g=o.clientY;if(s===gi){var p={x:d,y:g},c=a();if(c.type==="DRAGGING"){o.preventDefault(),c.actions.move(p);return}c.type!=="PENDING"&&S(!1);var u=c.point;if(ec(u,p)){o.preventDefault();var f=c.actions.fluidLift(p);n({type:"DRAGGING",actions:f})}}}},{eventName:"mouseup",fn:function(o){var s=a();if(s.type!=="DRAGGING"){r();return}o.preventDefault(),s.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:function(o){a().type==="DRAGGING"&&o.preventDefault(),r()}},{eventName:"keydown",fn:function(o){var s=a();if(s.type==="PENDING"){r();return}if(o.keyCode===sa){o.preventDefault(),r();return}ui(o)}},{eventName:"resize",fn:r},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){a().type==="PENDING"&&r()}},{eventName:"webkitmouseforcedown",fn:function(o){var s=a();if(s.type==="IDLE"&&S(!1),s.actions.shouldRespectForcePress()){r();return}o.preventDefault()}},{eventName:ct,fn:r}]}function tc(e){var r=I.useRef(Xa),t=I.useRef(Ie),a=G(function(){return{eventName:"mousedown",fn:function(c){if(!c.defaultPrevented&&c.button===gi&&!(c.ctrlKey||c.metaKey||c.shiftKey||c.altKey)){var u=e.findClosestDraggableId(c);if(u){var f=e.tryGetLock(u,o,{sourceEvent:c});if(f){c.preventDefault();var m={x:c.clientX,y:c.clientY};t.current(),g(f,m)}}}}}},[e]),n=G(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(c){if(!c.defaultPrevented){var u=e.findClosestDraggableId(c);if(u){var f=e.findOptionsForDraggable(u);f&&(f.shouldRespectForcePress||e.canGetLock(u)&&c.preventDefault())}}}}},[e]),i=A(function(){var c={passive:!1,capture:!0};t.current=fe(window,[n,a],c)},[n,a]),o=A(function(){var p=r.current;p.type!=="IDLE"&&(r.current=Xa,t.current(),i())},[i]),s=A(function(){var p=r.current;o(),p.type==="DRAGGING"&&p.actions.cancel({shouldBlockNextClick:!0}),p.type==="PENDING"&&p.actions.abort()},[o]),d=A(function(){var c={capture:!0,passive:!1},u=rc({cancel:s,completed:o,getPhase:function(){return r.current},setPhase:function(m){r.current=m}});t.current=fe(window,u,c)},[s,o]),g=A(function(c,u){r.current.type!=="IDLE"&&S(!1),r.current={type:"PENDING",point:u,actions:c},d()},[d]);ce(function(){return i(),function(){t.current()}},[i])}var Ke;function ac(){}var nc=(Ke={},Ke[qd]=!0,Ke[zd]=!0,Ke[Jd]=!0,Ke[Yd]=!0,Ke);function ic(e,r){function t(){r(),e.cancel()}function a(){r(),e.drop()}return[{eventName:"keydown",fn:function(i){if(i.keyCode===sa){i.preventDefault(),t();return}if(i.keyCode===ci){i.preventDefault(),a();return}if(i.keyCode===Zd){i.preventDefault(),e.moveDown();return}if(i.keyCode===Xd){i.preventDefault(),e.moveUp();return}if(i.keyCode===Qd){i.preventDefault(),e.moveRight();return}if(i.keyCode===Kd){i.preventDefault(),e.moveLeft();return}if(nc[i.keyCode]){i.preventDefault();return}ui(i)}},{eventName:"mousedown",fn:t},{eventName:"mouseup",fn:t},{eventName:"click",fn:t},{eventName:"touchstart",fn:t},{eventName:"resize",fn:t},{eventName:"wheel",fn:t,options:{passive:!0}},{eventName:ct,fn:t}]}function oc(e){var r=I.useRef(ac),t=G(function(){return{eventName:"keydown",fn:function(i){if(i.defaultPrevented||i.keyCode!==ci)return;var o=e.findClosestDraggableId(i);if(!o)return;var s=e.tryGetLock(o,p,{sourceEvent:i});if(!s)return;i.preventDefault();var d=!0,g=s.snapLift();r.current();function p(){d||S(!1),d=!1,r.current(),a()}r.current=fe(window,ic(g,p),{capture:!0,passive:!1})}}},[e]),a=A(function(){var i={passive:!1,capture:!0};r.current=fe(window,[t],i)},[t]);ce(function(){return a(),function(){r.current()}},[a])}var St={type:"IDLE"},sc=120,lc=.15;function dc(e){var r=e.cancel,t=e.getPhase;return[{eventName:"orientationchange",fn:r},{eventName:"resize",fn:r},{eventName:"contextmenu",fn:function(n){n.preventDefault()}},{eventName:"keydown",fn:function(n){if(t().type!=="DRAGGING"){r();return}n.keyCode===sa&&n.preventDefault(),r()}},{eventName:ct,fn:r}]}function cc(e){var r=e.cancel,t=e.completed,a=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(i){var o=a();if(o.type!=="DRAGGING"){r();return}o.hasMoved=!0;var s=i.touches[0],d=s.clientX,g=s.clientY,p={x:d,y:g};i.preventDefault(),o.actions.move(p)}},{eventName:"touchend",fn:function(i){var o=a();if(o.type!=="DRAGGING"){r();return}i.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:function(i){if(a().type!=="DRAGGING"){r();return}i.preventDefault(),r()}},{eventName:"touchforcechange",fn:function(i){var o=a();o.type==="IDLE"&&S(!1);var s=i.touches[0];if(s){var d=s.force>=lc;if(d){var g=o.actions.shouldRespectForcePress();if(o.type==="PENDING"){g&&r();return}if(g){if(o.hasMoved){i.preventDefault();return}r();return}i.preventDefault()}}}},{eventName:ct,fn:r}]}function uc(e){var r=I.useRef(St),t=I.useRef(Ie),a=A(function(){return r.current},[]),n=A(function(f){r.current=f},[]),i=G(function(){return{eventName:"touchstart",fn:function(f){if(!f.defaultPrevented){var m=e.findClosestDraggableId(f);if(m){var h=e.tryGetLock(m,s,{sourceEvent:f});if(h){var y=f.touches[0],x=y.clientX,w=y.clientY,N={x,y:w};t.current(),c(h,N)}}}}}},[e]),o=A(function(){var f={capture:!0,passive:!1};t.current=fe(window,[i],f)},[i]),s=A(function(){var u=r.current;u.type!=="IDLE"&&(u.type==="PENDING"&&clearTimeout(u.longPressTimerId),n(St),t.current(),o())},[o,n]),d=A(function(){var u=r.current;s(),u.type==="DRAGGING"&&u.actions.cancel({shouldBlockNextClick:!0}),u.type==="PENDING"&&u.actions.abort()},[s]),g=A(function(){var f={capture:!0,passive:!1},m={cancel:d,completed:s,getPhase:a},h=fe(window,cc(m),f),y=fe(window,dc(m),f);t.current=function(){h(),y()}},[d,a,s]),p=A(function(){var f=a();f.type!=="PENDING"&&S(!1);var m=f.actions.fluidLift(f.point);n({type:"DRAGGING",actions:m,hasMoved:!1})},[a,n]),c=A(function(f,m){a().type!=="IDLE"&&S(!1);var h=setTimeout(p,sc);n({type:"PENDING",point:m,actions:f,longPressTimerId:h}),g()},[g,a,n,p]);ce(function(){return o(),function(){t.current();var m=a();m.type==="PENDING"&&(clearTimeout(m.longPressTimerId),n(St))}},[a,o,n]),ce(function(){var f=fe(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}]);return f},[])}var gc={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function pi(e,r){if(r==null)return!1;var t=!!gc[r.tagName.toLowerCase()];if(t)return!0;var a=r.getAttribute("contenteditable");return a==="true"||a===""?!0:r===e?!1:pi(e,r.parentElement)}function pc(e,r){var t=r.target;return lt(t)?pi(e,t):!1}var fc=function(e){return me(e.getBoundingClientRect()).center};function vc(e){return e instanceof li(e).Element}var mc=function(){var e="matches";if(typeof document>"u")return e;var r=[e,"msMatchesSelector","webkitMatchesSelector"],t=Ae(r,function(a){return a in Element.prototype});return t||e}();function fi(e,r){return e==null?null:e[mc](r)?e:fi(e.parentElement,r)}function bc(e,r){return e.closest?e.closest(r):fi(e,r)}function hc(e){return"["+_e.contextId+'="'+e+'"]'}function yc(e,r){var t=r.target;if(!vc(t))return null;var a=hc(e),n=bc(t,a);return!n||!lt(n)?null:n}function xc(e,r){var t=yc(e,r);return t?t.getAttribute(_e.draggableId):null}function wc(e,r){var t="["+At.contextId+'="'+e+'"]',a=An(document.querySelectorAll(t)),n=Ae(a,function(i){return i.getAttribute(At.id)===r});return!n||!lt(n)?null:n}function Dc(e){e.preventDefault()}function Tr(e){var r=e.expected,t=e.phase,a=e.isLockActive;return e.shouldWarn,!(!a()||r!==t)}function vi(e){var r=e.lockAPI,t=e.store,a=e.registry,n=e.draggableId;if(r.isClaimed())return!1;var i=a.draggable.findById(n);return!(!i||!i.options.isEnabled||!ni(t.getState(),n))}function Sc(e){var r=e.lockAPI,t=e.contextId,a=e.store,n=e.registry,i=e.draggableId,o=e.forceSensorStop,s=e.sourceEvent,d=vi({lockAPI:r,store:a,registry:n,draggableId:i});if(!d)return null;var g=n.draggable.getById(i),p=wc(t,g.descriptor.id);if(!p||s&&!g.options.canDragInteractiveElements&&pc(p,s))return null;var c=r.claim(o||Ie),u="PRE_DRAG";function f(){return g.options.shouldRespectForcePress}function m(){return r.isActive(c)}function h(k,L){Tr({expected:k,phase:u,isLockActive:m,shouldWarn:!0})&&a.dispatch(L())}var y=h.bind(null,"DRAGGING");function x(k){function L(){r.release(),u="COMPLETED"}u!=="PRE_DRAG"&&(L(),u!=="PRE_DRAG"&&S(!1)),a.dispatch(ml(k.liftActionArgs)),u="DRAGGING";function F(M,E){if(E===void 0&&(E={shouldBlockNextClick:!1}),k.cleanup(),E.shouldBlockNextClick){var B=fe(window,[{eventName:"click",fn:Dc,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(B)}L(),a.dispatch(Qn({reason:M}))}return R({isActive:function(){return Tr({expected:"DRAGGING",phase:u,isLockActive:m,shouldWarn:!1})},shouldRespectForcePress:f,drop:function(E){return F("DROP",E)},cancel:function(E){return F("CANCEL",E)}},k.actions)}function w(k){var L=cr(function(M){y(function(){return Xn({client:M})})}),F=x({liftActionArgs:{id:i,clientSelection:k,movementMode:"FLUID"},cleanup:function(){return L.cancel()},actions:{move:L}});return R({},F,{move:L})}function N(){var k={moveUp:function(){return y(Nl)},moveRight:function(){return y(Pl)},moveDown:function(){return y(Il)},moveLeft:function(){return y(El)}};return x({liftActionArgs:{id:i,clientSelection:fc(p),movementMode:"SNAP"},cleanup:Ie,actions:k})}function D(){var k=Tr({expected:"PRE_DRAG",phase:u,isLockActive:m,shouldWarn:!0});k&&r.release()}var j={isActive:function(){return Tr({expected:"PRE_DRAG",phase:u,isLockActive:m,shouldWarn:!1})},shouldRespectForcePress:f,fluidLift:w,snapLift:N,abort:D};return j}var Cc=[tc,oc,uc];function Nc(e){var r=e.contextId,t=e.store,a=e.registry,n=e.customSensors,i=e.enableDefaultSensors,o=[].concat(i?Cc:[],n||[]),s=I.useState(function(){return Wd()})[0],d=A(function(w,N){w.isDragging&&!N.isDragging&&s.tryAbandon()},[s]);ce(function(){var w=t.getState(),N=t.subscribe(function(){var D=t.getState();d(w,D),w=D});return N},[s,t,d]),ce(function(){return s.tryAbandon},[s.tryAbandon]);for(var g=A(function(x){return vi({lockAPI:s,registry:a,store:t,draggableId:x})},[s,a,t]),p=A(function(x,w,N){return Sc({lockAPI:s,registry:a,contextId:r,store:t,draggableId:x,forceSensorStop:w,sourceEvent:N&&N.sourceEvent?N.sourceEvent:null})},[r,s,a,t]),c=A(function(x){return xc(r,x)},[r]),u=A(function(x){var w=a.draggable.findById(x);return w?w.options:null},[a.draggable]),f=A(function(){s.isClaimed()&&(s.tryAbandon(),t.getState().phase!=="IDLE"&&t.dispatch(Qt()))},[s,t]),m=A(s.isClaimed,[s]),h=G(function(){return{canGetLock:g,tryGetLock:p,findClosestDraggableId:c,findOptionsForDraggable:u,tryReleaseLock:f,isLockClaimed:m}},[g,p,c,u,f,m]),y=0;y<o.length;y++)o[y](h)}var Ic=function(r){return{onBeforeCapture:r.onBeforeCapture,onBeforeDragStart:r.onBeforeDragStart,onDragStart:r.onDragStart,onDragEnd:r.onDragEnd,onDragUpdate:r.onDragUpdate}};function sr(e){return e.current||S(!1),e.current}function Pc(e){var r=e.contextId,t=e.setCallbacks,a=e.sensors,n=e.nonce,i=e.dragHandleUsageInstructions,o=I.useRef(null),s=di(e),d=A(function(){return Ic(s.current)},[s]),g=Ld(r),p=Ud({contextId:r,text:i}),c=Ad(r,n),u=A(function(M){sr(o).dispatch(M)},[]),f=G(function(){return ya({publishWhileDragging:hl,updateDroppableScroll:xl,updateDroppableIsEnabled:wl,updateDroppableIsCombineEnabled:Dl,collectionStarting:yl},u)},[u]),m=Bd(),h=G(function(){return ld(m,f)},[m,f]),y=G(function(){return Nd(R({scrollWindow:dd,scrollDroppable:h.scrollDroppable},ya({move:Xn},u)))},[h.scrollDroppable,u]),x=Td(r),w=G(function(){return nd({announce:g,autoScroller:y,dimensionMarshal:h,focusMarshal:x,getResponders:d,styleMarshal:c})},[g,y,h,x,d,c]);o.current=w;var N=A(function(){var M=sr(o),E=M.getState();E.phase!=="IDLE"&&M.dispatch(Qt())},[]),D=A(function(){var M=sr(o).getState();return M.isDragging||M.phase==="DROP_ANIMATING"},[]),j=G(function(){return{isDragging:D,tryAbort:N}},[D,N]);t(j);var k=A(function(M){return ni(sr(o).getState(),M)},[]),L=A(function(){return Le(sr(o).getState())},[]),F=G(function(){return{marshal:h,focus:x,contextId:r,canLift:k,isMovementAllowed:L,dragHandleUsageInstructionsId:p,registry:m}},[r,h,p,x,k,L,m]);return Nc({contextId:r,store:w,registry:m,customSensors:a,enableDefaultSensors:e.enableDefaultSensors!==!1}),I.useEffect(function(){return N},[N]),z.createElement(dt.Provider,{value:F},z.createElement(Yi,{context:ia,store:w},e.children))}var Ec=0;function kc(){return G(function(){return""+Ec++},[])}function Ac(e){var r=kc(),t=e.dragHandleUsageInstructions||Or.dragHandleUsageInstructions;return z.createElement(os,null,function(a){return z.createElement(Pc,{nonce:e.nonce,contextId:r,setCallbacks:a,dragHandleUsageInstructions:t,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)})}var mi=function(r){return function(t){return r===t}},jc=mi("scroll"),Tc=mi("auto"),Qa=function(r,t){return t(r.overflowX)||t(r.overflowY)},Rc=function(r){var t=window.getComputedStyle(r),a={overflowX:t.overflowX,overflowY:t.overflowY};return Qa(a,jc)||Qa(a,Tc)},Bc=function(){return!1},Oc=function e(r){return r==null?null:r===document.body?Bc()?r:null:r===document.documentElement?null:Rc(r)?r:e(r.parentElement)},jt=function(e){return{x:e.scrollLeft,y:e.scrollTop}},Mc=function e(r){if(!r)return!1;var t=window.getComputedStyle(r);return t.position==="fixed"?!0:e(r.parentElement)},Lc=function(e){var r=Oc(e),t=Mc(e);return{closestScrollable:r,isFixedOnPage:t}},Fc=function(e){var r=e.descriptor,t=e.isEnabled,a=e.isCombineEnabled,n=e.isFixedOnPage,i=e.direction,o=e.client,s=e.page,d=e.closest,g=function(){if(!d)return null;var f=d.scrollSize,m=d.client,h=ri({scrollHeight:f.scrollHeight,scrollWidth:f.scrollWidth,height:m.paddingBox.height,width:m.paddingBox.width});return{pageMarginBox:d.page.marginBox,frameClient:m,scrollSize:f,shouldClipSubject:d.shouldClipSubject,scroll:{initial:d.scroll,current:d.scroll,max:h,diff:{value:K,displacement:K}}}}(),p=i==="vertical"?qt:On,c=Qe({page:s,withPlaceholder:null,axis:p,frame:g}),u={descriptor:r,isCombineEnabled:a,isFixedOnPage:n,axis:p,isEnabled:t,client:o,page:s,frame:g,subject:c};return u},Gc=function(r,t){var a=Nn(r);if(!t||r!==t)return a;var n=a.paddingBox.top-t.scrollTop,i=a.paddingBox.left-t.scrollLeft,o=n+t.scrollHeight,s=i+t.scrollWidth,d={top:n,right:s,bottom:o,left:i},g=Ut(d,a.border),p=Wt({borderBox:g,margin:a.margin,border:a.border,padding:a.padding});return p},$c=function(e){var r=e.ref,t=e.descriptor,a=e.env,n=e.windowScroll,i=e.direction,o=e.isDropDisabled,s=e.isCombineEnabled,d=e.shouldClipSubject,g=a.closestScrollable,p=Gc(r,g),c=Fr(p,n),u=function(){if(!g)return null;var m=Nn(g),h={scrollHeight:g.scrollHeight,scrollWidth:g.scrollWidth};return{client:m,page:Fr(m,n),scroll:jt(g),scrollSize:h,shouldClipSubject:d}}(),f=Fc({descriptor:t,isEnabled:!o,isCombineEnabled:s,isFixedOnPage:a.isFixedOnPage,direction:i,client:p,page:c,closest:u});return f},Uc={passive:!1},Wc={passive:!0},Za=function(e){return e.shouldPublishImmediately?Uc:Wc};function Vr(e){var r=I.useContext(e);return r||S(!1),r}var Rr=function(r){return r&&r.env.closestScrollable||null};function Hc(e){var r=I.useRef(null),t=Vr(dt),a=oa("droppable"),n=t.registry,i=t.marshal,o=di(e),s=G(function(){return{id:e.droppableId,type:e.type,mode:e.mode}},[e.droppableId,e.mode,e.type]),d=I.useRef(s),g=G(function(){return J(function(D,j){r.current||S(!1);var k={x:D,y:j};i.updateDroppableScroll(s.id,k)})},[s.id,i]),p=A(function(){var D=r.current;return!D||!D.env.closestScrollable?K:jt(D.env.closestScrollable)},[]),c=A(function(){var D=p();g(D.x,D.y)},[p,g]),u=G(function(){return cr(c)},[c]),f=A(function(){var D=r.current,j=Rr(D);D&&j||S(!1);var k=D.scrollOptions;if(k.shouldPublishImmediately){c();return}u()},[u,c]),m=A(function(D,j){r.current&&S(!1);var k=o.current,L=k.getDroppableRef();L||S(!1);var F=Lc(L),M={ref:L,descriptor:s,env:F,scrollOptions:j};r.current=M;var E=$c({ref:L,descriptor:s,env:F,windowScroll:D,direction:k.direction,isDropDisabled:k.isDropDisabled,isCombineEnabled:k.isCombineEnabled,shouldClipSubject:!k.ignoreContainerClipping}),B=F.closestScrollable;return B&&(B.setAttribute(Ya.contextId,t.contextId),B.addEventListener("scroll",f,Za(M.scrollOptions))),E},[t.contextId,s,f,o]),h=A(function(){var D=r.current,j=Rr(D);return D&&j||S(!1),jt(j)},[]),y=A(function(){var D=r.current;D||S(!1);var j=Rr(D);r.current=null,j&&(u.cancel(),j.removeAttribute(Ya.contextId),j.removeEventListener("scroll",f,Za(D.scrollOptions)))},[f,u]),x=A(function(D){var j=r.current;j||S(!1);var k=Rr(j);k||S(!1),k.scrollTop+=D.y,k.scrollLeft+=D.x},[]),w=G(function(){return{getDimensionAndWatchScroll:m,getScrollWhileDragging:h,dragStopped:y,scroll:x}},[y,m,h,x]),N=G(function(){return{uniqueId:a,descriptor:s,callbacks:w}},[w,s,a]);ce(function(){return d.current=N.descriptor,n.droppable.register(N),function(){r.current&&y(),n.droppable.unregister(N)}},[w,s,y,N,i,n.droppable]),ce(function(){r.current&&i.updateDroppableIsEnabled(d.current.id,!e.isDropDisabled)},[e.isDropDisabled,i]),ce(function(){r.current&&i.updateDroppableIsCombineEnabled(d.current.id,e.isCombineEnabled)},[e.isCombineEnabled,i])}function Ct(){}var _a={width:0,height:0,margin:gs},Vc=function(r){var t=r.isAnimatingOpenOnMount,a=r.placeholder,n=r.animate;return t||n==="close"?_a:{height:a.client.borderBox.height,width:a.client.borderBox.width,margin:a.client.margin}},zc=function(r){var t=r.isAnimatingOpenOnMount,a=r.placeholder,n=r.animate,i=Vc({isAnimatingOpenOnMount:t,placeholder:a,animate:n});return{display:a.display,boxSizing:"border-box",width:i.width,height:i.height,marginTop:i.margin.top,marginRight:i.margin.right,marginBottom:i.margin.bottom,marginLeft:i.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:n!=="none"?dr.placeholder:null}};function qc(e){var r=I.useRef(null),t=A(function(){r.current&&(clearTimeout(r.current),r.current=null)},[]),a=e.animate,n=e.onTransitionEnd,i=e.onClose,o=e.contextId,s=I.useState(e.animate==="open"),d=s[0],g=s[1];I.useEffect(function(){return d?a!=="open"?(t(),g(!1),Ct):r.current?Ct:(r.current=setTimeout(function(){r.current=null,g(!1)}),t):Ct},[a,d,t]);var p=A(function(u){u.propertyName==="height"&&(n(),a==="close"&&i())},[a,i,n]),c=zc({isAnimatingOpenOnMount:d,animate:e.animate,placeholder:e.placeholder});return z.createElement(e.placeholder.tagName,{style:c,"data-rbd-placeholder-context-id":o,onTransitionEnd:p,ref:e.innerRef})}var Yc=z.memo(qc),la=z.createContext(null),Jc=function(e){tn(r,e);function r(){for(var a,n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return a=e.call.apply(e,[this].concat(i))||this,a.state={isVisible:!!a.props.on,data:a.props.on,animate:a.props.shouldAnimate&&a.props.on?"open":"none"},a.onClose=function(){a.state.animate==="close"&&a.setState({isVisible:!1})},a}r.getDerivedStateFromProps=function(n,i){return n.shouldAnimate?n.on?{isVisible:!0,data:n.on,animate:"open"}:i.isVisible?{isVisible:!0,data:i.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!n.on,data:n.on,animate:"none"}};var t=r.prototype;return t.render=function(){if(!this.state.isVisible)return null;var n={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(n)},r}(z.PureComponent),en={dragging:5e3,dropAnimating:4500},Kc=function(r,t){return t?dr.drop(t.duration):r?dr.snap:dr.fluid},Xc=function(r,t){return r?t?fr.opacity.drop:fr.opacity.combining:null},Qc=function(r){return r.forceShouldAnimate!=null?r.forceShouldAnimate:r.mode==="SNAP"};function Zc(e){var r=e.dimension,t=r.client,a=e.offset,n=e.combineWith,i=e.dropping,o=!!n,s=Qc(e),d=!!i,g=d?Et.drop(a,o):Et.moveTo(a),p={position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Kc(s,i),transform:g,opacity:Xc(o,d),zIndex:d?en.dropAnimating:en.dragging,pointerEvents:"none"};return p}function _c(e){return{transform:Et.moveTo(e.offset),transition:e.shouldAnimateDisplacement?null:"none"}}function eu(e){return e.type==="DRAGGING"?Zc(e):_c(e)}function ru(e,r,t){t===void 0&&(t=K);var a=window.getComputedStyle(r),n=r.getBoundingClientRect(),i=Cn(n,a),o=Fr(i,t),s={client:i,tagName:r.tagName.toLowerCase(),display:a.display},d={x:i.marginBox.width,y:i.marginBox.height},g={descriptor:e,placeholder:s,displaceBy:d,client:i,page:o};return g}function tu(e){var r=oa("draggable"),t=e.descriptor,a=e.registry,n=e.getDraggableRef,i=e.canDragInteractiveElements,o=e.shouldRespectForcePress,s=e.isEnabled,d=G(function(){return{canDragInteractiveElements:i,shouldRespectForcePress:o,isEnabled:s}},[i,s,o]),g=A(function(f){var m=n();return m||S(!1),ru(t,m,f)},[t,n]),p=G(function(){return{uniqueId:r,descriptor:t,options:d,getDimension:g}},[t,g,d,r]),c=I.useRef(p),u=I.useRef(!0);ce(function(){return a.draggable.register(c.current),function(){return a.draggable.unregister(c.current)}},[a.draggable]),ce(function(){if(u.current){u.current=!1;return}var f=c.current;c.current=p,a.draggable.update(p,f)},[p,a.draggable])}function au(e){e.preventDefault()}function nu(e){var r=I.useRef(null),t=A(function(M){r.current=M},[]),a=A(function(){return r.current},[]),n=Vr(dt),i=n.contextId,o=n.dragHandleUsageInstructionsId,s=n.registry,d=Vr(la),g=d.type,p=d.droppableId,c=G(function(){return{id:e.draggableId,index:e.index,type:g,droppableId:p}},[e.draggableId,e.index,g,p]),u=e.children,f=e.draggableId,m=e.isEnabled,h=e.shouldRespectForcePress,y=e.canDragInteractiveElements,x=e.isClone,w=e.mapped,N=e.dropAnimationFinished;if(!x){var D=G(function(){return{descriptor:c,registry:s,getDraggableRef:a,canDragInteractiveElements:y,shouldRespectForcePress:h,isEnabled:m}},[c,s,a,y,h,m]);tu(D)}var j=G(function(){return m?{tabIndex:0,role:"button","aria-describedby":o,"data-rbd-drag-handle-draggable-id":f,"data-rbd-drag-handle-context-id":i,draggable:!1,onDragStart:au}:null},[i,o,f,m]),k=A(function(M){w.type==="DRAGGING"&&w.dropping&&M.propertyName==="transform"&&N()},[N,w]),L=G(function(){var M=eu(w),E=w.type==="DRAGGING"&&w.dropping?k:null,B={innerRef:t,draggableProps:{"data-rbd-draggable-context-id":i,"data-rbd-draggable-id":f,style:M,onTransitionEnd:E},dragHandleProps:j};return B},[i,j,f,w,k,t]),F=G(function(){return{draggableId:c.id,type:c.type,source:{index:c.index,droppableId:c.droppableId}}},[c.droppableId,c.id,c.index,c.type]);return u(L,w.snapshot,F)}var bi=function(e,r){return e===r},hi=function(e){var r=e.combine,t=e.destination;return t?t.droppableId:r?r.droppableId:null},iu=function(r){return r.combine?r.combine.draggableId:null},ou=function(r){return r.at&&r.at.type==="COMBINE"?r.at.combine.draggableId:null};function su(){var e=J(function(n,i){return{x:n,y:i}}),r=J(function(n,i,o,s,d){return{isDragging:!0,isClone:i,isDropAnimating:!!d,dropAnimation:d,mode:n,draggingOver:o,combineWith:s,combineTargetFor:null}}),t=J(function(n,i,o,s,d,g,p){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:d,combineWith:g,mode:i,offset:n,dimension:o,forceShouldAnimate:p,snapshot:r(i,s,d,g,null)}}}),a=function(i,o){if(i.isDragging){if(i.critical.draggable.id!==o.draggableId)return null;var s=i.current.client.offset,d=i.dimensions.draggables[o.draggableId],g=de(i.impact),p=ou(i.impact),c=i.forceShouldAnimate;return t(e(s.x,s.y),i.movementMode,d,o.isClone,g,p,c)}if(i.phase==="DROP_ANIMATING"){var u=i.completed;if(u.result.draggableId!==o.draggableId)return null;var f=o.isClone,m=i.dimensions.draggables[o.draggableId],h=u.result,y=h.mode,x=hi(h),w=iu(h),N=i.dropDuration,D={duration:N,curve:_t.drop,moveTo:i.newHomeClientOffset,opacity:w?fr.opacity.drop:null,scale:w?fr.scale.drop:null};return{mapped:{type:"DRAGGING",offset:i.newHomeClientOffset,dimension:m,dropping:D,draggingOver:x,combineWith:w,mode:y,forceShouldAnimate:null,snapshot:r(y,f,x,w,D)}}}return null};return a}function yi(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var lu={mapped:{type:"SECONDARY",offset:K,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:yi(null)}};function du(){var e=J(function(o,s){return{x:o,y:s}}),r=J(yi),t=J(function(o,s,d){return s===void 0&&(s=null),{mapped:{type:"SECONDARY",offset:o,combineTargetFor:s,shouldAnimateDisplacement:d,snapshot:r(s)}}}),a=function(s){return s?t(K,s,!0):null},n=function(s,d,g,p){var c=g.displaced.visible[s],u=!!(p.inVirtualList&&p.effected[s]),f=it(g),m=f&&f.draggableId===s?d:null;if(!c){if(!u)return a(m);if(g.displaced.invisible[s])return null;var h=er(p.displacedBy.point),y=e(h.x,h.y);return t(y,m,!0)}if(u)return a(m);var x=g.displacedBy.point,w=e(x.x,x.y);return t(w,m,c.shouldAnimate)},i=function(s,d){if(s.isDragging)return s.critical.draggable.id===d.draggableId?null:n(d.draggableId,s.critical.draggable.id,s.impact,s.afterCritical);if(s.phase==="DROP_ANIMATING"){var g=s.completed;return g.result.draggableId===d.draggableId?null:n(d.draggableId,g.result.draggableId,g.impact,g.afterCritical)}return null};return i}var cu=function(){var r=su(),t=du(),a=function(i,o){return r(i,o)||t(i,o)||lu};return a},uu={dropAnimationFinished:Zn},gu=Dn(cu,uu,null,{context:ia,pure:!0,areStatePropsEqual:bi})(nu);function xi(e){var r=Vr(la),t=r.isUsingCloneFor;return t===e.draggableId&&!e.isClone?null:z.createElement(gu,e)}function pu(e){var r=typeof e.isDragDisabled=="boolean"?!e.isDragDisabled:!0,t=!!e.disableInteractiveElementBlocking,a=!!e.shouldRespectForcePress;return z.createElement(xi,R({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:t,shouldRespectForcePress:a}))}function fu(e){var r=I.useContext(dt);r||S(!1);var t=r.contextId,a=r.isMovementAllowed,n=I.useRef(null),i=I.useRef(null),o=e.children,s=e.droppableId,d=e.type,g=e.mode,p=e.direction,c=e.ignoreContainerClipping,u=e.isDropDisabled,f=e.isCombineEnabled,m=e.snapshot,h=e.useClone,y=e.updateViewportMaxScroll,x=e.getContainerForClone,w=A(function(){return n.current},[]),N=A(function(B){n.current=B},[]);A(function(){return i.current},[]);var D=A(function(B){i.current=B},[]),j=A(function(){a()&&y({maxScroll:ai()})},[a,y]);Hc({droppableId:s,type:d,mode:g,direction:p,isDropDisabled:u,isCombineEnabled:f,ignoreContainerClipping:c,getDroppableRef:w});var k=z.createElement(Jc,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(B){var Q=B.onClose,Y=B.data,T=B.animate;return z.createElement(Yc,{placeholder:Y,onClose:Q,innerRef:D,animate:T,contextId:t,onTransitionEnd:j})}),L=G(function(){return{innerRef:N,placeholder:k,droppableProps:{"data-rbd-droppable-id":s,"data-rbd-droppable-context-id":t}}},[t,s,k,N]),F=h?h.dragging.draggableId:null,M=G(function(){return{droppableId:s,type:d,isUsingCloneFor:F}},[s,F,d]);function E(){if(!h)return null;var B=h.dragging,Q=h.render,Y=z.createElement(xi,{draggableId:B.draggableId,index:B.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(T,Z){return Q(T,Z,B)});return Pi.createPortal(Y,x())}return z.createElement(la.Provider,{value:M},o(L,m),E())}var Nt=function(r,t){return r===t.droppable.type},rn=function(r,t){return t.draggables[r.draggable.id]},vu=function(){var r={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=R({},r,{shouldAnimatePlaceholder:!1}),a=J(function(o){return{draggableId:o.id,type:o.type,source:{index:o.index,droppableId:o.droppableId}}}),n=J(function(o,s,d,g,p,c){var u=p.descriptor.id,f=p.descriptor.droppableId===o;if(f){var m=c?{render:c,dragging:a(p.descriptor)}:null,h={isDraggingOver:d,draggingOverWith:d?u:null,draggingFromThisWith:u,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!1,snapshot:h,useClone:m}}if(!s)return t;if(!g)return r;var y={isDraggingOver:d,draggingOverWith:u,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!0,snapshot:y,useClone:null}}),i=function(s,d){var g=d.droppableId,p=d.type,c=!d.isDropDisabled,u=d.renderClone;if(s.isDragging){var f=s.critical;if(!Nt(p,f))return t;var m=rn(f,s.dimensions),h=de(s.impact)===g;return n(g,c,h,h,m,u)}if(s.phase==="DROP_ANIMATING"){var y=s.completed;if(!Nt(p,y.critical))return t;var x=rn(y.critical,s.dimensions);return n(g,c,hi(y.result)===g,de(y.impact)===g,x,u)}if(s.phase==="IDLE"&&s.completed&&!s.shouldFlush){var w=s.completed;if(!Nt(p,w.critical))return t;var N=de(w.impact)===g,D=!!(w.impact.at&&w.impact.at.type==="COMBINE"),j=w.critical.droppable.id===g;return N?D?r:t:j?r:t}return t};return i},mu={updateViewportMaxScroll:Cl};function bu(){return document.body||S(!1),document.body}var hu={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:bu},wi=Dn(vu,mu,null,{context:ia,pure:!0,areStatePropsEqual:bi})(fu);wi.defaultProps=hu;const yu=({playlist:e,onDelete:r,onSync:t,onToggle:a,onEdit:n,onSetDefault:i,onOpenDetails:o,onSchedule:s,onCopyLink:d,onSetCollaborative:g,onReorderByVotes:p,formatDate:c,getStatusColor:u,getCategoryColor:f,formatDuration:m})=>{var Y;const[h,y]=I.useState(null),[x,w]=I.useState(!1),[N,D]=I.useState(1),j=50,k=Array.from(new Set((e.videos||[]).map(T=>T.genre).filter(Boolean))),L=h?(e.videos||[]).filter(T=>T.genre===h):e.videos||[],F=Math.max(1,Math.ceil(L.length/j)),M=Math.min(N,F),E=(M-1)*j,B=E+j,Q=L.slice(E,B);return l.jsx(he.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden",children:l.jsxs("div",{className:"p-4",children:[l.jsxs("div",{className:"relative cursor-pointer",onClick:()=>o(e),children:[e.thumbnail?l.jsx("img",{src:e.thumbnail,alt:e.name,className:"w-full aspect-video object-cover rounded"}):l.jsx("div",{className:"w-full aspect-video bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center",children:l.jsx(Ge,{className:"w-6 h-6 text-gray-400"})}),l.jsxs("div",{className:"absolute top-2 left-2 flex gap-1",children:[e.isDefault&&l.jsx("span",{className:"px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full",children:"Padrão"}),l.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs font-medium ${u(e.isActive??!0)}`,children:e.isActive??!0?"Ativa":"Inativa"}),e.category&&l.jsx("span",{className:`px-2 py-0.5 rounded text-xs ${f(e.category)}`,children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})]})]}),l.jsx("h3",{className:"mt-3 font-semibold text-gray-900 dark:text-white truncate",children:e.name}),l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2 min-h-[2.5rem]",children:e.description}),l.jsxs("div",{className:"mt-2 flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400",children:[l.jsxs("span",{children:[e.videoCount||0," vídeos"]}),e.totalDuration&&l.jsx("span",{children:m(e.totalDuration)}),e.lastSync&&l.jsxs("span",{children:["Sincronizado: ",c(e.lastSync).split(" ")[0]]}),((Y=e.schedule)==null?void 0:Y.enabled)&&l.jsxs("span",{className:"flex items-center",children:[l.jsx(Xe,{className:"w-3 h-3 mr-1"}),e.schedule.startTime," - ",e.schedule.endTime]})]}),l.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("button",{onClick:()=>a(e),className:`p-1.5 rounded-lg ${e.isActive??!0?"text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20":"text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"}`,title:e.isActive??!0?"Desativar":"Ativar",children:l.jsx(Tt,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>t(e.id),className:"p-1.5 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg",title:"Sincronizar com YouTube",children:l.jsx(Fe,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>s(e),className:"p-1.5 text-purple-600 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg",title:"Programar horários",children:l.jsx(Xe,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>g(e),className:"p-1.5 text-emerald-600 hover:bg-emerald-100 dark:hover:bg-emerald-900/20 rounded-lg",title:"Definir como Playlist Colaborativa",children:l.jsx(Mi,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>p(e),className:"p-1.5 text-indigo-600 hover:bg-indigo-100 dark:hover:bg-indigo-900/20 rounded-lg",title:"Reordenar por Votos (QA)",children:l.jsx(Li,{className:"w-4 h-4"})})]}),l.jsxs("div",{className:"flex items-center gap-1",children:[e.youtubeUrl&&l.jsx("a",{href:e.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg",title:"Ver no YouTube",children:l.jsx(zr,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>d(e.id),className:"p-1.5 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",title:"Copiar link",children:l.jsx(Fi,{className:"w-4 h-4"})}),l.jsx("button",{onClick:n,className:"p-1.5 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",title:"Editar",children:l.jsx(Rt,{className:"w-4 h-4"})}),!e.isDefault&&l.jsx("button",{onClick:()=>r(e.id),className:"p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg",title:"Excluir",children:l.jsx(an,{className:"w-4 h-4"})}),!e.isDefault&&l.jsx("button",{onClick:()=>i(e.id),className:"p-1.5 text-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-900/20 rounded-lg",title:"Definir como padrão",children:l.jsx(Gi,{className:"w-4 h-4"})})]})]}),l.jsxs("div",{className:"mt-4 border-t border-gray-200 dark:border-gray-700 pt-3",children:[l.jsx("button",{type:"button",onClick:()=>{w(T=>!T),D(1)},className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:x?"Ocultar vídeos":`Mostrar vídeos (${L.length})`}),x&&l.jsxs("div",{className:"mt-3",children:[l.jsxs("div",{className:"flex items-center mb-2 gap-2",children:[l.jsxs("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white flex items-center",children:[l.jsx(Ge,{className:"w-4 h-4 mr-2"}),"Vídeos",l.jsxs("span",{className:"ml-2 text-xs font-normal text-gray-500 dark:text-gray-400",children:["(",L.length," no total)"]})]}),k.length>0&&l.jsxs("select",{value:h||"",onChange:T=>{y(T.target.value||null),D(1)},className:"ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[l.jsx("option",{value:"",children:"Todos os gêneros"}),k.map(T=>l.jsx("option",{value:String(T),children:String(T).charAt(0).toUpperCase()+String(T).slice(1)},String(T)))]})]}),Q.length>0?l.jsx("div",{className:"space-y-2 max-h-60 overflow-y-auto pr-2",children:Q.map((T,Z)=>l.jsxs("div",{className:"flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg",children:[l.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-6 text-center",children:E+Z+1}),l.jsx("div",{className:"w-10 h-10 flex-shrink-0",children:l.jsx("img",{src:T.thumbnail,alt:T.title,className:"w-full h-full object-cover rounded",onError:q=>{const ae=q.currentTarget;ae.src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100'%3E%3Crect width='100%25' height='100%25' fill='%23888888'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='12'%3EVideo%3C/text%3E%3C/svg%3E"}})}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:T.title}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:T.artist||"Artista desconhecido"})]}),l.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:T.duration!=null?`${Math.floor((T.duration||0)/60)}:${((T.duration||0)%60).toString().padStart(2,"0")}`:"--:--"})]},T.id))}):l.jsxs("div",{className:"text-center py-6 bg-gray-50 dark:bg-gray-750 rounded-lg",children:[l.jsx(Ge,{className:"w-10 h-10 text-gray-400 mx-auto mb-2"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Nenhum vídeo disponível"})]}),L.length>0&&l.jsxs("div",{className:"mt-2 flex items-center justify-between text-xs text-gray-600 dark:text-gray-300",children:[l.jsx("button",{type:"button",onClick:()=>D(T=>Math.max(1,T-1)),disabled:M<=1,className:"px-3 py-1 rounded bg-gray-100 dark:bg-gray-700 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-600",children:"Anterior"}),l.jsxs("span",{children:["Página ",M," de ",F]}),l.jsx("button",{type:"button",onClick:()=>D(T=>Math.min(F,T+1)),disabled:M>=F,className:"px-3 py-1 rounded bg-gray-100 dark:bg-gray-700 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-600",children:"Próxima"})]})]})]})]})})},xu=({playlist:e,onDelete:r,onSync:t,onToggle:a,onEdit:n,onOpenDetails:i,onSchedule:o,formatDate:s,getStatusColor:d,getCategoryColor:g,formatDuration:p})=>{var c;return l.jsx(he.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden",children:l.jsxs("div",{className:"p-4 flex items-center gap-4",children:[l.jsx("div",{className:"w-12 h-12 flex-shrink-0 cursor-pointer",onClick:()=>i(e),children:e.thumbnail?l.jsx("img",{src:e.thumbnail,alt:e.name,className:"w-full h-full object-cover rounded"}):l.jsx("div",{className:"w-full h-full bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center",children:l.jsx(Ge,{className:"w-5 h-5 text-gray-400"})})}),l.jsxs("div",{className:"flex-1 min-w-0 cursor-pointer",onClick:()=>i(e),children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[e.order&&l.jsx("span",{className:"w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full flex items-center justify-center flex-shrink-0",children:e.order}),l.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white truncate",children:e.name}),e.isDefault&&l.jsx("span",{className:"px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full",children:"Padrão"}),l.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs font-medium ${d(e.isActive??!0)}`,children:e.isActive??!0?"Ativa":"Inativa"}),e.__activeBySchedule&&l.jsx("span",{className:"px-2 py-0.5 rounded-full text-xs bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300",children:"Ativa pelo agendamento agora"}),e.category&&l.jsx("span",{className:`px-2 py-0.5 rounded text-xs ${g(e.category)}`,children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})]}),l.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:e.description}),l.jsxs("div",{className:"flex items-center space-x-3 mt-1 text-xs text-gray-500 dark:text-gray-400",children:[l.jsxs("span",{children:[e.videoCount||0," vídeos"]}),e.totalDuration&&l.jsx("span",{children:p(e.totalDuration)}),e.lastSync&&l.jsxs("span",{children:["Sincronizado: ",s(e.lastSync).split(" ")[0]]}),((c=e.schedule)==null?void 0:c.enabled)&&l.jsxs("span",{className:"flex items-center",children:[l.jsx(Xe,{className:"w-3 h-3 mr-1"}),e.schedule.startTime," - ",e.schedule.endTime]}),e.isActive&&l.jsx("span",{className:"px-2 py-0.5 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400 rounded-full",children:"Colaborativa ativa"})]})]}),l.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0 flex-wrap max-w-full justify-end",children:[l.jsx("button",{onClick:()=>a(e),className:`p-1.5 rounded-lg ${e.isActive??!0?"text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20":"text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"}`,title:e.isActive??!0?"Desativar":"Ativar",children:l.jsx(Tt,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>t(e.id),className:"p-1.5 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg",title:"Sincronizar com YouTube",children:l.jsx(Fe,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>o(e),className:"p-1.5 text-purple-600 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg",title:"Programar horários",children:l.jsx(Xe,{className:"w-4 h-4"})}),e.youtubeUrl&&l.jsx("a",{href:e.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg",title:"Ver no YouTube",children:l.jsx(zr,{className:"w-4 h-4"})}),l.jsx("button",{onClick:n,className:"p-1.5 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",title:"Editar",children:l.jsx(Rt,{className:"w-4 h-4"})}),e.isDefault?l.jsx("div",{className:"w-[26px]"}):l.jsx("button",{onClick:()=>r(e.id),className:"p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg",title:"Excluir",children:l.jsx(an,{className:"w-4 h-4"})})]})]})})},wu=({playlist:e,onClose:r,onEdit:t,onSync:a,formatDate:n,formatDuration:i})=>{var o;return l.jsx(he.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs(he.div,{initial:{scale:.9,y:20},animate:{scale:1,y:0},exit:{scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-3xl relative overflow-hidden max-h-[90vh] flex flex-col",children:[l.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center",children:[l.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Detalhes da Playlist"}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>a(e.id),className:"p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg",title:"Sincronizar com YouTube",children:l.jsx(Fe,{className:"w-5 h-5"})}),l.jsx("button",{onClick:t,className:"p-2 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",title:"Editar playlist",children:l.jsx(Rt,{className:"w-5 h-5"})}),l.jsx("button",{onClick:r,className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:l.jsx(Br,{className:"w-5 h-5"})})]})]}),l.jsxs("div",{className:"overflow-y-auto flex-1 p-6",children:[l.jsxs("div",{className:"flex flex-col md:flex-row gap-6 mb-6",children:[l.jsx("div",{className:"flex-shrink-0 w-full md:w-48",children:e.thumbnail?l.jsx("img",{src:e.thumbnail,alt:e.name,className:"w-full h-auto object-cover rounded-lg"}):l.jsx("div",{className:"w-full aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center",children:l.jsx(Ge,{className:"w-12 h-12 text-gray-400"})})}),l.jsxs("div",{className:"flex-1",children:[l.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:e.name}),l.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-3",children:e.description||"Sem descrição"}),l.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.isDefault&&l.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full",children:"Padrão"}),l.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}`,children:e.isActive?"Ativa":"Inativa"}),e.category&&l.jsx("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 text-xs rounded",children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})]}),l.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"ID YouTube:"}),l.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:e.youtubePlaylistId||"N/A"})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Criada em:"}),l.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:n(e.createdAt)})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Última sincronização:"}),l.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:e.lastSync?n(e.lastSync):"Nunca"})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Volume:"}),l.jsxs("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:[e.volume||80,"%"]})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Duração total:"}),l.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:e.totalDuration?i(e.totalDuration):"Desconhecida"})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Número de vídeos:"}),l.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:e.videoCount||0})]})]}),e.tags&&e.tags.length>0&&l.jsxs("div",{className:"mt-4",children:[l.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tags:"}),l.jsx("div",{className:"flex flex-wrap gap-1",children:e.tags.map(s=>l.jsx("span",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded",children:s},s))})]}),((o=e.schedule)==null?void 0:o.enabled)&&l.jsxs("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[l.jsxs("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center",children:[l.jsx(Xe,{className:"w-4 h-4 mr-1"}),"Programação"]}),l.jsxs("div",{className:"text-sm text-blue-700 dark:text-blue-400",children:[l.jsxs("p",{children:["Horário: ",e.schedule.startTime," -"," ",e.schedule.endTime]}),l.jsxs("p",{children:["Dias:"," ",e.schedule.days.length===7?"Todos os dias":e.schedule.days.map(s=>({monday:"Segunda",tuesday:"Terça",wednesday:"Quarta",thursday:"Quinta",friday:"Sexta",saturday:"Sábado",sunday:"Domingo"})[s]||s).join(", ")]})]})]})]})]}),e.playCount!==void 0&&l.jsxs("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-750 rounded-lg",children:[l.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center",children:[l.jsx($i,{className:"w-5 h-5 mr-2"}),"Estatísticas de Reprodução"]}),l.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm",children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Total de Reproduções"}),l.jsx("p",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.playCount})]}),e.lastPlayed&&l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm",children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Última Reprodução"}),l.jsx("p",{className:"text-xl font-bold text-gray-900 dark:text-white",children:n(e.lastPlayed)})]}),e.suggestionsCount!==void 0&&l.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm",children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Sugestões dos Clientes"}),l.jsx("p",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.suggestionsCount})]})]})]})]}),l.jsxs("div",{className:"border-t border-gray-200 dark:border-gray-700 p-4 flex justify-between",children:[l.jsx("div",{children:e.youtubeUrl&&l.jsxs("a",{href:e.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[l.jsx(zr,{className:"w-4 h-4"}),l.jsx("span",{children:"Abrir no YouTube"})]})}),l.jsx("button",{onClick:r,className:"px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"Fechar"})]})]})})},Pu=()=>{var ua;const{restaurantId:e}=Ci(),[r,t]=I.useState([]),[a,n]=I.useState(!1),[i,o]=I.useState(!1),[s,d]=I.useState(null),[g,p]=I.useState(null),[c,u]=I.useState(null),[f,m]=I.useState("grid"),h=I.useRef(null),[y,x]=I.useState(""),[w,N]=I.useState(null),[D,j]=I.useState("order"),[k,L]=I.useState("desc"),[F,M]=I.useState(null),[E,B]=I.useState({name:"",description:"",youtubeUrl:"",tags:"",isActive:!0,category:"",volume:80,schedule:{enabled:!1,startTime:"08:00",endTime:"22:00",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]}}),[Q,Y]=I.useState(!1),[T,Z]=I.useState(null),[q,ae]=I.useState({enabled:!1,startTime:"08:00",endTime:"22:00",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]}),oe=[{id:"ambient",name:"Ambiente",color:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"},{id:"upbeat",name:"Animadas",color:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"},{id:"relaxing",name:"Relaxantes",color:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"},{id:"jazz",name:"Jazz",color:"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"},{id:"international",name:"Internacional",color:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"},{id:"seasonal",name:"Sazonal",color:"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"}],ye=[{id:"monday",label:"Segunda"},{id:"tuesday",label:"Terça"},{id:"wednesday",label:"Quarta"},{id:"thursday",label:"Quinta"},{id:"friday",label:"Sexta"},{id:"saturday",label:"Sábado"},{id:"sunday",label:"Domingo"}],V=(v="application/json")=>{const b={};v&&(b["Content-Type"]=v);const C=localStorage.getItem("authToken");return console.log(`🔑 Auth token exists: ${!!C}, value: ${C==null?void 0:C.substring(0,20)}...`),C&&(b.Authorization=`Bearer ${C}`),console.log("📋 Headers criados:",b),b},ne=I.useCallback(async()=>{var v,b,C,P,O,W,Be;if(console.log(`🔍 DEBUG: loadPlaylists called with restaurantId: ${e}`),!e){console.log("❌ RestaurantId não encontrado, não é possível carregar playlists");return}n(!0),u(null);try{const Ne=se(`/playlists/restaurant/${e}`);console.log(`🔄 Carregando playlists do restaurante: ${e} via ${Ne}`);const Oe=await fetch(Ne,{method:"GET",headers:V()});if(console.log(`📡 Response status: ${Oe.status}`),!Oe.ok)throw new Error(`Erro ao carregar playlists: ${Oe.status}`);const we=await Oe.json();console.log("✅ Resposta da API:",we);let Je=[];if(we.playlists&&Array.isArray(we.playlists))console.log(`✅ Encontradas ${we.playlists.length} playlists`),Je=we.playlists;else{console.log("⚠️ API não retornou playlists válidas, carregando dados de exemplo"),re();return}try{const Er=await fetch(se(`/playlist-schedules/${e}/current`),{method:"GET",headers:V()});if(Er.ok){const De=await Er.json(),ga=((b=(v=De==null?void 0:De.current)==null?void 0:v.playlist)==null?void 0:b.playlistId)||((O=(P=(C=De==null?void 0:De.current)==null?void 0:C.playlist)==null?void 0:P.playlist)==null?void 0:O.playlistId)||((Be=(W=De==null?void 0:De.current)==null?void 0:W.playlist)==null?void 0:Be.id);ga&&(Je=Je.map(gt=>gt.id===ga?{...gt,__activeBySchedule:!0}:{...gt,__activeBySchedule:!1}))}}catch(Er){console.warn("Não foi possível obter agendamento atual:",Er)}t(Je)}catch(Ne){console.error("Erro ao carregar playlists:",Ne),u(Ne.message||"Erro ao carregar playlists"),$.error("Erro ao carregar playlists. Carregando dados de exemplo."),re()}finally{n(!1)}},[e]),re=()=>{t([{id:"pl-1",name:"Música Ambiente Almoço",description:"Playlist suave para o horário de almoço",youtubePlaylistId:"PLR7XO54Pktt8EfB-fKT9y8jEzQjnUniom",youtubeUrl:"https://www.youtube.com/playlist?list=PLR7XO54Pktt8EfB-fKT9y8jEzQjnUniom",videoCount:42,isActive:!0,isDefault:!0,createdAt:"2023-05-15T10:23:45Z",lastSync:"2024-07-28T14:30:12Z",thumbnail:"https://i.ytimg.com/vi/5qap5aO4i9A/mqdefault.jpg",tags:["ambiente","jazz","relaxante"],suggestionsCount:12,schedule:{enabled:!0,startTime:"11:00",endTime:"15:00",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]},volume:75,category:"ambient",order:1,totalDuration:9540,playCount:128,lastPlayed:"2024-08-02T15:45:23Z",videos:[{id:"v-1",title:"Autumn Jazz Mix",artist:"Coffee Shop Vibes",thumbnail:"https://i.ytimg.com/vi/5qap5aO4i9A/mqdefault.jpg",duration:3600,isAvailable:!0},{id:"v-2",title:"Smooth Bossa Nova",artist:"Jazz Café",thumbnail:"https://i.ytimg.com/vi/2ccaHpy5Ewo/mqdefault.jpg",duration:2400,isAvailable:!0}]},{id:"pl-2",name:"Happy Hour",description:"Música animada para o final da tarde",youtubePlaylistId:"PLgzTt0k8mXzEk586ze4BjvDXR7c-TUSnx",youtubeUrl:"https://www.youtube.com/playlist?list=PLgzTt0k8mXzEk586ze4BjvDXR7c-TUSnx",videoCount:28,isActive:!0,isDefault:!1,createdAt:"2023-06-20T16:45:23Z",lastSync:"2024-07-20T18:15:45Z",thumbnail:"https://i.ytimg.com/vi/36YnV9STBqc/mqdefault.jpg",tags:["pop","animada","contemporânea"],suggestionsCount:8,schedule:{enabled:!0,startTime:"17:00",endTime:"20:00",days:["thursday","friday","saturday"]},volume:85,category:"upbeat",order:2,totalDuration:5460,playCount:84,lastPlayed:"2024-08-01T19:12:56Z"},{id:"pl-3",name:"Jantar Romântico",description:"Músicas suaves para jantares especiais",youtubePlaylistId:"PLQog_FHUHAFUDDQPOTeAWSHwzFV1Zz5PZ",youtubeUrl:"https://www.youtube.com/playlist?list=PLQog_FHUHAFUDDQPOTeAWSHwzFV1Zz5PZ",videoCount:35,isActive:!1,isDefault:!1,createdAt:"2023-07-10T20:10:30Z",lastSync:"2024-06-15T22:40:18Z",thumbnail:"https://i.ytimg.com/vi/gdLLRj1Ge7g/mqdefault.jpg",tags:["romântica","clássica","instrumental"],schedule:{enabled:!1,startTime:"19:00",endTime:"23:00",days:["friday","saturday","sunday"]},volume:65,category:"relaxing",order:3,totalDuration:7200,playCount:32,lastPlayed:"2024-07-15T21:08:47Z"},{id:"pl-4",name:"Brunch de Domingo",description:"Playlist leve para o brunch de domingo",youtubePlaylistId:"PLQdw4_EElB3OeIE9Gqu-2kBwN2SQbM6P7",youtubeUrl:"https://www.youtube.com/playlist?list=PLQdw4_EElB3OeIE9Gqu-2kBwN2SQbM6P7",videoCount:22,isActive:!0,isDefault:!1,createdAt:"2023-08-05T09:30:15Z",lastSync:"2024-07-25T10:20:33Z",thumbnail:"https://i.ytimg.com/vi/MYPVQccHhAQ/mqdefault.jpg",tags:["acoustic","folk","indie"],schedule:{enabled:!0,startTime:"10:00",endTime:"14:00",days:["sunday"]},volume:70,category:"ambient",order:4,totalDuration:4500,playCount:28,lastPlayed:"2024-07-28T11:35:22Z"}])};I.useEffect(()=>{console.log(`📋 PlaylistManager: restaurantId = ${e}`),e?(console.log(`📋 PlaylistManager: Iniciando carregamento de playlists para ${e}`),ne()):console.log("📋 PlaylistManager: restaurantId não disponível ainda")},[e,ne]);const Se=v=>{const b=/[?&]list=([^#\&\?]*)/,C=v.match(b);return C?C[1]:null},Ue=async()=>{if(!E.name.trim()){$.error("Nome da playlist é obrigatório");return}if(!E.youtubeUrl.trim()){$.error("URL da playlist do YouTube é obrigatória");return}const v=Se(E.youtubeUrl);if(!v){$.error("URL da playlist do YouTube inválida");return}n(!0);try{const b={restaurantId:e,name:E.name,description:E.description,youtubeUrl:E.youtubeUrl,youtubePlaylistId:v,tags:E.tags.split(",").map(O=>O.trim()).filter(Boolean),isActive:E.isActive,category:E.category||void 0,volume:E.volume||80,schedule:E.schedule.enabled?E.schedule:void 0},C=se("/playlists"),P=await fetch(C,{method:"POST",headers:V(),body:JSON.stringify(b)});if(!P.ok){const O=await P.json().catch(()=>({}));throw new Error(O.message||`Erro ao criar playlist: ${P.status}`)}await ne(),B({name:"",description:"",youtubeUrl:"",tags:"",isActive:!0,category:"",volume:80,schedule:{enabled:!1,startTime:"08:00",endTime:"22:00",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]}}),o(!1),$.success("Playlist adicionada com sucesso!")}catch(b){console.error("Erro ao adicionar playlist:",b),$.error(b.message||"Erro ao adicionar playlist")}finally{n(!1)}},ge=async v=>{n(!0);try{const b=se(`/playlists/${v}/sync`),C=await fetch(b,{method:"POST",headers:V("")});if(!C.ok){const O=await C.json().catch(()=>({}));throw new Error(O.message||`Erro ao sincronizar: ${C.status}`)}const P=await C.json();t(O=>O.map(W=>W.id===v?{...W,lastSync:new Date().toISOString(),videoCount:P.playlist.trackCount}:W)),$.success("Playlist sincronizada com sucesso!")}catch(b){console.error("Erro ao sincronizar playlist:",b),$.error(b.message||"Erro ao sincronizar playlist")}finally{n(!1)}},je=async v=>{const b=r.find(C=>C.id===v);if(!b){$.error("Playlist não encontrada");return}if(b.isDefault){$.error("Não é possível excluir a playlist padrão");return}if(window.confirm("Tem certeza que deseja excluir esta playlist? Esta ação não pode ser desfeita."))try{n(!0);const C=se(`/playlists/${v}`),P=await fetch(C,{method:"DELETE",headers:V("")});if(!P.ok){const O=await P.json().catch(()=>({}));throw new Error(O.message||`Erro ao excluir: ${P.status}`)}await ne(),$.success("Playlist excluída com sucesso!")}catch(C){console.error("Erro ao excluir playlist:",C),$.error(C.message||"Erro ao excluir playlist")}finally{n(!1)}},We=async v=>{try{if(n(!0),!v.name.trim())throw new Error("Nome da playlist é obrigatório");if(v.youtubeUrl&&!Se(v.youtubeUrl))throw new Error("URL da playlist do YouTube inválida");const b=se(`/playlists/${v.id}`),C=await fetch(b,{method:"PUT",headers:V(),body:JSON.stringify(v)});if(!C.ok){const P=await C.json().catch(()=>({}));throw new Error(P.message||`Erro ao atualizar: ${C.status}`)}d(null),await ne(),$.success("Playlist atualizada com sucesso!")}catch(b){console.error("Erro ao atualizar playlist:",b),$.error(b.message||"Erro ao atualizar playlist")}finally{n(!1)}},Te=async v=>{try{n(!0);const b=!v.isActive,C=se(`/playlists/${v.id}/status`),P=await fetch(C,{method:"PATCH",headers:V(),body:JSON.stringify({isActive:b})});if(!P.ok){const O=await P.json().catch(()=>({}));throw new Error(O.message||`Erro ao alterar status: ${P.status}`)}if(b){const O=r.filter(W=>W.id!==v.id&&(W.isActive??!1));await Promise.all(O.map(W=>fetch(se(`/playlists/${W.id}/status`),{method:"PATCH",headers:V(),body:JSON.stringify({isActive:!1})})))}t(O=>O.map(W=>W.id===v.id?{...W,isActive:b}:b?{...W,isActive:!1}:W)),$.success(`Playlist ${b?"ativada":"desativada"} com sucesso!`)}catch(b){console.error("Erro ao alterar status da playlist:",b),$.error(b.message||"Erro ao alterar status da playlist")}finally{n(!1)}},He=async v=>{try{n(!0);const b=se(`/playlists/${v.id}/status`),C=await fetch(b,{method:"PATCH",headers:V(),body:JSON.stringify({isActive:!0})});if(!C.ok){const O=await C.json().catch(()=>({}));throw new Error(O.message||"Falha ao ativar playlist como colaborativa")}const P=r.filter(O=>O.id!==v.id&&(O.isActive??!1));await Promise.all(P.map(O=>fetch(se(`/playlists/${O.id}/status`),{method:"PATCH",headers:V(),body:JSON.stringify({isActive:!1})}))),localStorage.setItem("currentRestaurantId",e),localStorage.setItem("currentPlaylistId",v.id),$.success(`Playlist "${v.name}" definida como colaborativa ativa`),await ne()}catch(b){console.error("Erro ao definir como colaborativa:",b),$.error(b.message||"Erro ao definir como colaborativa")}finally{n(!1)}},xe=async v=>{try{n(!0);const b=se(`/collaborative-playlist/${e}/reorder`),C=await fetch(b,{method:"POST",headers:V()}),P=await C.json().catch(()=>({}));if(!C.ok||(P==null?void 0:P.success)===!1)throw new Error((P==null?void 0:P.message)||`Falha ao reordenar: ${C.status}`);$.success((P==null?void 0:P.message)||"Playlist reordenada com sucesso (QA)")}catch(b){console.error("Erro ao reordenar por votos:",b),$.error(b.message||"Erro ao reordenar")}finally{n(!1)}},Ve=async v=>{try{n(!0);const b=se(`/playlists/${v}/default`),C=await fetch(b,{method:"PATCH",headers:V(),body:JSON.stringify({isDefault:!0})});if(!C.ok){const P=await C.json().catch(()=>({}));throw new Error(P.message||`Erro ao definir playlist padrão: ${C.status}`)}t(P=>P.map(O=>({...O,isDefault:O.id===v}))),$.success("Playlist definida como padrão")}catch(b){console.error("Erro ao definir playlist padrão:",b),$.error(b.message||"Erro ao definir playlist padrão")}finally{n(!1)}},Ir=async(v,b)=>{try{n(!0);const C=se(`/playlists/${v}/schedule`),P=await fetch(C,{method:"PATCH",headers:{...V(),"Content-Type":"application/json"},body:JSON.stringify({schedule:b})});if(!P.ok){const O=await P.json().catch(()=>({}));throw new Error(O.message||`Erro ao atualizar programação: ${P.status}`)}t(O=>O.map(W=>W.id===v?{...W,schedule:b}:W)),Z(null),Y(!1),$.success("Programação atualizada com sucesso!")}catch(C){console.error("Erro ao atualizar programação:",C),$.error(C.message||"Erro ao atualizar programação")}finally{n(!1)}},ar=v=>{var P;const b=(P=v.target.files)==null?void 0:P[0];if(!b)return;const C=new FileReader;C.onload=async O=>{var W;try{const Be=(W=O.target)==null?void 0:W.result,Ne=JSON.parse(Be);if(!Array.isArray(Ne))throw new Error("Formato de arquivo inválido. Esperava-se uma array de playlists.");n(!0),setTimeout(()=>{const Oe=Ne.map((we,Je)=>({...we,id:`imported-${Date.now()}-${Je}`,createdAt:new Date().toISOString(),isDefault:!1}));t(we=>[...we,...Oe]),n(!1),$.success(`${Oe.length} playlists importadas com sucesso!`)},1500)}catch(Be){console.error("Erro ao importar playlists:",Be),$.error(Be.message||"Erro ao importar playlists")}v.target&&(v.target.value="")},C.readAsText(b)},ze=()=>{try{const v=JSON.stringify(r,null,2),b=`data:application/json;charset=utf-8,${encodeURIComponent(v)}`,C=`playlists-${e}-${new Date().toISOString().slice(0,10)}.json`,P=document.createElement("a");P.setAttribute("href",b),P.setAttribute("download",C),P.click(),$.success("Playlists exportadas com sucesso!")}catch(v){console.error("Erro ao exportar playlists:",v),$.error("Erro ao exportar playlists")}},Pr=async v=>{if(!v.destination)return;const b=Array.from(r),[C]=b.splice(v.source.index,1);b.splice(v.destination.index,0,C);const P=b.map((O,W)=>({...O,order:W+1}));t(P),$.success("Ordem das playlists atualizada")},qe=v=>{const b=`https://yourapp.com/playlists/${v}`;navigator.clipboard.writeText(b).then(()=>$.success("Link copiado para a área de transferência")).catch(()=>$.error("Erro ao copiar link"))},Re=v=>new Date(v).toLocaleString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),nr=v=>{const b=Math.floor(v/3600),C=Math.floor(v%3600/60);return b>0?`${b}h ${C}min`:`${C} minutos`},Ce=v=>v?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",Ye=v=>{if(!v)return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";const b=oe.find(C=>C.id===v);return(b==null?void 0:b.color)||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"},da=r.filter(v=>{var O;const b=y===""||v.name.toLowerCase().includes(y.toLowerCase())||v.description.toLowerCase().includes(y.toLowerCase())||((O=v.tags)==null?void 0:O.some(W=>W.toLowerCase().includes(y.toLowerCase()))),C=w===null||v.isActive===w,P=F===null||v.category===F;return b&&C&&P}).sort((v,b)=>{let C=0;switch(D){case"name":C=v.name.localeCompare(b.name);break;case"date":C=new Date(v.createdAt).getTime()-new Date(b.createdAt).getTime();break;case"count":C=(v.videoCount||0)-(b.videoCount||0);break;case"lastSync":const P=v.lastSync?new Date(v.lastSync).getTime():0,O=b.lastSync?new Date(b.lastSync).getTime():0;C=P-O;break;case"order":C=(v.order||999)-(b.order||999);break;default:C=0}return k==="asc"?C:-C}),ca=v=>{Z(v),ae(v.schedule||{enabled:!1,startTime:"08:00",endTime:"22:00",days:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]}),Y(!0)},Di=()=>{T&&Ir(T.id,q)},ut=v=>{d(v)},Si=()=>{s&&We(s)};return l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[l.jsxs("div",{children:[l.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciar Playlists"}),l.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Configure e gerencie as playlists do seu restaurante"})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs("button",{onClick:ze,className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar playlists",children:[l.jsx(ki,{className:"w-4 h-4"}),l.jsx("span",{className:"hidden sm:inline",children:"Exportar"})]}),l.jsxs("button",{onClick:()=>{var v;return(v=h.current)==null?void 0:v.click()},className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Importar playlists",children:[l.jsx(Ai,{className:"w-4 h-4"}),l.jsx("span",{className:"hidden sm:inline",children:"Importar"})]}),l.jsx("input",{ref:h,type:"file",accept:".json",onChange:ar,className:"hidden"}),l.jsxs("button",{onClick:()=>o(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[l.jsx(pa,{className:"w-4 h-4"}),l.jsx("span",{children:"Nova Playlist"})]})]})]}),l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4",children:l.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[l.jsx("div",{className:"flex-1",children:l.jsxs("div",{className:"relative",children:[l.jsx(ji,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),l.jsx("input",{type:"text",placeholder:"Buscar playlists...",value:y,onChange:v=>x(v.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})}),l.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[l.jsxs("select",{value:w===null?"all":w.toString(),onChange:v=>N(v.target.value==="all"?null:v.target.value==="true"),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[l.jsx("option",{value:"all",children:"Todos os status"}),l.jsx("option",{value:"true",children:"Ativas"}),l.jsx("option",{value:"false",children:"Inativas"})]}),l.jsxs("select",{value:F||"all",onChange:v=>M(v.target.value==="all"?null:v.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[l.jsx("option",{value:"all",children:"Todas as categorias"}),oe.map(v=>l.jsx("option",{value:v.id,children:v.name},v.id))]}),l.jsxs("select",{value:D,onChange:v=>j(v.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[l.jsx("option",{value:"order",children:"Ordem de execução"}),l.jsx("option",{value:"date",children:"Data de criação"}),l.jsx("option",{value:"name",children:"Nome"}),l.jsx("option",{value:"count",children:"Número de vídeos"}),l.jsx("option",{value:"lastSync",children:"Última sincronização"})]}),l.jsx("button",{onClick:()=>L(v=>v==="asc"?"desc":"asc"),className:"p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600",title:`Ordenar ${k==="asc"?"decrescente":"crescente"}`,children:l.jsx(Ti,{className:"w-4 h-4"})}),l.jsxs("div",{className:"flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden",children:[l.jsx("button",{onClick:()=>m("grid"),className:`p-2 ${f==="grid"?"bg-blue-600 text-white":"bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600"}`,title:"Visualização em grade",children:l.jsx(Ri,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>m("list"),className:`p-2 ${f==="list"?"bg-blue-600 text-white":"bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600"}`,title:"Visualização em lista",children:l.jsx(Bi,{className:"w-4 h-4"})})]})]})]})}),l.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:l.jsx(Ge,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),l.jsxs("div",{className:"ml-3",children:[l.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total de Playlists"}),l.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.length})]})]})}),l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:l.jsx(Tt,{className:"w-5 h-5 text-green-600 dark:text-green-400"})}),l.jsxs("div",{className:"ml-3",children:[l.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Playlists Ativas"}),l.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.filter(v=>v.isActive).length})]})]})}),l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:l.jsx(zr,{className:"w-5 h-5 text-purple-600 dark:text-purple-400"})}),l.jsxs("div",{className:"ml-3",children:[l.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total de Vídeos"}),l.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.reduce((v,b)=>v+(b.videoCount||0),0)})]})]})}),l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:l.jsx(Xe,{className:"w-5 h-5 text-yellow-600 dark:text-yellow-400"})}),l.jsxs("div",{className:"ml-3",children:[l.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Com Programação"}),l.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.filter(v=>{var b;return(b=v.schedule)==null?void 0:b.enabled}).length})]})]})})]}),a?l.jsx("div",{className:"flex items-center justify-center py-12",children:l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Fe,{className:"w-5 h-5 animate-spin text-blue-600"}),l.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Carregando playlists..."})]})}):c?l.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx(Oi,{className:"w-5 h-5 text-red-600 dark:text-red-400 mr-2"}),l.jsx("span",{className:"text-red-800 dark:text-red-200",children:c})]})}):da.length===0?l.jsxs("div",{className:"text-center py-12",children:[l.jsx(Ge,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:y||w!==null||F?"Nenhuma playlist encontrada":"Nenhuma playlist cadastrada"}),l.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:y||w!==null||F?"Tente ajustar os filtros de busca":"Comece adicionando sua primeira playlist do YouTube"}),!y&&w===null&&!F&&l.jsxs("button",{onClick:()=>o(!0),className:"inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[l.jsx(pa,{className:"w-4 h-4"}),l.jsx("span",{children:"Adicionar Primeira Playlist"})]})]}):l.jsx(Ac,{onDragEnd:Pr,children:l.jsx(wi,{droppableId:"playlists",children:v=>l.jsxs("div",{...v.droppableProps,ref:v.innerRef,className:f==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:[da.map((b,C)=>l.jsx(pu,{draggableId:b.id,index:C,children:(P,O)=>l.jsx("div",{ref:P.innerRef,...P.draggableProps,...P.dragHandleProps,className:O.isDragging?"opacity-75":"",children:f==="grid"?l.jsx(yu,{playlist:b,onDelete:je,onSync:ge,onToggle:Te,onEdit:()=>ut(b),onSetDefault:Ve,onOpenDetails:p,onSchedule:ca,onCopyLink:qe,onSetCollaborative:He,onReorderByVotes:xe,formatDate:Re,getStatusColor:Ce,getCategoryColor:Ye,formatDuration:nr}):l.jsx(xu,{playlist:b,onDelete:je,onSync:ge,onToggle:Te,onEdit:()=>ut(b),onSetDefault:Ve,onOpenDetails:p,onSchedule:ca,onCopyLink:qe,onSetCollaborative:He,onReorderByVotes:xe,formatDate:Re,getStatusColor:Ce,getCategoryColor:Ye,formatDuration:nr})})},b.id)),v.placeholder]})})}),l.jsx(kr,{children:i&&l.jsx(he.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs(he.div,{initial:{scale:.9,y:20},animate:{scale:1,y:0},exit:{scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[l.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Nova Playlist"}),l.jsx("button",{onClick:()=>o(!1),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:l.jsx(Br,{className:"w-5 h-5"})})]})}),l.jsxs("div",{className:"p-6 space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome da Playlist *"}),l.jsx("input",{type:"text",value:E.name,onChange:v=>B(b=>({...b,name:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Música Ambiente Almoço"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),l.jsx("textarea",{value:E.description,onChange:v=>B(b=>({...b,description:v.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descreva o estilo e propósito desta playlist"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"URL da Playlist do YouTube *"}),l.jsx("input",{type:"url",value:E.youtubeUrl,onChange:v=>B(b=>({...b,youtubeUrl:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"https://www.youtube.com/playlist?list=..."})]}),l.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Categoria"}),l.jsxs("select",{value:E.category,onChange:v=>B(b=>({...b,category:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[l.jsx("option",{value:"",children:"Selecione uma categoria"}),oe.map(v=>l.jsx("option",{value:v.id,children:v.name},v.id))]})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Volume (",E.volume,"%)"]}),l.jsx("input",{type:"range",min:"0",max:"100",value:E.volume,onChange:v=>B(b=>({...b,volume:parseInt(v.target.value)})),className:"w-full"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tags (separadas por vírgula)"}),l.jsx("input",{type:"text",value:E.tags,onChange:v=>B(b=>({...b,tags:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"jazz, relaxante, ambiente"})]}),l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",id:"isActive",checked:E.isActive,onChange:v=>B(b=>({...b,isActive:v.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"}),l.jsx("label",{htmlFor:"isActive",className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Playlist ativa"})]}),l.jsxs("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center mb-3",children:[l.jsx("input",{type:"checkbox",id:"scheduleEnabled",checked:E.schedule.enabled,onChange:v=>B(b=>({...b,schedule:{...b.schedule,enabled:v.target.checked}})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"}),l.jsx("label",{htmlFor:"scheduleEnabled",className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Programar horários de reprodução"})]}),E.schedule.enabled&&l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Início"}),l.jsx("input",{type:"time",value:E.schedule.startTime,onChange:v=>B(b=>({...b,schedule:{...b.schedule,startTime:v.target.value}})),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Fim"}),l.jsx("input",{type:"time",value:E.schedule.endTime,onChange:v=>B(b=>({...b,schedule:{...b.schedule,endTime:v.target.value}})),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2",children:"Dias da semana"}),l.jsx("div",{className:"grid grid-cols-4 sm:grid-cols-7 gap-2",children:ye.map(v=>l.jsxs("label",{className:"flex items-center text-xs",children:[l.jsx("input",{type:"checkbox",checked:E.schedule.days.includes(v.id),onChange:b=>{const C=b.target.checked?[...E.schedule.days,v.id]:E.schedule.days.filter(P=>P!==v.id);B(P=>({...P,schedule:{...P.schedule,days:C}}))},className:"w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600 mr-1"}),l.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:v.label})]},v.id))})]})]})]})]}),l.jsxs("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3",children:[l.jsx("button",{onClick:()=>o(!1),className:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:"Cancelar"}),l.jsxs("button",{onClick:Ue,disabled:a||!E.name.trim()||!E.youtubeUrl.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2",children:[a&&l.jsx(Fe,{className:"w-4 h-4 animate-spin"}),l.jsx("span",{children:"Adicionar Playlist"})]})]})]})})}),l.jsx(kr,{children:s&&l.jsx(he.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs(he.div,{initial:{scale:.9,y:20},animate:{scale:1,y:0},exit:{scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[l.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Editar Playlist"}),l.jsx("button",{onClick:()=>d(null),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:l.jsx(Br,{className:"w-5 h-5"})})]})}),l.jsxs("div",{className:"p-6 space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome da Playlist *"}),l.jsx("input",{type:"text",value:s.name,onChange:v=>d(b=>b?{...b,name:v.target.value}:null),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),l.jsx("textarea",{value:s.description,onChange:v=>d(b=>b?{...b,description:v.target.value}:null),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"URL da Playlist do YouTube"}),l.jsx("input",{type:"url",value:s.youtubeUrl||"",onChange:v=>d(b=>b?{...b,youtubeUrl:v.target.value}:null),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),l.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Categoria"}),l.jsxs("select",{value:s.category||"",onChange:v=>d(b=>b?{...b,category:v.target.value}:null),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[l.jsx("option",{value:"",children:"Selecione uma categoria"}),oe.map(v=>l.jsx("option",{value:v.id,children:v.name},v.id))]})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Volume (",s.volume||80,"%)"]}),l.jsx("input",{type:"range",min:"0",max:"100",value:s.volume||80,onChange:v=>d(b=>b?{...b,volume:parseInt(v.target.value)}:null),className:"w-full"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tags (separadas por vírgula)"}),l.jsx("input",{type:"text",value:((ua=s.tags)==null?void 0:ua.join(", "))||"",onChange:v=>d(b=>b?{...b,tags:v.target.value.split(",").map(C=>C.trim()).filter(Boolean)}:null),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",id:"editIsActive",checked:s.isActive??!0,onChange:v=>d(b=>b?{...b,isActive:v.target.checked}:null),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"}),l.jsx("label",{htmlFor:"editIsActive",className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Playlist ativa"})]})]}),l.jsxs("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3",children:[l.jsx("button",{onClick:()=>d(null),className:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:"Cancelar"}),l.jsxs("button",{onClick:Si,disabled:a||!s.name.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2",children:[a&&l.jsx(Fe,{className:"w-4 h-4 animate-spin"}),l.jsx("span",{children:"Salvar Alterações"})]})]})]})})}),l.jsx(kr,{children:Q&&T&&l.jsx(he.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs(he.div,{initial:{scale:.9,y:20},animate:{scale:1,y:0},exit:{scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-md",children:[l.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Programar Playlist"}),l.jsx("button",{onClick:()=>Y(!1),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:l.jsx(Br,{className:"w-5 h-5"})})]}),l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:T.name})]}),l.jsxs("div",{className:"p-6 space-y-4",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",id:"scheduleEnabledEdit",checked:q.enabled,onChange:v=>ae(b=>({...b,enabled:v.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"}),l.jsx("label",{htmlFor:"scheduleEnabledEdit",className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Habilitar programação automática"})]}),q.enabled&&l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Horário de início"}),l.jsx("input",{type:"time",value:q.startTime,onChange:v=>ae(b=>({...b,startTime:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Horário de fim"}),l.jsx("input",{type:"time",value:q.endTime,onChange:v=>ae(b=>({...b,endTime:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Dias da semana"}),l.jsx("div",{className:"space-y-2",children:ye.map(v=>l.jsxs("label",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",checked:q.days.includes(v.id),onChange:b=>{const C=b.target.checked?[...q.days,v.id]:q.days.filter(P=>P!==v.id);ae(P=>({...P,days:C}))},className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"}),l.jsx("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:v.label})]},v.id))})]})]})]}),l.jsxs("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3",children:[l.jsx("button",{onClick:()=>Y(!1),className:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:"Cancelar"}),l.jsxs("button",{onClick:Di,disabled:a,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2",children:[a&&l.jsx(Fe,{className:"w-4 h-4 animate-spin"}),l.jsx("span",{children:"Salvar Programação"})]})]})]})})}),l.jsx(kr,{children:g&&l.jsx(wu,{playlist:g,onClose:()=>p(null),onEdit:()=>{ut(g),p(null)},onSync:ge,formatDate:Re,formatDuration:nr})})]})};export{Pu as default};
//# sourceMappingURL=PlaylistManager-e94771c8.js.map
