"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketService = exports.WebSocketError = exports.SuggestionStatusDto = exports.VoteUpdateDto = exports.TableEventDto = exports.RestaurantEventDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const logger_1 = require("../utils/logger");
const redis_1 = require("../config/redis");
/**
 * Classes de validação para parâmetros de entrada
 */
class RestaurantEventDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RestaurantEventDto.prototype, "restaurantId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RestaurantEventDto.prototype, "event", void 0);
exports.RestaurantEventDto = RestaurantEventDto;
class TableEventDto extends RestaurantEventDto {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TableEventDto.prototype, "tableNumber", void 0);
exports.TableEventDto = TableEventDto;
class VoteUpdateDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], VoteUpdateDto.prototype, "restaurantId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], VoteUpdateDto.prototype, "suggestionId", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], VoteUpdateDto.prototype, "votes", void 0);
exports.VoteUpdateDto = VoteUpdateDto;
class SuggestionStatusDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SuggestionStatusDto.prototype, "restaurantId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SuggestionStatusDto.prototype, "suggestionId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SuggestionStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SuggestionStatusDto.prototype, "reason", void 0);
exports.SuggestionStatusDto = SuggestionStatusDto;
/**
 * Classe de erro personalizada para WebSocket
 */
class WebSocketError extends Error {
    constructor(message, code = "WEBSOCKET_ERROR", statusCode = 500, isOperational = true) {
        super(message);
        this.name = "WebSocketError";
        this.code = code;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.WebSocketError = WebSocketError;
/**
 * Serviço WebSocket melhorado para gerenciar eventos em tempo real
 * da aplicação de playlist interativa para restaurantes.
 *
 * @class WebSocketService
 * @description Gerencia conexões WebSocket, autenticação, validação de dados,
 * tratamento de erros e estatísticas de conexão.
 *
 * @example
 * ```typescript
 * const wsService = WebSocketService.getInstance(io);
 * wsService.notifyNewSuggestion('restaurant-123', suggestionData);
 * ```
 */
class WebSocketService {
    constructor(io) {
        this.authenticatedSockets = new Map();
        this.roomStats = new Map();
        this.userSockets = new Map(); // Map userId -> Set of socket IDs
        this.io = io;
        this.setupEventHandlers();
        this.startStatsCollection();
    }
    /**
     * Obtém a instância singleton do WebSocketService
     * @param io - Instância do Socket.IO Server
     * @returns Instância do WebSocketService
     */
    static getInstance(io) {
        if (!WebSocketService.instance && io) {
            WebSocketService.instance = new WebSocketService(io);
        }
        return WebSocketService.instance;
    }
    /**
     * Configura os manipuladores de eventos WebSocket
     * @private
     */
    setupEventHandlers() {
        this.io.on("connection", (socket) => {
            logger_1.logger.info(`Nova conexão WebSocket: ${socket.id}`);
            // Evento de autenticação
            socket.on("authenticate", async (data) => {
                try {
                    await this.authenticateSocket(socket, data);
                }
                catch (error) {
                    this.handleSocketError(socket, error, "AUTHENTICATION_FAILED");
                }
            });
            // Evento de junção a sala
            socket.on("joinRoom", async (data) => {
                try {
                    await this.handleJoinRoom(socket, data);
                }
                catch (error) {
                    this.handleSocketError(socket, error, "JOIN_ROOM_FAILED");
                }
            });
            // Evento de desconexão
            socket.on("disconnect", () => {
                this.handleDisconnect(socket);
            });
            // Evento de erro
            socket.on("error", (error) => {
                this.handleSocketError(socket, error, "SOCKET_ERROR");
            });
        });
    }
    /**
     * Autentica um socket usando token JWT
     * @private
     */
    async authenticateSocket(socket, data) {
        try {
            // Aqui você implementaria a verificação do JWT
            // Por simplicidade, vamos simular uma autenticação básica
            const { token, restaurantId } = data;
            if (!token) {
                throw new WebSocketError("Token de autenticação obrigatório", "MISSING_TOKEN", 401);
            }
            // Simular verificação de token (implementar JWT real em produção)
            const userData = this.verifyToken(token);
            this.authenticatedSockets.set(socket.id, {
                userId: userData.userId,
                role: userData.role,
                restaurantId: restaurantId || userData.restaurantId,
            });
            socket.emit("authenticated", { success: true, userId: userData.userId });
            logger_1.logger.info(`Socket ${socket.id} autenticado para usuário ${userData.userId}`);
        }
        catch (error) {
            socket.emit("authenticationError", {
                error: error instanceof WebSocketError
                    ? error.message
                    : "Falha na autenticação",
            });
            throw error;
        }
    }
    /**
     * Verifica token de autenticação (implementação simplificada)
     * @private
     */
    verifyToken(token) {
        // Em produção, implementar verificação JWT real
        if (token === "admin-token") {
            return {
                userId: "admin-user",
                role: "admin",
                restaurantId: "demo-restaurant",
            };
        }
        if (token === "client-token") {
            return { userId: "client-user", role: "client" };
        }
        throw new WebSocketError("Token inválido", "INVALID_TOKEN", 401);
    }
    /**
     * Manipula a entrada de um socket em uma sala
     * @private
     */
    async handleJoinRoom(socket, data) {
        const { restaurantId, tableNumber, role } = data;
        // Validar dados de entrada
        const validation = await this.validateRestaurantAccess(socket, restaurantId);
        if (!validation.isValid) {
            throw new WebSocketError(validation.error, "ACCESS_DENIED", 403);
        }
        // Definir salas baseadas no papel do usuário
        const rooms = [`restaurant-${restaurantId}`];
        if (role === "admin" || role === "staff") {
            rooms.push(`restaurant-${restaurantId}-admins`);
        }
        if (tableNumber) {
            rooms.push(`restaurant-${restaurantId}-table-${tableNumber}`);
        }
        // Juntar-se às salas
        for (const room of rooms) {
            await socket.join(room);
            this.updateRoomStats(room, 1);
        }
        socket.emit("joinedRoom", { restaurantId, tableNumber, rooms });
        logger_1.logger.info(`Socket ${socket.id} juntou-se às salas: ${rooms.join(", ")}`);
    }
    /**
     * Manipula a desconexão de um socket
     * @private
     */
    handleDisconnect(socket) {
        const authData = this.authenticatedSockets.get(socket.id);
        // Atualizar estatísticas das salas
        const rooms = Array.from(socket.rooms);
        rooms.forEach((room) => {
            if (room !== socket.id) {
                this.updateRoomStats(room, -1);
            }
        });
        this.authenticatedSockets.delete(socket.id);
        logger_1.logger.info(`Socket ${socket.id} desconectado`, {
            userId: authData?.userId,
            rooms: rooms.filter((room) => room !== socket.id),
        });
    }
    /**
     * Manipula erros de socket
     * @private
     */
    handleSocketError(socket, error, code) {
        const errorData = {
            code,
            message: error.message,
            timestamp: new Date().toISOString(),
            socketId: socket.id,
        };
        socket.emit("error", errorData);
        logger_1.logger.error(`Erro no socket ${socket.id}:`, errorData);
    }
    /**
     * Valida se um socket tem acesso a um restaurante
     * @private
     */
    async validateRestaurantAccess(socket, restaurantId) {
        const authData = this.authenticatedSockets.get(socket.id);
        if (!authData) {
            return { isValid: false, error: "Socket não autenticado" };
        }
        // Admins têm acesso a todos os restaurantes
        if (authData.role === "admin") {
            return { isValid: true };
        }
        // Verificar se o usuário tem acesso ao restaurante específico
        if (authData.restaurantId && authData.restaurantId !== restaurantId) {
            return { isValid: false, error: "Acesso negado ao restaurante" };
        }
        return { isValid: true };
    }
    /**
     * Atualiza estatísticas de salas
     * @private
     */
    updateRoomStats(room, delta) {
        const current = this.roomStats.get(room) || 0;
        const newCount = Math.max(0, current + delta);
        if (newCount === 0) {
            this.roomStats.delete(room);
        }
        else {
            this.roomStats.set(room, newCount);
        }
    }
    /**
     * Inicia coleta de estatísticas periódicas
     * @private
     */
    startStatsCollection() {
        setInterval(() => {
            this.collectAndCacheStats();
        }, 30000); // A cada 30 segundos
    }
    /**
     * Coleta e armazena estatísticas no Redis
     * @private
     */
    async collectAndCacheStats() {
        try {
            const stats = this.getConnectionStats();
            await redis_1.redisClient.getClient().setEx("websocket:stats", 300, // 5 minutos
            JSON.stringify(stats));
        }
        catch (error) {
            logger_1.logger.error("Erro ao coletar estatísticas WebSocket:", error);
        }
    }
    /**
     * Valida dados de entrada usando class-validator
     * @private
     */
    async validateInput(dto, data) {
        const instance = (0, class_transformer_1.plainToClass)(dto, data);
        const errors = await (0, class_validator_1.validate)(instance);
        if (errors.length > 0) {
            const errorMessages = errors
                .map((error) => Object.values(error.constraints || {}).join(", "))
                .join("; ");
            throw new WebSocketError(`Dados inválidos: ${errorMessages}`, "VALIDATION_ERROR", 400);
        }
        return instance;
    }
    /**
     * Verifica se uma sala está ativa antes de emitir eventos
     * @private
     */
    isRoomActive(room) {
        return (this.roomStats.get(room) || 0) > 0;
    }
    /**
     * Emite evento para todos os clientes de um restaurante com validação
     * @param restaurantId - ID do restaurante
     * @param event - Nome do evento
     * @param data - Dados do evento
     */
    async emitToRestaurant(restaurantId, event, data) {
        try {
            // Validar entrada
            await this.validateInput(RestaurantEventDto, {
                restaurantId,
                event,
                data,
            });
            const room = `restaurant-${restaurantId}`;
            // Verificar se a sala está ativa
            if (!this.isRoomActive(room)) {
                logger_1.logger.warn(`Tentativa de emitir para sala inativa: ${room}`);
                return;
            }
            const eventData = {
                event,
                data,
                timestamp: new Date().toISOString(),
                restaurantId,
            };
            this.io.to(room).emit(event, eventData);
            logger_1.logger.info(`Evento ${event} emitido para restaurante ${restaurantId}`, {
                room,
                dataSize: JSON.stringify(data).length,
                connections: this.roomStats.get(room) || 0,
            });
        }
        catch (error) {
            logger_1.logger.error("Erro ao emitir evento para restaurante:", error);
            throw new WebSocketError(`Falha ao emitir evento: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "EMIT_ERROR");
        }
    }
    /**
     * Alias para broadcastToRestaurant (usado no PlaybackService)
     * @param restaurantId - ID do restaurante
     * @param event - Nome do evento
     * @param data - Dados do evento
     */
    async broadcastToRestaurant(restaurantId, event, data) {
        await this.emitToRestaurant(restaurantId, event, data);
    }
    /**
     * Emite evento para um usuário específico
     * @param userId - ID do usuário
     * @param event - Nome do evento
     * @param data - Dados do evento
     */
    async emitToUser(userId, event, data) {
        try {
            const socketIds = this.userSockets.get(userId);
            if (socketIds && socketIds.size > 0) {
                for (const socketId of socketIds) {
                    this.io.to(socketId).emit(event, data);
                }
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro ao emitir evento para usuário ${userId}:`, error);
        }
    }
    /**
     * Broadcast global para todos os conectados
     * @param event - Nome do evento
     * @param data - Dados do evento
     */
    async broadcast(event, data) {
        try {
            this.io.emit(event, data);
        }
        catch (error) {
            logger_1.logger.error("Erro ao fazer broadcast:", error);
        }
    }
    /**
     * Emite evento para uma mesa específica com validação
     * @param restaurantId - ID do restaurante
     * @param tableNumber - Número da mesa
     * @param event - Nome do evento
     * @param data - Dados do evento
     */
    async emitToTable(restaurantId, tableNumber, event, data) {
        try {
            // Validar entrada
            await this.validateInput(TableEventDto, {
                restaurantId,
                tableNumber,
                event,
                data,
            });
            const room = `restaurant-${restaurantId}-table-${tableNumber}`;
            // Verificar se a sala está ativa
            if (!this.isRoomActive(room)) {
                logger_1.logger.warn(`Tentativa de emitir para mesa inativa: ${room}`);
                return;
            }
            const eventData = {
                event,
                data,
                timestamp: new Date().toISOString(),
                restaurantId,
            };
            this.io.to(room).emit(event, eventData);
            logger_1.logger.info(`Evento ${event} emitido para mesa ${tableNumber} do restaurante ${restaurantId}`, {
                room,
                dataSize: JSON.stringify(data).length,
                connections: this.roomStats.get(room) || 0,
            });
        }
        catch (error) {
            logger_1.logger.error("Erro ao emitir evento para mesa:", error);
            throw new WebSocketError(`Falha ao emitir evento para mesa: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "EMIT_TABLE_ERROR");
        }
    }
    /**
     * Emite evento para administradores do restaurante com autenticação
     * @param restaurantId - ID do restaurante
     * @param event - Nome do evento
     * @param data - Dados do evento
     * @param requiredRole - Papel mínimo necessário (padrão: 'admin')
     */
    async emitToAdmins(restaurantId, event, data, requiredRole = "admin") {
        try {
            // Validar entrada
            await this.validateInput(RestaurantEventDto, {
                restaurantId,
                event,
                data,
            });
            const room = `restaurant-${restaurantId}-admins`;
            // Verificar se a sala está ativa
            if (!this.isRoomActive(room)) {
                logger_1.logger.warn(`Tentativa de emitir para admins inativos: ${room}`);
                return;
            }
            // Verificar autorização dos sockets conectados
            const authorizedSockets = Array.from(this.authenticatedSockets.entries())
                .filter(([socketId, authData]) => {
                const socket = this.io.sockets.sockets.get(socketId);
                return (socket &&
                    socket.rooms.has(room) &&
                    this.hasRequiredRole(authData.role, requiredRole));
            })
                .map(([socketId]) => socketId);
            if (authorizedSockets.length === 0) {
                logger_1.logger.warn(`Nenhum admin autorizado encontrado para ${room}`);
                return;
            }
            const eventData = {
                event,
                data,
                timestamp: new Date().toISOString(),
                restaurantId,
            };
            // Emitir apenas para sockets autorizados
            authorizedSockets.forEach((socketId) => {
                this.io.to(socketId).emit(event, eventData);
            });
            logger_1.logger.info(`Evento ${event} emitido para admins do restaurante ${restaurantId}`, {
                room,
                authorizedSockets: authorizedSockets.length,
                requiredRole,
                dataSize: JSON.stringify(data).length,
            });
        }
        catch (error) {
            logger_1.logger.error("Erro ao emitir evento para admins:", error);
            throw new WebSocketError(`Falha ao emitir evento para admins: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "EMIT_ADMIN_ERROR");
        }
    }
    /**
     * Verifica se um papel tem a autorização necessária
     * @private
     */
    hasRequiredRole(userRole, requiredRole) {
        const roleHierarchy = ["client", "staff", "moderator", "admin"];
        const userLevel = roleHierarchy.indexOf(userRole);
        const requiredLevel = roleHierarchy.indexOf(requiredRole);
        return userLevel >= requiredLevel;
    }
    // Emitir evento global para todos os clientes conectados
    emitGlobal(event, data) {
        try {
            this.io.emit(event, data);
            logger_1.logger.info(`Evento global ${event} emitido`, { data });
        }
        catch (error) {
            logger_1.logger.error("Erro ao emitir evento global:", error);
        }
    }
    /**
     * Notifica sobre uma nova sugestão musical
     * @param restaurantId - ID do restaurante
     * @param suggestion - Dados da sugestão
     */
    async notifyNewSuggestion(restaurantId, suggestion) {
        try {
            // Emitir para todos os clientes
            await this.emitToRestaurant(restaurantId, "newSuggestion", suggestion);
            // Emitir versão detalhada para admins
            const adminData = {
                ...suggestion,
                metadata: {
                    clientIP: suggestion.sessionId,
                    timestamp: suggestion.createdAt,
                    requiresModeration: !suggestion.isPaid,
                },
            };
            await this.emitToAdmins(restaurantId, "newSuggestionAdmin", adminData);
            logger_1.logger.info(`Nova sugestão notificada: ${suggestion.title} no restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar nova sugestão:", error);
            throw new WebSocketError("Falha ao notificar nova sugestão", "NOTIFY_SUGGESTION_ERROR");
        }
    }
    /**
     * Notifica sobre sugestão prioritária (paga)
     * @param restaurantId - ID do restaurante
     * @param suggestion - Dados da sugestão prioritária
     */
    async notifyPrioritySuggestion(restaurantId, suggestion) {
        try {
            const priorityData = {
                ...suggestion,
                priority: true,
                paymentConfirmed: true,
                timestamp: new Date().toISOString(),
            };
            await this.emitToRestaurant(restaurantId, "prioritySuggestion", priorityData);
            await this.emitToAdmins(restaurantId, "adminPrioritySuggestion", priorityData);
            logger_1.logger.info(`Sugestão prioritária notificada: ${suggestion.title} no restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar sugestão prioritária:", error);
            throw new WebSocketError("Falha ao notificar sugestão prioritária", "NOTIFY_PRIORITY_ERROR");
        }
    }
    /**
     * Notifica sobre atualização de votos
     * @param restaurantId - ID do restaurante
     * @param suggestionId - ID da sugestão
     * @param votes - Dados dos votos atualizados
     */
    async notifyVoteUpdate(restaurantId, suggestionId, votes) {
        try {
            // Validar entrada
            await this.validateInput(VoteUpdateDto, {
                restaurantId,
                suggestionId,
                votes: votes.total,
            });
            const voteData = {
                suggestionId,
                votes,
                timestamp: new Date().toISOString(),
            };
            await this.emitToRestaurant(restaurantId, "voteUpdate", voteData);
            logger_1.logger.info(`Votos atualizados para sugestão ${suggestionId}: ${votes.total} votos`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar atualização de votos:", error);
            throw new WebSocketError("Falha ao notificar atualização de votos", "NOTIFY_VOTE_ERROR");
        }
    }
    /**
     * Notifica sobre mudança de status de sugestão
     * @param restaurantId - ID do restaurante
     * @param suggestionId - ID da sugestão
     * @param status - Novo status
     * @param reason - Motivo da mudança (opcional)
     */
    async notifySuggestionStatusUpdate(restaurantId, suggestionId, status, reason) {
        try {
            // Validar entrada
            await this.validateInput(SuggestionStatusDto, {
                restaurantId,
                suggestionId,
                status,
                reason,
            });
            const statusData = {
                suggestionId,
                status,
                reason,
                timestamp: new Date().toISOString(),
            };
            await this.emitToRestaurant(restaurantId, "suggestionStatusUpdate", statusData);
            // Notificar admins com informações adicionais
            const adminStatusData = {
                ...statusData,
                metadata: {
                    moderatedBy: "system",
                    previousStatus: "pending", // Em produção, buscar do banco
                },
            };
            await this.emitToAdmins(restaurantId, "suggestionStatusUpdateAdmin", adminStatusData);
            logger_1.logger.info(`Status da sugestão ${suggestionId} atualizado para ${status}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar atualização de status:", error);
            throw new WebSocketError("Falha ao notificar atualização de status", "NOTIFY_STATUS_ERROR");
        }
    }
    /**
     * Notifica sobre início de reprodução
     * @param restaurantId - ID do restaurante
     * @param track - Dados da faixa
     */
    async notifyPlaybackStart(restaurantId, track) {
        try {
            const playbackData = {
                track,
                event: "start",
                timestamp: new Date().toISOString(),
                serverTime: Date.now(),
            };
            await this.emitToRestaurant(restaurantId, "playbackStart", playbackData);
            logger_1.logger.info(`Reprodução iniciada: ${track.title} no restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar início de reprodução:", error);
            throw new WebSocketError("Falha ao notificar início de reprodução", "NOTIFY_PLAYBACK_START_ERROR");
        }
    }
    /**
     * Notifica sobre fim de reprodução
     * @param restaurantId - ID do restaurante
     * @param track - Dados da faixa
     */
    async notifyPlaybackEnd(restaurantId, track) {
        try {
            const playbackData = {
                track,
                event: "end",
                timestamp: new Date().toISOString(),
                serverTime: Date.now(),
            };
            await this.emitToRestaurant(restaurantId, "playbackEnd", playbackData);
            logger_1.logger.info(`Reprodução finalizada: ${track.title} no restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar fim de reprodução:", error);
            throw new WebSocketError("Falha ao notificar fim de reprodução", "NOTIFY_PLAYBACK_END_ERROR");
        }
    }
    /**
     * Notifica sobre atualização da fila de reprodução
     * @param restaurantId - ID do restaurante
     * @param queue - Fila atualizada
     */
    async notifyQueueUpdate(restaurantId, queue) {
        try {
            const queueData = {
                queue,
                totalItems: queue.length,
                priorityItems: queue.filter((item) => item.isPaid).length,
                normalItems: queue.filter((item) => !item.isPaid).length,
                estimatedDuration: queue.reduce((total, item) => total + item.duration, 0),
                timestamp: new Date().toISOString(),
            };
            await this.emitToRestaurant(restaurantId, "queueUpdate", queueData);
            logger_1.logger.info(`Fila atualizada para restaurante ${restaurantId}: ${queue.length} itens`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar atualização da fila:", error);
            throw new WebSocketError("Falha ao notificar atualização da fila", "NOTIFY_QUEUE_ERROR");
        }
    }
    /**
     * Notifica atualização de reprodução
     * @param restaurantId - ID do restaurante
     * @param data - Dados da atualização
     */
    async notifyPlaybackUpdate(restaurantId, data) {
        try {
            const updateData = {
                ...data,
                timestamp: new Date().toISOString(),
                serverTime: Date.now(),
            };
            await this.emitToRestaurant(restaurantId, "playbackUpdate", updateData);
            logger_1.logger.info(`Atualização de reprodução notificada para restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar atualização de reprodução:", error);
            throw new WebSocketError("Falha ao notificar atualização de reprodução", "NOTIFY_PLAYBACK_UPDATE_ERROR");
        }
    }
    /**
     * Notifica sobre pagamento PIX aprovado
     * @param restaurantId - ID do restaurante
     * @param paymentId - ID do pagamento
     * @param suggestionId - ID da sugestão
     */
    async notifyPaymentApproved(restaurantId, paymentId, suggestionId) {
        try {
            const paymentData = {
                paymentId,
                suggestionId,
                status: "approved",
                timestamp: new Date().toISOString(),
            };
            await this.emitToRestaurant(restaurantId, "paymentApproved", paymentData);
            await this.emitToAdmins(restaurantId, "adminPaymentApproved", paymentData);
            logger_1.logger.info(`Pagamento aprovado: ${paymentId} para sugestão ${suggestionId}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar pagamento aprovado:", error);
            throw new WebSocketError("Falha ao notificar pagamento aprovado", "NOTIFY_PAYMENT_APPROVED_ERROR");
        }
    }
    /**
     * Notifica sobre erro de pagamento
     * @param restaurantId - ID do restaurante
     * @param paymentId - ID do pagamento
     * @param error - Descrição do erro
     */
    async notifyPaymentError(restaurantId, paymentId, error) {
        try {
            const errorData = {
                paymentId,
                error,
                status: "failed",
                timestamp: new Date().toISOString(),
            };
            await this.emitToRestaurant(restaurantId, "paymentError", errorData);
            await this.emitToAdmins(restaurantId, "adminPaymentError", errorData);
            logger_1.logger.error(`Erro de pagamento: ${paymentId} - ${error}`);
        }
        catch (error) {
            logger_1.logger.error("Erro ao notificar erro de pagamento:", error);
            throw new WebSocketError("Falha ao notificar erro de pagamento", "NOTIFY_PAYMENT_ERROR_ERROR");
        }
    }
    /**
     * Obtém estatísticas detalhadas de conexões
     * @returns Estatísticas completas das conexões WebSocket
     */
    getConnectionStats() {
        const sockets = this.io.sockets.sockets;
        const restaurantConnections = {};
        const adminConnections = {};
        const tableConnections = {};
        const activeRooms = [];
        // Analisar conexões por tipo
        this.authenticatedSockets.forEach((authData, socketId) => {
            const socket = sockets.get(socketId);
            if (!socket)
                return;
            const { restaurantId, role } = authData;
            if (restaurantId) {
                restaurantConnections[restaurantId] =
                    (restaurantConnections[restaurantId] || 0) + 1;
                if (role === "admin" || role === "staff") {
                    adminConnections[restaurantId] =
                        (adminConnections[restaurantId] || 0) + 1;
                }
            }
            // Analisar salas de mesa
            socket.rooms.forEach((room) => {
                if (room.includes("-table-")) {
                    const [, restaurantPart, , tableNumber] = room.split("-");
                    const restId = restaurantPart;
                    if (!tableConnections[restId]) {
                        tableConnections[restId] = {};
                    }
                    tableConnections[restId][tableNumber] =
                        (tableConnections[restId][tableNumber] || 0) + 1;
                }
            });
        });
        // Coletar salas ativas
        this.roomStats.forEach((count, room) => {
            if (count > 0) {
                activeRooms.push(room);
            }
        });
        return {
            totalConnections: sockets.size,
            activeRooms,
            restaurantConnections,
            adminConnections,
            tableConnections,
            timestamp: new Date().toISOString(),
        };
    }
    /**
     * Obtém estatísticas de uma sala específica
     * @param room - Nome da sala
     * @returns Número de conexões na sala
     */
    getRoomStats(room) {
        return this.roomStats.get(room) || 0;
    }
    /**
     * Limpa conexões inativas e atualiza estatísticas
     */
    async cleanupInactiveConnections() {
        try {
            const sockets = this.io.sockets.sockets;
            // Remover sockets autenticados que não existem mais
            for (const [socketId] of this.authenticatedSockets) {
                if (!sockets.has(socketId)) {
                    this.authenticatedSockets.delete(socketId);
                }
            }
            // Atualizar estatísticas das salas
            const activeRooms = new Map();
            sockets.forEach((socket) => {
                socket.rooms.forEach((room) => {
                    if (room !== socket.id) {
                        activeRooms.set(room, (activeRooms.get(room) || 0) + 1);
                    }
                });
            });
            this.roomStats = activeRooms;
            logger_1.logger.info("Limpeza de conexões inativas concluída", {
                authenticatedSockets: this.authenticatedSockets.size,
                activeRooms: this.roomStats.size,
            });
        }
        catch (error) {
            logger_1.logger.error("Erro na limpeza de conexões:", error);
        }
    }
}
exports.WebSocketService = WebSocketService;
//# sourceMappingURL=WebSocketService.js.map