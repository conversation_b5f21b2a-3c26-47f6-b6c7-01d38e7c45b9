"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.playlistReorderService = exports.PlaylistReorderService = void 0;
const database_1 = require("../config/database");
const Restaurant_1 = require("../models/Restaurant");
const Playlist_1 = require("../models/Playlist");
const Suggestion_1 = require("../models/Suggestion");
const YouTubeOAuthService_1 = require("./YouTubeOAuthService");
const CollaborativePlaylistService_1 = require("./CollaborativePlaylistService");
const logger_1 = require("../utils/logger");
const typeorm_1 = require("typeorm");
const WebSocketService_1 = require("./WebSocketService");
const PlaylistSchedule_1 = require("../models/PlaylistSchedule");
class PlaylistReorderService {
    constructor() {
        this.restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        this.playlistRepository = database_1.AppDataSource.getRepository(Playlist_1.Playlist);
        this.suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        this.scheduleRepository = database_1.AppDataSource.getRepository(PlaylistSchedule_1.PlaylistSchedule);
        this.reorderInterval = null;
        this.isRunning = false;
        this.webSocketService = WebSocketService_1.WebSocketService.getInstance();
        logger_1.logger.info("🔄 PlaylistReorderService inicializado");
    }
    static getInstance() {
        if (!PlaylistReorderService.instance) {
            PlaylistReorderService.instance = new PlaylistReorderService();
        }
        return PlaylistReorderService.instance;
    }
    /**
     * 🚀 INICIAR REORDENAÇÃO AUTOMÁTICA
     * Executa a cada 5 minutos (300.000ms)
     */
    startAutoReorder() {
        if (this.isRunning) {
            logger_1.logger.warn("⚠️ Reordenação automática já está em execução");
            return;
        }
        this.isRunning = true;
        logger_1.logger.info("🚀 Iniciando reordenação automática a cada 5 minutos");
        // Executar imediatamente na inicialização
        this.executeReorderCycle();
        // Agendar execução a cada 5 minutos
        this.reorderInterval = setInterval(() => {
            this.executeReorderCycle();
        }, 5 * 60 * 1000); // 5 minutos
    }
    /**
     * 🛑 PARAR REORDENAÇÃO AUTOMÁTICA
     */
    stopAutoReorder() {
        if (this.reorderInterval) {
            clearInterval(this.reorderInterval);
            this.reorderInterval = null;
        }
        this.isRunning = false;
        logger_1.logger.info("🛑 Reordenação automática parada");
    }
    /**
     * 🔄 EXECUTAR CICLO DE REORDENAÇÃO
     * Processa todos os restaurantes ativos
     */
    async executeReorderCycle() {
        const startTime = new Date();
        logger_1.logger.info("🔄 Iniciando ciclo de reordenação automática");
        try {
            // Buscar restaurantes ativos com playlists
            const restaurants = await this.restaurantRepository.find({
                where: { isActive: true },
                relations: ["playlists"],
            });
            const results = [];
            for (const restaurant of restaurants) {
                try {
                    const result = await this.reorderRestaurantPlaylists(restaurant.id);
                    results.push(result);
                }
                catch (error) {
                    logger_1.logger.error(`❌ Erro ao reordenar playlists do restaurante ${restaurant.id}:`, error);
                    results.push({
                        success: false,
                        restaurantId: restaurant.id,
                        tracksReordered: 0,
                        message: `Erro: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
                        timestamp: new Date(),
                    });
                }
            }
            // Log do resultado do ciclo
            const successful = results.filter((r) => r.success).length;
            const failed = results.filter((r) => !r.success).length;
            const totalTracks = results.reduce((sum, r) => sum + r.tracksReordered, 0);
            const duration = Date.now() - startTime.getTime();
            logger_1.logger.info(`✅ Ciclo de reordenação concluído em ${duration}ms:`);
            logger_1.logger.info(`   📊 ${successful} sucessos, ${failed} falhas`);
            logger_1.logger.info(`   🎵 ${totalTracks} faixas reordenadas no total`);
        }
        catch (error) {
            logger_1.logger.error("❌ Erro no ciclo de reordenação:", error);
        }
    }
    /**
     * 🏪 REORDENAR PLAYLISTS DE UM RESTAURANTE
     */
    async reorderRestaurantPlaylists(restaurantId) {
        try {
            // Verificar se restaurante está autenticado com YouTube
            const isAuthenticated = await YouTubeOAuthService_1.youtubeOAuthService.isAuthenticated(restaurantId);
            if (!isAuthenticated) {
                return {
                    success: false,
                    restaurantId,
                    tracksReordered: 0,
                    message: "Restaurante não autenticado com YouTube",
                    timestamp: new Date(),
                };
            }
            // 1) Verificar agendamento e ativar playlist do slot atual (se houver)
            const schedules = await this.scheduleRepository.find({
                where: { restaurantId, isActive: true },
            });
            let activePlaylist = await this.playlistRepository.findOne({
                where: {
                    restaurant: { id: restaurantId },
                    status: Playlist_1.PlaylistStatus.ACTIVE,
                    youtubePlaylistId: (0, typeorm_1.Not)((0, typeorm_1.IsNull)()),
                },
                order: { isDefault: "DESC", createdAt: "DESC" },
            });
            if (schedules.length) {
                for (const schedule of schedules) {
                    const current = schedule.getCurrentActivePlaylist();
                    if (current?.playlistId) {
                        // Se a playlist agendada é diferente da ACTIVE atual, trocá-la
                        if (!activePlaylist || activePlaylist.id !== current.playlistId) {
                            const scheduledPlaylist = await this.playlistRepository.findOne({
                                where: {
                                    id: current.playlistId,
                                    restaurant: { id: restaurantId },
                                },
                            });
                            if (scheduledPlaylist) {
                                // Desativar outras ACTIVE e ativar a agendada
                                await this.playlistRepository.update({ restaurant: { id: restaurantId } }, { status: Playlist_1.PlaylistStatus.INACTIVE });
                                scheduledPlaylist.status = Playlist_1.PlaylistStatus.ACTIVE;
                                await this.playlistRepository.save(scheduledPlaylist);
                                activePlaylist = scheduledPlaylist;
                                logger_1.logger.info(`⏰ Playlist agendada ativada: ${scheduledPlaylist.name} (${scheduledPlaylist.id})`);
                            }
                        }
                        break; // Um schedule ativo por vez
                    }
                }
            }
            // Buscar playlist ativa principal (após considerar agendamento)
            if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
                return {
                    success: false,
                    restaurantId,
                    tracksReordered: 0,
                    message: "Nenhuma playlist ativa encontrada",
                    timestamp: new Date(),
                };
            }
            // Buscar ranking de supervotos
            const rankingResult = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, 50);
            if (!rankingResult.success ||
                !rankingResult.data ||
                rankingResult.data.length === 0) {
                return {
                    success: true,
                    restaurantId,
                    playlistId: activePlaylist.id,
                    tracksReordered: 0,
                    message: "Nenhuma música com votos para reordenar",
                    timestamp: new Date(),
                };
            }
            // Preparar nova ordem baseada no ranking, restrita às faixas da playlist
            const allowed = new Set((activePlaylist.tracks ?? []).map((t) => t.youtubeVideoId));
            const filtered = rankingResult.data.filter((it) => allowed.has(it.youtubeVideoId));
            const newOrder = filtered.map((item, index) => ({
                videoId: item.youtubeVideoId,
                position: index,
                title: item.title,
                voteCount: item.voteCount,
                isPaid: item.isPaid,
                paymentAmount: item.paymentAmount,
            }));
            if (newOrder.length === 0) {
                return {
                    success: true,
                    restaurantId,
                    playlistId: activePlaylist.id,
                    tracksReordered: 0,
                    message: "Nenhuma música elegível para reordenar (todas fora da playlist)",
                    timestamp: new Date(),
                };
            }
            // Reordenar playlist no YouTube
            const reorderSuccess = await YouTubeOAuthService_1.youtubeOAuthService.reorderPlaylist(restaurantId, activePlaylist.youtubePlaylistId, newOrder);
            if (!reorderSuccess) {
                return {
                    success: false,
                    restaurantId,
                    playlistId: activePlaylist.id,
                    tracksReordered: 0,
                    message: "Falha ao reordenar playlist no YouTube",
                    timestamp: new Date(),
                };
            }
            // Atualizar ordem local na base de dados
            if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
                activePlaylist.tracks = activePlaylist.tracks
                    .map((track) => {
                    const newOrderItem = newOrder.find((item) => item.videoId === track.youtubeVideoId);
                    return {
                        ...track,
                        position: newOrderItem ? newOrderItem.position : track.position,
                    };
                })
                    .sort((a, b) => a.position - b.position);
                await this.playlistRepository.save(activePlaylist);
            }
            logger_1.logger.info(`🎵 Playlist reordenada: ${activePlaylist.name} (${newOrder.length} faixas)`);
            // 🔔 Notificar reordenação em tempo real
            await this.notifyPlaylistReorder(restaurantId, {
                playlistId: activePlaylist.id,
                playlistName: activePlaylist.name,
                tracksReordered: newOrder.length,
                topTracks: newOrder.slice(0, 5), // Top 5 músicas
            });
            return {
                success: true,
                restaurantId,
                playlistId: activePlaylist.id,
                tracksReordered: newOrder.length,
                message: `Playlist reordenada com sucesso (${newOrder.length} faixas)`,
                timestamp: new Date(),
            };
        }
        catch (error) {
            logger_1.logger.error(`❌ Erro ao reordenar playlists do restaurante ${restaurantId}:`, error);
            return {
                success: false,
                restaurantId,
                tracksReordered: 0,
                message: `Erro: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
                timestamp: new Date(),
            };
        }
    }
    /**
     * 📊 OBTER STATUS DO SERVIÇO
     */
    getStatus() {
        const nextExecution = this.isRunning && this.reorderInterval
            ? new Date(Date.now() + 5 * 60 * 1000)
            : undefined;
        return {
            isRunning: this.isRunning,
            nextExecution,
            uptime: process.uptime(),
        };
    }
    /**
     * 🔔 NOTIFICAR REORDENAÇÃO DE PLAYLIST
     */
    async notifyPlaylistReorder(restaurantId, reorderData) {
        try {
            const notificationData = {
                type: "playlist_reordered",
                playlist: {
                    id: reorderData.playlistId,
                    name: reorderData.playlistName,
                    tracksReordered: reorderData.tracksReordered,
                },
                topTracks: reorderData.topTracks.map((track, index) => ({
                    position: index + 1,
                    title: track.title,
                    artist: track.artist,
                    voteCount: track.voteCount,
                    isPaid: track.isPaid,
                    paymentAmount: track.paymentAmount,
                })),
                timestamp: new Date().toISOString(),
                message: `Playlist reordenada automaticamente - ${reorderData.tracksReordered} músicas`,
            };
            // Notificar todos os clientes do restaurante
            await this.webSocketService.emitToRestaurant(restaurantId, "playlistReordered", notificationData);
            // Notificar administradores com dados detalhados
            await this.webSocketService.emitToAdmins(restaurantId, "playlistReorderedAdmin", {
                ...notificationData,
                adminDetails: {
                    autoReorderEnabled: true,
                    nextReorderTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // +5 min
                },
            });
            logger_1.logger.info(`🔔 Notificação de reordenação enviada: ${reorderData.playlistName}`);
        }
        catch (error) {
            logger_1.logger.error("❌ Erro ao notificar reordenação:", error);
            // Não falhar o processo principal por erro de notificação
        }
    }
    /**
     * 🔧 REORDENAR MANUALMENTE (para testes)
     */
    async manualReorder(restaurantId) {
        logger_1.logger.info(`🔧 Reordenação manual solicitada para restaurante ${restaurantId}`);
        return await this.reorderRestaurantPlaylists(restaurantId);
    }
}
exports.PlaylistReorderService = PlaylistReorderService;
// Exportar instância singleton
exports.playlistReorderService = PlaylistReorderService.getInstance();
//# sourceMappingURL=PlaylistReorderService.js.map