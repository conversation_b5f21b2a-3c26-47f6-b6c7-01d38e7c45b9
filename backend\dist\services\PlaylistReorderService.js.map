{"version": 3, "file": "PlaylistReorderService.js", "sourceRoot": "", "sources": ["../../src/services/PlaylistReorderService.ts"], "names": [], "mappings": ";;;AAAA,iDAAmD;AACnD,qDAAkD;AAClD,iDAA8D;AAC9D,qDAAoE;AACpE,+DAA4D;AAC5D,iFAA8E;AAC9E,4CAAyC;AACzC,qCAAsC;AACtC,yDAAsD;AACtD,iEAA8D;AAiC9D,MAAa,sBAAsB;IAUjC;QARQ,yBAAoB,GAAG,wBAAa,CAAC,aAAa,CAAC,uBAAU,CAAC,CAAC;QAC/D,uBAAkB,GAAG,wBAAa,CAAC,aAAa,CAAC,mBAAQ,CAAC,CAAC;QAC3D,yBAAoB,GAAG,wBAAa,CAAC,aAAa,CAAC,uBAAU,CAAC,CAAC;QAC/D,uBAAkB,GAAG,wBAAa,CAAC,aAAa,CAAC,mCAAgB,CAAC,CAAC;QACnE,oBAAe,GAA0B,IAAI,CAAC;QAC9C,cAAS,GAAG,KAAK,CAAC;QAIxB,IAAI,CAAC,gBAAgB,GAAG,mCAAgB,CAAC,WAAW,EAAE,CAAC;QACvD,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE;YACpC,sBAAsB,CAAC,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAC;SAChE;QACD,OAAO,sBAAsB,CAAC,QAAQ,CAAC;IACzC,CAAC;IAED;;;OAGG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO;SACR;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAEpE,0CAA0C;QAC1C,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,oCAAoC;QACpC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,YAAY;IACjC,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,mBAAmB;QAC/B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAE5D,IAAI;YACF,2CAA2C;YAC3C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACzB,SAAS,EAAE,CAAC,WAAW,CAAC;aACzB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAoB,EAAE,CAAC;YAEpC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,IAAI;oBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;oBACpE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACtB;gBAAC,OAAO,KAAK,EAAE;oBACd,eAAM,CAAC,KAAK,CACV,gDAAgD,UAAU,CAAC,EAAE,GAAG,EAChE,KAAK,CACN,CAAC;oBACF,OAAO,CAAC,IAAI,CAAC;wBACX,OAAO,EAAE,KAAK;wBACd,YAAY,EAAE,UAAU,CAAC,EAAE;wBAC3B,eAAe,EAAE,CAAC;wBAClB,OAAO,EAAE,SACP,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAC3C,EAAE;wBACF,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAC;iBACJ;aACF;YAED,4BAA4B;YAC5B,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YACxD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAChC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe,EACnC,CAAC,CACF,CAAC;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YAElD,eAAM,CAAC,IAAI,CAAC,uCAAuC,QAAQ,KAAK,CAAC,CAAC;YAClE,eAAM,CAAC,IAAI,CAAC,SAAS,UAAU,cAAc,MAAM,SAAS,CAAC,CAAC;YAC9D,eAAM,CAAC,IAAI,CAAC,SAAS,WAAW,8BAA8B,CAAC,CAAC;SACjE;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;SACxD;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CACtC,YAAoB;QAEpB,IAAI;YACF,wDAAwD;YACxD,MAAM,eAAe,GAAG,MAAM,yCAAmB,CAAC,eAAe,CAC/D,YAAY,CACb,CAAC;YACF,IAAI,CAAC,eAAe,EAAE;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,YAAY;oBACZ,eAAe,EAAE,CAAC;oBAClB,OAAO,EAAE,yCAAyC;oBAClD,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;aACH;YAED,uEAAuE;YACvE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBACnD,KAAK,EAAE,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE;aACxC,CAAC,CAAC;YAEH,IAAI,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE;oBACL,UAAU,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;oBAChC,MAAM,EAAE,yBAAc,CAAC,MAAM;oBAC7B,iBAAiB,EAAE,IAAA,aAAG,EAAC,IAAA,gBAAM,GAAE,CAAC;iBACjC;gBACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;aAChD,CAAC,CAAC;YAEH,IAAI,SAAS,CAAC,MAAM,EAAE;gBACpB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;oBAChC,MAAM,OAAO,GAAG,QAAQ,CAAC,wBAAwB,EAAE,CAAC;oBACpD,IAAI,OAAO,EAAE,UAAU,EAAE;wBACvB,+DAA+D;wBAC/D,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,EAAE,KAAK,OAAO,CAAC,UAAU,EAAE;4BAC/D,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gCAC9D,KAAK,EAAE;oCACL,EAAE,EAAE,OAAO,CAAC,UAAU;oCACtB,UAAU,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;iCACjC;6BACF,CAAC,CAAC;4BACH,IAAI,iBAAiB,EAAE;gCACrB,8CAA8C;gCAC9C,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAClC,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EACpC,EAAE,MAAM,EAAE,yBAAc,CAAC,QAAQ,EAAE,CACpC,CAAC;gCACF,iBAAiB,CAAC,MAAM,GAAG,yBAAc,CAAC,MAAM,CAAC;gCACjD,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gCACtD,cAAc,GAAG,iBAAiB,CAAC;gCACnC,eAAM,CAAC,IAAI,CACT,gCAAgC,iBAAiB,CAAC,IAAI,KAAK,iBAAiB,CAAC,EAAE,GAAG,CACnF,CAAC;6BACH;yBACF;wBACD,MAAM,CAAC,4BAA4B;qBACpC;iBACF;aACF;YAED,gEAAgE;YAChE,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;gBACxD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,YAAY;oBACZ,eAAe,EAAE,CAAC;oBAClB,OAAO,EAAE,mCAAmC;oBAC5C,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;aACH;YAED,+BAA+B;YAC/B,MAAM,aAAa,GAAG,MAAM,2DAA4B,CAAC,gBAAgB,CACvE,YAAY,EACZ,EAAE,CACH,CAAC;YAEF,IACE,CAAC,aAAa,CAAC,OAAO;gBACtB,CAAC,aAAa,CAAC,IAAI;gBACnB,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAC/B;gBACA,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,YAAY;oBACZ,UAAU,EAAE,cAAc,CAAC,EAAE;oBAC7B,eAAe,EAAE,CAAC;oBAClB,OAAO,EAAE,yCAAyC;oBAClD,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;aACH;YAED,yEAAyE;YACzE,MAAM,OAAO,GAAG,IAAI,GAAG,CACrB,CAAC,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAC3D,CAAC;YACF,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,cAAc,CAAC,CAC/B,CAAC;YAEF,MAAM,QAAQ,GAAmB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC9D,OAAO,EAAE,IAAI,CAAC,cAAc;gBAC5B,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC,CAAC;YAEJ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,YAAY;oBACZ,UAAU,EAAE,cAAc,CAAC,EAAE;oBAC7B,eAAe,EAAE,CAAC;oBAClB,OAAO,EACL,iEAAiE;oBACnE,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;aACH;YAED,gCAAgC;YAChC,MAAM,cAAc,GAAG,MAAM,yCAAmB,CAAC,eAAe,CAC9D,YAAY,EACZ,cAAc,CAAC,iBAAiB,EAChC,QAAQ,CACT,CAAC;YAEF,IAAI,CAAC,cAAc,EAAE;gBACnB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,YAAY;oBACZ,UAAU,EAAE,cAAc,CAAC,EAAE;oBAC7B,eAAe,EAAE,CAAC;oBAClB,OAAO,EAAE,wCAAwC;oBACjD,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;aACH;YAED,yCAAyC;YACzC,IAAI,cAAc,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;gBACjE,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM;qBAC1C,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBACb,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAChC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,cAAc,CAChD,CAAC;oBACF,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ;qBAChE,CAAC;gBACJ,CAAC,CAAC;qBACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAE3C,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACpD;YAED,eAAM,CAAC,IAAI,CACT,2BAA2B,cAAc,CAAC,IAAI,KAAK,QAAQ,CAAC,MAAM,UAAU,CAC7E,CAAC;YAEF,yCAAyC;YACzC,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE;gBAC7C,UAAU,EAAE,cAAc,CAAC,EAAE;gBAC7B,YAAY,EAAE,cAAc,CAAC,IAAI;gBACjC,eAAe,EAAE,QAAQ,CAAC,MAAM;gBAChC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,gBAAgB;aAClD,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,YAAY;gBACZ,UAAU,EAAE,cAAc,CAAC,EAAE;gBAC7B,eAAe,EAAE,QAAQ,CAAC,MAAM;gBAChC,OAAO,EAAE,oCAAoC,QAAQ,CAAC,MAAM,UAAU;gBACtE,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CACV,gDAAgD,YAAY,GAAG,EAC/D,KAAK,CACN,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,YAAY;gBACZ,eAAe,EAAE,CAAC;gBAClB,OAAO,EAAE,SACP,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAC3C,EAAE;gBACF,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;SACH;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QAKP,MAAM,aAAa,GACjB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe;YACpC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YACtC,CAAC,CAAC,SAAS,CAAC;QAEhB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa;YACb,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,YAAoB,EACpB,WAKC;QAED,IAAI;YACF,MAAM,gBAAgB,GAAG;gBACvB,IAAI,EAAE,oBAAoB;gBAC1B,QAAQ,EAAE;oBACR,EAAE,EAAE,WAAW,CAAC,UAAU;oBAC1B,IAAI,EAAE,WAAW,CAAC,YAAY;oBAC9B,eAAe,EAAE,WAAW,CAAC,eAAe;iBAC7C;gBACD,SAAS,EAAE,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBACtD,QAAQ,EAAE,KAAK,GAAG,CAAC;oBACnB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,aAAa,EAAE,KAAK,CAAC,aAAa;iBACnC,CAAC,CAAC;gBACH,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,yCAAyC,WAAW,CAAC,eAAe,UAAU;aACxF,CAAC;YAEF,6CAA6C;YAC7C,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAC1C,YAAY,EACZ,mBAAmB,EACnB,gBAAgB,CACjB,CAAC;YAEF,iDAAiD;YACjD,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CACtC,YAAY,EACZ,wBAAwB,EACxB;gBACE,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,kBAAkB,EAAE,IAAI;oBACxB,eAAe,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,SAAS;iBAC/E;aACF,CACF,CAAC;YAEF,eAAM,CAAC,IAAI,CACT,0CAA0C,WAAW,CAAC,YAAY,EAAE,CACrE,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,0DAA0D;SAC3D;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,YAAoB;QACtC,eAAM,CAAC,IAAI,CACT,qDAAqD,YAAY,EAAE,CACpE,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;CACF;AAhZD,wDAgZC;AAED,+BAA+B;AAClB,QAAA,sBAAsB,GAAG,sBAAsB,CAAC,WAAW,EAAE,CAAC"}