// Obsoleto: componente removido. Mantido como stub para evitar importações quebradas durante transição.
// Este arquivo foi descontinuado em favor do PlaybackController.
// Não é mais utilizado e pode ser removido definitivamente após limpeza de referências.

export default function SuggestionsManager() {
  return null;
}
        const errorText = await response.text().catch(() => "");
        throw new Error(
          `Erro ${response.status}: ${
            errorText || "Erro ao carregar sugestões"
          }`
        );
      }

      const data = await response.json();
      console.log("✅ Sugestões carregadas:", data);
      setSuggestions(data.suggestions || []);
    } catch (error) {
      console.error("Erro ao carregar sugestões:", error);
      setSuggestions([
        {
          id: "1",
          youtubeVideoId: "example",
          title: "Música de Exemplo",
          artist: "Artista Demo",
          thumbnailUrl: "https://img.youtube.com/vi/example/mqdefault.jpg",
          duration: 0,
          upvotes: 0,
          downvotes: 0,
          status: "pending",
          createdAt: new Date().toISOString(),
          suggestedBy: "Demo User",
        },
      ]);
      toast.info("Carregando dados de exemplo (servidor offline)");
    } finally {
      setLoading(false);
    }
  }, [restaurantId, filter]);

  const approveSuggestion = useCallback(
    async (suggestionId: string) => {
      if (!restaurantId) {
        toast.error("Restaurant ID is required");
        return;
      }

      try {
        console.log(
          "🔄 Aprovando sugestão:",
          suggestionId,
          "para restaurante:",
          restaurantId
        );
        // Obter token de autenticação
        const authToken = localStorage.getItem("authToken");
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
        };

        if (authToken) {
          headers.Authorization = `Bearer ${authToken}`;
        }

        const response = await fetch(
          buildApiUrl(`/suggestions/${restaurantId}/${suggestionId}/approve`),
          {
            method: "POST",
            headers,
          }
        );

        console.log(
          "📡 Resposta da API (approve):",
          response.status,
          response.statusText
        );

        if (!response.ok) {
          const errorText = await response.text().catch(() => "");
          console.error("❌ Erro na resposta:", errorText);
          throw new Error(
            `Erro ${response.status}: ${
              errorText || "Erro ao aprovar sugestão"
            }`
          );
        }

        const data = await response.json().catch(() => null);
        console.log("✅ Dados da resposta:", data);

        setSuggestions((prev) =>
          prev.map((s) =>
            s.id === suggestionId ? { ...s, status: "approved" } : s
          )
        );
        toast.success("Sugestão aprovada!");
      } catch (error) {
        console.error("Erro ao aprovar sugestão:", error);
        toast.error(
          error instanceof Error ? error.message : "Erro ao aprovar sugestão"
        );
      }
    },
    [restaurantId]
  );

  const rejectSuggestion = useCallback(
    async (suggestionId: string) => {
      if (!restaurantId) {
        toast.error("Restaurant ID is required");
        return;
      }

      try {
        console.log(
          "🔄 Rejeitando sugestão:",
          suggestionId,
          "para restaurante:",
          restaurantId
        );
        // Obter token de autenticação
        const authToken = localStorage.getItem("authToken");
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
        };

        if (authToken) {
          headers.Authorization = `Bearer ${authToken}`;
        }

        const response = await fetch(
          buildApiUrl(`/suggestions/${restaurantId}/${suggestionId}/reject`),
          {
            method: "POST",
            headers,
          }
        );

        console.log(
          "📡 Resposta da API (reject):",
          response.status,
          response.statusText
        );

        if (!response.ok) {
          const errorText = await response.text().catch(() => "");
          console.error("❌ Erro na resposta:", errorText);
          throw new Error(
            `Erro ${response.status}: ${
              errorText || "Erro ao rejeitar sugestão"
            }`
          );
        }

        const data = await response.json().catch(() => null);
        console.log("✅ Dados da resposta:", data);

        setSuggestions((prev) =>
          prev.map((s) =>
            s.id === suggestionId ? { ...s, status: "rejected" } : s
          )
        );
        toast.success("Sugestão rejeitada!");
      } catch (error) {
        console.error("Erro ao rejeitar sugestão:", error);
        toast.error(
          error instanceof Error ? error.message : "Erro ao rejeitar sugestão"
        );
      }
    },
    [restaurantId]
  );

  const filteredSuggestions = suggestions.filter(
    (suggestion) =>
      suggestion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      suggestion.artist.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "playing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case "played":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4" />;
      case "approved":
        return <CheckCircle className="w-4 h-4" />;
      case "rejected":
        return <XCircle className="w-4 h-4" />;
      case "playing":
        return <Play className="w-4 h-4" />;
      case "played":
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  useEffect(() => {
    loadSuggestions();
  }, [loadSuggestions]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciar Sugestões
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Modere e gerencie as sugestões dos clientes
          </p>
        </div>

        <button
          onClick={loadSuggestions}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          aria-label="Atualizar sugestões"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
          <span>Atualizar</span>
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar por título ou artista..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          <div className="flex space-x-2">
            {["all", "pending", "approved", "rejected"].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === status
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                }`}
              >
                {status === "all"
                  ? "Todas"
                  : status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          </div>
        ) : filteredSuggestions.length > 0 ? (
          <AnimatePresence>
            {filteredSuggestions.map((suggestion) => (
              <motion.div
                key={suggestion.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-start space-x-4">
                  <img
                    src={suggestion.thumbnailUrl}
                    alt={suggestion.title}
                    className="w-20 h-15 object-cover rounded-lg flex-shrink-0"
                  />

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                          {suggestion.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          {suggestion.artist}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                          Sugerido por: {suggestion.suggestedBy} •{" "}
                          {new Date(suggestion.createdAt).toLocaleString()}
                        </p>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(
                            suggestion.status
                          )}`}
                        >
                          {getStatusIcon(suggestion.status)}
                          <span>{suggestion.status}</span>
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1 text-green-600">
                          <ThumbsUp className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {suggestion.upvotes}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 text-red-600">
                          <ThumbsDown className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {suggestion.downvotes}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          Score: {suggestion.upvotes - suggestion.downvotes}
                        </div>
                      </div>

                      {suggestion.status === "pending" && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => approveSuggestion(suggestion.id)}
                            className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm"
                            aria-label={`Aprovar sugestão ${suggestion.title}`}
                          >
                            <CheckCircle className="w-4 h-4" />
                            <span>Aprovar</span>
                          </button>
                          <button
                            onClick={() => rejectSuggestion(suggestion.id)}
                            className="flex items-center space-x-1 px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm"
                            aria-label={`Rejeitar sugestão ${suggestion.title}`}
                          >
                            <XCircle className="w-4 h-4" />
                            <span>Rejeitar</span>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        ) : (
          <div className="text-center py-12">
            <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhuma sugestão encontrada
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {filter === "all"
                ? "Ainda não há sugestões dos clientes"
                : `Não há sugestões com status "${filter}"`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SuggestionsManager;
