"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../utils/validation");
const asyncHandler_1 = __importDefault(require("../middleware/asyncHandler"));
const auth_1 = require("../middleware/auth");
const CollaborativePlaylistService_1 = require("../services/CollaborativePlaylistService");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/create:
 *   post:
 *     summary: Criar playlist colaborativa para restaurante
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               playlistName:
 *                 type: string
 *                 example: "Playlist Principal"
 *               description:
 *                 type: string
 *                 example: "Playlist colaborativa do restaurante"
 *               initialTracks:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dQw4w9WgXcQ", "kJQP7kiw5Fk"]
 *     responses:
 *       201:
 *         description: Playlist colaborativa criada com sucesso
 */
router.post("/:restaurantId/create", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("playlistName")
        .notEmpty()
        .withMessage("Nome da playlist é obrigatório"),
    (0, validation_1.body)("description").optional().isString(),
    (0, validation_1.body)("initialTracks").optional().isArray(),
], auth_1.authMiddleware, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { playlistName, description, initialTracks = [] } = req.body;
    console.log(`🎵 Criando playlist colaborativa: ${playlistName} para ${restaurantId}`);
    // 1. Criar playlist colaborativa
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.createRestaurantPlaylist(restaurantId, playlistName, description);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    // 2. Adicionar músicas iniciais se fornecidas
    let initialTracksResult = null;
    if (initialTracks.length > 0 && result.playlistId) {
        initialTracksResult = await CollaborativePlaylistService_1.collaborativePlaylistService.addInitialTracks(result.playlistId, initialTracks);
    }
    res.status(201).json({
        success: true,
        message: result.message,
        data: {
            playlistId: result.playlistId,
            youtubePlaylistId: result.youtubePlaylistId,
            initialTracksAdded: initialTracksResult?.addedCount || 0,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/stats:
 *   get:
 *     summary: Obter estatísticas da playlist colaborativa
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas da playlist colaborativa
 */
router.get("/:restaurantId/stats", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.getPlaylistStats(restaurantId);
    if (!result.success) {
        return res.status(404).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: result.data,
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/vote:
 *   post:
 *     summary: Processar voto normal (gratuito)
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 example: "dQw4w9WgXcQ"
 *               tableNumber:
 *                 type: integer
 *                 example: 5
 *               clientSessionId:
 *                 type: string
 *                 example: "session_123"
 *     responses:
 *       200:
 *         description: Voto registrado com sucesso
 */
router.post("/:restaurantId/vote", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("youtubeVideoId")
        .notEmpty()
        .withMessage("ID do vídeo do YouTube é obrigatório"),
    (0, validation_1.body)("tableNumber").optional().isInt(),
    (0, validation_1.body)("clientSessionId").optional().isString(),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { youtubeVideoId, tableNumber, clientSessionId } = req.body;
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.processNormalVote(restaurantId, youtubeVideoId, tableNumber, clientSessionId);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: {
            voteWeight: result.voteWeight,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/supervote:
 *   post:
 *     summary: Processar supervoto (pago)
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 example: "dQw4w9WgXcQ"
 *               paymentAmount:
 *                 type: number
 *                 example: 5.00
 *                 description: "Valor em reais (R$ 5, R$ 20 ou R$ 50)"
 *               paymentId:
 *                 type: string
 *                 example: "pix_123456"
 *               tableNumber:
 *                 type: integer
 *                 example: 5
 *               clientSessionId:
 *                 type: string
 *                 example: "session_123"
 *     responses:
 *       200:
 *         description: Supervoto processado com sucesso
 */
router.post("/:restaurantId/supervote", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("youtubeVideoId")
        .notEmpty()
        .withMessage("ID do vídeo do YouTube é obrigatório"),
    (0, validation_1.body)("paymentAmount")
        .isFloat({ min: 5 })
        .withMessage("Valor do pagamento deve ser no mínimo R$ 5,00"),
    (0, validation_1.body)("paymentId").notEmpty().withMessage("ID do pagamento é obrigatório"),
    (0, validation_1.body)("tableNumber").optional().isInt(),
    (0, validation_1.body)("clientSessionId").optional().isString(),
    (0, validation_1.body)("clientMessage").optional().isString().isLength({ max: 200 }),
    (0, validation_1.body)("clientName").optional().isString().isLength({ max: 80 }),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { youtubeVideoId, paymentAmount, paymentId, tableNumber, clientSessionId, clientMessage, clientName, } = req.body;
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.processSuperVote(restaurantId, youtubeVideoId, paymentAmount, paymentId, tableNumber, clientSessionId, clientMessage, clientName);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: {
            voteWeight: result.voteWeight,
            paymentAmount,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/add-tracks:
 *   post:
 *     summary: Adicionar músicas à playlist colaborativa
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               videoIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dQw4w9WgXcQ", "kJQP7kiw5Fk"]
 *     responses:
 *       200:
 *         description: Músicas adicionadas com sucesso
 */
router.post("/:restaurantId/add-tracks", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("videoIds")
        .isArray({ min: 1 })
        .withMessage("Lista de vídeos é obrigatória"),
], auth_1.authMiddleware, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { videoIds } = req.body;
    // Buscar playlist do restaurante
    const playlist = await CollaborativePlaylistService_1.collaborativePlaylistService.getPlaylistStats(restaurantId);
    if (!playlist.success) {
        return res.status(404).json({
            success: false,
            message: "Playlist não encontrada",
        });
    }
    // Adicionar músicas (assumindo que temos o playlistId)
    // Esta implementação precisa ser ajustada para obter o playlistId
    res.json({
        success: true,
        message: "Funcionalidade em desenvolvimento",
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/ranking:
 *   get:
 *     summary: Obter ranking de votação
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: Ranking de votação
 */
router.get("/:restaurantId/ranking", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.query)("limit").optional().isInt({ min: 1, max: 100 }),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { limit = 20 } = req.query;
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, parseInt(limit));
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: result.data,
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/reorder:
 *   post:
 *     summary: Reordenar playlist baseada em votos
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist reordenada com sucesso
 */
router.post("/:restaurantId/reorder", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    // Usar o serviço central de reordenação (emite WebSocket e atualiza DB)
    const { playlistReorderService } = await Promise.resolve().then(() => __importStar(require("../services/PlaylistReorderService")));
    const reorderResult = await playlistReorderService.manualReorder(restaurantId);
    if (!reorderResult.success) {
        return res.status(400).json({
            success: false,
            message: reorderResult.message,
        });
    }
    res.json({
        success: true,
        message: reorderResult.message,
        data: {
            reorderedCount: reorderResult.tracksReordered,
            playlistId: reorderResult.playlistId,
        },
    });
}));
exports.default = router;
//# sourceMappingURL=collaborativePlaylist.js.map