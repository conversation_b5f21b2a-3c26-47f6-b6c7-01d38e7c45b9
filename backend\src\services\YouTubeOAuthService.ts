import { google } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";

interface YouTubeCredentials {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expiry_date: number;
}

interface PlaylistItem {
  videoId: string;
  position: number;
}

export class YouTubeOAuthService {
  private oauth2Client: OAuth2Client;
  private youtube: any;
  private restaurantRepository = AppDataSource.getRepository(Restaurant);

  constructor() {
    const clientId = process.env.YOUTUBE_CLIENT_ID;
    const clientSecret = process.env.YOUTUBE_CLIENT_SECRET;

    if (!clientId || clientId === 'your_client_id_here' || !clientSecret) {
      console.warn("⚠️ YouTube OAuth não configurado. Reordenação de playlists desabilitada.");
      // Usar API key apenas para leitura
      this.youtube = google.youtube({
        version: "v3",
        auth: process.env.YOUTUBE_API_KEY,
      });
      return;
    }

    this.oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      process.env.YOUTUBE_REDIRECT_URI ||
        "http://localhost:8001/auth/youtube/callback"
    );

    this.youtube = google.youtube({
      version: "v3",
      auth: this.oauth2Client,
    });
  }

  /**
   * Gerar URL de autorização OAuth
   */
  generateAuthUrl(restaurantId: string): string {
    const scopes = [
      "https://www.googleapis.com/auth/youtube",
      "https://www.googleapis.com/auth/youtube.force-ssl",
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      state: restaurantId, // Passar restaurantId no state
      prompt: "consent", // Força novo refresh_token
    });
  }

  /**
   * Trocar código de autorização por tokens
   */
  async exchangeCodeForTokens(
    code: string,
    restaurantId: string
  ): Promise<YouTubeCredentials> {
    try {
      const response = await this.oauth2Client.getToken(code);
      const tokens = response.tokens;

      if (!tokens.access_token || !tokens.refresh_token) {
        throw new Error("Tokens inválidos recebidos do YouTube");
      }

      const credentials: YouTubeCredentials = {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token!,
        scope: tokens.scope || "",
        token_type: tokens.token_type || "Bearer",
        expiry_date: tokens.expiry_date || Date.now() + 3600000,
      };

      // Salvar credenciais no restaurante
      await this.saveCredentials(restaurantId, credentials);

      console.log(
        `✅ Credenciais YouTube salvas para restaurante: ${restaurantId}`
      );
      return credentials;
    } catch (error) {
      console.error("Erro ao trocar código por tokens:", error);
      throw new Error("Falha na autenticação com YouTube");
    }
  }

  /**
   * Salvar credenciais do YouTube no restaurante
   */
  private async saveCredentials(
    restaurantId: string,
    credentials: YouTubeCredentials
  ): Promise<void> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    // Salvar credenciais de forma segura (criptografadas em produção)
    restaurant.youtubeCredentials = {
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
      expiry_date: credentials.expiry_date,
      scope: credentials.scope,
    };

    await this.restaurantRepository.save(restaurant);
  }

  /**
   * Carregar e configurar credenciais para um restaurante
   */
  private async loadCredentials(restaurantId: string): Promise<boolean> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant?.youtubeCredentials) {
      return false;
    }

    const credentials = restaurant.youtubeCredentials;

    // Verificar se o token expirou
    if (credentials.expiry_date && credentials.expiry_date < Date.now()) {
      // Tentar renovar o token
      try {
        this.oauth2Client.setCredentials({
          refresh_token: credentials.refresh_token,
        });

        const { credentials: newCredentials } =
          await this.oauth2Client.refreshAccessToken();

        // Atualizar credenciais
        await this.saveCredentials(restaurantId, {
          access_token: newCredentials.access_token!,
          refresh_token: credentials.refresh_token,
          expiry_date: newCredentials.expiry_date!,
          scope: credentials.scope,
          token_type: "Bearer",
        });

        console.log(`🔄 Token renovado para restaurante: ${restaurantId}`);
      } catch (error) {
        console.error("Erro ao renovar token:", error);
        return false;
      }
    }

    // Configurar credenciais no cliente OAuth
    this.oauth2Client.setCredentials({
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
      expiry_date: credentials.expiry_date,
    });

    return true;
  }

  /**
   * Criar nova playlist no YouTube
   */
  async createPlaylist(
    restaurantId: string,
    title: string,
    description: string
  ): Promise<string> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      const response = await this.youtube.playlists.insert({
        part: ["snippet", "status"],
        requestBody: {
          snippet: {
            title,
            description,
            defaultLanguage: "pt-BR",
          },
          status: {
            privacyStatus: "public", // ou 'unlisted' para não listada
          },
        },
      });

      const playlistId = response.data.id;
      console.log(`✅ Playlist criada no YouTube: ${playlistId}`);
      return playlistId;
    } catch (error) {
      console.error("Erro ao criar playlist:", error);
      throw new Error("Falha ao criar playlist no YouTube");
    }
  }

  /**
   * Reordenar playlist baseado em votações
   */
  async reorderPlaylist(
    restaurantId: string,
    playlistId: string,
    newOrder: PlaylistItem[]
  ): Promise<boolean> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      // Obter itens atuais da playlist
      const currentItems = await this.getPlaylistItems(playlistId);

      // Reordenar itens
      for (let i = 0; i < newOrder.length; i++) {
        const item = newOrder[i];
        const currentItem = currentItems.find(
          (ci) => ci.videoId === item.videoId
        );

        if (currentItem && currentItem.position !== item.position) {
          // Atualizar posição do item
          await this.youtube.playlistItems.update({
            part: ["snippet"],
            requestBody: {
              id: currentItem.id,
              snippet: {
                playlistId,
                resourceId: {
                  kind: "youtube#video",
                  videoId: item.videoId,
                },
                position: item.position,
              },
            },
          });
        }
      }

      console.log(
        `🔄 Playlist reordenada: ${playlistId} (${newOrder.length} itens)`
      );
      return true;
    } catch (error) {
      console.error("Erro ao reordenar playlist:", error);
      return false;
    }
  }

  /**
   * Obter itens da playlist
   */
  private async getPlaylistItems(playlistId: string): Promise<any[]> {
    try {
      const response = await this.youtube.playlistItems.list({
        part: ["snippet", "contentDetails"],
        playlistId,
        maxResults: 50,
      });

      return response.data.items.map((item: any, index: number) => ({
        id: item.id,
        videoId: item.contentDetails.videoId,
        position: item.snippet.position || index,
        title: item.snippet.title,
      }));
    } catch (error) {
      console.error("Erro ao obter itens da playlist:", error);
      return [];
    }
  }

  /**
   * Adicionar vídeo à playlist
   */
  async addVideoToPlaylist(
    restaurantId: string,
    playlistId: string,
    videoId: string,
    position?: number
  ): Promise<boolean> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      await this.youtube.playlistItems.insert({
        part: ["snippet"],
        requestBody: {
          snippet: {
            playlistId,
            resourceId: {
              kind: "youtube#video",
              videoId,
            },
            position,
          },
        },
      });

      console.log(`➕ Vídeo adicionado à playlist: ${videoId}`);
      return true;
    } catch (error) {
      console.error("Erro ao adicionar vídeo:", error);
      return false;
    }
  }

  /**
   * Remover vídeo da playlist
   */
  async removeVideoFromPlaylist(
    restaurantId: string,
    playlistItemId: string
  ): Promise<boolean> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      await this.youtube.playlistItems.delete({
        id: playlistItemId,
      });

      console.log(`➖ Vídeo removido da playlist: ${playlistItemId}`);
      return true;
    } catch (error) {
      console.error("Erro ao remover vídeo:", error);
      return false;
    }
  }

  /**
   * Verificar se restaurante está autenticado
   */
  async isAuthenticated(restaurantId: string): Promise<boolean> {
    return await this.loadCredentials(restaurantId);
  }
}

export const youtubeOAuthService = new YouTubeOAuthService();
