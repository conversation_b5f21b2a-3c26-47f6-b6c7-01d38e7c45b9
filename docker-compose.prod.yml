

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: restaurant-playlist-db
    environment:
      POSTGRES_DB: restaurant_playlist
      POSTGRES_USER: playlist_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_change_in_production}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/database/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - app-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: restaurant-playlist-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-secure_redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - app-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: restaurant-playlist-api
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: restaurant_playlist
      DB_USER: playlist_user
      DB_PASSWORD: ${DB_PASSWORD:-secure_password_change_in_production}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-secure_redis_password}
      JWT_SECRET: ${JWT_SECRET:-change_this_in_production_very_long_secret}
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      CORS_ORIGIN: ${FRONTEND_URL:-https://playlist.yourdomain.com}
    depends_on:
      - postgres
      - redis
    ports:
      - "5000:5000"
    restart: unless-stopped
    networks:
      - app-network
    volumes:
      - ./logs:/app/logs

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        VITE_API_URL: ${API_URL:-https://api.yourdomain.com}
        VITE_WS_URL: ${WS_URL:-https://api.yourdomain.com}
    container_name: restaurant-playlist-frontend
    ports:
      - "3000:80"
    restart: unless-stopped
    networks:
      - app-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: restaurant-playlist-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge
