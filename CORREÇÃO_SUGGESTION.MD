Visão geral do fluxo
Importar/sincronizar playlist (Play Manager)
Você usa o Play Manager na UI: arquivo frontend PlaylistManager.tsx.

Ele chama rotas do backend para importar/sincronizar uma playlist do YouTube para o sistema local: playlists.ts.

O backend persiste a playlist (DB) e mantém um “estado ao vivo” usado pelo player e pela reordenação: serviços PlaybackService.ts e LivePlaylist (exposto via /live-playlist/:restaurantId/state).

Votação (gratuita e paga/supervoto)
Cliente vota via UI (ClientInterface + PaymentModal para PIX). Principais arquivos no frontend:

PaymentModal.tsx (supervotos pagos)
useRealTimeVoting.ts (assinaturas e atualizações)
O backend processa votos e supervotos centralmente:

CollaborativePlaylistService.ts
Aplica pesos: livre/normal (1) e supervotos 5→10, 20→30, 50→70.
Atualiza pontuação/ranking das músicas.
Emite eventos em tempo real (Socket) como “superVoteReceived”, “vote-updated”.
Ranking e reordenação automática
A pontuação acumulada por música forma o ranking (mesmo serviço acima).
Reordenação para o YouTube:

Serviço agendado e manual: PlaylistReorderService.ts (intervalo ~5 min e endpoints de start/stop/status/reorder).
Quando dispara, o serviço calcula a nova ordem com base nas pontuações e usa o serviço de integração com YouTube (OAuth único do sistema) para reordenar a playlist no YouTube.
O backend emite eventos “playlistReordered” para o frontend reagir em tempo real.

Player/Controle e Painéis
Playback Controller (painel/console do restaurante): PlaybackController.tsx
Carrega estado e filas (prioritária/normal) da Live Playlist e assina eventos de atualização (queue updated, current track etc).
Observação: ele ainda possui chamadas a “/suggestions” como fonte de itens de fila; isso está em transição para usar apenas a LivePlaylist/queue.
Restaurant Dashboard: RestaurantDashboard.tsx
Mostra KPIs, fila, atividade recente e assina eventos (stats update, queue updated, current track).
Observação: ainda escuta “new-suggestion” e busca “getSuggestions” para “atividade recente”; vamos trocar isso por eventos/filas da LivePlaylist.
Analytics e relatórios
Endpoints: analytics.ts
Cálculo no serviço: AnalyticsService.ts (já com os pesos 10/30/70 para supervotos).
Frontend unificado: UnifiedAnalytics.tsx e Admin SupervotesDashboard.tsx.
Quem conecta com o Play Manager
O Play Manager (frontend) fala com as rotas de playlist no backend (playlists.ts).
O backend, por sua vez, usa serviços (PlaybackService.ts + integração YouTube) para ler/escrever metadados e manter o estado ao vivo, e o PlaylistReorderService.ts para reordenar no YouTube.
Onde os votos influenciam no código
Backend:
CollaborativePlaylistService.ts
Funções-chave: processamento de votos/supervotos, agregação de pontos, emissão de eventos.
Regra de pesos atual: 5→10, 20→30, 50→70.
Esses pontos alimentam o ranking que será usado pela reordenação.
Frontend:
useRealTimeVoting.ts assina eventos de voto

PaymentModal.tsx inicia supervoto (PIX) e confirmações para o cliente
Como volta para reordenar a playlist no YouTube
O PlaylistReorderService.ts pega o ranking atualizado e chama a integração YouTube (OAuth único do sistema, não por restaurante) para reordenar a playlist no YouTube.
Isso ocorre automaticamente no agendamento e também pode ser disparado manualmente por endpoints/admin.
Eventos “playlistReordered” são emitidos; o frontend se atualiza.
AUTH único (sem AUTH por restaurante)
O desenho atual já considera um AUTH único do sistema para YouTube (compartilhado), removendo a dependência de cada restaurante ter seu próprio OAuth.
O Play Manager e o Reorder usam esse AUTH central no backend. Documentos relacionados: SISTEMA_AUTH_PLAYLIST.md.
Legado “suggestions”: estado atual e plano
O papel original de “suggestions” era moderação (aprovar/reprovar) e servir de fonte da fila. No novo modelo, a música entra e a ordem é decidida pelos votos — sem gate manual.
Onde ainda aparece:
PlaybackController.tsx: usa /suggestions para montar priorityQueue/normalQueue (mesmo quando obtém o estado da LivePlaylist).
RestaurantDashboard.tsx: usa getSuggestions e evento “new-suggestion” para “atividade recente”.
Risco: manter essas dependências pode conflitar com o modelo 100% automático.
Ação recomendada:
Trocar no PlaybackController a origem das filas para endpoints de LivePlaylist/queue e remover chamadas a /suggestions.
No RestaurantDashboard, derivar “atividade recente” e contadores dos eventos da LivePlaylist (ex.: “playlistReordered”, “track-added”, “superVoteReceived”) e remover listeners/queries de suggestion.
Depois disso, podemos retirar o SuggestionsManager.tsx e rotas legadas com segurança.
Em resumo

Fluxo desejado já está implementado no backend: votos com pesos alimentam o ranking, o PlaylistReorderService reordena automaticamente no YouTube com AUTH único, e os painéis recebem tudo em tempo real.
O que falta alinhar 100% no frontend: eliminar o uso residual de “suggestions” no PlaybackController.tsx e RestaurantDashboard.tsx, passando a usar apenas os dados/eventos da LivePlaylist. Isso garante que nada dependa da estrutura antiga.
Se quiser, eu já faço essas duas trocas no frontend agora e rodo um build de verificação.

