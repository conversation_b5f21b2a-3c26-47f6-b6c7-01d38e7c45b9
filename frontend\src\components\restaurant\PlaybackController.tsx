import React, { useState, useEffect, useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useRestaurantContext } from "./RestaurantDashboard";
import { buildApiUrl, getAuthHeaders } from "../../config/api";
import { useWebSocket, wsService } from "@/services/websocket";
import {
  Play,
  Pause,
  SkipForward,
  Volume2,
  VolumeX,
  Music,
  Clock,
  Users,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Loader2,
  X,
  ChevronUp,
  Shuffle,
  Repeat,
  Settings2,
  ArrowUp,
  ArrowDown,
  Lock,
  Unlock,
  Trash2,
  ExternalLink,
  Activity,
  DollarSign,
  Gift,
  Zap,
  ThumbsUp,
} from "lucide-react";
import { toast, Toaster } from "react-hot-toast";

interface Track {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  duration: number;
  thumbnailUrl: string;
  upvotes: number;
  downvotes: number;
  score: number;
  suggestedBy?: string;
  createdAt: Date;
}

// Itens de ranking do sistema colaborativo
interface RankingItem {
  youtubeVideoId: string;
  title?: string;
  artist?: string;
  voteCount: number;
  superVoteCount: number;
  normalVoteCount: number;
  totalRevenue: number;
  isPaid: boolean;
  paymentAmount: number;
  tableNumber?: number;
}

interface PlaybackState {
  currentTrack: Track | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  queue: Track[];
  priorityQueue: Track[];
  normalQueue: Track[];
  history: Track[];
  estimatedWaitTime?: number;
  connectionStatus?: "connected" | "disconnected" | "reconnecting";
}

// Cache Manager para armazenamento local
class PlaybackCache {
  private static CACHE_PREFIX = "playback_controller_";

  static setCache(key: string, data: any, ttl: number = 2 * 60 * 1000) {
    const cacheData = {
      data,
      timestamp: Date.now(),
      ttl,
    };
    localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));
  }

  static getCache(key: string) {
    try {
      const cached = localStorage.getItem(this.CACHE_PREFIX + key);
      if (!cached) return null;

      const { data, timestamp, ttl } = JSON.parse(cached);
      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(this.CACHE_PREFIX + key);
        return null;
      }

      return data;
    } catch {
      return null;
    }
  }
}

const PlaybackController: React.FC = () => {
  console.log("🎵 PlaybackController: Componente renderizando...");

  const { restaurantId: paramRestaurantId } = useParams<{
    restaurantId: string;
  }>();
  const { restaurantId: contextRestaurantId, isConnected } = useRestaurantContext();
  const restaurantId = paramRestaurantId || contextRestaurantId;

  console.log("🎵 PlaybackController: IDs obtidos", { paramRestaurantId, contextRestaurantId, restaurantId });
  const { on, off, emit } = useWebSocket();

  const [playbackState, setPlaybackState] = useState<PlaybackState | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [priorityQueue, setPriorityQueue] = useState<Track[]>([]);
  const [normalQueue, setNormalQueue] = useState<Track[]>([]);
  const [queueStats, setQueueStats] = useState({
    totalItems: 0,
    paidItems: 0,
    freeItems: 0,
    estimatedWaitTime: 0,
  });
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  const [retryCount, setRetryCount] = useState(0);

  // Estado para Votações (Colaborativa)
  const [rankingPaid, setRankingPaid] = useState<RankingItem[]>([]);
  const [rankingFree, setRankingFree] = useState<RankingItem[]>([]);
  const [nextReorderAt, setNextReorderAt] = useState<Date | null>(null);
  const [countdown, setCountdown] = useState<number>(0);
  const [autoReorder, setAutoReorder] = useState<boolean>(false);
  const [autoPreview, setAutoPreview] = useState<RankingItem[]>([]);
  const [totalVotes, setTotalVotes] = useState<number>(0);
  const [reorderHistory, setReorderHistory] = useState<any[]>([]);
  const [collabStats, setCollabStats] = useState<any>(null);
  const [voteAggregates, setVoteAggregates] = useState({
    totalSuperVotes: 0,
    totalNormalVotes: 0,
    paidItems: 0,
    freeItems: 0,
  });
  const [computedRevenue, setComputedRevenue] = useState<number>(0);

  // Configurações locais (apenas UI por enquanto)
  const [localSettings, setLocalSettings] = useState({
    shuffle: false,
    repeat: false,
    autoPlay: false,
    crossfade: 0,
    lockVoting: false,
  });

  // Adição manual
  const [manualVideoInput, setManualVideoInput] = useState<string>("");
  const [manualIsPaid, setManualIsPaid] = useState<boolean>(false);

  // Refs for cleanup and caching
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const rankingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket setup for real-time updates
  useEffect(() => {
    if (!restaurantId || !isConnected) return;

    const handlePlaybackUpdate = (data: Partial<PlaybackState>) => {
      setPlaybackState((prev) => (prev ? { ...prev, ...data } : null));
      setLastUpdateTime(new Date());
      PlaybackCache.setCache(`playback_${restaurantId}`, data);
    };

  const handleQueueUpdate = (data: { priorityQueue: Track[]; normalQueue: Track[] }) => {
      setPriorityQueue(data.priorityQueue || []);
      setNormalQueue(data.normalQueue || []);

      const estimatedWait = calculateEstimatedWaitTime([...data.priorityQueue, ...data.normalQueue]);
      setQueueStats((prev) => ({ ...prev, estimatedWaitTime: estimatedWait }));

      PlaybackCache.setCache(`queue_${restaurantId}`, data);
    };

    // Atualizar status via serviço
  const unsubscribeStatus = wsService.onConnectionStatusChange((status) => {
      setPlaybackState((prev) =>
    prev ? { ...prev, connectionStatus: (status as any) } : null
      );
    });

    // Register WebSocket listeners
  on("playback-state-update" as any, handlePlaybackUpdate as any);
  on("queue-update" as any, handleQueueUpdate as any);

    // Reagir à reordenação automática (websocket backend)
    const handlePlaylistReordered = (payload: any) => {
      toast.success(payload?.message || "Playlist reordenada por votos");
      // Registrar histórico de reordenação
      setReorderHistory((prev) => [
        {
          time: new Date().toISOString(),
          playlistName: payload?.playlist?.name || payload?.playlistName,
          count: payload?.playlist?.tracksReordered || payload?.tracksReordered || 0,
          details: (payload?.topTracks || []).slice(0, 5),
        },
        ...prev.slice(0, 19),
      ]);
      // Recarregar dados críticos
      loadQueues();
      loadPlaybackState();
      loadVotingRanking();
      loadCollaborativeStats();
      // Reinciar countdown de 5min, se disponível
      const target = new Date(Date.now() + 5 * 60 * 1000);
      setNextReorderAt(target);
      startCountdown(target);
    };
    on("playlistReordered", handlePlaylistReordered);

    // Join restaurant room
    emit("join-restaurant-playback", { restaurantId });

    return () => {
  off("playback-state-update" as any, handlePlaybackUpdate as any);
  off("queue-update" as any, handleQueueUpdate as any);
  unsubscribeStatus?.();
      off("playlistReordered", handlePlaylistReordered);
      emit("leave-restaurant-playback", { restaurantId });
    };
  }, [restaurantId, isConnected, on, off, emit]);

  // Initial data load with cache fallback
  useEffect(() => {
    loadInitialData();

    // Fallback polling para quando WebSocket não estiver disponível
    if (!isConnected) {
      intervalRef.current = setInterval(() => {
        loadPlaybackState();
        loadQueues();
      }, 10000); // 10 segundos quando offline
    }

    // Iniciar carga e atualização periódica do ranking colaborativo + stats
    loadVotingRanking();
    loadCollaborativeStats();
    if (rankingIntervalRef.current) clearInterval(rankingIntervalRef.current);
    rankingIntervalRef.current = setInterval(() => {
      loadVotingRanking();
      loadCollaborativeStats();
    }, 15000); // a cada 15s

    // Iniciar (ou reiniciar) contador de 5 minutos, se ainda não existir
    if (!nextReorderAt) {
      const target = new Date(Date.now() + 5 * 60 * 1000);
      setNextReorderAt(target);
      startCountdown(target);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (rankingIntervalRef.current) {
        clearInterval(rankingIntervalRef.current);
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [restaurantId, isConnected]);

  const loadInitialData = async () => {
    // Tentar carregar do cache primeiro
    const cachedPlayback = PlaybackCache.getCache(`playback_${restaurantId}`);
    const cachedQueue = PlaybackCache.getCache(`queue_${restaurantId}`);

    if (cachedPlayback) {
      setPlaybackState(cachedPlayback);
  toast("Carregando dados em cache...", { duration: 2000, icon: "ℹ️" });
    }

    if (cachedQueue) {
      setPriorityQueue(cachedQueue.priorityQueue || []);
      setNormalQueue(cachedQueue.normalQueue || []);
    }

    // Carregar dados atuais
    await Promise.all([loadPlaybackState(), loadQueues()]);
  };

  // Carregar ranking de votações (Colaborativa)
  const loadVotingRanking = async () => {
    if (!restaurantId) return;

    try {
      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`, { limit: "50" });
  const res = await fetch(url, { headers: getAuthHeaders() });
      if (!res.ok) throw new Error(`Falha ao carregar ranking (${res.status})`);
      const json = await res.json();
      const items: RankingItem[] = json?.data || [];

  const coerce = (x: any) => ({ ...x, paymentAmount: Number(x.paymentAmount) || 0, voteCount: Number(x.voteCount) || 0 });
  const normalized = items.map(coerce);
  const paid = normalized.filter((i) => i.isPaid).sort((a, b) => b.voteCount - a.voteCount);
  const free = normalized.filter((i) => !i.isPaid).sort((a, b) => b.voteCount - a.voteCount);

      setRankingPaid(paid);
      setRankingFree(free);
    } catch (e) {
      console.warn("Não foi possível obter ranking colaborativo:", e);
    }
  };

  // Carregar estatísticas agregadas (Colaborativa)
  const loadCollaborativeStats = async () => {
    if (!restaurantId) return;
    try {
      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/stats`);
  const res = await fetch(url, { headers: getAuthHeaders() });
      if (!res.ok) throw new Error(`Falha ao carregar stats (${res.status})`);
      const json = await res.json();
      // Aceitar tanto data quanto root
      setCollabStats(json?.data ?? json ?? null);
    } catch (e) {
      console.warn("Não foi possível obter stats colaborativas:", e);
    }
  };

  // Recalcular prévia automática e total de votos quando o ranking muda
  useEffect(() => {
    const combined = [...rankingPaid, ...rankingFree].sort((a, b) => {
      // Paga primeiro, depois maior valor pago, depois mais votos
      const paidDiff = Number(b.isPaid) - Number(a.isPaid);
      if (paidDiff !== 0) return paidDiff;
      const payDiff = toNumber(b.paymentAmount) - toNumber(a.paymentAmount);
      if (payDiff !== 0) return payDiff;
      return toNumber(b.voteCount) - toNumber(a.voteCount);
    });
    setAutoPreview(combined);
    setTotalVotes(combined.reduce((sum, it) => sum + toNumber(it.voteCount), 0));
    // Aggregates for header chips
    const totalSuperVotes = combined.reduce((sum, it) => sum + toNumber(it.superVoteCount), 0);
    const totalNormalVotes = combined.reduce((sum, it) => sum + toNumber(it.normalVoteCount), 0);
    setVoteAggregates({
      totalSuperVotes,
      totalNormalVotes,
      paidItems: rankingPaid.length,
      freeItems: rankingFree.length,
    });
  // Compute revenue fallback from paid items
  const revenue = rankingPaid.reduce((sum, it) => sum + toNumber((it as any).paymentAmount ?? 0), 0);
  setComputedRevenue(revenue);
  }, [rankingPaid, rankingFree]);

  // Iniciar contador regressivo até próxima reordenação
  const startCountdown = (target: Date) => {
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);

    const tick = async () => {
      const diff = Math.max(0, Math.floor((target.getTime() - Date.now()) / 1000));
      setCountdown(diff);
      if (diff <= 0) {
        clearInterval(countdownIntervalRef.current as NodeJS.Timeout);
        countdownIntervalRef.current = null;
        if (autoReorder) {
          await handleReorderByVotes();
        }
      }
    };

    tick();
    countdownIntervalRef.current = setInterval(tick, 1000);
  };

  // Disparar reordenação no servidor
  const handleReorderByVotes = async () => {
    if (!restaurantId) return;
    try {
      const res = await fetch(buildApiUrl(`/collaborative-playlist/${restaurantId}/reorder`), {
        method: "POST",
  headers: getAuthHeaders(),
      });
      if (!res.ok) throw new Error(`Falha ao reordenar (${res.status})`);
      toast.success("Playlist reordenada por votos");
      // Atualizar filas e ranking após reordenar
      await Promise.all([loadQueues(), loadVotingRanking(), loadPlaybackState()]);
    } catch (e) {
      console.error("Erro ao reordenar por votos:", e);
      toast.error("Erro ao reordenar por votos");
    } finally {
      const target = new Date(Date.now() + 5 * 60 * 1000);
      setNextReorderAt(target);
      startCountdown(target);
    }
  };

  const calculateEstimatedWaitTime = useCallback((queue: Track[]) => {
    return queue.reduce((total, track, index) => {
      if (index === 0) return 0; // Música atual
      return total + (track.duration || 180); // 3min padrão
    }, 0);
  }, []);

  // ===== Ações de Voto (Admin no Controller) =====
  const voteNormalFromController = async (youtubeVideoId: string) => {
    if (!restaurantId) return;
    try {
      const res = await fetch(
        buildApiUrl(`/collaborative-playlist/${restaurantId}/vote`),
        {
          method: "POST",
          headers: getAuthHeaders("application/json"),
          body: JSON.stringify({ youtubeVideoId, clientSessionId: "admin_panel" }),
        }
      );
      if (!res.ok) {
        const t = await res.text().catch(() => "");
        throw new Error(`Erro ao votar: ${res.status} ${t}`);
      }
      toast.success("Voto registrado (normal)");
      await Promise.all([loadVotingRanking(), loadQueues()]);
    } catch (e) {
      console.error(e);
      toast.error("Falha ao registrar voto (normal)");
    }
  };

  const superVoteFromController = async (
    youtubeVideoId: string,
    paymentAmount: 5 | 20 | 50
  ) => {
    if (!restaurantId) return;
    try {
      const res = await fetch(
        buildApiUrl(`/collaborative-playlist/${restaurantId}/supervote`),
        {
          method: "POST",
          headers: getAuthHeaders("application/json"),
          body: JSON.stringify({
            youtubeVideoId,
            paymentAmount,
            paymentId: `admin_test_${Date.now()}`,
            clientSessionId: "admin_panel",
          }),
        }
      );
      if (!res.ok) {
        const t = await res.text().catch(() => "");
        throw new Error(`Erro no supervoto: ${res.status} ${t}`);
      }
      toast.success(`Supervoto R$ ${paymentAmount} registrado`);
      await Promise.all([loadVotingRanking(), loadQueues()]);
    } catch (e) {
      console.error(e);
      toast.error("Falha ao registrar supervoto");
    }
  };

  const makeApiCall = async (url: string, options?: RequestInit, retries = 3): Promise<any> => {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        const baseHeaders: Record<string, string> = {
          "Content-Type": "application/json",
        };
        const token = typeof localStorage !== "undefined" ? localStorage.getItem("authToken") : null;
        if (token) baseHeaders["Authorization"] = `Bearer ${token}`;

        const response = await fetch(url, {
          ...options,
          headers: {
            ...baseHeaders,
            ...(options?.headers as any),
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        setRetryCount(0); // Reset retry count on success
        return data;
      } catch (error) {
        console.error(`API call failed (attempt ${attempt + 1}):`, error);

        if (attempt === retries - 1) {
          setRetryCount((prev) => prev + 1);
          toast.error(`Falha na conexão. Tentativa ${retryCount + 1}`);
          return undefined;
        }

        // Exponential backoff
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
    return undefined;
  };

  const loadPlaybackState = async () => {
    if (!restaurantId) {
      console.error("Restaurant ID não encontrado");
      return;
    }

    try {
      const data = await makeApiCall(buildApiUrl(`/playback/${restaurantId}/state`));

      if (data?.state) {
        // Ensure createdAt is a Date object
        if (data.state.currentTrack && data.state.currentTrack.createdAt) {
          data.state.currentTrack.createdAt = new Date(data.state.currentTrack.createdAt);
        }
        setPlaybackState(data.state);
        PlaybackCache.setCache(`playback_${restaurantId}`, data.state);
      } else {
        // Estado padrão se não houver dados
        const defaultState: PlaybackState = {
          isPlaying: false,
          currentTrack: null,
          volume: 50,
          currentTime: 0,
          queue: [],
          priorityQueue: [],
          normalQueue: [],
          history: [],
          connectionStatus: isConnected ? "connected" : "disconnected",
        };
        setPlaybackState(defaultState);
      }
    } catch (error) {
      console.error("Erro ao carregar estado de reprodução:", error);

      // Usar cache em caso de erro
      const cached = PlaybackCache.getCache(`playback_${restaurantId}`);
      if (cached) {
        setPlaybackState(cached);
  toast("Usando dados em cache devido à falha de conexão", { icon: "ℹ️" });
      }
    } finally {
      setLoading(false);
      setLastUpdateTime(new Date());
    }
  };

  const loadQueues = async () => {
    if (!restaurantId) return;

    try {
      // 🎯 USAR LIVEPLAYLISTSERVICE COMO PADRÃO
      console.log("🎵 Carregando filas via LivePlaylistService...");

      // 1. Obter estado da playlist ao vivo
      const livePlaylistData = await makeApiCall(
        buildApiUrl(`/live-playlist/${restaurantId}/state`)
      );

      if (livePlaylistData?.success && livePlaylistData?.data) {
        const state = livePlaylistData.data;

        // 2. Buscar sugestões pagas (fila prioritária)
        const paidSuggestions = await makeApiCall(
          buildApiUrl(`/suggestions/${restaurantId}?isPaid=true&status=approved`)
        );

        // 3. Buscar sugestões gratuitas (fila normal)
        const freeSuggestions = await makeApiCall(
          buildApiUrl(`/suggestions/${restaurantId}?isPaid=false&status=approved`)
        );

        const priority = (paidSuggestions?.suggestions || []).map((track: any) => ({
          ...track,
          createdAt: new Date(track.createdAt),
          isPaid: true,
        }));

        const normal = (freeSuggestions?.suggestions || []).map((track: any) => ({
          ...track,
          createdAt: new Date(track.createdAt),
          isPaid: false,
        }));

        setPriorityQueue(priority);
        setNormalQueue(normal);

        const estimatedWait = calculateEstimatedWaitTime([...priority, ...normal]);

        // 4. Usar estatísticas do LivePlaylistService
        setQueueStats({
          totalItems: state.paidSuggestionsCount + state.freeSuggestionsCount,
          paidItems: state.paidSuggestionsCount,
          freeItems: state.freeSuggestionsCount,
          estimatedWaitTime: estimatedWait,
        });

        PlaybackCache.setCache(`queue_${restaurantId}`, { priorityQueue: priority, normalQueue: normal });

        console.log(
          `🎵 LivePlaylist: ${state.paidSuggestionsCount} prioritárias, ${state.freeSuggestionsCount} normais, tempo estimado: ${Math.round(
            estimatedWait / 60
          )}min`
        );

        return; // Sucesso com LivePlaylistService
      }
    } catch (liveError) {
      console.warn("⚠️ LivePlaylistService falhou, usando fallback:", liveError);
    }

    // 🔄 FALLBACK PARA SISTEMA ANTIGO
    try {
      console.log("🔄 Usando sistema de filas antigo como fallback...");
      const data = await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}`));

      // Separar filas por tipo de pagamento
      const allQueue = (data.queue || []).map((track: any) => ({
        ...track,
        createdAt: new Date(track.createdAt), // Ensure createdAt is a Date
      }));
      const priority = allQueue.filter((track: any) => track.isPaid);
      const normal = allQueue.filter((track: any) => !track.isPaid);

      setPriorityQueue(priority);
      setNormalQueue(normal);

      const estimatedWait = calculateEstimatedWaitTime(allQueue);

      setQueueStats({
        totalItems: allQueue.length,
        paidItems: priority.length,
        freeItems: normal.length,
        estimatedWaitTime: estimatedWait,
      });

      PlaybackCache.setCache(`queue_${restaurantId}`, { priorityQueue: priority, normalQueue: normal });

      console.log(
        `🎵 Fallback: ${priority.length} prioritárias, ${normal.length} normais, tempo estimado: ${Math.round(
          estimatedWait / 60
        )}min`
      );
    } catch (error) {
      console.error("❌ Erro ao carregar filas (fallback também falhou):", error);

      // Usar cache em caso de erro
      const cached = PlaybackCache.getCache(`queue_${restaurantId}`);
      if (cached) {
        setPriorityQueue(cached.priorityQueue || []);
        setNormalQueue(cached.normalQueue || []);
  toast("Usando dados de fila em cache", { icon: "ℹ️" });
      }
    }
  };

  // ===== Helpers e Ações somente Front =====
  const extractYouTubeId = (input: string): string | null => {
    try {
      if (!input) return null;
      // Caso já seja um ID simples
      if (/^[a-zA-Z0-9_-]{6,}$/.test(input)) return input;
      const url = new URL(input);
      if (url.hostname.includes("youtu.be")) {
        return url.pathname.replace("/", "");
      }
      if (url.hostname.includes("youtube.com")) {
        return url.searchParams.get("v");
      }
      return null;
    } catch {
      // Não é URL, talvez seja um ID simples
      return /^[a-zA-Z0-9_-]{6,}$/.test(input) ? input : null;
    }
  };

  const handleManualAdd = (toPaid: boolean) => {
    const id = extractYouTubeId(manualVideoInput.trim());
    if (!id) {
      toast.error("Informe um link ou ID válido do YouTube");
      return;
    }
    const newTrack: Track = {
      id: `local_${Date.now()}`,
      youtubeVideoId: id,
      title: `Música (${id})`,
      artist: "Artista Desconhecido",
      duration: 180,
      thumbnailUrl: `https://img.youtube.com/vi/${id}/mqdefault.jpg`,
      upvotes: 0,
      downvotes: 0,
      score: 0,
      createdAt: new Date(),
    };
    if (toPaid) {
      setPriorityQueue((prev) => [newTrack, ...prev]);
      toast.success("Adicionada à fila prioritária (somente UI)");
    } else {
      setNormalQueue((prev) => [newTrack, ...prev]);
      toast.success("Adicionada à fila normal (somente UI)");
    }
    setManualVideoInput("");
  };

  const handleClearQueues = () => {
    setPriorityQueue([]);
    setNormalQueue([]);
    setQueueStats((s) => ({ ...s, totalItems: 0, paidItems: 0, freeItems: 0 }));
    toast.success("Filas limpas (somente UI)");
  };

  const handleMoveInQueue = (
    which: "priority" | "normal",
    trackId: string,
    direction: "up" | "down"
  ) => {
    const list = which === "priority" ? [...priorityQueue] : [...normalQueue];
    const index = list.findIndex((t) => t.id === trackId);
    if (index < 0) return;
    const targetIndex = direction === "up" ? index - 1 : index + 1;
    if (targetIndex < 0 || targetIndex >= list.length) return;
    const temp = list[targetIndex];
    list[targetIndex] = list[index];
    list[index] = temp;
    if (which === "priority") setPriorityQueue(list);
    else setNormalQueue(list);
  };

  const handleDemoteToNormal = (trackId: string) => {
    const idx = priorityQueue.findIndex((t) => t.id === trackId);
    if (idx >= 0) {
      const item = priorityQueue[idx];
      const nextPrio = [...priorityQueue];
      nextPrio.splice(idx, 1);
      setPriorityQueue(nextPrio);
      setNormalQueue((prev) => [item, ...prev]);
      toast("Movida para fila normal (somente UI)");
    }
  };

  const toggleLocal = (key: keyof typeof localSettings) => {
    setLocalSettings((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handlePlayPause = async () => {
    if (!playbackState || !restaurantId) return;

    setActionLoading("playpause");

    try {
      const endpoint = playbackState.isPlaying ? "pause" : "resume";
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/${endpoint}`), {
        method: "POST",
      });

      const newState = { ...playbackState, isPlaying: !playbackState.isPlaying };
      setPlaybackState(newState);
      PlaybackCache.setCache(`playback_${restaurantId}`, newState);

      toast.success(playbackState.isPlaying ? "Reprodução pausada" : "Reprodução retomada");

      // Registro de analytics
      logUserAction("playback_toggle", {
        action: endpoint,
        trackId: playbackState.currentTrack?.id,
      });
    } catch (error) {
      toast.error("Erro ao controlar reprodução");
    } finally {
      setActionLoading(null);
    }
  };

  const handleSkip = async () => {
    if (!restaurantId) return;

    setActionLoading("skip");

    try {
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/skip`), { method: "POST" });

      toast.success("Música pulada");

      // Registro de analytics
      logUserAction("track_skip", {
        trackId: playbackState?.currentTrack?.id,
        skipTime: playbackState?.currentTime,
      });

      // Recarregar estado após skip
      await loadPlaybackState();
      await loadQueues();
    } catch (error) {
      toast.error("Erro ao pular música");
    } finally {
      setActionLoading(null);
    }
  };

  const handleStartNext = async () => {
    if (!restaurantId) return;

    setActionLoading("start");

    // Lógica de prioridade: fila paga > fila normal
    let nextTrack = null;
    let queueType = "";

    if (priorityQueue.length > 0) {
      nextTrack = priorityQueue[0];
      queueType = "priority";
      console.log(`🔥 Tocando da fila prioritária: ${nextTrack.title}`);
      toast.success(`Tocando da fila prioritária: ${nextTrack.title}`);
    } else if (normalQueue.length > 0) {
      // Ordenar fila normal por votos
      const sortedNormal = [...normalQueue].sort(
        (a, b) => b.upvotes - b.downvotes - (a.upvotes - a.downvotes)
      );
      nextTrack = sortedNormal[0];
      queueType = "normal";
      console.log(`📋 Tocando da fila normal: ${nextTrack.title}`);
      toast.success(`Tocando da fila normal: ${nextTrack.title}`);
    }

    if (!nextTrack) {
      toast.error("Nenhuma música na fila");
      setActionLoading(null);
      return;
    }

    try {
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/play`), {
        method: "POST",
        body: JSON.stringify({ songId: nextTrack.id }),
      });

      toast.success(`Tocando: ${nextTrack.title}`);

      // Registro de analytics
      logUserAction("track_start", {
        trackId: nextTrack.id,
        queueType,
        trackTitle: nextTrack.title,
      });

      // Recarregar dados
      await Promise.all([loadPlaybackState(), loadQueues()]);
    } catch (error) {
      console.error("Erro ao iniciar próxima música:", error);
      toast.error("Erro ao iniciar próxima música");
    } finally {
      setActionLoading(null);
    }
  };

  const handleVolumeChange = async (newVolume: number) => {
    if (!restaurantId || !playbackState) return;

    try {
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/volume`), {
        method: "POST",
        body: JSON.stringify({ volume: newVolume }),
      });

      const newState = { ...playbackState, volume: newVolume };
      setPlaybackState(newState);
      PlaybackCache.setCache(`playback_${restaurantId}`, newState);

      // Registro de analytics
      logUserAction("volume_change", {
        previousVolume: playbackState.volume,
        newVolume,
      });
    } catch (error) {
      toast.error("Erro ao ajustar volume");
    }
  };

  const toggleMute = () => {
    if (!playbackState) return;

    const newVolume = playbackState.volume === 0 ? 50 : 0;
    handleVolumeChange(newVolume);

    // Registro de analytics
    logUserAction("volume_mute_toggle", {
      action: newVolume === 0 ? "mute" : "unmute",
    });
  };

  const handleRemoveFromQueue = async (trackId: string) => {
    if (!restaurantId) return;

    setActionLoading(`remove-priority-${trackId}`);

    try {
      await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}/remove`), {
        method: "POST",
        body: JSON.stringify({ trackId }),
      });

      toast.success("Música removida da fila");

      // Registro de analytics
      logUserAction("track_remove", { trackId });

      // Recarregar filas
      await loadQueues();
    } catch (error) {
      toast.error("Erro ao remover música da fila");
    } finally {
      setActionLoading(null);
    }
  };

  const handlePromoteTrack = async (trackId: string) => {
    if (!restaurantId) return;

    setActionLoading(`promote-${trackId}`);

    try {
      await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}/promote`), {
        method: "POST",
        body: JSON.stringify({ trackId }),
      });

      toast.success("Música promovida para fila prioritária");

      // Registro de analytics
      logUserAction("track_promote", { trackId });

      // Recarregar filas
      await loadQueues();
    } catch (error) {
      toast.error("Erro ao promover música");
    } finally {
      setActionLoading(null);
    }
  };

  // Função para registrar ações do usuário para analytics
  const logUserAction = (action: string, data: any) => {
    try {
      // Enviar para analytics (implementar conforme necessário)
      console.log(`📊 Analytics: ${action}`, data);

      // Opcional: salvar no localStorage para análise posterior
      const analyticsData = {
        timestamp: new Date().toISOString(),
        restaurantId,
        action,
        data,
      };

      const existingLogs = JSON.parse(localStorage.getItem("playback_analytics") || "[]");
      existingLogs.push(analyticsData);

      // Manter apenas os últimos 100 registros
      if (existingLogs.length > 100) {
        existingLogs.splice(0, existingLogs.length - 100);
      }

      localStorage.setItem("playback_analytics", JSON.stringify(existingLogs));
    } catch (error) {
      console.error("Erro ao registrar analytics:", error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatEstimatedTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}min`;
    return `${Math.round(seconds / 3600)}h`;
  };

  // Helpers para valores monetários (evita erro de toFixed quando vier string/null)
  const toNumber = (v: any): number => {
    const n = Number(v);
    return Number.isFinite(n) ? n : 0;
  };
  const formatBRL = (v: any): string =>
    toNumber(v).toLocaleString("pt-BR", { minimumFractionDigits: 2, maximumFractionDigits: 2 });

  const getProgressPercentage = () => {
    if (!playbackState?.currentTrack) return 0;
    return (playbackState.currentTime / playbackState.currentTrack.duration) * 100;
  };

  // Conexão: não exibir status visual (solicitado para remover da UI)

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-center items-center h-32">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
  <Toaster position="top-left" />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Controle da Playlist (Colaborativa)</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">Gerencie a reprodução e acompanhe votos, supervotos e reordenações automáticas.</p>
        </div>
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-3 py-1.5 rounded-lg">
            <input type="checkbox" checked={autoReorder} onChange={(e) => setAutoReorder(e.target.checked)} />
            Auto-reordenar
          </label>
          <button
            onClick={handleReorderByVotes}
            className="px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm"
          >
            Reordenar agora
          </button>
          <button
            onClick={loadInitialData}
            disabled={actionLoading === "refresh"}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            aria-label="Atualizar dados de reprodução"
          >
            {actionLoading === "refresh" ? <Loader2 className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Atualizar
          </button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Próxima reordenação</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatEstimatedTime(countdown)}</p>
            </div>
            <RefreshCw className="w-7 h-7 text-indigo-500" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total de votos</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{collabStats?.totalVotes ?? totalVotes}</p>
            </div>
            <TrendingUp className="w-7 h-7 text-green-500" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Itens pagos</p>
              <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">{voteAggregates.paidItems}</p>
            </div>
            <Users className="w-7 h-7 text-yellow-500" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Itens grátis</p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">{voteAggregates.freeItems}</p>
            </div>
            <Users className="w-7 h-7 text-green-500" />
          </div>
        </div>
      </div>

      {/* Receita (link para Analytics) */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/10 rounded-lg p-4 border border-indigo-200 dark:border-indigo-700 flex items-center justify-between">
        <div>
          <p className="text-sm text-indigo-700 dark:text-indigo-300">Receita total acumulada</p>
          <p className="text-2xl font-bold text-indigo-900 dark:text-indigo-100">R$ {formatBRL(collabStats?.totalRevenue ?? computedRevenue)}</p>
        </div>
        <a
          href={`/restaurant/${restaurantId}/dashboard/analytics`}
          className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm"
        >
          <ExternalLink className="w-4 h-4" /> Ver Analytics
        </a>
      </div>

      {/* Player Principal */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        {playbackState?.currentTrack ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            {/* Informações da música atual */}
            <div className="flex items-center space-x-4">
              <img
                src={playbackState.currentTrack.thumbnailUrl}
                alt={playbackState.currentTrack.title}
                className="w-16 h-12 object-cover rounded-lg shadow-sm"
              />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {playbackState.currentTrack.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {playbackState.currentTrack.artist}
                </p>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center space-x-1 text-green-600">
                    <TrendingUp className="w-4 h-4" aria-hidden="true" />
                    <span className="text-sm">{playbackState.currentTrack.upvotes}</span>
                  </div>
                  <div className="flex items-center space-x-1 text-red-600">
                    <AlertTriangle className="w-4 h-4" aria-hidden="true" />
                    <span className="text-sm">{playbackState.currentTrack.downvotes}</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    Score: {playbackState.currentTrack.score}
                  </div>
                </div>
              </div>
            </div>

            {/* Barra de progresso */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-500">
                <span>{formatTime(playbackState.currentTime)}</span>
                <span>{formatTime(playbackState.currentTrack.duration)}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-blue-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${getProgressPercentage()}%` }}
                  transition={{ duration: 1 }}
                  role="progressbar"
                  aria-valuenow={getProgressPercentage()}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  aria-label="Progresso da música"
                />
              </div>
            </div>

            {/* Controles */}
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={handlePlayPause}
                disabled={actionLoading === "playpause"}
                className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label={playbackState.isPlaying ? "Pausar" : "Reproduzir"}
              >
                {actionLoading === "playpause" ? (
                  <Loader2 className="w-6 h-6 animate-spin" />
                ) : playbackState.isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6 ml-1" />
                )}
              </button>

              <button
                onClick={handleSkip}
                disabled={actionLoading === "skip"}
                className="flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Pular música"
              >
                {actionLoading === "skip" ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <SkipForward className="w-5 h-5" />
                )}
              </button>

              {/* Controle de volume */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  aria-label={playbackState.volume === 0 ? "Ativar som" : "Silenciar"}
                >
                  {playbackState.volume === 0 ? (
                    <VolumeX className="w-5 h-5 text-gray-600" />
                  ) : (
                    <Volume2 className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={playbackState.volume}
                  onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                  className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  aria-label="Controle de volume"
                />
                <span className="text-sm text-gray-500 w-8">{playbackState.volume}</span>
              </div>
            </div>
          </motion.div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="w-16 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Sem reprodução</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Quando a próxima música começar, os controles ficam ativos.</p>
              </div>
            </div>

            {/* Ações Rápidas (somente UI) */}
            <div className="bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <Settings2 className="w-4 h-4" />
                  <span className="font-medium text-sm">Ações Rápidas</span>
                </div>
                <button onClick={handleClearQueues} className="flex items-center gap-1 text-red-600 hover:text-red-700 text-sm">
                  <Trash2 className="w-4 h-4" /> Limpar Filas
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                <button onClick={loadInitialData} className="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">Atualizar</button>
                <button onClick={handleReorderByVotes} className="px-3 py-1.5 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm">Reordenar por Votos</button>
                <button onClick={() => toggleLocal('shuffle')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${localSettings.shuffle ? 'bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}>
                  <Shuffle className="w-4 h-4" /> Shuffle
                </button>
                <button onClick={() => toggleLocal('repeat')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${localSettings.repeat ? 'bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}>
                  <Repeat className="w-4 h-4" /> Repeat
                </button>
                <button onClick={() => toggleLocal('lockVoting')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 ${localSettings.lockVoting ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}>
                  {localSettings.lockVoting ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />} {localSettings.lockVoting ? 'Votação Travada' : 'Votação Liberada'}
                </button>
              </div>
            </div>

            {/* Adicionar Manualmente (somente UI) */}
            <div className="bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-3">
                <ExternalLink className="w-4 h-4" />
                <span className="font-medium text-sm">Adicionar música manualmente</span>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <input
                  value={manualVideoInput}
                  onChange={(e) => setManualVideoInput(e.target.value)}
                  placeholder="Cole o link ou ID do YouTube"
                  className="flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                />
                <div className="flex gap-2">
                  <button onClick={() => handleManualAdd(false)} className="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm">Adicionar na Fila Normal</button>
                  <button onClick={() => handleManualAdd(true)} className="px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm">Adicionar na Fila Prioritária</button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Estatísticas das Filas + Histórico de Reordenação */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Estatísticas */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total na Fila</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {queueStats.totalItems}
              </p>
            </div>
            <Music className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-yellow-200 dark:border-yellow-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-yellow-600 dark:text-yellow-400">Fila Prioritária</p>
              <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">
                {queueStats.paidItems}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-green-200 dark:border-green-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 dark:text-green-400">Fila Normal</p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                {queueStats.freeItems}
              </p>
            </div>
            <Users className="w-8 h-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Histórico de Reordenação */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-indigo-200 dark:border-indigo-700 mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Histórico de Reordenação</h3>
          <span className="text-xs px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200">
            {reorderHistory.length} eventos
          </span>
        </div>
        {reorderHistory.length === 0 ? (
          <div className="text-sm text-gray-600 dark:text-gray-400">Nenhum evento de reordenação ainda</div>
        ) : (
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {reorderHistory.map((evt, idx) => (
              <div key={idx} className="p-3 rounded border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20">
                <div className="flex items-center justify-between text-sm">
                  <div className="font-medium text-gray-800 dark:text-gray-200">
                    {new Date(evt.time).toLocaleString("pt-BR")} • {evt.playlistName || "Playlist"}
                  </div>
                  <div className="text-xs text-indigo-700 dark:text-indigo-300">{evt.count} músicas impactadas</div>
                </div>
                <div className="mt-2 text-xs text-gray-700 dark:text-gray-300">
                  Top 5:
                  <ul className="list-disc list-inside">
                    {(evt.details || []).map((t: any, i: number) => (
                      <li key={i} className="truncate">
                        {t.title || t.videoId} {t.isPaid ? "(Paga)" : ""} — votos: {t.voteCount ?? "?"}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Filas Separadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fila Prioritária */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-yellow-200 dark:border-yellow-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              🔥 Fila Prioritária ({priorityQueue.length})
            </h3>
            <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium">
              SuperVoto (R$ 5, 20, 50)
            </div>
          </div>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {loading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: i * 0.1 }}
                    className="flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg animate-pulse"
                  >
                    <div className="w-8 h-8 bg-yellow-200 dark:bg-yellow-700 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-yellow-200 dark:bg-yellow-700 rounded w-3/4" />
                      <div className="h-3 bg-yellow-200 dark:bg-yellow-700 rounded w-1/2" />
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : priorityQueue.length > 0 ? (
              <AnimatePresence>
                {priorityQueue.map((track, index) => (
                  <motion.div
                    key={track.id}
                    layout
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-xs flex items-center justify-center font-bold">
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {track.title}
                      </h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {track.artist}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-xs text-gray-500">{formatTime(track.duration)}</div>
                      <button
                        onClick={() => handleMoveInQueue('priority', track.id, 'up')}
                        className="p-1 text-gray-600 hover:text-gray-800"
                        title="Mover para cima"
                        aria-label="Mover para cima"
                      >
                        <ArrowUp className="w-3 h-3" />
                      </button>
                      <button
                        onClick={() => handleMoveInQueue('priority', track.id, 'down')}
                        className="p-1 text-gray-600 hover:text-gray-800"
                        title="Mover para baixo"
                        aria-label="Mover para baixo"
                      >
                        <ArrowDown className="w-3 h-3" />
                      </button>
                      <button
                        onClick={() => handleDemoteToNormal(track.id)}
                        className="p-1 text-blue-600 hover:text-blue-800"
                        title="Mover para fila normal"
                        aria-label="Mover para fila normal"
                      >
                        <ChevronUp className="w-3 h-3 rotate-180" />
                      </button>
                      <button
                        onClick={() => handleRemoveFromQueue(track.id)}
                        disabled={actionLoading === `remove-priority-${track.id}`}
                        className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        aria-label="Remover da fila prioritária"
                        title="Remover"
                      >
                        {actionLoading === `remove-priority-${track.id}` ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <X className="w-3 h-3" />
                        )}
                      </button>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-8"
              >
                <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-yellow-600" />
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Nenhuma música na fila prioritária
                </p>
              </motion.div>
            )}
          </div>
        </div>

        {/* Fila Normal */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-green-200 dark:border-green-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              📋 Fila Normal ({normalQueue.length})
            </h3>
            <div className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs font-medium">
              Gratuito
            </div>
          </div>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {loading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: i * 0.1 }}
                    className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg animate-pulse"
                  >
                    <div className="w-6 h-6 bg-green-200 dark:bg-green-700 rounded" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-green-200 dark:bg-green-700 rounded w-3/4" />
                      <div className="h-3 bg-green-200 dark:bg-green-700 rounded w-1/2" />
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : normalQueue.length > 0 ? (
              <AnimatePresence>
                {normalQueue
                  .slice()
                  .sort((a, b) => b.upvotes - b.downvotes - (a.upvotes - a.downvotes))
                  .map((track, index) => (
                    <motion.div
                      key={track.id}
                      layout
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.2 }}
                      className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700"
                    >
                      <div className="w-6 h-6 bg-green-600 text-white rounded text-xs flex items-center justify-center font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {track.title}
                        </h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                          {track.artist}
                        </p>
                        <div className="flex items-center space-x-3 mt-1">
                          <div className="flex items-center space-x-1">
                            <TrendingUp className="w-3 h-3 text-green-600" aria-hidden="true" />
                            <span className="text-xs text-green-600">{track.upvotes}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <AlertTriangle className="w-3 h-3 text-red-600" aria-hidden="true" />
                            <span className="text-xs text-red-600">{track.downvotes}</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            Score: {track.score || track.upvotes - track.downvotes}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="text-xs text-gray-500">{formatTime(track.duration)}</div>
                        <button
                          onClick={() => handleMoveInQueue('normal', track.id, 'up')}
                          className="p-1 text-gray-600 hover:text-gray-800"
                          title="Mover para cima"
                          aria-label="Mover para cima"
                        >
                          <ArrowUp className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => handleMoveInQueue('normal', track.id, 'down')}
                          className="p-1 text-gray-600 hover:text-gray-800"
                          title="Mover para baixo"
                          aria-label="Mover para baixo"
                        >
                          <ArrowDown className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => handlePromoteTrack(track.id)}
                          disabled={actionLoading === `promote-${track.id}`}
                          className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          aria-label="Promover para fila prioritária"
                          title="Promover"
                        >
                          {actionLoading === `promote-${track.id}` ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            <ChevronUp className="w-3 h-3" />
                          )}
                        </button>
                        <button
                          onClick={() => handleRemoveFromQueue(track.id)}
                          disabled={actionLoading === `remove-normal-${track.id}`}
                          className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          aria-label="Remover da fila normal"
                          title="Remover"
                        >
                          {actionLoading === `remove-normal-${track.id}` ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            <X className="w-3 h-3" />
                          )}
                        </button>
                      </div>
                    </motion.div>
                  ))}
              </AnimatePresence>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-8"
              >
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Nenhuma música na fila normal
                </p>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Analytics Tracking */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="mt-6"
      >
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Sistema ativo - Última atualização: {new Date().toLocaleTimeString('pt-BR')}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-600 dark:text-green-400">Online</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Votações (Colaborativa) */}
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Votações (Colaborativa)</h3>
            <span className="inline-flex items-center gap-1 text-xs px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200">
              <RefreshCw className="w-3 h-3" /> {formatEstimatedTime(countdown)}
            </span>
          </div>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Reordenação automática baseada em votos
          </p>

          {/* Mini-cards grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-3">
            <div className="bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600 dark:text-gray-400">Próx. reordenação</span>
                <Clock className="w-4 h-4 text-indigo-500" />
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white">{formatEstimatedTime(countdown)}</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600 dark:text-gray-400">Total de votos</span>
                <ThumbsUp className="w-4 h-4 text-green-500" />
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white">{collabStats?.totalVotes ?? totalVotes}</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600 dark:text-gray-400">Receita total</span>
                <DollarSign className="w-4 h-4 text-yellow-500" />
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white">R$ {formatBRL(collabStats?.totalRevenue ?? computedRevenue)}</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded p-3 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600 dark:text-gray-400">Pagas/Grátis</span>
                <Gift className="w-4 h-4 text-purple-500" />
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white">{voteAggregates.paidItems}/{voteAggregates.freeItems}</div>
              <div className="mt-1 text-xs text-gray-600 dark:text-gray-400">SV: {voteAggregates.totalSuperVotes} • VN: {voteAggregates.totalNormalVotes}</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* SuperVotos (Pagas) */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-yellow-200 dark:border-yellow-700">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">SuperVotos (Pagas)</h4>
              <span className="text-xs px-2 py-1 rounded bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200">
                {rankingPaid.length} itens
              </span>
            </div>
            <div className="space-y-2 max-h-72 overflow-y-auto">
              {rankingPaid.length === 0 ? (
                <div className="text-sm text-gray-600 dark:text-gray-400">Nenhum supervoto no momento</div>
              ) : (
                rankingPaid.map((item, idx) => (
                  <div key={item.youtubeVideoId + idx} className="flex items-center gap-3 p-3 rounded border border-yellow-200 dark:border-yellow-700 bg-yellow-50/60 dark:bg-yellow-900/20">
                    <div className="w-7 h-7 bg-yellow-500 text-white rounded flex items-center justify-center text-xs font-bold">{idx + 1}</div>
                    <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className="w-10 h-8 object-cover rounded" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{item.title || item.youtubeVideoId}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{item.artist || "—"}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-gray-700 dark:text-gray-300 mr-1">Votos: {item.voteCount}</div>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => voteNormalFromController(item.youtubeVideoId)}
                          className="px-2 py-0.5 border border-gray-300 dark:border-gray-600 rounded text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
                          title="Voto normal"
                        >+1</button>
                        <button
                          onClick={() => superVoteFromController(item.youtubeVideoId, 5)}
                          className="px-2 py-0.5 border border-yellow-400 dark:border-yellow-700 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                          title="Supervoto R$5"
                        >5</button>
                        <button
                          onClick={() => superVoteFromController(item.youtubeVideoId, 20)}
                          className="px-2 py-0.5 border border-yellow-500 dark:border-yellow-800 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/30"
                          title="Supervoto R$20"
                        >20</button>
                        <button
                          onClick={() => superVoteFromController(item.youtubeVideoId, 50)}
                          className="px-2 py-0.5 border border-yellow-600 dark:border-yellow-900 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/40"
                          title="Supervoto R$50"
                        >50</button>
                      </div>
                      <div className="text-xs text-green-700 dark:text-green-300 ml-1">R$ {formatBRL(item.paymentAmount)}</div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Votos (Grátis) */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-green-200 dark:border-green-700">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Votos (Grátis)</h4>
              <span className="text-xs px-2 py-1 rounded bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
                {rankingFree.length} itens
              </span>
            </div>
            <div className="space-y-2 max-h-72 overflow-y-auto">
              {rankingFree.length === 0 ? (
                <div className="text-sm text-gray-600 dark:text-gray-400">Nenhum voto gratuito no momento</div>
              ) : (
                rankingFree.map((item, idx) => (
                  <div key={item.youtubeVideoId + idx} className="flex items-center gap-3 p-3 rounded border border-green-200 dark:border-green-700 bg-green-50/60 dark:bg-green-900/20">
                    <div className="w-7 h-7 bg-green-600 text-white rounded flex items-center justify-center text-xs font-bold">{idx + 1}</div>
                    <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className="w-10 h-8 object-cover rounded" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{item.title || item.youtubeVideoId}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{item.artist || "—"}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-gray-700 dark:text-gray-300 mr-1">Votos: {item.voteCount}</div>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => voteNormalFromController(item.youtubeVideoId)}
                          className="px-2 py-0.5 border border-gray-300 dark:border-gray-600 rounded text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
                          title="Voto normal"
                        >+1</button>
                        <button
                          onClick={() => superVoteFromController(item.youtubeVideoId, 5)}
                          className="px-2 py-0.5 border border-yellow-400 dark:border-yellow-700 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                          title="Supervoto R$5"
                        >5</button>
                        <button
                          onClick={() => superVoteFromController(item.youtubeVideoId, 20)}
                          className="px-2 py-0.5 border border-yellow-500 dark:border-yellow-800 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/30"
                          title="Supervoto R$20"
                        >20</button>
                        <button
                          onClick={() => superVoteFromController(item.youtubeVideoId, 50)}
                          className="px-2 py-0.5 border border-yellow-600 dark:border-yellow-900 rounded text-xs hover:bg-yellow-50 dark:hover:bg-yellow-900/40"
                          title="Supervoto R$50"
                        >50</button>
                      </div>
                      <div className="text-xs text-gray-500 ml-1">Mesa: {item.tableNumber ?? "—"}</div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        {/* Prévia da Fila Automática de Reordenação */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Fila Automática (Prévia)</h4>
            <span className="text-xs text-gray-600 dark:text-gray-300">Aplicada quando o contador chegar a 0</span>
          </div>
          <div className="space-y-2 max-h-80 overflow-y-auto">
            {autoPreview.length === 0 ? (
              <div className="text-sm text-gray-600 dark:text-gray-400">Sem itens suficientes para prévia</div>
            ) : (
              autoPreview.slice(0, 20).map((item, idx) => (
                <div key={`${item.youtubeVideoId}-${idx}`} className="flex items-center gap-3 p-3 rounded border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/30">
                  <div className={`w-7 h-7 rounded text-white flex items-center justify-center text-xs font-bold ${item.isPaid ? 'bg-yellow-600' : 'bg-green-600'}`}>{idx + 1}</div>
                  <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className="w-10 h-8 object-cover rounded" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{item.title || item.youtubeVideoId}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{item.artist || '—'}</div>
                  </div>
                  <div className="flex items-center gap-3 text-right">
                    <span className={`text-xs px-2 py-0.5 rounded ${item.isPaid ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'}`}>
                      {item.isPaid ? 'Paga' : 'Grátis'}
                    </span>
                    <div className="text-xs text-gray-700 dark:text-gray-300">Votos: {item.voteCount}</div>
                    {item.isPaid && (
                      <div className="text-xs text-green-700 dark:text-green-300">R$ {formatBRL(item.paymentAmount)}</div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaybackController;