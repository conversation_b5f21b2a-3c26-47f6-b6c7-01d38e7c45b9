/**
 * 🎵 SERVIÇO DE PLAYLIST COLABORATIVA CENTRAL
 *
 * Usa uma única conta YouTube Premium para gerenciar todas as playlists
 * dos restaurantes, eliminando a necessidade de OAuth individual.
 */
export declare class CollaborativePlaylistService {
    private oauth2Client;
    private youtube;
    private restaurantRepository;
    private playlistRepository;
    private suggestionRepository;
    private youtubeService;
    private webSocketService;
    constructor();
    /**
     * 🏪 CRIAR PLAYLIST COLABORATIVA PARA NOVO RESTAURANTE
     */
    createRestaurantPlaylist(restaurantId: string, playlistName: string, description?: string): Promise<{
        success: boolean;
        playlistId?: string;
        youtubePlaylistId?: string;
        message: string;
    }>;
    /**
     * 🎵 ADICIONAR MÚSICAS INICIAIS À PLAYLIST
     */
    addInitialTracks(playlistId: string, videoIds: string[]): Promise<{
        success: boolean;
        message: string;
        addedCount?: number;
    }>;
    /**
     * 🚀 PROCESSAR SUGESTÃO PAGA (FILA PRIORITÁRIA)
     */
    processPaidSuggestion(restaurantId: string, suggestionId: string): Promise<{
        success: boolean;
        message: string;
        newPosition?: number;
    }>;
    /**
     * 🔒 Verifica se um vídeo pertence à playlist ativa do restaurante e retorna o track
     */
    private getActivePlaylistTrack;
    /**
     * 🗳️ PROCESSAR VOTO NORMAL (GRATUITO)
     */
    processNormalVote(restaurantId: string, youtubeVideoId: string, tableNumber?: number, clientSessionId?: string): Promise<{
        success: boolean;
        message: string;
        voteWeight: number;
    }>;
    /**
     * 💰 PROCESSAR SUPERVOTO (PAGO)
     */
    processSuperVote(restaurantId: string, youtubeVideoId: string, paymentAmount: number, paymentId: string, tableNumber?: number, clientSessionId?: string, clientMessage?: string, clientName?: string): Promise<{
        success: boolean;
        message: string;
        voteWeight: number;
    }>;
    /**
     * 🧮 CALCULAR PESO DO SUPERVOTO
     */
    private calculateSuperVoteWeight;
    /**
     * 🔄 ATUALIZAR METADADOS DE SUGESTÕES COM FALLBACK
     * Tenta recuperar metadados de vídeos que foram criados com dados mínimos
     */
    updateFallbackMetadata(restaurantId: string): Promise<{
        success: boolean;
        updated: number;
        message: string;
    }>;
    /**
     * 🔔 NOTIFICAR SUPERVOTO EM TEMPO REAL
     */
    private notifySuperVote;
    /**
     * 🔔 NOTIFICAR FALHA DA YOUTUBE API
     */
    private notifyYouTubeAPIFailure;
    /**
     * 🔔 NOTIFICAR ATUALIZAÇÃO DE METADADOS
     */
    private notifyMetadataUpdate;
    /**
     * 📊 OBTER RANKING DE VOTAÇÃO
     */
    getVotingRanking(restaurantId: string, limit?: number): Promise<{
        success: boolean;
        data?: Array<{
            youtubeVideoId: string;
            title?: string;
            artist?: string;
            voteCount: number;
            superVoteCount: number;
            normalVoteCount: number;
            totalRevenue: number;
            isPaid: boolean;
            paymentAmount: number;
            tableNumber?: number;
        }>;
        message: string;
    }>;
    /**
     * 🔄 REORDENAR PLAYLIST BASEADA EM VOTOS
     */
    reorderPlaylistByVotes(restaurantId: string): Promise<{
        success: boolean;
        message: string;
        reorderedCount?: number;
    }>;
    /**
     * 📊 OBTER ESTATÍSTICAS DA PLAYLIST COLABORATIVA
     */
    getPlaylistStats(restaurantId: string): Promise<{
        success: boolean;
        data?: {
            playlistName: string;
            youtubePlaylistId: string;
            totalTracks: number;
            paidSuggestions: number;
            freeSuggestions: number;
            totalRevenue: number;
            totalVotes: number;
            activeTables: number;
        };
        message: string;
    }>;
}
export declare const collaborativePlaylistService: CollaborativePlaylistService;
//# sourceMappingURL=CollaborativePlaylistService.d.ts.map