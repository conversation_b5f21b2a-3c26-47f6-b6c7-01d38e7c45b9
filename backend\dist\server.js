"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.server = exports.app = void 0;
require("reflect-metadata");
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = require("dotenv");
const logger_1 = require("./utils/logger");
const database_1 = require("./config/database");
const redis_1 = require("./config/redis");
const uuid_1 = require("uuid");
// Carregar variáveis de ambiente
(0, dotenv_1.config)();
// Importar rotas modulares
const genres_1 = __importDefault(require("./routes/genres"));
const restaurants_1 = __importDefault(require("./routes/restaurants"));
const suggestions_1 = __importDefault(require("./routes/suggestions"));
const playlists_1 = __importDefault(require("./routes/playlists"));
const analytics_1 = __importDefault(require("./routes/analytics"));
const auth_1 = __importDefault(require("./routes/auth"));
const client_1 = __importDefault(require("./routes/client"));
const notifications_1 = __importDefault(require("./routes/notifications"));
const payments_1 = __importDefault(require("./routes/payments"));
const playback_1 = __importDefault(require("./routes/playback"));
const playbackQueue_1 = __importDefault(require("./routes/playbackQueue"));
const rewards_1 = __importDefault(require("./routes/rewards"));
const youtube_1 = __importDefault(require("./routes/youtube"));
const livePlaylist_1 = __importDefault(require("./routes/livePlaylist"));
const collaborativePlaylist_1 = __importDefault(require("./routes/collaborativePlaylist"));
const test_youtube_1 = __importDefault(require("./routes/test-youtube"));
const youtubeAuth_1 = __importDefault(require("./routes/youtubeAuth"));
const qrcode_1 = __importDefault(require("./routes/qrcode"));
const businessHours_1 = __importDefault(require("./routes/businessHours"));
const competitiveVoting_1 = __importDefault(require("./routes/competitiveVoting"));
const lyrics_1 = __importDefault(require("./routes/lyrics"));
const schedules_1 = __importDefault(require("./routes/schedules"));
const playlistSchedules_1 = __importDefault(require("./routes/playlistSchedules"));
const playlistReorder_1 = __importDefault(require("./routes/playlistReorder"));
// Importar modelos
const Restaurant_1 = require("./models/Restaurant");
const User_1 = require("./models/User");
const Suggestion_1 = require("./models/Suggestion");
const Vote_1 = require("./models/Vote");
// Importar serviços
const YouTubeService_1 = require("./services/YouTubeService");
const NotificationService_1 = __importDefault(require("./services/NotificationService"));
const PlaybackService_1 = require("./services/PlaybackService");
const RewardService_1 = __importDefault(require("./services/RewardService"));
const WebSocketService_1 = require("./services/WebSocketService");
// Configurar Express
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
// Configurar CORS
const corsOptions = {
    origin: [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://localhost:5173",
        "https://restaurant-playlist.vercel.app",
        "https://restaurant-playlist-frontend.vercel.app",
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Accept",
        "Origin",
        "X-Tenant-ID",
        "X-Session-Token",
        "X-Session-ID",
        "X-Device-Info",
        "Cache-Control",
        "Pragma",
    ],
};
app.use((0, cors_1.default)(corsOptions));
// Middleware de segurança
app.use((0, helmet_1.default)({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false,
}));
// Compression
app.use((0, compression_1.default)());
// Logging
if (process.env.NODE_ENV === "development") {
    app.use((0, morgan_1.default)("dev"));
}
// Rate limiting - configuração mais permissiva para desenvolvimento
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 1 * 60 * 1000,
    max: 100,
    message: {
        error: "Muitas requisições deste IP, tente novamente em 1 minuto.",
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Ignorar rate limit para desenvolvimento local
    skip: (req) => {
        const isDevelopment = process.env.NODE_ENV === "development" ||
            req.ip === "127.0.0.1" ||
            req.ip === "::1";
        return isDevelopment;
    },
});
app.use(limiter);
// Middleware básico
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true, limit: "10mb" }));
// Middleware de tenant
const tenantMiddleware = (req, res, next) => {
    const tenantId = req.headers["x-tenant-id"] || req.query.restaurantId;
    if (tenantId) {
        req.tenantId = tenantId;
    }
    next();
};
app.use(tenantMiddleware);
// Logging personalizado
app.use((req, res, next) => {
    const start = Date.now();
    res.on("finish", () => {
        const duration = Date.now() - start;
        logger_1.logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
    });
    next();
});
// ==================== PRODUCTION DATA ONLY ====================
// Sistema configurado para usar apenas dados reais do banco de dados
// Health check
app.get("/health", (req, res) => {
    res.json({
        status: "OK",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        environment: process.env.NODE_ENV || "development",
        database: database_1.AppDataSource.isInitialized ? "connected" : "disconnected",
        redis: redis_1.redisClient.isReady ? "connected" : "disconnected",
    });
});
// Rota raiz
app.get("/", (req, res) => {
    res.json({
        message: "Restaurant Playlist API está funcionando!",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        environment: process.env.NODE_ENV || "development",
        endpoints: {
            health: "/health",
            api: "/api/v1",
            docs: "/api/docs",
        },
    });
});
// Socket.IO
const io = new socket_io_1.Server(server, {
    cors: corsOptions,
    transports: ["websocket", "polling"],
    pingTimeout: 60000,
    pingInterval: 25000,
});
exports.io = io;
// Inicializar WebSocketService
const wsService = WebSocketService_1.WebSocketService.getInstance(io);
// Disponibilizar Socket.IO para as rotas
app.set("io", io);
console.log("🔧 DEBUG: About to register routes...");
// Registrar rotas modulares
app.use("/api/v1/genres", genres_1.default);
app.use("/api/v1/restaurants", restaurants_1.default);
app.use("/api/v1/suggestions", suggestions_1.default);
app.use("/api/v1/playlists", playlists_1.default);
app.use("/api/v1/analytics", analytics_1.default);
app.use("/api/v1/auth", auth_1.default);
app.use("/api/v1/client", client_1.default);
app.use("/api/v1/notifications", notifications_1.default);
app.use("/api/v1/payments", payments_1.default);
app.use("/api/v1/playback", playback_1.default);
app.use("/api/v1/playback-queue", playbackQueue_1.default);
// Rota de compatibilidade para o frontend
app.use("/api/v1/queues", playbackQueue_1.default);
app.use("/api/v1/rewards", rewards_1.default);
app.use("/api/v1/youtube", youtube_1.default);
app.use("/api/v1/live-playlist", livePlaylist_1.default);
app.use("/api/v1/collaborative-playlist", collaborativePlaylist_1.default);
app.use("/api/v1/youtube-auth", youtubeAuth_1.default);
app.use("/api/test-youtube", test_youtube_1.default);
app.use("/api/v1/qrcode", qrcode_1.default);
app.use("/api/v1/business-hours", businessHours_1.default);
app.use("/api/v1/competitive-voting", competitiveVoting_1.default);
app.use("/api/v1/lyrics", lyrics_1.default);
app.use("/api/v1/schedules", schedules_1.default);
app.use("/api/v1/playlist-schedules", playlistSchedules_1.default);
app.use("/api/v1/playlist-reorder", playlistReorder_1.default);
console.log("✅ Routes registered successfully");
// Endpoints adicionais para compatibilidade
// Endpoint para obter dados de um restaurante específico
app.get("/api/v1/restaurants/:id", async (req, res) => {
    try {
        const { id } = req.params;
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurant = await restaurantRepository.findOne({
            where: { id },
            relations: ["playlists", "suggestions", "users"],
        });
        if (!restaurant) {
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        res.json({ restaurant: restaurant.toPublicJSON() });
    }
    catch (error) {
        logger_1.logger.error("Erro ao buscar restaurante:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para listar restaurantes
app.get("/api/v1/restaurants", async (req, res) => {
    try {
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurants = await restaurantRepository.find({
            where: { isActive: true },
            order: { name: "ASC" },
        });
        res.json({
            restaurants: restaurants.map((r) => r.toPublicJSON()),
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao listar restaurantes:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para atualizar um restaurante
app.put("/api/v1/admin/restaurants/:id", async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, phone, address, email, password } = req.body;
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const userRepository = database_1.AppDataSource.getRepository(User_1.User);
        const restaurant = await restaurantRepository.findOne({
            where: { id },
            relations: ["users"],
        });
        if (!restaurant) {
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        // Atualizar dados do restaurante
        if (name)
            restaurant.name = name;
        if (description)
            restaurant.description = description;
        if (phone)
            restaurant.phone = phone;
        if (address)
            restaurant.address = address;
        await restaurantRepository.save(restaurant);
        // Atualizar dados do usuário admin se fornecidos
        if (email || password) {
            const adminUser = restaurant.users?.find((u) => u.role === "admin");
            if (adminUser) {
                if (email && email !== adminUser.email) {
                    // Verificar se o novo email já está em uso
                    const existingUser = await userRepository.findOne({
                        where: { email },
                        relations: ["restaurant"],
                    });
                    if (existingUser && existingUser.id !== adminUser.id) {
                        return res.status(400).json({ error: "Email já está em uso" });
                    }
                    adminUser.email = email;
                }
                if (password) {
                    const bcrypt = require("bcryptjs");
                    adminUser.password = await bcrypt.hash(password, 10);
                }
                await userRepository.save(adminUser);
            }
        }
        // Buscar dados atualizados
        const updatedRestaurant = await restaurantRepository.findOne({
            where: { id },
            relations: ["users"],
        });
        const adminUser = updatedRestaurant?.users?.find((u) => u.role === "admin");
        res.json({
            success: true,
            message: "Restaurante atualizado com sucesso",
            restaurant: {
                id: updatedRestaurant.id,
                name: updatedRestaurant.name,
                description: updatedRestaurant.description,
                phone: updatedRestaurant.phone,
                address: updatedRestaurant.address,
                isActive: updatedRestaurant.isActive,
                createdAt: updatedRestaurant.createdAt,
                adminUser: adminUser
                    ? {
                        id: adminUser.id,
                        email: adminUser.email,
                        name: adminUser.name,
                        isActive: adminUser.isActive,
                    }
                    : null,
            },
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao atualizar restaurante:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para alterar status do restaurante (ativar/desativar)
app.patch("/api/v1/admin/restaurants/:id/status", async (req, res) => {
    try {
        const { id } = req.params;
        const { isActive } = req.body;
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurant = await restaurantRepository.findOne({
            where: { id },
        });
        if (!restaurant) {
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        restaurant.isActive = isActive;
        await restaurantRepository.save(restaurant);
        const action = isActive ? "ativado" : "desativado";
        res.json({
            success: true,
            message: `Restaurante ${action} com sucesso`,
            restaurant: {
                id: restaurant.id,
                name: restaurant.name,
                isActive: restaurant.isActive,
            },
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao alterar status do restaurante:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para deletar um restaurante
app.delete("/api/v1/admin/restaurants/:id", async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`🗑️ Iniciando deleção do restaurante: ${id}`);
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const userRepository = database_1.AppDataSource.getRepository(User_1.User);
        const restaurant = await restaurantRepository.findOne({
            where: { id },
            relations: ["users"],
        });
        if (!restaurant) {
            console.log(`❌ Restaurante ${id} não encontrado`);
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        console.log(`📋 Restaurante encontrado: ${restaurant.name}, usuários: ${restaurant.users?.length || 0}`);
        // Iniciar transação para garantir consistência
        await database_1.AppDataSource.transaction(async (transactionalEntityManager) => {
            console.log("🔄 Iniciando transação de deleção");
            // 1. Deletar QR codes do restaurante
            console.log("🗑️ Deletando QR codes...");
            const qrResult = await transactionalEntityManager.query("DELETE FROM qr_codes WHERE restaurant_id = $1", [id]);
            console.log(`✅ QR codes deletados: ${qrResult[1]} registros`);
            // 2. Deletar sugestões do restaurante
            console.log("🗑️ Deletando sugestões...");
            const suggestionsResult = await transactionalEntityManager.query("DELETE FROM suggestions WHERE restaurant_id = $1", [id]);
            console.log(`✅ Sugestões deletadas: ${suggestionsResult[1]} registros`);
            // 3. Deletar playlists do restaurante
            console.log("🗑️ Deletando playlists...");
            const playlistsResult = await transactionalEntityManager.query("DELETE FROM playlists WHERE restaurant_id = $1", [id]);
            console.log(`✅ Playlists deletadas: ${playlistsResult[1]} registros`);
            // 4. Deletar usuários associados ao restaurante
            if (restaurant.users && restaurant.users.length > 0) {
                console.log(`🗑️ Deletando ${restaurant.users.length} usuários...`);
                await transactionalEntityManager
                    .getRepository(User_1.User)
                    .remove(restaurant.users);
                console.log("✅ Usuários deletados");
            }
            // 5. Finalmente, deletar o restaurante
            console.log("🗑️ Deletando restaurante...");
            await transactionalEntityManager
                .getRepository(Restaurant_1.Restaurant)
                .remove(restaurant);
            console.log("✅ Restaurante deletado");
        });
        console.log(`🎉 Restaurante ${id} deletado com sucesso`);
        res.json({
            success: true,
            message: "Restaurante deletado com sucesso",
        });
    }
    catch (error) {
        console.error("❌ Erro ao deletar restaurante:", error);
        logger_1.logger.error("Erro ao deletar restaurante:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint removido - usando rota modular em /routes/qrcode.ts
// Endpoint removido - usando rota modular em /routes/qrcode.ts
// Endpoint removido - usando rota modular em /routes/qrcode.ts
// Endpoint removido - usando rota modular em /routes/qrcode.ts
// Endpoint para gêneros removido temporariamente para evitar problemas de metadata
// Endpoint para criar sugestão musical (gratuita ou paga)
app.post("/api/v1/suggestions", async (req, res) => {
    try {
        const { restaurantId, youtubeId, youtubeVideoId, title, artist, duration, thumbnail, thumbnailUrl, clientName, tableNumber, sessionId, isPriority = false, // false = gratuita, true = paga (R$2,00)
        paymentId = null, // ID do pagamento PIX se for prioritária
        paymentAmount, } = req.body;
        if (!restaurantId || !youtubeId || !title) {
            return res.status(400).json({
                error: "RestaurantId, youtubeId e title são obrigatórios",
            });
        }
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        // Verificar se o restaurante existe
        const restaurant = await restaurantRepository.findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) {
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        // Se for sugestão prioritária, verificar se o pagamento foi processado
        if (isPriority && !paymentId) {
            return res.status(400).json({
                error: "PaymentId é obrigatório para sugestões prioritárias",
            });
        }
        // Restaurant já foi verificado acima
        // Criar a sugestão
        const suggestion = suggestionRepository.create({
            youtubeVideoId: youtubeVideoId || youtubeId,
            title,
            artist: artist || "Artista Desconhecido",
            duration: duration || 0,
            thumbnailUrl: thumbnailUrl || thumbnail || "",
            clientName: clientName || "Anônimo",
            tableNumber: tableNumber || null,
            clientSessionId: sessionId || null,
            status: isPriority ? Suggestion_1.SuggestionStatus.APPROVED : Suggestion_1.SuggestionStatus.PENDING,
            isPriority,
            paymentId,
            paymentAmount: isPriority
                ? typeof paymentAmount === "number"
                    ? paymentAmount
                    : 2.0
                : 0.0,
            paymentStatus: isPriority ? "paid" : null,
            restaurant: restaurant,
        });
        const savedSuggestion = await suggestionRepository.save(suggestion);
        // Emitir evento via Socket.IO para atualizar em tempo real
        io.to(`restaurant-${restaurantId}`).emit("newSuggestion", {
            suggestion: savedSuggestion,
            isPriority,
        });
        // Se for prioritária, também emitir para a fila de reprodução
        if (isPriority) {
            io.to(`restaurant-${restaurantId}`).emit("prioritySuggestion", {
                suggestion: savedSuggestion,
            });
        }
        res.json({
            success: true,
            suggestion: savedSuggestion,
            message: isPriority
                ? "Sugestão prioritária criada com sucesso!"
                : "Sugestão criada com sucesso!",
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao criar sugestão:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para gerar pagamento PIX para sugestão prioritária
app.post("/api/v1/payments/pix/suggestion", async (req, res) => {
    try {
        const { restaurantId, youtubeId, title, artist, clientName, tableNumber, sessionId, amount: rawAmountCents, } = req.body;
        if (!restaurantId || !youtubeId || !title) {
            return res.status(400).json({
                error: "RestaurantId, youtubeId e title são obrigatórios",
            });
        }
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurant = await restaurantRepository.findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) {
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        // Gerar ID único para o pagamento
        const paymentId = (0, uuid_1.v4)();
        const amountCents = Number(rawAmountCents) && Number(rawAmountCents) > 0
            ? Number(rawAmountCents)
            : 500; // default R$5,00
        const amount = parseFloat((amountCents / 100).toFixed(2));
        // Gerar código PIX mais realista (em produção, usar gateway como PagSeguro, Mercado Pago, etc.)
        const generatePixCode = (paymentId, amount, merchantName) => {
            // Formato EMV básico para PIX
            const merchantAccount = paymentId.substring(0, 25); // Simular chave PIX
            const formattedAmount = amount.toFixed(2);
            const merchantCity = "SAO PAULO";
            const merchantCategoryCode = "0000";
            const countryCode = "BR";
            // Construir código PIX seguindo padrão EMV
            const payload = [
                "********",
                "580014BR.GOV.BCB.PIX",
                `0136${merchantAccount}`,
                `5204${merchantCategoryCode}`,
                "********",
                `54${formattedAmount.length
                    .toString()
                    .padStart(2, "0")}${formattedAmount}`,
                `5802${countryCode}`,
                `59${merchantName.length.toString().padStart(2, "0")}${merchantName}`,
                `60${merchantCity.length.toString().padStart(2, "0")}${merchantCity}`,
                "***********",
                "6304", // CRC16 placeholder
            ].join("");
            return payload;
        };
        const pixCode = generatePixCode(paymentId, amount, restaurant.name);
        // Dados do PIX
        const pixData = {
            id: paymentId,
            amount,
            description: `Sugestão prioritária: ${title}`,
            restaurantId,
            youtubeId,
            title,
            artist,
            clientName,
            tableNumber,
            sessionId,
            status: "pending",
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
            // NOTA: Em produção, integrar com gateway real (PagSeguro, Mercado Pago, Stone, etc.)
            // Este é apenas um formato de exemplo para desenvolvimento
            pixCode,
            qrCodeData: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`, // Placeholder - em produção gerar QR real
        };
        // Em produção, salvar no banco de dados
        // const paymentRepository = AppDataSource.getRepository(Payment);
        // await paymentRepository.save(pixData);
        res.json({
            success: true,
            payment: pixData,
            message: "Código PIX gerado com sucesso!",
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao gerar pagamento PIX:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para verificar status do pagamento PIX
app.get("/api/v1/payments/pix/:paymentId/status", async (req, res) => {
    try {
        const { paymentId } = req.params;
        if (!paymentId) {
            return res.status(400).json({ error: "PaymentId é obrigatório" });
        }
        // Em produção, consultar o gateway de pagamento real
        // Por enquanto, simular verificação
        const paymentStatus = {
            id: paymentId,
            status: "pending",
            paidAt: null,
            amount: undefined,
        };
        // Simular pagamento aprovado após 30 segundos (apenas para demo)
        const createdTime = new Date(paymentId.substring(0, 8)).getTime();
        const now = Date.now();
        if (now - createdTime > 30000) {
            paymentStatus.status = "paid";
            paymentStatus.paidAt = new Date().toISOString();
        }
        res.json({
            success: true,
            payment: paymentStatus,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao verificar status do pagamento:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para confirmar pagamento e criar sugestão prioritária
app.post("/api/v1/payments/pix/:paymentId/confirm", async (req, res) => {
    try {
        const { paymentId } = req.params;
        const { restaurantId, youtubeId, title, artist, clientName, tableNumber, sessionId, } = req.body;
        if (!paymentId || !restaurantId || !youtubeId || !title) {
            return res.status(400).json({
                error: "PaymentId, restaurantId, youtubeId e title são obrigatórios",
            });
        }
        // Verificar se o pagamento foi aprovado
        // Em produção, consultar o gateway de pagamento
        const paymentStatus = "paid"; // Simular pagamento aprovado
        if (paymentStatus !== "paid") {
            return res.status(400).json({
                error: "Pagamento não foi aprovado ainda",
            });
        }
        // Get restaurant entity
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurant = await restaurantRepository.findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) {
            return res.status(404).json({
                error: "Restaurante não encontrado",
            });
        }
        // Criar sugestão prioritária
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        const suggestion = suggestionRepository.create({
            youtubeVideoId: youtubeId,
            title,
            artist: artist || "Artista Desconhecido",
            clientName: clientName || "Anônimo",
            tableNumber: tableNumber || null,
            clientSessionId: sessionId || null,
            status: Suggestion_1.SuggestionStatus.APPROVED,
            isPriority: true,
            paymentId,
            paymentAmount: undefined,
            paymentStatus: "paid",
            restaurant: restaurant,
        });
        // Definir a relação com o restaurante
        suggestion.restaurant = { id: restaurantId };
        const savedSuggestion = await suggestionRepository.save(suggestion);
        // Emitir eventos via Socket.IO
        io.to(`restaurant-${restaurantId}`).emit("prioritySuggestion", {
            suggestion: savedSuggestion,
        });
        res.json({
            success: true,
            suggestion: savedSuggestion,
            message: "Sugestão prioritária criada com sucesso!",
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao confirmar pagamento:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para listar sugestões de um restaurante
app.get("/api/v1/suggestions/:restaurantId", async (req, res) => {
    try {
        const { restaurantId } = req.params;
        const { status, limit = 50, offset = 0 } = req.query;
        if (!restaurantId) {
            return res.status(400).json({ error: "RestaurantId é obrigatório" });
        }
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        const whereConditions = { restaurant: { id: restaurantId } };
        if (status) {
            whereConditions.status = status;
        }
        const suggestions = await suggestionRepository.find({
            where: whereConditions,
            order: {
                isPriority: "DESC",
                createdAt: "DESC",
            },
            take: Number(limit),
            skip: Number(offset),
        });
        res.json({
            success: true,
            suggestions,
            total: suggestions.length,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao listar sugestões:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para votar em uma sugestão (com suporte a supervotos)
app.post("/api/v1/suggestions/:suggestionId/vote", async (req, res) => {
    try {
        const { suggestionId } = req.params;
        const { sessionId, tableNumber, voteType = "normal", paymentAmount, paymentId, } = req.body;
        if (!suggestionId) {
            return res.status(400).json({ error: "SuggestionId é obrigatório" });
        }
        // Validar tipo de voto
        if (!["normal", "super"].includes(voteType)) {
            return res
                .status(400)
                .json({ error: "Tipo de voto inválido. Use 'normal' ou 'super'" });
        }
        // Se for supervoto, validar pagamento
        if (voteType === "super") {
            if (!paymentAmount || !paymentId) {
                return res.status(400).json({
                    error: "Supervotos requerem paymentAmount e paymentId",
                });
            }
            if (![5, 20, 50].includes(paymentAmount)) {
                return res.status(400).json({
                    error: "Valores de supervoto válidos: R$ 5, R$ 20 ou R$ 50",
                });
            }
        }
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        const voteRepository = database_1.AppDataSource.getRepository(Vote_1.Vote);
        // Verificar se a sugestão existe
        const suggestion = await suggestionRepository.findOne({
            where: { id: suggestionId },
        });
        if (!suggestion) {
            return res.status(404).json({ error: "Sugestão não encontrada" });
        }
        // Verificar se já votou (por sessionId ou tableNumber)
        const existingVote = await voteRepository.findOne({
            where: [
                { suggestionId, sessionId },
                { suggestionId, tableNumber },
            ],
        });
        if (existingVote) {
            return res.status(400).json({ error: "Você já votou nesta sugestão" });
        }
        // Calcular peso do voto
        let voteWeight = 1; // Voto normal
        if (voteType === "super") {
            if (paymentAmount >= 50)
                voteWeight = 20; // R$ 50+ = 20 votos
            else if (paymentAmount >= 20)
                voteWeight = 8; // R$ 20+ = 8 votos
            else if (paymentAmount >= 5)
                voteWeight = 3; // R$ 5+ = 3 votos
        }
        // Criar o voto
        const vote = voteRepository.create({
            suggestionId,
            sessionId,
            tableNumber,
            voteType: Vote_1.VoteType.UP,
            clientSessionId: sessionId,
        });
        await voteRepository.save(vote);
        // Atualizar contador de votos na sugestão
        suggestion.voteCount = (suggestion.voteCount || 0) + voteWeight;
        suggestion.upvotes = (suggestion.upvotes || 0) + voteWeight;
        // Se for supervoto, atualizar informações de pagamento
        if (voteType === "super") {
            suggestion.isPaid = true;
            suggestion.paymentAmount = Math.max(suggestion.paymentAmount || 0, paymentAmount);
            suggestion.paymentId = paymentId;
            suggestion.paymentStatus = "paid";
        }
        await suggestionRepository.save(suggestion);
        console.log(`🗳️ ${voteType === "super" ? "Supervoto" : "Voto normal"} registrado: +${voteWeight} votos (${suggestion.voteCount} total)`);
        // Emitir evento via Socket.IO
        io.to(`restaurant-${suggestion.restaurant.id}`).emit("voteUpdate", {
            suggestionId,
            votes: suggestion.voteCount,
            voteType,
            voteWeight,
            paymentAmount: voteType === "super" ? paymentAmount : undefined,
        });
        res.json({
            success: true,
            votes: suggestion.voteCount,
            voteWeight,
            voteType,
            paymentAmount: voteType === "super" ? paymentAmount : undefined,
            message: voteType === "super"
                ? `Supervoto de R$ ${paymentAmount.toFixed(2)} registrado! (+${voteWeight} votos)`
                : "Voto registrado com sucesso!",
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao votar:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para obter ranking de votação
app.get("/api/v1/restaurants/:restaurantId/voting-ranking", async (req, res) => {
    try {
        const { restaurantId } = req.params;
        const { limit = 20 } = req.query;
        if (!restaurantId) {
            return res.status(400).json({ error: "RestaurantId é obrigatório" });
        }
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        // Buscar sugestões ordenadas por votos (supervotos primeiro)
        const suggestions = await suggestionRepository
            .createQueryBuilder("suggestion")
            .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
            .andWhere("suggestion.status = :status", { status: "approved" })
            .orderBy("suggestion.is_paid", "DESC") // Supervotos primeiro
            .addOrderBy("suggestion.payment_amount", "DESC") // Maior valor primeiro
            .addOrderBy('suggestion."voteCount"', "DESC") // Mais votos primeiro
            .limit(parseInt(limit))
            .getMany();
        const ranking = suggestions.map((suggestion, index) => {
            // Calcular quantos supervotos e votos normais
            const superVoteWeight = suggestion.isPaid
                ? suggestion.paymentAmount >= 50
                    ? 20
                    : suggestion.paymentAmount >= 20
                        ? 8
                        : suggestion.paymentAmount >= 5
                            ? 3
                            : 1
                : 0;
            const normalVoteCount = Math.max(0, (suggestion.voteCount || 0) - superVoteWeight);
            return {
                position: index + 1,
                youtubeVideoId: suggestion.youtubeVideoId,
                title: suggestion.title,
                artist: suggestion.artist,
                voteCount: suggestion.voteCount || 0,
                superVoteCount: suggestion.isPaid ? 1 : 0,
                normalVoteCount,
                totalRevenue: suggestion.paymentAmount || 0,
                isPaid: suggestion.isPaid || false,
                paymentAmount: suggestion.paymentAmount || 0,
                tableNumber: suggestion.tableNumber,
                thumbnailUrl: suggestion.thumbnailUrl,
            };
        });
        res.json({
            success: true,
            data: ranking,
            message: `${ranking.length} músicas no ranking`,
            totalSongs: ranking.length,
            totalVotes: ranking.reduce((sum, item) => sum + item.voteCount, 0),
            totalRevenue: ranking.reduce((sum, item) => sum + item.totalRevenue, 0),
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao obter ranking:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para aprovar/rejeitar sugestão (admin)
app.patch("/api/v1/suggestions/:suggestionId/status", async (req, res) => {
    try {
        const { suggestionId } = req.params;
        const { status, reason } = req.body;
        if (!suggestionId || !status) {
            return res.status(400).json({
                error: "SuggestionId e status são obrigatórios",
            });
        }
        if (!["approved", "rejected", "pending"].includes(status)) {
            return res.status(400).json({
                error: "Status deve ser: approved, rejected ou pending",
            });
        }
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        const suggestion = await suggestionRepository.findOne({
            where: { id: suggestionId },
        });
        if (!suggestion) {
            return res.status(404).json({ error: "Sugestão não encontrada" });
        }
        // Atualizar status
        suggestion.status = status;
        if (reason) {
            suggestion.rejectionReason = reason;
        }
        suggestion.updatedAt = new Date();
        await suggestionRepository.save(suggestion);
        // Emitir evento via Socket.IO
        io.to(`restaurant-${suggestion.restaurant.id}`).emit("suggestionStatusUpdate", {
            suggestionId,
            status,
            reason,
        });
        res.json({
            success: true,
            suggestion,
            message: `Sugestão ${status === "approved" ? "aprovada" : "rejeitada"} com sucesso!`,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao atualizar status da sugestão:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint para buscar músicas no YouTube
app.get("/api/v1/youtube/search", async (req, res) => {
    try {
        const { q, maxResults = 10 } = req.query;
        if (!q) {
            return res.status(400).json({ error: "Query (q) é obrigatória" });
        }
        // Em produção, usar a API real do YouTube
        // Por enquanto, retornar dados simulados
        const mockResults = [
            {
                id: { videoId: "dQw4w9WgXcQ" },
                snippet: {
                    title: "Rick Astley - Never Gonna Give You Up",
                    channelTitle: "Rick Astley",
                    thumbnails: {
                        default: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/default.jpg" },
                        medium: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg" },
                        high: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg" },
                    },
                    publishedAt: "2009-10-25T06:57:33Z",
                },
                contentDetails: {
                    duration: "PT3M33S",
                },
            },
        ];
        res.json({
            success: true,
            items: mockResults,
            query: q,
            maxResults: Number(maxResults),
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao buscar no YouTube:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Endpoint removido - usando rota modular em /routes/qrcode.ts
// Endpoint duplicado removido - usar routes/client.ts
// Endpoint para verificar sessão do cliente
app.get("/api/v1/client/session/:sessionId", async (req, res) => {
    try {
        const { sessionId } = req.params;
        if (!sessionId) {
            return res.status(400).json({ error: "SessionId é obrigatório" });
        }
        // Buscar sessão no Redis
        const sessionData = await redis_1.redisClient.get(`client_session:${sessionId}`);
        if (!sessionData) {
            return res
                .status(404)
                .json({ error: "Sessão não encontrada ou expirada" });
        }
        const session = JSON.parse(sessionData);
        // Buscar dados do restaurante
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurant = await restaurantRepository.findOne({
            where: { id: session.restaurantId },
        });
        if (!restaurant) {
            return res.status(404).json({ error: "Restaurante não encontrado" });
        }
        res.json({
            success: true,
            session,
            restaurant: {
                id: restaurant.id,
                name: restaurant.name,
                description: restaurant.description,
            },
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao verificar sessão do cliente:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Socket.IO handlers
io.on("connection", (socket) => {
    console.log(`Cliente conectado: ${socket.id}`);
    // Join restaurant room
    socket.on("join-restaurant", (restaurantId) => {
        socket.join(`restaurant-${restaurantId}`);
        console.log(`Socket ${socket.id} joined restaurant ${restaurantId}`);
    });
    // Join table room
    socket.on("join-table", (data) => {
        const roomName = `restaurant-${data.restaurantId}-table-${data.tableNumber}`;
        socket.join(roomName);
        console.log(`Socket ${socket.id} joined table ${data.tableNumber} in restaurant ${data.restaurantId}`);
    });
    // Handle suggestion events
    socket.on("new-suggestion", (data) => {
        socket.to(`restaurant-${data.restaurantId}`).emit("suggestion-added", data);
    });
    // Handle vote events
    socket.on("new-vote", (data) => {
        socket.to(`restaurant-${data.restaurantId}`).emit("vote-added", data);
    });
    // Handle playback events
    socket.on("playback-update", (data) => {
        socket
            .to(`restaurant-${data.restaurantId}`)
            .emit("playback-state-changed", data);
    });
    socket.on("disconnect", () => {
        console.log(`Cliente desconectado: ${socket.id}`);
    });
});
// Middleware de tratamento de erros
app.use((err, req, res, next) => {
    logger_1.logger.error("Erro não tratado:", err);
    if (res.headersSent) {
        return next(err);
    }
    const statusCode = err.statusCode || err.status || 500;
    const message = err.message || "Erro interno do servidor";
    res.status(statusCode).json({
        error: message,
        ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    });
});
// ==================== ENDPOINTS QR CODE ====================
// MOVIDOS PARA: /routes/qrcode.ts
/*
// Listar QR Codes de um restaurante
app.get("/api/v1/qrcode/:restaurantId", async (req, res) => {
  try {
    const { restaurantId } = req.params;
    logger.info("📱 Carregando QR Codes para restaurante:", restaurantId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    // Buscar QR codes do banco de dados
    const qrCodes = await qrCodeRepository.find({
      where: {
        restaurant: { id: restaurantId },
        isActive: true,
      },
      relations: ["restaurant"],
      order: { createdAt: "DESC" },
    });

    logger.info("📊 Total de QR Codes encontrados:", qrCodes.length);

    res.json({
      success: true,
      qrCodes: qrCodes.map((qr) => qr.toPublicJSON()),
      total: qrCodes.length,
      restaurantId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao buscar QR Codes:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Code para mesa específica
app.post("/api/v1/qrcode/table", async (req, res) => {
  try {
    console.log("🚨 ROTA EXECUTADA: /api/v1/qrcode/table");
    console.log("🚨 BODY:", req.body);

    const { restaurantId, tableNumber, tableName } = req.body;

    if (!restaurantId || !tableNumber) {
      return res.status(400).json({
        success: false,
        error: "restaurantId e tableNumber são obrigatórios",
      });
    }

    logger.info("🏷️ Gerando QR Code para mesa:", {
      restaurantId,
      tableNumber,
      tableName,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    // Verificar se já existe QR Code para esta mesa
    const existingQR = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        tableNumber: String(tableNumber),
        type: QRCodeType.TABLE,
        isActive: true,
      },
    });

    if (existingQR) {
      return res.status(400).json({
        success: false,
        error: `QR Code para mesa ${tableNumber} já existe`,
      });
    }

    // Gerar URL do QR Code
    const qrUrl = `http://localhost:8000/client/${restaurantId}?tableNumber=${tableNumber}`;

    // Gerar QR Code visual usando a biblioteca qrcode
    const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      quality: 0.92,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    // Criar QR Code no banco de dados
    const qrCode = qrCodeRepository.create({
      restaurant: restaurant,
      type: QRCodeType.TABLE,
      name: tableName || `Mesa ${tableNumber}`,
      tableNumber: String(tableNumber),
      url: qrUrl,
      qrCodeData: qrCodeDataURL,
      isActive: true,
    });

    const savedQRCode = await qrCodeRepository.save(qrCode);

    logger.info("✅ QR Code criado no banco:", savedQRCode.id);

    res.status(201).json({
      success: true,
      message: "QR Code gerado com sucesso",
      qrCode: savedQRCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Codes em lote para múltiplas mesas
app.post("/api/v1/qrcode/bulk-tables", async (req, res) => {
  try {
    const { restaurantId, tableCount, tablePrefix = "Mesa" } = req.body;

    if (!restaurantId || !tableCount || tableCount < 1 || tableCount > 50) {
      return res.status(400).json({
        success: false,
        error:
          "restaurantId é obrigatório e tableCount deve estar entre 1 e 50",
      });
    }

    console.log("🏷️ Gerando QR Codes em lote:", {
      restaurantId,
      tableCount,
      tablePrefix,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    const newQRCodes = [];
    let generated = 0;

    for (let i = 1; i <= tableCount; i++) {
      // Verificar se já existe
      const existingQR = await qrCodeRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          tableNumber: String(i),
          type: QRCodeType.TABLE,
          isActive: true,
        },
      });

      if (!existingQR) {
        const qrUrl = `http://localhost:8000/client/${restaurantId}?tableNumber=${i}`;

        // Gerar QR Code visual usando a biblioteca qrcode
        const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
          errorCorrectionLevel: "M",
          type: "image/png",
          quality: 0.92,
          margin: 1,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
          width: 256,
        });

        const qrCode = qrCodeRepository.create({
          restaurant: restaurant,
          type: QRCodeType.TABLE,
          name: `${tablePrefix} ${i}`,
          tableNumber: String(i),
          url: qrUrl,
          qrCodeData: qrCodeDataURL,
          isActive: true,
        });

        const savedQRCode = await qrCodeRepository.save(qrCode);
        newQRCodes.push(savedQRCode.toPublicJSON());
        generated++;
      }
    }

    console.log(`✅ ${generated} QR Codes gerados em lote`);

    res.status(201).json({
      success: true,
      message: `${generated} QR Codes gerados com sucesso`,
      qrCodes: newQRCodes,
      totalGenerated: generated,
      skipped: tableCount - generated,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Codes em lote:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Code geral do restaurante
app.post("/api/v1/qrcode/restaurant", async (req, res) => {
  try {
    const { restaurantId, restaurantName } = req.body;

    if (!restaurantId) {
      return res.status(400).json({
        success: false,
        error: "restaurantId é obrigatório",
      });
    }

    console.log("🏪 Gerando QR Code geral do restaurante:", {
      restaurantId,
      restaurantName,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    // Verificar se já existe QR Code geral para este restaurante
    const existingQR = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        type: QRCodeType.RESTAURANT,
        isActive: true,
      },
    });

    if (existingQR) {
      return res.status(400).json({
        success: false,
        error: "QR Code geral do restaurante já existe",
      });
    }

    const qrUrl = `http://localhost:8000/client/${restaurantId}`;

    // Gerar QR Code visual usando a biblioteca qrcode
    const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      quality: 0.92,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    const qrCode = qrCodeRepository.create({
      restaurant: restaurant,
      type: QRCodeType.RESTAURANT,
      name: restaurantName || "Acesso Geral",
      url: qrUrl,
      qrCodeData: qrCodeDataURL,
      isActive: true,
    });

    const savedQRCode = await qrCodeRepository.save(qrCode);

    console.log("✅ QR Code geral criado:", savedQRCode.id);

    res.status(201).json({
      success: true,
      message: "QR Code geral gerado com sucesso",
      qrCode: savedQRCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Code geral:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Deletar QR Code
app.delete("/api/v1/qrcode/:qrCodeId", async (req, res) => {
  try {
    const { qrCodeId } = req.params;

    if (!qrCodeId) {
      return res.status(400).json({
        success: false,
        error: "qrCodeId é obrigatório",
      });
    }

    console.log("🗑️ Deletando QR Code:", qrCodeId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    const qrCode = await qrCodeRepository.findOne({
      where: { id: qrCodeId },
      relations: ["restaurant"],
    });

    if (!qrCode) {
      return res.status(404).json({
        success: false,
        error: "QR Code não encontrado",
      });
    }

    // Soft delete - marcar como inativo ao invés de deletar
    qrCode.isActive = false;
    await qrCodeRepository.save(qrCode);

    console.log("✅ QR Code desativado:", qrCode.name);

    res.json({
      success: true,
      message: "QR Code deletado com sucesso",
      deletedQRCode: qrCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao deletar QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Atualizar imagem do QR Code
app.post("/api/v1/qrcode/update-image", async (req, res) => {
  try {
    const { qrCodeId, qrCodeDataURL } = req.body;

    if (!qrCodeId || !qrCodeDataURL) {
      return res.status(400).json({
        success: false,
        error: "qrCodeId e qrCodeDataURL são obrigatórios",
      });
    }

    console.log("🔄 Atualizando imagem do QR Code:", qrCodeId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    const qrCode = await qrCodeRepository.findOne({
      where: { id: qrCodeId },
      relations: ["restaurant"],
    });

    if (!qrCode) {
      return res.status(404).json({
        success: false,
        error: "QR Code não encontrado",
      });
    }

    // Atualizar a imagem do QR Code
    qrCode.qrCodeData = qrCodeDataURL;
    await qrCodeRepository.save(qrCode);

    console.log("✅ Imagem do QR Code atualizada:", qrCode.name);

    res.json({
      success: true,
      message: "Imagem do QR Code atualizada com sucesso",
      qrCode: qrCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao atualizar imagem do QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});
*/
// ==================== ROTAS ADMIN TEMPORÁRIAS (ANTES DO 404) ====================
// Rota de teste admin
app.get("/api/v1/admin/test", (req, res) => {
    res.json({
        message: "Admin routes funcionando!",
        timestamp: new Date().toISOString(),
    });
});
// Listar todos os restaurantes (Admin)
app.get("/api/v1/admin/restaurants", async (req, res) => {
    try {
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurants = await restaurantRepository.find({
            order: { createdAt: "DESC" },
            relations: ["users"],
        });
        const restaurantsWithAdminData = restaurants.map((restaurant) => {
            const adminUser = restaurant.users?.find((u) => u.role === "admin");
            return {
                ...restaurant.toJSON(),
                adminUser: adminUser
                    ? {
                        id: adminUser.id,
                        email: adminUser.email,
                        name: adminUser.name,
                        isActive: adminUser.isActive,
                    }
                    : null,
            };
        });
        res.json({
            success: true,
            restaurants: restaurantsWithAdminData,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao listar restaurantes (admin):", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Criar novo restaurante (Admin)
app.post("/api/v1/admin/restaurants", async (req, res) => {
    try {
        const { name, email, description, address, phone, password } = req.body;
        if (!name || !email) {
            return res.status(400).json({ error: "Nome e email são obrigatórios" });
        }
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const userRepository = database_1.AppDataSource.getRepository(User_1.User);
        // Verificar se email já existe
        const existingUser = await userRepository.findOne({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ error: "Email já está em uso" });
        }
        // Criar restaurante
        const restaurant = new Restaurant_1.Restaurant();
        restaurant.id = `restaurant-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`;
        restaurant.name = name;
        restaurant.description = description;
        restaurant.address = address;
        restaurant.phone = phone;
        restaurant.isActive = true;
        restaurant.settings = {
            moderation: {
                requireApproval: false,
                maxSuggestionsPerUser: 3,
            },
            playlist: {
                defaultVolume: 70,
                crossfadeDuration: 3,
                shuffleMode: false,
                repeatMode: "none",
                maxQueueSize: 50,
                allowDuplicates: false,
            },
            schedule: {
                timezone: "America/Sao_Paulo",
                operatingHours: {
                    monday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    tuesday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    wednesday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    thursday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    friday: { isOpen: true, openTime: "09:00", closeTime: "23:00" },
                    saturday: { isOpen: true, openTime: "10:00", closeTime: "23:00" },
                    sunday: { isOpen: true, openTime: "10:00", closeTime: "21:00" },
                },
            },
        };
        const savedRestaurant = await restaurantRepository.save(restaurant);
        // Usar senha fornecida ou gerar senha temporária
        const userPassword = password || Math.random().toString(36).slice(-8);
        const bcrypt = require("bcryptjs");
        const hashedPassword = await bcrypt.hash(userPassword, 10);
        // Criar usuário admin do restaurante
        const user = new User_1.User();
        user.email = email;
        user.password = hashedPassword;
        user.name = `Admin ${name}`;
        user.role = "admin"; // Usar role válido do enum
        user.restaurant = savedRestaurant;
        user.isActive = true;
        await userRepository.save(user);
        logger_1.logger.info("Restaurante criado via admin", {
            restaurantId: savedRestaurant.id,
            restaurantName: name,
            adminEmail: email,
        });
        res.status(201).json({
            success: true,
            restaurant: savedRestaurant.toJSON(),
            credentials: {
                email,
                password: userPassword,
            },
            loginUrl: `${process.env.FRONTEND_URL || "http://localhost:3000"}/restaurant/${savedRestaurant.id}/login`,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao criar restaurante (admin):", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Analytics do Admin
app.get("/api/v1/admin/analytics", async (req, res) => {
    try {
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurants = await restaurantRepository.find({
            relations: ["users"],
        });
        const totalRestaurants = restaurants.length;
        const activeRestaurants = restaurants.filter((r) => r.isActive).length;
        const totalUsers = restaurants.reduce((sum, r) => sum + (r.users?.length || 0), 0);
        const activeUsers = restaurants.reduce((sum, r) => sum + (r.users?.filter((u) => u.isActive).length || 0), 0);
        // Dados mock para demonstração
        const analytics = {
            overview: {
                totalRestaurants,
                activeRestaurants,
                totalUsers,
                activeUsers,
                totalRevenue: Math.floor(Math.random() * 100000),
                monthlyGrowth: Math.floor(Math.random() * 20) + 5,
            },
            recentActivity: [
                {
                    id: 1,
                    type: "restaurant_created",
                    message: "Novo restaurante cadastrado",
                    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                },
                {
                    id: 2,
                    type: "user_login",
                    message: "Usuário fez login",
                    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
                },
                {
                    id: 3,
                    type: "payment_received",
                    message: "Pagamento processado",
                    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
                },
            ],
            topRestaurants: restaurants.slice(0, 5).map((r) => ({
                id: r.id,
                name: r.name,
                revenue: Math.floor(Math.random() * 10000),
                users: r.users?.length || 0,
                status: r.isActive ? "active" : "inactive",
            })),
        };
        res.json({
            success: true,
            analytics,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao buscar analytics admin:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Configurações do Admin
app.get("/api/v1/admin/settings", async (req, res) => {
    try {
        // Configurações mock para demonstração
        const settings = {
            system: {
                maintenanceMode: false,
                allowNewRegistrations: true,
                maxRestaurantsPerPlan: 100,
                defaultTrialDays: 30,
            },
            notifications: {
                emailNotifications: true,
                smsNotifications: false,
                webhookUrl: "",
            },
            payments: {
                stripeEnabled: true,
                pixEnabled: true,
                defaultCurrency: "BRL",
                commissionRate: 5.0,
            },
            features: {
                analyticsEnabled: true,
                competitiveVotingEnabled: true,
                playlistSchedulingEnabled: true,
                qrCodeGenerationEnabled: true,
            },
        };
        res.json({
            success: true,
            settings,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao buscar configurações admin:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Atualizar configurações do Admin
app.put("/api/v1/admin/settings", async (req, res) => {
    try {
        const { settings } = req.body;
        // Em produção, salvar as configurações no banco de dados
        logger_1.logger.info("Configurações admin atualizadas:", settings);
        res.json({
            success: true,
            message: "Configurações atualizadas com sucesso",
            settings,
        });
    }
    catch (error) {
        logger_1.logger.error("Erro ao atualizar configurações admin:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Revenue do Admin
app.get("/api/v1/admin/revenue", async (req, res) => {
    try {
        const { period = "30d" } = req.query;
        const startDate = new Date();
        const endDate = new Date();
        switch (period) {
            case "7d":
                startDate.setDate(endDate.getDate() - 7);
                break;
            case "30d":
                startDate.setDate(endDate.getDate() - 30);
                break;
            case "90d":
                startDate.setDate(endDate.getDate() - 90);
                break;
            case "1y":
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
            default:
                startDate.setDate(endDate.getDate() - 30);
        }
        const paymentRepo = database_1.AppDataSource.getRepository(require("./models/Payment").Payment);
        const suggestionRepo = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant); // placeholder to ensure import
        // Buscar pagamentos aprovados no período, com join em Suggestion -> Restaurant via query crua (JS agregação)
        const paymentRepository = database_1.AppDataSource.getRepository(require("./models/Payment").Payment);
        const suggestionRepository = database_1.AppDataSource.getRepository(require("./models/Suggestion").Suggestion);
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const payments = await paymentRepository.find({
            where: {
                status: "approved",
                createdAt: require("typeorm").Between(startDate, endDate),
            },
            order: { createdAt: "ASC" },
        });
        // Carregar sugestões e mapear para restaurante
        const suggestionIds = Array.from(new Set(payments.map((p) => p.suggestionId).filter(Boolean)));
        const suggestions = suggestionIds.length
            ? await suggestionRepository.find({
                where: { id: require("typeorm").In(suggestionIds) },
                relations: ["restaurant"],
            })
            : [];
        const suggestionToRestaurant = {};
        for (const s of suggestions) {
            suggestionToRestaurant[s.id] = {
                id: s.restaurant?.id,
                name: s.restaurant?.name,
            };
        }
        // Agregações
        let totalAmountCents = 0;
        let totalPlatformFee = 0;
        let totalRestaurantAmount = 0;
        const byRestaurant = {};
        const byMethod = {};
        const byMonth = {};
        for (const p of payments) {
            totalAmountCents += p.amount || 0;
            totalPlatformFee += p.platformFee || 0;
            totalRestaurantAmount += p.restaurantAmount || 0;
            const sugInfo = suggestionToRestaurant[p.suggestionId];
            const rKey = sugInfo?.id || "unknown";
            if (!byRestaurant[rKey]) {
                byRestaurant[rKey] = {
                    id: rKey,
                    name: sugInfo?.name || "Desconhecido",
                    sum: 0,
                    platform: 0,
                    count: 0,
                };
            }
            byRestaurant[rKey].sum += p.amount || 0;
            byRestaurant[rKey].platform += p.platformFee || 0;
            byRestaurant[rKey].count += 1;
            const method = p.paymentMethod || "pix";
            if (!byMethod[method])
                byMethod[method] = { count: 0, revenue: 0 };
            byMethod[method].count += 1;
            byMethod[method].revenue += (p.amount || 0) / 100;
            const mKey = `${p.createdAt.getFullYear()}-${String(p.createdAt.getMonth() + 1).padStart(2, "0")}`;
            if (!byMonth[mKey])
                byMonth[mKey] = { revenue: 0, platform: 0, transactions: 0 };
            byMonth[mKey].revenue += (p.amount || 0) / 100;
            byMonth[mKey].platform += (p.platformFee || 0) / 100;
            byMonth[mKey].transactions += 1;
        }
        const totalRevenue = totalAmountCents / 100;
        const platformRevenue = totalPlatformFee / 100;
        const restaurantRevenue = totalRestaurantAmount / 100;
        const totalTransactions = payments.length;
        const averageTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
        // Converter agregações para arrays
        const revenueByRestaurant = Object.values(byRestaurant)
            .filter((r) => r.id !== "unknown")
            .map((r) => ({
            restaurantId: r.id,
            restaurantName: r.name,
            totalRevenue: r.sum / 100,
            platformShare: r.platform / 100,
            transactions: r.count,
            averageValue: r.count > 0 ? r.sum / 100 / r.count : 0,
        }))
            .sort((a, b) => b.totalRevenue - a.totalRevenue);
        const revenueByMonth = Object.keys(byMonth)
            .sort()
            .map((key) => {
            const [year, month] = key.split("-");
            const date = new Date(Number(year), Number(month) - 1, 1);
            return {
                month: date.toLocaleDateString("pt-BR", { month: "short" }),
                totalRevenue: byMonth[key].revenue,
                platformRevenue: byMonth[key].platform,
                transactions: byMonth[key].transactions,
            };
        });
        const paymentMethods = Object.entries(byMethod).map(([method, v]) => ({
            method: method.toUpperCase(),
            count: v.count,
            revenue: v.revenue,
            percentage: totalRevenue > 0 ? (v.revenue / totalRevenue) * 100 : 0,
        }));
        const revenueData = {
            totalRevenue,
            platformRevenue,
            restaurantRevenue,
            platformFeePercentage: totalRevenue > 0 ? (platformRevenue / totalRevenue) * 100 : 0,
            monthlyGrowth: 0,
            totalTransactions,
            averageTransactionValue,
            revenueByRestaurant,
            revenueByMonth,
            paymentMethods,
            period,
            generatedAt: new Date().toISOString(),
        };
        res.json({ success: true, revenue: revenueData });
    }
    catch (error) {
        logger_1.logger.error("Erro ao buscar dados de receita admin:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// Export JSON de receitas (todos os restaurantes)
app.get("/api/v1/admin/revenue/export", async (req, res) => {
    try {
        const { period = "30d" } = req.query;
        // Reutiliza a lógica anterior via chamada interna HTTP seria custosa; replicamos aqui simplificadamente
        req.url = `/api/v1/admin/revenue?period=${period}`;
        // Em vez de proxy, refazemos a consulta chamando a função acima seria ideal. Para simplicidade, copiamos o cálculo.
        // Para evitar duplicação de lógica, poderíamos extrair para uma função utilitária. Mantido simples aqui.
        // Chamar a própria rota é complexo no mesmo handler; vamos refazer de forma compacta:
        const startDate = new Date();
        const endDate = new Date();
        switch (period) {
            case "7d":
                startDate.setDate(endDate.getDate() - 7);
                break;
            case "30d":
                startDate.setDate(endDate.getDate() - 30);
                break;
            case "90d":
                startDate.setDate(endDate.getDate() - 90);
                break;
            case "1y":
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
            default:
                startDate.setDate(endDate.getDate() - 30);
        }
        const paymentRepository = database_1.AppDataSource.getRepository(require("./models/Payment").Payment);
        const suggestionRepository = database_1.AppDataSource.getRepository(require("./models/Suggestion").Suggestion);
        const payments = await paymentRepository.find({
            where: {
                status: "approved",
                createdAt: require("typeorm").Between(startDate, endDate),
            },
            order: { createdAt: "ASC" },
        });
        const suggestionIds = Array.from(new Set(payments.map((p) => p.suggestionId).filter(Boolean)));
        const suggestions = suggestionIds.length
            ? await suggestionRepository.find({
                where: { id: require("typeorm").In(suggestionIds) },
                relations: ["restaurant"],
            })
            : [];
        const suggestionToRestaurant = {};
        for (const s of suggestions) {
            suggestionToRestaurant[s.id] = {
                id: s.restaurant?.id,
                name: s.restaurant?.name,
            };
        }
        const byRestaurant = [];
        const agg = {};
        for (const p of payments) {
            const r = suggestionToRestaurant[p.suggestionId];
            const key = r?.id || "unknown";
            if (!agg[key])
                agg[key] = {
                    id: key,
                    name: r?.name || "Desconhecido",
                    sum: 0,
                    platform: 0,
                    count: 0,
                };
            agg[key].sum += p.amount || 0;
            agg[key].platform += p.platformFee || 0;
            agg[key].count += 1;
        }
        for (const v of Object.values(agg)) {
            if (v.id === "unknown")
                continue;
            byRestaurant.push({
                restaurantId: v.id,
                restaurantName: v.name,
                totalRevenue: v.sum / 100,
                platformShare: v.platform / 100,
                transactions: v.count,
                averageValue: v.count > 0 ? v.sum / 100 / v.count : 0,
            });
        }
        const payload = {
            period,
            exportedAt: new Date().toISOString(),
            restaurants: byRestaurant.sort((a, b) => b.totalRevenue - a.totalRevenue),
        };
        res.setHeader("Content-Type", "application/json");
        res.setHeader("Content-Disposition", `attachment; filename="revenue_${period}_${new Date()
            .toISOString()
            .slice(0, 10)}.json"`);
        res.status(200).send(JSON.stringify(payload, null, 2));
    }
    catch (error) {
        logger_1.logger.error("Erro ao exportar dados de receita:", error);
        res.status(500).json({ error: "Erro interno do servidor" });
    }
});
// 404 handler
app.use("*", (req, res) => {
    res.status(404).json({
        error: "Endpoint não encontrado",
        path: req.originalUrl,
        method: req.method,
    });
});
// Função para inicializar o servidor
async function startServer() {
    try {
        console.log("🔄 Inicializando servidor...");
        // Inicializar banco de dados primeiro
        console.log("🔄 Conectando ao banco de dados...");
        await (0, database_1.initializeDatabase)();
        console.log("✅ Database initialized");
        // Verificar se as entidades estão carregadas
        const entities = database_1.AppDataSource.entityMetadatas;
        console.log(`✅ ${entities.length} entidades carregadas:`, entities.map((e) => e.name));
        // Inicializar Redis
        console.log("🔄 Conectando ao Redis...");
        if (!redis_1.redisClient.isReady) {
            await redis_1.redisClient.connect();
            console.log("✅ Redis connected");
        }
        else {
            console.log("✅ Redis already connected");
        }
        // Inicializar serviços
        console.log("🔄 Inicializando serviços...");
        const youtubeService = new YouTubeService_1.YouTubeService();
        const notificationService = new NotificationService_1.default();
        const playbackService = PlaybackService_1.PlaybackService.getInstance();
        const rewardService = new RewardService_1.default();
        // Inicializar serviço de reordenação automática de playlists
        const { playlistReorderService } = await Promise.resolve().then(() => __importStar(require("./services/PlaylistReorderService")));
        playlistReorderService.startAutoReorder();
        // Endpoint de teste para o serviço de reordenação
        app.get("/api/v1/test-reorder-status", (req, res) => {
            const status = playlistReorderService.getStatus();
            res.json({
                success: true,
                message: "Serviço de reordenação funcionando",
                status: {
                    isRunning: status.isRunning,
                    nextExecution: status.nextExecution?.toISOString(),
                    uptime: Math.round(status.uptime),
                },
            });
        });
        // Endpoint para atualizar metadados de fallback
        app.post("/api/v1/test-update-fallback/:restaurantId", async (req, res) => {
            try {
                const { collaborativePlaylistService } = await Promise.resolve().then(() => __importStar(require("./services/CollaborativePlaylistService")));
                const result = await collaborativePlaylistService.updateFallbackMetadata(req.params.restaurantId);
                res.json(result);
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    message: `Erro: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
                });
            }
        });
        // Endpoint para testar notificações em tempo real
        app.post("/api/v1/test-notifications/:restaurantId", async (req, res) => {
            try {
                const { WebSocketService } = await Promise.resolve().then(() => __importStar(require("./services/WebSocketService")));
                const webSocketService = WebSocketService.getInstance();
                const testNotification = {
                    type: "test_notification",
                    message: "Sistema de notificações funcionando!",
                    timestamp: new Date().toISOString(),
                    data: {
                        restaurantId: req.params.restaurantId,
                        testType: "notification_system",
                    },
                };
                await webSocketService.emitToRestaurant(req.params.restaurantId, "testNotification", testNotification);
                res.json({
                    success: true,
                    message: "Notificação de teste enviada",
                    notification: testNotification,
                });
            }
            catch (error) {
                res.status(500).json({
                    success: false,
                    message: `Erro: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
                });
            }
        });
        console.log("✅ Services initialized");
        // Criar dados de exemplo se não existirem
        await createSampleData();
        // Iniciar servidor
        const PORT = process.env.PORT || 5000;
        server.listen(PORT, () => {
            console.log(`🚀 Servidor rodando na porta ${PORT}`);
            console.log(`📊 Health check: http://localhost:${PORT}/health`);
            console.log(`🌐 API: http://localhost:${PORT}/api/v1`);
            console.log(`🎵 Sistema de Playlist Interativa - Pronto!`);
        });
    }
    catch (error) {
        console.error("❌ Erro ao inicializar servidor:", error);
        console.error("Stack trace:", error.stack);
        process.exit(1);
    }
}
// Função para criar dados de exemplo
async function createSampleData() {
    try {
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        // Verificar se já existe o restaurante demo
        const existingRestaurant = await restaurantRepository.findOne({
            where: { id: "demo-restaurant" },
        });
        if (!existingRestaurant) {
            console.log("🔄 Criando restaurante demo...");
            const demoRestaurant = new Restaurant_1.Restaurant();
            demoRestaurant.name = "Restaurante Demo";
            demoRestaurant.description = "Restaurante de demonstração do sistema";
            demoRestaurant.email = "<EMAIL>";
            demoRestaurant.phone = "(11) 99999-9999";
            demoRestaurant.isActive = true;
            demoRestaurant.status = "active";
            demoRestaurant.settings = {
                moderation: {
                    autoApprove: false,
                    requireApproval: true,
                    maxSuggestionsPerUser: 5,
                    maxSuggestionsPerHour: 20,
                    allowExplicitContent: false,
                    allowLiveVideos: true,
                    minVideoDuration: 30,
                    maxVideoDuration: 600,
                },
                playlist: {
                    maxQueueSize: 50,
                    allowDuplicates: false,
                    shuffleMode: false,
                    repeatMode: "none",
                    crossfadeDuration: 3,
                    defaultVolume: 70,
                },
                interface: {
                    theme: "light",
                    primaryColor: "#007bff",
                    secondaryColor: "#6c757d",
                    showVoteCount: true,
                    showQueuePosition: true,
                    allowAnonymousSuggestions: true,
                    requireSessionId: false,
                },
            };
            demoRestaurant.businessHours = {
                monday: { open: "18:00", close: "02:00", closed: false },
                tuesday: { open: "18:00", close: "02:00", closed: false },
                wednesday: { open: "18:00", close: "02:00", closed: false },
                thursday: { open: "18:00", close: "02:00", closed: false },
                friday: { open: "18:00", close: "03:00", closed: false },
                saturday: { open: "18:00", close: "03:00", closed: false },
                sunday: { open: "18:00", close: "01:00", closed: false },
            };
            const savedRestaurant = await restaurantRepository.save(demoRestaurant);
            // Definir o ID após salvar
            savedRestaurant.id = "demo-restaurant";
            await restaurantRepository.save(savedRestaurant);
            console.log("✅ Restaurante demo criado");
        }
        else {
            console.log("✅ Restaurante demo já existe");
        }
    }
    catch (error) {
        console.error("❌ Erro ao criar dados de exemplo:", error);
        // Não falhar o servidor por causa disso
    }
}
// Graceful shutdown
process.on("SIGTERM", async () => {
    console.log("🔄 Recebido SIGTERM, encerrando servidor...");
    server.close(async () => {
        await database_1.AppDataSource.destroy();
        await redis_1.redisClient.quit();
        console.log("✅ Servidor encerrado graciosamente");
        process.exit(0);
    });
});
process.on("SIGINT", async () => {
    console.log("🔄 Recebido SIGINT, encerrando servidor...");
    server.close(async () => {
        await database_1.AppDataSource.destroy();
        await redis_1.redisClient.quit();
        console.log("✅ Servidor encerrado graciosamente");
        process.exit(0);
    });
});
// Inicializar servidor
startServer();
//# sourceMappingURL=server.js.map