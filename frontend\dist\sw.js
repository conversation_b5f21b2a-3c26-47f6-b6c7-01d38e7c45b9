if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(n,r)=>{const l=e||("document"in self?document.currentScript.src:"")||location.href;if(s[l])return;let t={};const a=e=>i(e,l),u={module:{uri:l},exports:t,require:a};s[l]=Promise.all(n.map(e=>u[e]||a(e))).then(e=>(r(...e),t))}}define(["./workbox-e20531c6"],function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/AdvancedModeration-db913f6a.js",revision:null},{url:"assets/EnhancedRestaurantProfile-c6ee7bab.js",revision:null},{url:"assets/GenreManager-f5564d47.js",revision:null},{url:"assets/index-6e2e4ef2.js",revision:null},{url:"assets/index-b8ba42af.css",revision:null},{url:"assets/MusicPlayer-c1da3b65.js",revision:null},{url:"assets/PlaybackController-d0d556e1.js",revision:null},{url:"assets/PlaylistManager-e94771c8.js",revision:null},{url:"assets/ProblematicTracksAlert-5fb6ff31.js",revision:null},{url:"assets/QRCodeManager-a9b0104f.js",revision:null},{url:"assets/RestaurantProfile-60dba3be.js",revision:null},{url:"assets/RestaurantSettings-36105006.js",revision:null},{url:"assets/router-f729e475.js",revision:null},{url:"assets/ui-a5f8f5f0.js",revision:null},{url:"assets/UnifiedAnalytics-f94ce56f.js",revision:null},{url:"assets/utils-08f61814.js",revision:null},{url:"assets/vendor-66b0ef43.js",revision:null},{url:"favicon.svg",revision:"c96e9c7ecec474350501d8a935b938d5"},{url:"icons/icon-144x144.png",revision:"c9df239901a3fed8c62afd267b59f293"},{url:"icons/icon-144x144.svg",revision:"81c9cbf42df0146e58c7a51443275f69"},{url:"index.html",revision:"3d2eabd2b1768ebe58323fd689b1e7fd"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"manifest.webmanifest",revision:"8a04d6eb7deb84e16ad45a8c4051427d"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/api\.*/i,new e.NetworkFirst({cacheName:"api-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/^https:\/\/i\.ytimg\.com\/.*/i,new e.CacheFirst({cacheName:"youtube-thumbnails",plugins:[new e.ExpirationPlugin({maxEntries:200,maxAgeSeconds:604800})]}),"GET")});
//# sourceMappingURL=sw.js.map
