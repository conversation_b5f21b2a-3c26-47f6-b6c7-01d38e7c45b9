{"version": 3, "file": "RestaurantProfile-60dba3be.js", "sources": ["../../src/components/restaurant/RestaurantProfile.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  User,\r\n  Mail,\r\n  Phone,\r\n  MapPin,\r\n  Building2,\r\n  Clock,\r\n  Save,\r\n  Edit,\r\n  X,\r\n  Check,\r\n  AlertCircle,\r\n  RefreshCw,\r\n  Settings,\r\n  FileImage,\r\n  Radio,\r\n  ExternalLink,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { buildApiUrl, API_CONFIG } from \"../../config/api\";\r\n\r\n// Tipos e Interfaces\r\ntype TabType = \"basic\" | \"hours\" | \"settings\" | \"appearance\" | \"integrations\";\r\n\r\ninterface BusinessHours {\r\n  open: string;\r\n  close: string;\r\n  isOpen: boolean;\r\n}\r\n\r\ninterface RestaurantSettings {\r\n  allowSuggestions: boolean;\r\n  moderationRequired: boolean;\r\n  maxSuggestionsPerUser: number;\r\n  autoPlayEnabled: boolean;\r\n  autoSkipDisliked?: boolean;\r\n  darkMode?: boolean;\r\n  primaryColor?: string;\r\n}\r\n\r\ninterface RestaurantProfile {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  address: string;\r\n  description: string;\r\n  businessHours: {\r\n    [key: string]: BusinessHours;\r\n  };\r\n  settings: RestaurantSettings;\r\n  logoUrl?: string;\r\n  bannerUrl?: string;\r\n  appearance?: {\r\n    darkMode: boolean;\r\n    primaryColor: string;\r\n    accentColor: string;\r\n    fontFamily: string;\r\n  };\r\n  integrations?: {\r\n    youtubeApiEnabled: boolean;\r\n    spotifyConnected: boolean;\r\n    googleAnalytics: boolean;\r\n  };\r\n}\r\n\r\ninterface TabProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  id: TabType;\r\n  active: boolean;\r\n  onClick: (id: TabType) => void;\r\n}\r\n\r\n// Componente de Tab\r\nconst Tab: React.FC<TabProps> = ({ icon, label, id, active, onClick }) => (\r\n  <button\r\n    onClick={() => onClick(id)}\r\n    className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${\r\n      active\r\n        ? \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 font-medium\"\r\n        : \"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800\"\r\n    }`}\r\n    aria-selected={active}\r\n    role=\"tab\"\r\n    id={`tab-${id}`}\r\n    aria-controls={`panel-${id}`}\r\n  >\r\n    {React.cloneElement(icon as React.ReactElement, {\r\n      className: `w-5 h-5 ${\r\n        active\r\n          ? \"text-blue-600 dark:text-blue-400\"\r\n          : \"text-gray-500 dark:text-gray-400\"\r\n      }`,\r\n    })}\r\n    <span>{label}</span>\r\n  </button>\r\n);\r\n\r\n// Componente Principal\r\nconst RestaurantProfile: React.FC = () => {\r\n  const { restaurantId } = useParams<{\r\n    restaurantId: string;\r\n  }>();\r\n\r\n  // Validar que restaurantId existe\r\n  if (!restaurantId) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-2xl font-bold text-red-600\">Erro de Rota</h1>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            ID do restaurante não fornecido na URL\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const [profile, setProfile] = useState<RestaurantProfile | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [editing, setEditing] = useState(false);\r\n  const [saving, setSaving] = useState(false);\r\n  const [editedProfile, setEditedProfile] = useState<RestaurantProfile | null>(\r\n    null\r\n  );\r\n  const [activeTab, setActiveTab] = useState<TabType>(\"basic\");\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const daysOfWeek = [\r\n    { key: \"monday\", label: \"Segunda-feira\" },\r\n    { key: \"tuesday\", label: \"Terça-feira\" },\r\n    { key: \"wednesday\", label: \"Quarta-feira\" },\r\n    { key: \"thursday\", label: \"Quinta-feira\" },\r\n    { key: \"friday\", label: \"Sexta-feira\" },\r\n    { key: \"saturday\", label: \"Sábado\" },\r\n    { key: \"sunday\", label: \"Domingo\" },\r\n  ];\r\n\r\n  const tabs = [\r\n    {\r\n      id: \"basic\" as TabType,\r\n      label: \"Informações Básicas\",\r\n      icon: <Building2 />,\r\n    },\r\n    {\r\n      id: \"hours\" as TabType,\r\n      label: \"Horário de Funcionamento\",\r\n      icon: <Clock />,\r\n    },\r\n    { id: \"settings\" as TabType, label: \"Configurações\", icon: <Settings /> },\r\n    { id: \"appearance\" as TabType, label: \"Aparência\", icon: <FileImage /> },\r\n    {\r\n      id: \"integrations\" as TabType,\r\n      label: \"Integrações\",\r\n      icon: <ExternalLink />,\r\n    },\r\n  ];\r\n\r\n  // Função para carregar o perfil\r\n  const loadProfile = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      console.log(\"🏪 Carregando perfil do restaurante:\", restaurantId);\r\n\r\n      const url = buildApiUrl(\r\n        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`\r\n      );\r\n      console.log(\"🏪 Fazendo requisição para:\", url);\r\n\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 10000);\r\n\r\n      const response = await fetch(url, {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        signal: controller.signal,\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n      console.log(\"🏪 Response status:\", response.status);\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🏪 Profile loaded:\", data);\r\n\r\n        if (data.success && data.profile) {\r\n          // Garantir que todas as propriedades existam\r\n          const loadedProfile = {\r\n            ...data.profile,\r\n            appearance: data.profile.appearance || {\r\n              darkMode: false,\r\n              primaryColor: \"#3b82f6\",\r\n              accentColor: \"#10b981\",\r\n              fontFamily: \"Inter\",\r\n            },\r\n            integrations: data.profile.integrations || {\r\n              youtubeApiEnabled: true,\r\n              spotifyConnected: false,\r\n              googleAnalytics: false,\r\n            },\r\n          };\r\n\r\n          setProfile(loadedProfile);\r\n          setEditedProfile(loadedProfile);\r\n          toast.success(\"Perfil carregado com sucesso!\");\r\n          return;\r\n        }\r\n      } else {\r\n        console.error(\r\n          \"🏪 Response não ok:\",\r\n          response.status,\r\n          response.statusText\r\n        );\r\n        const errorText = await response.text().catch(() => \"\");\r\n        console.error(\"🏪 Error details:\", errorText);\r\n        throw new Error(`API returned status ${response.status}`);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"🏪 Erro ao carregar perfil:\", error);\r\n      setError(error.message || \"Erro ao carregar dados\");\r\n\r\n      // Mock data como fallback\r\n      const mockProfile: RestaurantProfile = {\r\n        id: restaurantId,\r\n        name: \"Restaurante Demo\",\r\n        email: \"<EMAIL>\",\r\n        phone: \"(11) 99999-9999\",\r\n        address: \"Rua das Flores, 123 - São Paulo, SP\",\r\n        description:\r\n          \"Um restaurante aconchegante com música ambiente personalizada pelos clientes.\",\r\n        businessHours: {\r\n          monday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n          tuesday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n          wednesday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n          thursday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n          friday: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n          saturday: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n          sunday: { open: \"12:00\", close: \"22:00\", isOpen: true },\r\n        },\r\n        settings: {\r\n          allowSuggestions: true,\r\n          moderationRequired: true,\r\n          maxSuggestionsPerUser: 3,\r\n          autoPlayEnabled: true,\r\n          autoSkipDisliked: false,\r\n        },\r\n        logoUrl: \"https://placehold.co/400x400?text=Logo\",\r\n        bannerUrl: \"https://placehold.co/1200x300?text=Banner\",\r\n        appearance: {\r\n          darkMode: false,\r\n          primaryColor: \"#3b82f6\",\r\n          accentColor: \"#10b981\",\r\n          fontFamily: \"Inter\",\r\n        },\r\n        integrations: {\r\n          youtubeApiEnabled: true,\r\n          spotifyConnected: false,\r\n          googleAnalytics: false,\r\n        },\r\n      };\r\n\r\n      setProfile(mockProfile);\r\n      setEditedProfile(mockProfile);\r\n\r\n      if (error.name === \"AbortError\") {\r\n        toast.error(\"Timeout - carregando dados de exemplo\");\r\n      } else if (error.message?.includes(\"CONNECTION_REFUSED\")) {\r\n  toast(\"Servidor offline - usando dados de exemplo\", { icon: \"ℹ️\" });\r\n      } else {\r\n  toast(\"Carregado em modo demo (dados de exemplo)\", { icon: \"ℹ️\" });\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId]);\r\n\r\n  useEffect(() => {\r\n    loadProfile();\r\n  }, [loadProfile]);\r\n\r\n  const handleSave = async () => {\r\n    if (!editedProfile) return;\r\n\r\n    try {\r\n      setSaving(true);\r\n      console.log(\"🏪 Salvando perfil do restaurante:\", restaurantId);\r\n\r\n      const url = buildApiUrl(\r\n        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`\r\n      );\r\n      console.log(\"🏪 Salvando para:\", url);\r\n\r\n      const response = await fetch(url, {\r\n        method: \"PUT\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(editedProfile),\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🏪 Profile saved:\", data);\r\n        setProfile(data.profile || editedProfile);\r\n        setEditing(false);\r\n        toast.success(\"Perfil atualizado com sucesso!\");\r\n      } else {\r\n        console.error(\r\n          \"🏪 Erro ao salvar:\",\r\n          response.status,\r\n          response.statusText\r\n        );\r\n        const errorText = await response.text().catch(() => \"\");\r\n        console.error(\"🏪 Error details:\", errorText);\r\n        throw new Error(`API returned status ${response.status}`);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"🏪 Erro ao salvar perfil:\", error);\r\n\r\n      // Mesmo em caso de erro de conectividade, salvar localmente\r\n      setProfile(editedProfile);\r\n      setEditing(false);\r\n\r\n      if (error?.response?.status) {\r\n        toast.error(\r\n          `Erro ${error.response.status}: ${\r\n            error.response.data?.message || \"Erro ao salvar perfil\"\r\n          }`\r\n        );\r\n      } else {\r\n  toast(\"Perfil salvo localmente (sem conexão com servidor)\", { icon: \"⚠️\" });\r\n      }\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setEditedProfile(profile);\r\n    setEditing(false);\r\n  };\r\n\r\n  const updateField = (field: string, value: any) => {\r\n    if (!editedProfile) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      [field]: value,\r\n    });\r\n  };\r\n\r\n  const updateBusinessHours = (day: string, field: string, value: any) => {\r\n    if (!editedProfile) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      businessHours: {\r\n        ...editedProfile.businessHours,\r\n        [day]: {\r\n          ...editedProfile.businessHours[day],\r\n          [field]: value,\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  const updateSettings = (field: string, value: any) => {\r\n    if (!editedProfile) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      settings: {\r\n        ...editedProfile.settings,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const updateAppearance = (field: string, value: any) => {\r\n    if (!editedProfile || !editedProfile.appearance) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      appearance: {\r\n        ...editedProfile.appearance,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const updateIntegrations = (field: string, value: any) => {\r\n    if (!editedProfile || !editedProfile.integrations) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      integrations: {\r\n        ...editedProfile.integrations,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Renderizadores de abas\r\n  const renderBasicInfo = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-basic\"\r\n      aria-labelledby=\"tab-basic\"\r\n    >\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n          Informações Básicas\r\n        </h3>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <label\r\n              htmlFor=\"restaurant-name\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Nome do Restaurante\r\n            </label>\r\n            {editing ? (\r\n              <input\r\n                id=\"restaurant-name\"\r\n                type=\"text\"\r\n                value={editedProfile?.name || \"\"}\r\n                onChange={(e) => updateField(\"name\", e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                <Building2\r\n                  className=\"w-4 h-4 text-gray-500\"\r\n                  aria-hidden=\"true\"\r\n                />\r\n                <span>{profile?.name}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label\r\n              htmlFor=\"email\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Email\r\n            </label>\r\n            {editing ? (\r\n              <input\r\n                id=\"email\"\r\n                type=\"email\"\r\n                value={editedProfile?.email || \"\"}\r\n                onChange={(e) => updateField(\"email\", e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                <Mail className=\"w-4 h-4 text-gray-500\" aria-hidden=\"true\" />\r\n                <span>{profile?.email}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label\r\n              htmlFor=\"phone\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Telefone\r\n            </label>\r\n            {editing ? (\r\n              <input\r\n                id=\"phone\"\r\n                type=\"tel\"\r\n                value={editedProfile?.phone || \"\"}\r\n                onChange={(e) => updateField(\"phone\", e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                <Phone className=\"w-4 h-4 text-gray-500\" aria-hidden=\"true\" />\r\n                <span>{profile?.phone}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label\r\n              htmlFor=\"address\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Endereço\r\n            </label>\r\n            {editing ? (\r\n              <input\r\n                id=\"address\"\r\n                type=\"text\"\r\n                value={editedProfile?.address || \"\"}\r\n                onChange={(e) => updateField(\"address\", e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                <MapPin className=\"w-4 h-4 text-gray-500\" aria-hidden=\"true\" />\r\n                <span>{profile?.address}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-6\">\r\n          <label\r\n            htmlFor=\"description\"\r\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n          >\r\n            Descrição\r\n          </label>\r\n          {editing ? (\r\n            <textarea\r\n              id=\"description\"\r\n              value={editedProfile?.description || \"\"}\r\n              onChange={(e) => updateField(\"description\", e.target.value)}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n            />\r\n          ) : (\r\n            <div className=\"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n              <span>{profile?.description}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Imagens do Restaurante */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n          Imagens do Restaurante\r\n        </h3>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Logo\r\n            </label>\r\n            {editing ? (\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-center h-40 w-40 mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-hidden\">\r\n                  {editedProfile?.logoUrl ? (\r\n                    <img\r\n                      src={editedProfile.logoUrl}\r\n                      alt=\"Logo do Restaurante\"\r\n                      className=\"max-h-full max-w-full object-contain\"\r\n                    />\r\n                  ) : (\r\n                    <div className=\"text-center p-4\">\r\n                      <FileImage\r\n                        className=\"w-8 h-8 text-gray-400 mx-auto mb-2\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Arraste ou clique para enviar logo\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"URL da imagem\"\r\n                  value={editedProfile?.logoUrl || \"\"}\r\n                  onChange={(e) => updateField(\"logoUrl\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center justify-center h-40 w-40 mx-auto bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden\">\r\n                {profile?.logoUrl ? (\r\n                  <img\r\n                    src={profile.logoUrl}\r\n                    alt=\"Logo do Restaurante\"\r\n                    className=\"max-h-full max-w-full object-contain\"\r\n                  />\r\n                ) : (\r\n                  <Building2\r\n                    className=\"w-12 h-12 text-gray-400\"\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Banner\r\n            </label>\r\n            {editing ? (\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-center h-32 w-full mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-hidden\">\r\n                  {editedProfile?.bannerUrl ? (\r\n                    <img\r\n                      src={editedProfile.bannerUrl}\r\n                      alt=\"Banner do Restaurante\"\r\n                      className=\"max-h-full max-w-full object-cover w-full h-full\"\r\n                    />\r\n                  ) : (\r\n                    <div className=\"text-center p-4\">\r\n                      <FileImage\r\n                        className=\"w-8 h-8 text-gray-400 mx-auto mb-2\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Arraste ou clique para enviar banner\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"URL da imagem\"\r\n                  value={editedProfile?.bannerUrl || \"\"}\r\n                  onChange={(e) => updateField(\"bannerUrl\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center justify-center h-32 w-full mx-auto bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden\">\r\n                {profile?.bannerUrl ? (\r\n                  <img\r\n                    src={profile.bannerUrl}\r\n                    alt=\"Banner do Restaurante\"\r\n                    className=\"max-h-full max-w-full object-cover w-full h-full\"\r\n                  />\r\n                ) : (\r\n                  <Building2\r\n                    className=\"w-12 h-12 text-gray-400\"\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderBusinessHours = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-hours\"\r\n      aria-labelledby=\"tab-hours\"\r\n    >\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2\">\r\n          <Clock className=\"w-5 h-5\" aria-hidden=\"true\" />\r\n          <span>Horário de Funcionamento</span>\r\n        </h3>\r\n\r\n        <div className=\"space-y-4\">\r\n          {daysOfWeek.map((day) => (\r\n            <div\r\n              key={day.key}\r\n              className=\"flex flex-wrap md:flex-nowrap items-center gap-4 p-3 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg transition-colors\"\r\n            >\r\n              <div className=\"w-full md:w-32\">\r\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  {day.label}\r\n                </span>\r\n              </div>\r\n\r\n              {editing ? (\r\n                <>\r\n                  <label className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={\r\n                        editedProfile?.businessHours[day.key]?.isOpen || false\r\n                      }\r\n                      onChange={(e) =>\r\n                        updateBusinessHours(day.key, \"isOpen\", e.target.checked)\r\n                      }\r\n                      className=\"rounded text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"text-sm\">Aberto</span>\r\n                  </label>\r\n\r\n                  {editedProfile?.businessHours[day.key]?.isOpen && (\r\n                    <div className=\"flex flex-wrap md:flex-nowrap items-center gap-2\">\r\n                      <label\r\n                        className=\"text-sm sr-only\"\r\n                        htmlFor={`open-${day.key}`}\r\n                      >\r\n                        Horário de abertura\r\n                      </label>\r\n                      <input\r\n                        id={`open-${day.key}`}\r\n                        type=\"time\"\r\n                        value={editedProfile.businessHours[day.key]?.open || \"\"}\r\n                        onChange={(e) =>\r\n                          updateBusinessHours(day.key, \"open\", e.target.value)\r\n                        }\r\n                        className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                      <span className=\"text-gray-500\">às</span>\r\n                      <label\r\n                        className=\"text-sm sr-only\"\r\n                        htmlFor={`close-${day.key}`}\r\n                      >\r\n                        Horário de fechamento\r\n                      </label>\r\n                      <input\r\n                        id={`close-${day.key}`}\r\n                        type=\"time\"\r\n                        value={\r\n                          editedProfile.businessHours[day.key]?.close || \"\"\r\n                        }\r\n                        onChange={(e) =>\r\n                          updateBusinessHours(day.key, \"close\", e.target.value)\r\n                        }\r\n                        className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <div className=\"flex items-center space-x-2\">\r\n                  {profile?.businessHours[day.key]?.isOpen ? (\r\n                    <>\r\n                      <Check\r\n                        className=\"w-5 h-5 text-green-600\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                        {profile.businessHours[day.key]?.open} às{\" \"}\r\n                        {profile.businessHours[day.key]?.close}\r\n                      </span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <X className=\"w-5 h-5 text-red-600\" aria-hidden=\"true\" />\r\n                      <span className=\"text-sm text-gray-500\">Fechado</span>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderSettings = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-settings\"\r\n      aria-labelledby=\"tab-settings\"\r\n    >\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2\">\r\n          <Settings className=\"w-5 h-5\" aria-hidden=\"true\" />\r\n          <span>Configurações da Playlist</span>\r\n        </h3>\r\n\r\n        <div className=\"space-y-6 divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Permitir Sugestões dos Clientes\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Clientes podem sugerir músicas via QR code\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"allow-suggestions\"\r\n                    checked={editedProfile?.settings.allowSuggestions || false}\r\n                    onChange={(e) =>\r\n                      updateSettings(\"allowSuggestions\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"allow-suggestions\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.settings.allowSuggestions\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.settings.allowSuggestions\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.settings.allowSuggestions\r\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                      : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.settings.allowSuggestions ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Moderação Obrigatória\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Sugestões precisam ser aprovadas antes de tocar\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"moderation-required\"\r\n                    checked={\r\n                      editedProfile?.settings.moderationRequired || false\r\n                    }\r\n                    onChange={(e) =>\r\n                      updateSettings(\"moderationRequired\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"moderation-required\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.settings.moderationRequired\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.settings.moderationRequired\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.settings.moderationRequired\r\n                      ? \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400\"\r\n                      : \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.settings.moderationRequired ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Reprodução Automática\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Próxima música inicia automaticamente\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"auto-play\"\r\n                    checked={editedProfile?.settings.autoPlayEnabled || false}\r\n                    onChange={(e) =>\r\n                      updateSettings(\"autoPlayEnabled\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"auto-play\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.settings.autoPlayEnabled\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.settings.autoPlayEnabled\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.settings.autoPlayEnabled\r\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                      : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.settings.autoPlayEnabled ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Pular Músicas com Muitos Votos Negativos\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Músicas com muitos dislikes serão puladas automaticamente\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"auto-skip\"\r\n                    checked={editedProfile?.settings.autoSkipDisliked || false}\r\n                    onChange={(e) =>\r\n                      updateSettings(\"autoSkipDisliked\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"auto-skip\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.settings.autoSkipDisliked\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.settings.autoSkipDisliked\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.settings.autoSkipDisliked\r\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                      : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.settings.autoSkipDisliked ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Máximo de Sugestões por Cliente\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Limite de sugestões por cliente por sessão\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max=\"10\"\r\n                  value={editedProfile?.settings.maxSuggestionsPerUser || 3}\r\n                  onChange={(e) =>\r\n                    updateSettings(\r\n                      \"maxSuggestionsPerUser\",\r\n                      parseInt(e.target.value)\r\n                    )\r\n                  }\r\n                  className=\"w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              ) : (\r\n                <div className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\r\n                  {profile?.settings.maxSuggestionsPerUser}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderAppearance = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-appearance\"\r\n      aria-labelledby=\"tab-appearance\"\r\n    >\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2\">\r\n          <FileImage className=\"w-5 h-5\" aria-hidden=\"true\" />\r\n          <span>Aparência do Player</span>\r\n        </h3>\r\n\r\n        <div className=\"space-y-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Modo Escuro\r\n              </label>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Ativar interface com tema escuro para o player de música\r\n              </p>\r\n            </div>\r\n            {editing ? (\r\n              <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  id=\"dark-mode\"\r\n                  checked={editedProfile?.appearance?.darkMode || false}\r\n                  onChange={(e) =>\r\n                    updateAppearance(\"darkMode\", e.target.checked)\r\n                  }\r\n                  className=\"sr-only\"\r\n                />\r\n                <label\r\n                  htmlFor=\"dark-mode\"\r\n                  className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                    editedProfile?.appearance?.darkMode\r\n                      ? \"bg-blue-600\"\r\n                      : \"bg-gray-300 dark:bg-gray-600\"\r\n                  }`}\r\n                >\r\n                  <span\r\n                    className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                      editedProfile?.appearance?.darkMode\r\n                        ? \"translate-x-6\"\r\n                        : \"translate-x-0\"\r\n                    }`}\r\n                  />\r\n                </label>\r\n              </div>\r\n            ) : (\r\n              <div\r\n                className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.appearance?.darkMode\r\n                    ? \"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400\"\r\n                    : \"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400\"\r\n                }`}\r\n              >\r\n                {profile?.appearance?.darkMode ? \"Escuro\" : \"Claro\"}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label\r\n              htmlFor=\"primary-color\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Cor Primária\r\n            </label>\r\n            <div className=\"flex items-center space-x-3\">\r\n              {editing ? (\r\n                <input\r\n                  id=\"primary-color\"\r\n                  type=\"color\"\r\n                  value={editedProfile?.appearance?.primaryColor || \"#3b82f6\"}\r\n                  onChange={(e) =>\r\n                    updateAppearance(\"primaryColor\", e.target.value)\r\n                  }\r\n                  className=\"h-10 w-20 border-0 p-0 rounded\"\r\n                />\r\n              ) : (\r\n                <div\r\n                  className=\"h-6 w-6 rounded-full border border-gray-300 dark:border-gray-600\"\r\n                  style={{\r\n                    backgroundColor:\r\n                      profile?.appearance?.primaryColor || \"#3b82f6\",\r\n                  }}\r\n                ></div>\r\n              )}\r\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                {profile?.appearance?.primaryColor || \"#3b82f6\"}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div>\r\n            <label\r\n              htmlFor=\"accent-color\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Cor de Destaque\r\n            </label>\r\n            <div className=\"flex items-center space-x-3\">\r\n              {editing ? (\r\n                <input\r\n                  id=\"accent-color\"\r\n                  type=\"color\"\r\n                  value={editedProfile?.appearance?.accentColor || \"#10b981\"}\r\n                  onChange={(e) =>\r\n                    updateAppearance(\"accentColor\", e.target.value)\r\n                  }\r\n                  className=\"h-10 w-20 border-0 p-0 rounded\"\r\n                />\r\n              ) : (\r\n                <div\r\n                  className=\"h-6 w-6 rounded-full border border-gray-300 dark:border-gray-600\"\r\n                  style={{\r\n                    backgroundColor:\r\n                      profile?.appearance?.accentColor || \"#10b981\",\r\n                  }}\r\n                ></div>\r\n              )}\r\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                {profile?.appearance?.accentColor || \"#10b981\"}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div>\r\n            <label\r\n              htmlFor=\"font-family\"\r\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n            >\r\n              Fonte\r\n            </label>\r\n            {editing ? (\r\n              <select\r\n                id=\"font-family\"\r\n                value={editedProfile?.appearance?.fontFamily || \"Inter\"}\r\n                onChange={(e) => updateAppearance(\"fontFamily\", e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              >\r\n                <option value=\"Inter\">Inter</option>\r\n                <option value=\"Roboto\">Roboto</option>\r\n                <option value=\"Poppins\">Poppins</option>\r\n                <option value=\"Montserrat\">Montserrat</option>\r\n                <option value=\"Open Sans\">Open Sans</option>\r\n              </select>\r\n            ) : (\r\n              <div className=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                <span\r\n                  style={{\r\n                    fontFamily: profile?.appearance?.fontFamily || \"Inter\",\r\n                  }}\r\n                >\r\n                  {profile?.appearance?.fontFamily || \"Inter\"}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"p-4 bg-gray-50 dark:bg-gray-750 rounded-lg\">\r\n            <h4 className=\"font-medium text-sm text-gray-700 dark:text-gray-300 mb-3\">\r\n              Visualização\r\n            </h4>\r\n            <div\r\n              className=\"h-36 rounded-lg bg-white dark:bg-gray-850 border border-gray-200 dark:border-gray-700 p-3 flex flex-col\"\r\n              style={{\r\n                backgroundColor: editedProfile?.appearance?.darkMode\r\n                  ? \"#1f2937\"\r\n                  : \"#ffffff\",\r\n                color: editedProfile?.appearance?.darkMode\r\n                  ? \"#f3f4f6\"\r\n                  : \"#111827\",\r\n                fontFamily: editedProfile?.appearance?.fontFamily || \"Inter\",\r\n              }}\r\n            >\r\n              <div className=\"text-center mb-2\">\r\n                <div\r\n                  className=\"font-bold\"\r\n                  style={{ color: editedProfile?.appearance?.primaryColor }}\r\n                >\r\n                  Agora Tocando\r\n                </div>\r\n                <div className=\"text-sm\">Nome da Música - Artista</div>\r\n              </div>\r\n              <div className=\"flex-1 flex items-center justify-center\">\r\n                <div className=\"h-2 w-full rounded-full bg-gray-200 dark:bg-gray-600\">\r\n                  <div\r\n                    className=\"h-2 rounded-full\"\r\n                    style={{\r\n                      width: \"40%\",\r\n                      backgroundColor:\r\n                        editedProfile?.appearance?.accentColor || \"#10b981\",\r\n                    }}\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex justify-between text-xs mt-2\">\r\n                <span>1:30</span>\r\n                <span>3:45</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderIntegrations = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-integrations\"\r\n      aria-labelledby=\"tab-integrations\"\r\n    >\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2\">\r\n          <ExternalLink className=\"w-5 h-5\" aria-hidden=\"true\" />\r\n          <span>Integrações</span>\r\n        </h3>\r\n\r\n        <div className=\"space-y-6 divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  API do YouTube\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Usar a API oficial do YouTube para buscar músicas\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"youtube-api\"\r\n                    checked={\r\n                      editedProfile?.integrations?.youtubeApiEnabled || false\r\n                    }\r\n                    onChange={(e) =>\r\n                      updateIntegrations(\"youtubeApiEnabled\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"youtube-api\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.integrations?.youtubeApiEnabled\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.integrations?.youtubeApiEnabled\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.integrations?.youtubeApiEnabled\r\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                      : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.integrations?.youtubeApiEnabled\r\n                    ? \"Conectado\"\r\n                    : \"Desconectado\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Spotify\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Integrar com sua conta Spotify para playlists\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"spotify-connected\"\r\n                    checked={\r\n                      editedProfile?.integrations?.spotifyConnected || false\r\n                    }\r\n                    onChange={(e) =>\r\n                      updateIntegrations(\"spotifyConnected\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"spotify-connected\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.integrations?.spotifyConnected\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.integrations?.spotifyConnected\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.integrations?.spotifyConnected\r\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                      : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.integrations?.spotifyConnected\r\n                    ? \"Conectado\"\r\n                    : \"Desconectado\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Google Analytics\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Monitorar estatísticas de uso do player\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <div className=\"relative inline-block w-12 mr-2 align-middle select-none\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"google-analytics\"\r\n                    checked={\r\n                      editedProfile?.integrations?.googleAnalytics || false\r\n                    }\r\n                    onChange={(e) =>\r\n                      updateIntegrations(\"googleAnalytics\", e.target.checked)\r\n                    }\r\n                    className=\"sr-only\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"google-analytics\"\r\n                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${\r\n                      editedProfile?.integrations?.googleAnalytics\r\n                        ? \"bg-blue-600\"\r\n                        : \"bg-gray-300 dark:bg-gray-600\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${\r\n                        editedProfile?.integrations?.googleAnalytics\r\n                          ? \"translate-x-6\"\r\n                          : \"translate-x-0\"\r\n                      }`}\r\n                    />\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    profile?.integrations?.googleAnalytics\r\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                      : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {profile?.integrations?.googleAnalytics ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <RefreshCw\r\n          className=\"w-8 h-8 animate-spin text-blue-600\"\r\n          aria-hidden=\"true\"\r\n        />\r\n        <span className=\"sr-only\">Carregando perfil do restaurante</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!profile) {\r\n    return (\r\n      <div className=\"text-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg\">\r\n        <AlertCircle\r\n          className=\"w-12 h-12 text-red-400 mx-auto mb-4\"\r\n          aria-hidden=\"true\"\r\n        />\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n          Erro ao carregar perfil\r\n        </h3>\r\n        <p className=\"text-red-500 mb-4\">\r\n          {error || \"Não foi possível carregar os dados do perfil\"}\r\n        </p>\r\n        <button\r\n          onClick={loadProfile}\r\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n        >\r\n          Tentar novamente\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            Perfil do Restaurante\r\n          </h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Gerencie as informações e configurações do seu restaurante\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex space-x-2\">\r\n          {editing ? (\r\n            <>\r\n              <button\r\n                onClick={handleCancel}\r\n                disabled={saving}\r\n                className=\"flex items-center space-x-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n                aria-label=\"Cancelar edição\"\r\n              >\r\n                <X className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                <span>Cancelar</span>\r\n              </button>\r\n              <button\r\n                onClick={handleSave}\r\n                disabled={saving}\r\n                className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n                aria-label=\"Salvar alterações\"\r\n              >\r\n                {saving ? (\r\n                  <RefreshCw\r\n                    className=\"w-4 h-4 animate-spin\"\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                ) : (\r\n                  <Save className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n                )}\r\n                <span>Salvar</span>\r\n              </button>\r\n            </>\r\n          ) : (\r\n            <button\r\n              onClick={() => setEditing(true)}\r\n              className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n              aria-label=\"Editar perfil\"\r\n            >\r\n              <Edit className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n              <span>Editar</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <div className=\"flex flex-col space-y-6\">\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-x-auto\">\r\n          <div className=\"flex p-2 gap-1 min-w-max\" role=\"tablist\">\r\n            {tabs.map((tab) => (\r\n              <Tab\r\n                key={tab.id}\r\n                id={tab.id}\r\n                label={tab.label}\r\n                icon={tab.icon}\r\n                active={activeTab === tab.id}\r\n                onClick={setActiveTab}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tab Content */}\r\n        <AnimatePresence mode=\"wait\">\r\n          <motion.div\r\n            key={activeTab}\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n          >\r\n            {activeTab === \"basic\" && renderBasicInfo()}\r\n            {activeTab === \"hours\" && renderBusinessHours()}\r\n            {activeTab === \"settings\" && renderSettings()}\r\n            {activeTab === \"appearance\" && renderAppearance()}\r\n            {activeTab === \"integrations\" && renderIntegrations()}\r\n          </motion.div>\r\n        </AnimatePresence>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RestaurantProfile;\r\n"], "names": ["Tab", "icon", "label", "id", "active", "onClick", "jsxs", "React", "jsx", "RestaurantProfile", "restaurantId", "useParams", "profile", "setProfile", "useState", "loading", "setLoading", "editing", "setEditing", "saving", "setSaving", "editedProfile", "setEditedProfile", "activeTab", "setActiveTab", "error", "setError", "daysOfWeek", "tabs", "Building2", "Clock", "Settings", "FileImage", "ExternalLink", "loadProfile", "useCallback", "url", "buildApiUrl", "API_CONFIG", "controller", "timeoutId", "response", "data", "loadedProfile", "toast", "errorText", "mockProfile", "useEffect", "handleSave", "handleCancel", "updateField", "field", "value", "updateBusinessHours", "day", "updateSettings", "updateAppearance", "updateIntegrations", "renderBasicInfo", "e", "Mail", "Phone", "MapPin", "renderBusinessHours", "Fragment", "_a", "_b", "_c", "_d", "_e", "Check", "_f", "_g", "X", "renderSettings", "renderAppearance", "_h", "_i", "_j", "_k", "_l", "_m", "_n", "_o", "_p", "_q", "_r", "_s", "renderIntegrations", "RefreshCw", "Save", "Edit", "tab", "AnimatePresence", "motion", "AlertCircle"], "mappings": "+UA+EA,MAAMA,GAA0B,CAAC,CAAE,KAAAC,EAAM,MAAAC,EAAO,GAAAC,EAAI,OAAAC,EAAQ,QAAAC,KAC1DC,EAAA,KAAC,SAAA,CACC,QAAS,IAAMD,EAAQF,CAAE,EACzB,UAAW,sEACTC,EACI,4EACA,2EACN,GACA,gBAAeA,EACf,KAAK,MACL,GAAI,OAAOD,CAAE,GACb,gBAAe,SAASA,CAAE,GAEzB,SAAA,CAAAI,GAAM,aAAaN,EAA4B,CAC9C,UAAW,WACTG,EACI,mCACA,kCACN,EAAA,CACD,EACDI,EAAAA,IAAC,QAAM,SAAMN,CAAA,CAAA,CAAA,CAAA,CACf,EAIIO,GAA8B,IAAM,CAClC,KAAA,CAAE,aAAAC,GAAiBC,KAKzB,GAAI,CAACD,EACH,aACG,MAAI,CAAA,UAAU,gDACb,SAACJ,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kCAAkC,SAAY,eAAA,EAC3DA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,yCAAA,CAAA,CACF,CAAA,CACF,CAAA,EAIJ,KAAM,CAACI,EAASC,CAAU,EAAIC,WAAmC,IAAI,EAC/D,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAI,EACrC,CAACG,EAASC,CAAU,EAAIJ,WAAS,EAAK,EACtC,CAACK,EAAQC,CAAS,EAAIN,WAAS,EAAK,EACpC,CAACO,EAAeC,CAAgB,EAAIR,EAAA,SACxC,IAAA,EAEI,CAACS,EAAWC,CAAY,EAAIV,WAAkB,OAAO,EACrD,CAACW,EAAOC,CAAQ,EAAIZ,WAAwB,IAAI,EAEhDa,EAAa,CACjB,CAAE,IAAK,SAAU,MAAO,eAAgB,EACxC,CAAE,IAAK,UAAW,MAAO,aAAc,EACvC,CAAE,IAAK,YAAa,MAAO,cAAe,EAC1C,CAAE,IAAK,WAAY,MAAO,cAAe,EACzC,CAAE,IAAK,SAAU,MAAO,aAAc,EACtC,CAAE,IAAK,WAAY,MAAO,QAAS,EACnC,CAAE,IAAK,SAAU,MAAO,SAAU,CAAA,EAG9BC,GAAO,CACX,CACE,GAAI,QACJ,MAAO,sBACP,WAAOC,EAAU,EAAA,CACnB,EACA,CACE,GAAI,QACJ,MAAO,2BACP,WAAOC,EAAM,EAAA,CACf,EACA,CAAE,GAAI,WAAuB,MAAO,gBAAiB,KAAMtB,EAAA,IAACuB,IAAS,CAAG,EACxE,CAAE,GAAI,aAAyB,MAAO,YAAa,KAAMvB,EAAA,IAACwB,IAAU,CAAG,EACvE,CACE,GAAI,eACJ,MAAO,cACP,WAAOC,EAAa,EAAA,CACtB,CAAA,EAIIC,EAAcC,EAAAA,YAAY,SAAY,OACtC,GAAA,CACFnB,EAAW,EAAI,EACfU,EAAS,IAAI,EACL,QAAA,IAAI,uCAAwChB,CAAY,EAEhE,MAAM0B,EAAMC,EACV,GAAGC,EAAW,UAAU,WAAW,IAAI5B,CAAY,UAAA,EAE7C,QAAA,IAAI,8BAA+B0B,CAAG,EAExC,MAAAG,EAAa,IAAI,gBACjBC,EAAY,WAAW,IAAMD,EAAW,MAAA,EAAS,GAAK,EAEtDE,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,OAAQG,EAAW,MAAA,CACpB,EAKD,GAHA,aAAaC,CAAS,EACd,QAAA,IAAI,sBAAuBC,EAAS,MAAM,EAE9CA,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OAGxB,GAFI,QAAA,IAAI,qBAAsBC,CAAI,EAElCA,EAAK,SAAWA,EAAK,QAAS,CAEhC,MAAMC,EAAgB,CACpB,GAAGD,EAAK,QACR,WAAYA,EAAK,QAAQ,YAAc,CACrC,SAAU,GACV,aAAc,UACd,YAAa,UACb,WAAY,OACd,EACA,aAAcA,EAAK,QAAQ,cAAgB,CACzC,kBAAmB,GACnB,iBAAkB,GAClB,gBAAiB,EACnB,CAAA,EAGF7B,EAAW8B,CAAa,EACxBrB,EAAiBqB,CAAa,EAC9BC,EAAM,QAAQ,+BAA+B,EAC7C,MACF,CAAA,KACK,CACG,QAAA,MACN,sBACAH,EAAS,OACTA,EAAS,UAAA,EAEX,MAAMI,EAAY,MAAMJ,EAAS,KAAO,EAAA,MAAM,IAAM,EAAE,EAC9C,cAAA,MAAM,oBAAqBI,CAAS,EACtC,IAAI,MAAM,uBAAuBJ,EAAS,MAAM,EAAE,CAC1D,QACOhB,EAAY,CACX,QAAA,MAAM,8BAA+BA,CAAK,EACzCA,EAAAA,EAAM,SAAW,wBAAwB,EAGlD,MAAMqB,EAAiC,CACrC,GAAIpC,EACJ,KAAM,mBACN,MAAO,iBACP,MAAO,kBACP,QAAS,sCACT,YACE,gFACF,cAAe,CACb,OAAQ,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACtD,QAAS,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACvD,UAAW,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACzD,SAAU,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACxD,OAAQ,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACtD,SAAU,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACxD,OAAQ,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,CACxD,EACA,SAAU,CACR,iBAAkB,GAClB,mBAAoB,GACpB,sBAAuB,EACvB,gBAAiB,GACjB,iBAAkB,EACpB,EACA,QAAS,yCACT,UAAW,4CACX,WAAY,CACV,SAAU,GACV,aAAc,UACd,YAAa,UACb,WAAY,OACd,EACA,aAAc,CACZ,kBAAmB,GACnB,iBAAkB,GAClB,gBAAiB,EACnB,CAAA,EAGFG,EAAWiC,CAAW,EACtBxB,EAAiBwB,CAAW,EAExBrB,EAAM,OAAS,aACjBmB,EAAM,MAAM,uCAAuC,GAC1CnB,EAAAA,EAAM,UAANA,MAAAA,EAAe,SAAS,sBACvCmB,EAAM,6CAA8C,CAAE,KAAM,IAAM,CAAA,EAElEA,EAAM,4CAA6C,CAAE,KAAM,IAAM,CAAA,CAC7D,QACA,CACA5B,EAAW,EAAK,CAClB,CAAA,EACC,CAACN,CAAY,CAAC,EAEjBqC,EAAAA,UAAU,IAAM,CACFb,GAAA,EACX,CAACA,CAAW,CAAC,EAEhB,MAAMc,GAAa,SAAY,SAC7B,GAAK3B,EAED,GAAA,CACFD,EAAU,EAAI,EACN,QAAA,IAAI,qCAAsCV,CAAY,EAE9D,MAAM0B,EAAMC,EACV,GAAGC,EAAW,UAAU,WAAW,IAAI5B,CAAY,UAAA,EAE7C,QAAA,IAAI,oBAAqB0B,CAAG,EAE9B,MAAAK,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAUf,CAAa,CAAA,CACnC,EAED,GAAIoB,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OACpB,QAAA,IAAI,oBAAqBC,CAAI,EAC1B7B,EAAA6B,EAAK,SAAWrB,CAAa,EACxCH,EAAW,EAAK,EAChB0B,EAAM,QAAQ,gCAAgC,CAAA,KACzC,CACG,QAAA,MACN,qBACAH,EAAS,OACTA,EAAS,UAAA,EAEX,MAAMI,EAAY,MAAMJ,EAAS,KAAO,EAAA,MAAM,IAAM,EAAE,EAC9C,cAAA,MAAM,oBAAqBI,CAAS,EACtC,IAAI,MAAM,uBAAuBJ,EAAS,MAAM,EAAE,CAC1D,QACOhB,EAAY,CACX,QAAA,MAAM,4BAA6BA,CAAK,EAGhDZ,EAAWQ,CAAa,EACxBH,EAAW,EAAK,GAEZO,EAAAA,GAAAA,YAAAA,EAAO,WAAPA,MAAAA,EAAiB,OACbmB,EAAA,MACJ,QAAQnB,EAAM,SAAS,MAAM,OAC3BA,EAAAA,EAAM,SAAS,OAAfA,YAAAA,EAAqB,UAAW,uBAClC,EAAA,EAGRmB,EAAM,qDAAsD,CAAE,KAAM,IAAM,CAAA,CACtE,QACA,CACAxB,EAAU,EAAK,CACjB,CAAA,EAGI6B,GAAe,IAAM,CACzB3B,EAAiBV,CAAO,EACxBM,EAAW,EAAK,CAAA,EAGZgC,EAAc,CAACC,EAAeC,IAAe,CAC5C/B,GACYC,EAAA,CACf,GAAGD,EACH,CAAC8B,CAAK,EAAGC,CAAA,CACV,CAAA,EAGGC,EAAsB,CAACC,EAAaH,EAAeC,IAAe,CACjE/B,GACYC,EAAA,CACf,GAAGD,EACH,cAAe,CACb,GAAGA,EAAc,cACjB,CAACiC,CAAG,EAAG,CACL,GAAGjC,EAAc,cAAciC,CAAG,EAClC,CAACH,CAAK,EAAGC,CACX,CACF,CAAA,CACD,CAAA,EAGGG,EAAiB,CAACJ,EAAeC,IAAe,CAC/C/B,GACYC,EAAA,CACf,GAAGD,EACH,SAAU,CACR,GAAGA,EAAc,SACjB,CAAC8B,CAAK,EAAGC,CACX,CAAA,CACD,CAAA,EAGGI,EAAmB,CAACL,EAAeC,IAAe,CAClD,CAAC/B,GAAiB,CAACA,EAAc,YACpBC,EAAA,CACf,GAAGD,EACH,WAAY,CACV,GAAGA,EAAc,WACjB,CAAC8B,CAAK,EAAGC,CACX,CAAA,CACD,CAAA,EAGGK,EAAqB,CAACN,EAAeC,IAAe,CACpD,CAAC/B,GAAiB,CAACA,EAAc,cACpBC,EAAA,CACf,GAAGD,EACH,aAAc,CACZ,GAAGA,EAAc,aACjB,CAAC8B,CAAK,EAAGC,CACX,CAAA,CACD,CAAA,EAIGM,GAAkB,IACtBpD,EAAA,KAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,cACH,kBAAgB,YAEhB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,sBAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,kBACR,UAAU,kEACX,SAAA,qBAAA,CAED,EACCS,EACCT,EAAA,IAAC,QAAA,CACC,GAAG,kBACH,KAAK,OACL,OAAOa,GAAA,YAAAA,EAAe,OAAQ,GAC9B,SAAWsC,GAAMT,EAAY,OAAQS,EAAE,OAAO,KAAK,EACnD,UAAU,wLAAA,CAGZ,EAAArD,EAAA,KAAC,MAAI,CAAA,UAAU,yEACb,SAAA,CAAAE,EAAA,IAACqB,EAAA,CACC,UAAU,wBACV,cAAY,MAAA,CACd,EACArB,EAAAA,IAAC,OAAM,CAAA,SAAAI,GAAA,YAAAA,EAAS,IAAK,CAAA,CAAA,EACvB,CAAA,EAEJ,SAEC,MACC,CAAA,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,QAAQ,QACR,UAAU,kEACX,SAAA,OAAA,CAED,EACCS,EACCT,EAAA,IAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,OAAOa,GAAA,YAAAA,EAAe,QAAS,GAC/B,SAAWsC,GAAMT,EAAY,QAASS,EAAE,OAAO,KAAK,EACpD,UAAU,wLAAA,CAGZ,EAAArD,EAAA,KAAC,MAAI,CAAA,UAAU,yEACb,SAAA,CAAAE,EAAA,IAACoD,GAAK,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC3DpD,EAAAA,IAAC,OAAM,CAAA,SAAAI,GAAA,YAAAA,EAAS,KAAM,CAAA,CAAA,EACxB,CAAA,EAEJ,SAEC,MACC,CAAA,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,QAAQ,QACR,UAAU,kEACX,SAAA,UAAA,CAED,EACCS,EACCT,EAAA,IAAC,QAAA,CACC,GAAG,QACH,KAAK,MACL,OAAOa,GAAA,YAAAA,EAAe,QAAS,GAC/B,SAAWsC,GAAMT,EAAY,QAASS,EAAE,OAAO,KAAK,EACpD,UAAU,wLAAA,CAGZ,EAAArD,EAAA,KAAC,MAAI,CAAA,UAAU,yEACb,SAAA,CAAAE,EAAA,IAACqD,GAAM,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC5DrD,EAAAA,IAAC,OAAM,CAAA,SAAAI,GAAA,YAAAA,EAAS,KAAM,CAAA,CAAA,EACxB,CAAA,EAEJ,SAEC,MACC,CAAA,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,QAAQ,UACR,UAAU,kEACX,SAAA,UAAA,CAED,EACCS,EACCT,EAAA,IAAC,QAAA,CACC,GAAG,UACH,KAAK,OACL,OAAOa,GAAA,YAAAA,EAAe,UAAW,GACjC,SAAWsC,GAAMT,EAAY,UAAWS,EAAE,OAAO,KAAK,EACtD,UAAU,wLAAA,CAGZ,EAAArD,EAAA,KAAC,MAAI,CAAA,UAAU,yEACb,SAAA,CAAAE,EAAA,IAACsD,GAAO,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC7DtD,EAAAA,IAAC,OAAM,CAAA,SAAAI,GAAA,YAAAA,EAAS,OAAQ,CAAA,CAAA,EAC1B,CAAA,EAEJ,CAAA,EACF,EAEAN,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,cACR,UAAU,kEACX,SAAA,WAAA,CAED,EACCS,EACCT,EAAA,IAAC,WAAA,CACC,GAAG,cACH,OAAOa,GAAA,YAAAA,EAAe,cAAe,GACrC,SAAWsC,GAAMT,EAAY,cAAeS,EAAE,OAAO,KAAK,EAC1D,KAAM,EACN,UAAU,wLAAA,CACZ,QAEC,MAAI,CAAA,UAAU,6CACb,SAACnD,MAAA,OAAA,CAAM,SAASI,GAAA,YAAAA,EAAA,WAAA,CAAY,CAC9B,CAAA,CAAA,EAEJ,CAAA,EACF,EAGAN,EAAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,yBAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,OAAA,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAE,EAAA,IAAC,MAAI,CAAA,UAAU,wKACZ,SAAAa,GAAA,MAAAA,EAAe,QACdb,EAAA,IAAC,MAAA,CACC,IAAKa,EAAc,QACnB,IAAI,sBACJ,UAAU,sCAAA,CAGZ,EAAAf,EAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAAAE,EAAA,IAACwB,EAAA,CACC,UAAU,qCACV,cAAY,MAAA,CACd,EACCxB,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,qCAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,gBACZ,OAAOa,GAAA,YAAAA,EAAe,UAAW,GACjC,SAAWsC,GAAMT,EAAY,UAAWS,EAAE,OAAO,KAAK,EACtD,UAAU,yIAAA,CACZ,CAAA,EACF,EAECnD,MAAA,MAAA,CAAI,UAAU,4GACZ,oBAAS,QACRA,EAAA,IAAC,MAAA,CACC,IAAKI,EAAQ,QACb,IAAI,sBACJ,UAAU,sCAAA,CAAA,EAGZJ,EAAA,IAACqB,EAAA,CACC,UAAU,0BACV,cAAY,MAAA,CAAA,EAGlB,CAAA,EAEJ,SAEC,MACC,CAAA,SAAA,CAACrB,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,SAAA,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAE,EAAA,IAAC,MAAI,CAAA,UAAU,0KACZ,SAAAa,GAAA,MAAAA,EAAe,UACdb,EAAA,IAAC,MAAA,CACC,IAAKa,EAAc,UACnB,IAAI,wBACJ,UAAU,kDAAA,CAGZ,EAAAf,EAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAAAE,EAAA,IAACwB,EAAA,CACC,UAAU,qCACV,cAAY,MAAA,CACd,EACCxB,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,uCAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,gBACZ,OAAOa,GAAA,YAAAA,EAAe,YAAa,GACnC,SAAWsC,GAAMT,EAAY,YAAaS,EAAE,OAAO,KAAK,EACxD,UAAU,yIAAA,CACZ,CAAA,EACF,EAECnD,MAAA,MAAA,CAAI,UAAU,8GACZ,oBAAS,UACRA,EAAA,IAAC,MAAA,CACC,IAAKI,EAAQ,UACb,IAAI,wBACJ,UAAU,kDAAA,CAAA,EAGZJ,EAAA,IAACqB,EAAA,CACC,UAAU,0BACV,cAAY,MAAA,CAAA,EAGlB,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,CAAA,CAAA,CAAA,EAIEkC,GAAsB,IAC1BvD,EAAA,IAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,cACH,kBAAgB,YAEhB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uFACZ,SAAA,CAAAE,EAAA,IAACsB,EAAM,CAAA,UAAU,UAAU,cAAY,OAAO,EAC9CtB,EAAAA,IAAC,QAAK,SAAwB,0BAAA,CAAA,CAAA,EAChC,QAEC,MAAI,CAAA,UAAU,YACZ,SAAWmB,EAAA,IAAK2B,sBACfhD,OAAAA,EAAA,KAAC,MAAA,CAEC,UAAU,4HAEV,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC,QAAK,UAAU,uDACb,SAAI8C,EAAA,KAAA,CACP,CACF,CAAA,EAECrC,EAEGX,EAAA,KAAA0D,WAAA,CAAA,SAAA,CAAC1D,EAAAA,KAAA,QAAA,CAAM,UAAU,8BACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UACEyD,EAAA5C,GAAA,YAAAA,EAAe,cAAciC,EAAI,OAAjC,YAAAW,EAAuC,SAAU,GAEnD,SAAWN,GACTN,EAAoBC,EAAI,IAAK,SAAUK,EAAE,OAAO,OAAO,EAEzD,UAAU,2CAAA,CACZ,EACCnD,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAM,SAAA,CAAA,EAClC,IAEC0D,EAAA7C,GAAA,YAAAA,EAAe,cAAciC,EAAI,OAAjC,YAAAY,EAAuC,SACtC5D,EAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,UAAU,kBACV,QAAS,QAAQ8C,EAAI,GAAG,GACzB,SAAA,qBAAA,CAED,EACA9C,EAAA,IAAC,QAAA,CACC,GAAI,QAAQ8C,EAAI,GAAG,GACnB,KAAK,OACL,QAAOa,EAAA9C,EAAc,cAAciC,EAAI,GAAG,IAAnC,YAAAa,EAAsC,OAAQ,GACrD,SAAWR,GACTN,EAAoBC,EAAI,IAAK,OAAQK,EAAE,OAAO,KAAK,EAErD,UAAU,8KAAA,CACZ,EACCnD,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAE,KAAA,EAClCA,EAAA,IAAC,QAAA,CACC,UAAU,kBACV,QAAS,SAAS8C,EAAI,GAAG,GAC1B,SAAA,uBAAA,CAED,EACA9C,EAAA,IAAC,QAAA,CACC,GAAI,SAAS8C,EAAI,GAAG,GACpB,KAAK,OACL,QACEc,EAAA/C,EAAc,cAAciC,EAAI,GAAG,IAAnC,YAAAc,EAAsC,QAAS,GAEjD,SAAWT,GACTN,EAAoBC,EAAI,IAAK,QAASK,EAAE,OAAO,KAAK,EAEtD,UAAU,8KAAA,CACZ,CAAA,EACF,CAAA,CAEJ,CAAA,EAECnD,EAAAA,IAAA,MAAA,CAAI,UAAU,8BACZ,UAAS6D,EAAAzD,GAAA,YAAAA,EAAA,cAAc0C,EAAI,OAAlB,MAAAe,EAAwB,OAE9B/D,EAAAA,KAAA0D,EAAAA,SAAA,CAAA,SAAA,CAAAxD,EAAA,IAAC8D,GAAA,CACC,UAAU,yBACV,cAAY,MAAA,CACd,EACAhE,EAAAA,KAAC,OAAK,CAAA,UAAU,2CACb,SAAA,EAAQiE,EAAA3D,EAAA,cAAc0C,EAAI,GAAG,IAArB,YAAAiB,EAAwB,KAAK,MAAI,KACzCC,EAAA5D,EAAQ,cAAc0C,EAAI,GAAG,IAA7B,YAAAkB,EAAgC,KAAA,EACnC,CAAA,CAAA,CACF,EAGElE,EAAAA,KAAA0D,EAAA,SAAA,CAAA,SAAA,CAAAxD,EAAA,IAACiE,EAAE,CAAA,UAAU,uBAAuB,cAAY,OAAO,EACtDjE,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAO,UAAA,CAAA,CAAA,CACjD,CAEJ,CAAA,CAAA,CAAA,EAlFG8C,EAAI,GAqFZ,EAAA,EACH,CAAA,EACF,CAAA,CAAA,EAIEoB,GAAiB,IACrBlE,EAAA,IAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,iBACH,kBAAgB,eAEhB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uFACZ,SAAA,CAAAE,EAAA,IAACuB,EAAS,CAAA,UAAU,UAAU,cAAY,OAAO,EACjDvB,EAAAA,IAAC,QAAK,SAAyB,2BAAA,CAAA,CAAA,EACjC,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,OACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,kCAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,6CAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,oBACH,SAASa,GAAA,YAAAA,EAAe,SAAS,mBAAoB,GACrD,SAAWsC,GACTJ,EAAe,mBAAoBI,EAAE,OAAO,OAAO,EAErD,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,oBACR,UAAW,2EACTa,GAAA,MAAAA,EAAe,SAAS,iBACpB,cACA,8BACN,GAEA,SAAAb,EAAA,IAAC,OAAA,CACC,UAAW,6EACTa,GAAA,MAAAA,EAAe,SAAS,iBACpB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEAb,EAAA,IAAC,MAAA,CACC,UAAW,8CACTI,GAAA,MAAAA,EAAS,SAAS,iBACd,uEACA,8DACN,GAEC,SAAAA,GAAA,MAAAA,EAAS,SAAS,iBAAmB,QAAU,SAAA,CAClD,CAAA,CAAA,CAEJ,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,OACb,SAACN,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,kDAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,sBACH,SACEa,GAAA,YAAAA,EAAe,SAAS,qBAAsB,GAEhD,SAAWsC,GACTJ,EAAe,qBAAsBI,EAAE,OAAO,OAAO,EAEvD,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,sBACR,UAAW,2EACTa,GAAA,MAAAA,EAAe,SAAS,mBACpB,cACA,8BACN,GAEA,SAAAb,EAAA,IAAC,OAAA,CACC,UAAW,6EACTa,GAAA,MAAAA,EAAe,SAAS,mBACpB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEAb,EAAA,IAAC,MAAA,CACC,UAAW,8CACTI,GAAA,MAAAA,EAAS,SAAS,mBACd,2EACA,sEACN,GAEC,SAAAA,GAAA,MAAAA,EAAS,SAAS,mBAAqB,QAAU,SAAA,CACpD,CAAA,CAAA,CAEJ,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,OACb,SAACN,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,wCAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,YACH,SAASa,GAAA,YAAAA,EAAe,SAAS,kBAAmB,GACpD,SAAWsC,GACTJ,EAAe,kBAAmBI,EAAE,OAAO,OAAO,EAEpD,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,YACR,UAAW,2EACTa,GAAA,MAAAA,EAAe,SAAS,gBACpB,cACA,8BACN,GAEA,SAAAb,EAAA,IAAC,OAAA,CACC,UAAW,6EACTa,GAAA,MAAAA,EAAe,SAAS,gBACpB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEAb,EAAA,IAAC,MAAA,CACC,UAAW,8CACTI,GAAA,MAAAA,EAAS,SAAS,gBACd,uEACA,8DACN,GAEC,SAAAA,GAAA,MAAAA,EAAS,SAAS,gBAAkB,QAAU,SAAA,CACjD,CAAA,CAAA,CAEJ,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,OACb,SAACN,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,2CAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,4DAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,YACH,SAASa,GAAA,YAAAA,EAAe,SAAS,mBAAoB,GACrD,SAAWsC,GACTJ,EAAe,mBAAoBI,EAAE,OAAO,OAAO,EAErD,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,YACR,UAAW,2EACTa,GAAA,MAAAA,EAAe,SAAS,iBACpB,cACA,8BACN,GAEA,SAAAb,EAAA,IAAC,OAAA,CACC,UAAW,6EACTa,GAAA,MAAAA,EAAe,SAAS,iBACpB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEAb,EAAA,IAAC,MAAA,CACC,UAAW,8CACTI,GAAA,MAAAA,EAAS,SAAS,iBACd,uEACA,8DACN,GAEC,SAAAA,GAAA,MAAAA,EAAS,SAAS,iBAAmB,QAAU,SAAA,CAClD,CAAA,CAAA,CAEJ,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,OACb,SAACN,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,kCAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,6CAAA,CAAA,EACF,EACCS,EACCT,EAAA,IAAC,QAAA,CACC,KAAK,SACL,IAAI,IACJ,IAAI,KACJ,OAAOa,GAAA,YAAAA,EAAe,SAAS,wBAAyB,EACxD,SAAWsC,GACTJ,EACE,wBACA,SAASI,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,kMAAA,CAAA,EAGXnD,EAAA,IAAA,MAAA,CAAI,UAAU,8GACZ,SAAAI,GAAA,YAAAA,EAAS,SAAS,sBACrB,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,CAAA,EACF,CAAA,CAAA,EAIE+D,GAAmB,IAAA,2CACvBnE,OAAAA,EAAA,IAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,mBACH,kBAAgB,iBAEhB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uFACZ,SAAA,CAAAE,EAAA,IAACwB,EAAU,CAAA,UAAU,UAAU,cAAY,OAAO,EAClDxB,EAAAA,IAAC,QAAK,SAAmB,qBAAA,CAAA,CAAA,EAC3B,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,cAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,2DAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,YACH,UAASyD,EAAA5C,GAAA,YAAAA,EAAe,aAAf,YAAA4C,EAA2B,WAAY,GAChD,SAAWN,GACTH,EAAiB,WAAYG,EAAE,OAAO,OAAO,EAE/C,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,YACR,UAAW,4EACT0D,EAAA7C,GAAA,YAAAA,EAAe,aAAf,MAAA6C,EAA2B,SACvB,cACA,8BACN,GAEA,SAAA1D,EAAA,IAAC,OAAA,CACC,UAAW,8EACT2D,EAAA9C,GAAA,YAAAA,EAAe,aAAf,MAAA8C,EAA2B,SACvB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEA3D,EAAA,IAAC,MAAA,CACC,UAAW,+CACT4D,EAAAxD,GAAA,YAAAA,EAAS,aAAT,MAAAwD,EAAqB,SACjB,2EACA,sEACN,GAEC,UAAAC,EAAAzD,GAAA,YAAAA,EAAS,aAAT,MAAAyD,EAAqB,SAAW,SAAW,OAAA,CAC9C,CAAA,EAEJ,SAEC,MACC,CAAA,SAAA,CAAA7D,EAAA,IAAC,QAAA,CACC,QAAQ,gBACR,UAAU,kEACX,SAAA,cAAA,CAED,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CACCW,EAAAT,EAAA,IAAC,QAAA,CACC,GAAG,gBACH,KAAK,QACL,QAAO+D,EAAAlD,GAAA,YAAAA,EAAe,aAAf,YAAAkD,EAA2B,eAAgB,UAClD,SAAWZ,GACTH,EAAiB,eAAgBG,EAAE,OAAO,KAAK,EAEjD,UAAU,gCAAA,CAAA,EAGZnD,EAAA,IAAC,MAAA,CACC,UAAU,mEACV,MAAO,CACL,kBACEgE,EAAA5D,GAAA,YAAAA,EAAS,aAAT,YAAA4D,EAAqB,eAAgB,SACzC,CAAA,CACD,QAEF,OAAK,CAAA,UAAU,2CACb,WAASI,EAAAhE,GAAA,YAAAA,EAAA,aAAA,YAAAgE,EAAY,eAAgB,UACxC,CAAA,EACF,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAApE,EAAA,IAAC,QAAA,CACC,QAAQ,eACR,UAAU,kEACX,SAAA,iBAAA,CAED,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CACCW,EAAAT,EAAA,IAAC,QAAA,CACC,GAAG,eACH,KAAK,QACL,QAAOqE,EAAAxD,GAAA,YAAAA,EAAe,aAAf,YAAAwD,EAA2B,cAAe,UACjD,SAAWlB,GACTH,EAAiB,cAAeG,EAAE,OAAO,KAAK,EAEhD,UAAU,gCAAA,CAAA,EAGZnD,EAAA,IAAC,MAAA,CACC,UAAU,mEACV,MAAO,CACL,kBACEsE,EAAAlE,GAAA,YAAAA,EAAS,aAAT,YAAAkE,EAAqB,cAAe,SACxC,CAAA,CACD,QAEF,OAAK,CAAA,UAAU,2CACb,WAASC,EAAAnE,GAAA,YAAAA,EAAA,aAAA,YAAAmE,EAAY,cAAe,UACvC,CAAA,EACF,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAAvE,EAAA,IAAC,QAAA,CACC,QAAQ,cACR,UAAU,kEACX,SAAA,OAAA,CAED,EACCS,EACCX,EAAA,KAAC,SAAA,CACC,GAAG,cACH,QAAO0E,EAAA3D,GAAA,YAAAA,EAAe,aAAf,YAAA2D,EAA2B,aAAc,QAChD,SAAWrB,GAAMH,EAAiB,aAAcG,EAAE,OAAO,KAAK,EAC9D,UAAU,yLAEV,SAAA,CAACnD,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAM,SAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAM,UAAU,SAAO,UAAA,EAC9BA,EAAA,IAAA,SAAA,CAAO,MAAM,aAAa,SAAU,aAAA,EACpCA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAS,YAAA,CAAA,CAAA,CAAA,EAGrCA,EAAAA,IAAC,MAAI,CAAA,UAAU,mDACb,SAAAA,EAAA,IAAC,OAAA,CACC,MAAO,CACL,aAAYyE,EAAArE,GAAA,YAAAA,EAAS,aAAT,YAAAqE,EAAqB,aAAc,OACjD,EAEC,WAAAC,EAAAtE,GAAA,YAAAA,EAAS,aAAT,YAAAsE,EAAqB,aAAc,OAAA,CAAA,EAExC,CAAA,EAEJ,EAEA5E,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,4DAA4D,SAE1E,eAAA,EACAF,EAAA,KAAC,MAAA,CACC,UAAU,0GACV,MAAO,CACL,iBAAiB6E,EAAA9D,GAAA,YAAAA,EAAe,aAAf,MAAA8D,EAA2B,SACxC,UACA,UACJ,OAAOC,EAAA/D,GAAA,YAAAA,EAAe,aAAf,MAAA+D,EAA2B,SAC9B,UACA,UACJ,aAAYC,EAAAhE,GAAA,YAAAA,EAAe,aAAf,YAAAgE,EAA2B,aAAc,OACvD,EAEA,SAAA,CAAC/E,EAAAA,KAAA,MAAA,CAAI,UAAU,mBACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,UAAU,YACV,MAAO,CAAE,OAAO8E,EAAAjE,GAAA,YAAAA,EAAe,aAAf,YAAAiE,EAA2B,YAAa,EACzD,SAAA,eAAA,CAED,EACC9E,EAAA,IAAA,MAAA,CAAI,UAAU,UAAU,SAAwB,2BAAA,CAAA,EACnD,QACC,MAAI,CAAA,UAAU,0CACb,SAACA,MAAA,MAAA,CAAI,UAAU,uDACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,mBACV,MAAO,CACL,MAAO,MACP,kBACE+E,EAAAlE,GAAA,YAAAA,EAAe,aAAf,YAAAkE,EAA2B,cAAe,SAC9C,CAAA,GAEJ,CACF,CAAA,EACAjF,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAE,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,EACVA,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CACF,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,CAAA,GAIEgF,GAAqB,IAAA,mCACzBhF,OAAAA,EAAA,IAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,qBACH,kBAAgB,mBAEhB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uFACZ,SAAA,CAAAE,EAAA,IAACyB,EAAa,CAAA,UAAU,UAAU,cAAY,OAAO,EACrDzB,EAAAA,IAAC,QAAK,SAAW,aAAA,CAAA,CAAA,EACnB,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,0DACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,OACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,iBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,oDAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,cACH,UACEyD,EAAA5C,GAAA,YAAAA,EAAe,eAAf,YAAA4C,EAA6B,oBAAqB,GAEpD,SAAWN,GACTF,EAAmB,oBAAqBE,EAAE,OAAO,OAAO,EAE1D,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,cACR,UAAW,4EACT0D,EAAA7C,GAAA,YAAAA,EAAe,eAAf,MAAA6C,EAA6B,kBACzB,cACA,8BACN,GAEA,SAAA1D,EAAA,IAAC,OAAA,CACC,UAAW,8EACT2D,EAAA9C,GAAA,YAAAA,EAAe,eAAf,MAAA8C,EAA6B,kBACzB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEA3D,EAAA,IAAC,MAAA,CACC,UAAW,+CACT4D,EAAAxD,GAAA,YAAAA,EAAS,eAAT,MAAAwD,EAAuB,kBACnB,uEACA,8DACN,GAEC,UAAAC,EAAAzD,GAAA,YAAAA,EAAS,eAAT,MAAAyD,EAAuB,kBACpB,YACA,cAAA,CACN,CAAA,CAAA,CAEJ,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,OACb,SAAC/D,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,UAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,gDAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,oBACH,UACE+D,EAAAlD,GAAA,YAAAA,EAAe,eAAf,YAAAkD,EAA6B,mBAAoB,GAEnD,SAAWZ,GACTF,EAAmB,mBAAoBE,EAAE,OAAO,OAAO,EAEzD,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,oBACR,UAAW,4EACTgE,EAAAnD,GAAA,YAAAA,EAAe,eAAf,MAAAmD,EAA6B,iBACzB,cACA,8BACN,GAEA,SAAAhE,EAAA,IAAC,OAAA,CACC,UAAW,8EACToE,EAAAvD,GAAA,YAAAA,EAAe,eAAf,MAAAuD,EAA6B,iBACzB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEApE,EAAA,IAAC,MAAA,CACC,UAAW,+CACTqE,EAAAjE,GAAA,YAAAA,EAAS,eAAT,MAAAiE,EAAuB,iBACnB,uEACA,8DACN,GAEC,UAAAC,EAAAlE,GAAA,YAAAA,EAAS,eAAT,MAAAkE,EAAuB,iBACpB,YACA,cAAA,CACN,CAAA,CAAA,CAEJ,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,OACb,SAACxE,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,0CAAA,CAAA,EACF,EACCS,EACCX,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,mBACH,UACEuE,EAAA1D,GAAA,YAAAA,EAAe,eAAf,YAAA0D,EAA6B,kBAAmB,GAElD,SAAWpB,GACTF,EAAmB,kBAAmBE,EAAE,OAAO,OAAO,EAExD,UAAU,SAAA,CACZ,EACAnD,EAAA,IAAC,QAAA,CACC,QAAQ,mBACR,UAAW,4EACTwE,EAAA3D,GAAA,YAAAA,EAAe,eAAf,MAAA2D,EAA6B,gBACzB,cACA,8BACN,GAEA,SAAAxE,EAAA,IAAC,OAAA,CACC,UAAW,8EACTyE,EAAA5D,GAAA,YAAAA,EAAe,eAAf,MAAA4D,EAA6B,gBACzB,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EAEAzE,EAAA,IAAC,MAAA,CACC,UAAW,+CACT0E,EAAAtE,GAAA,YAAAA,EAAS,eAAT,MAAAsE,EAAuB,gBACnB,uEACA,8DACN,GAEC,UAAAC,EAAAvE,GAAA,YAAAA,EAAS,eAAT,MAAAuE,EAAuB,gBAAkB,QAAU,SAAA,CACtD,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,CAAA,EACF,CAAA,CAAA,GAIJ,OAAIpE,EAEAT,EAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAE,EAAA,IAACiF,EAAA,CACC,UAAU,qCACV,cAAY,MAAA,CACd,EACCjF,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAgC,mCAAA,CAC5D,CAAA,CAAA,EAICI,EAwBHN,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8EACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAEjE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,6DAAA,CAAA,EACF,EAECA,MAAA,MAAA,CAAI,UAAU,iBACZ,WAEGF,EAAA,KAAA0D,WAAA,CAAA,SAAA,CAAA1D,EAAA,KAAC,SAAA,CACC,QAAS2C,GACT,SAAU9B,EACV,UAAU,2NACV,aAAW,kBAEX,SAAA,CAAAX,EAAA,IAACiE,EAAE,CAAA,UAAU,UAAU,cAAY,OAAO,EAC1CjE,EAAAA,IAAC,QAAK,SAAQ,UAAA,CAAA,CAAA,CAAA,CAChB,EACAF,EAAA,KAAC,SAAA,CACC,QAAS0C,GACT,SAAU7B,EACV,UAAU,2NACV,aAAW,oBAEV,SAAA,CACCA,EAAAX,EAAA,IAACiF,EAAA,CACC,UAAU,uBACV,cAAY,MAAA,CAAA,EAGbjF,EAAAA,IAAAkF,GAAA,CAAK,UAAU,UAAU,cAAY,OAAO,EAE/ClF,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CACd,CAAA,CAAA,CACF,EAEAF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMY,EAAW,EAAI,EAC9B,UAAU,oMACV,aAAW,gBAEX,SAAA,CAAAV,EAAA,IAACmF,GAAK,CAAA,UAAU,UAAU,cAAY,OAAO,EAC7CnF,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CAAA,EAGlB,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAE,EAAA,IAAC,MAAI,CAAA,UAAU,iEACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,2BAA2B,KAAK,UAC5C,SAAAoB,GAAK,IAAKgE,GACTpF,EAAA,IAACR,GAAA,CAEC,GAAI4F,EAAI,GACR,MAAOA,EAAI,MACX,KAAMA,EAAI,KACV,OAAQrE,IAAcqE,EAAI,GAC1B,QAASpE,CAAA,EALJoE,EAAI,EAAA,CAOZ,EACH,CACF,CAAA,EAGApF,EAAAA,IAACqF,GAAgB,CAAA,KAAK,OACpB,SAAAvF,EAAA,KAACwF,GAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAE3B,SAAA,CAAAvE,IAAc,SAAWmC,GAAgB,EACzCnC,IAAc,SAAWwC,GAAoB,EAC7CxC,IAAc,YAAcmD,GAAe,EAC3CnD,IAAc,cAAgBoD,GAAiB,EAC/CpD,IAAc,gBAAkBiE,GAAmB,CAAA,CAAA,EAV/CjE,CAAA,EAYT,CAAA,EACF,CACF,CAAA,CAAA,EA9GEjB,EAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAAAE,EAAA,IAACuF,GAAA,CACC,UAAU,sCACV,cAAY,MAAA,CACd,EACCvF,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,0BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,oBACV,YAAS,+CACZ,EACAA,EAAA,IAAC,SAAA,CACC,QAAS0B,EACT,UAAU,wKACX,SAAA,kBAAA,CAED,CACF,CAAA,CAAA,CA+FN"}