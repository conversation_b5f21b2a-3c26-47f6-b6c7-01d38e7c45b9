{"version": 3, "file": "RestaurantSettings-36105006.js", "sources": ["../../src/components/restaurant/YouTubeAuthManager.tsx", "../../src/components/admin/RestaurantSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Youtube,\r\n  CheckCircle,\r\n  AlertCircle,\r\n  Play,\r\n  TrendingUp,\r\n  RefreshCw,\r\n} from \"lucide-react\";\r\nimport toast from \"react-hot-toast\";\r\nimport { buildApiUrl } from \"../../config/api\";\r\n\r\ntype YouTubeAuthStatus = {\r\n  success?: boolean;\r\n  isAuthenticated: boolean;\r\n  capabilities: string[];\r\n  message: string;\r\n};\r\n\r\ntype PlaylistReorderResult = {\r\n  success: boolean;\r\n  message: string;\r\n  tracksReordered: number;\r\n  newOrder: Array<{\r\n    title: string;\r\n    artist: string;\r\n    voteScore: number;\r\n    upvotes: number;\r\n    downvotes: number;\r\n    positionChange: number;\r\n  }>;\r\n};\r\n\r\ninterface YouTubeAuthManagerProps {\r\n  restaurantId: string;\r\n  onAuthStatusChange?: (isAuthenticated: boolean) => void;\r\n}\r\n\r\nexport const YouTubeAuthManager: React.FC<YouTubeAuthManagerProps> = ({\r\n  restaurantId,\r\n  onAuthStatusChange,\r\n}) => {\r\n  console.log(\r\n    \"🎵 YouTubeAuthManager renderizado com restaurantId:\",\r\n    restaurantId\r\n  );\r\n\r\n  const [authStatus, setAuthStatus] = useState<YouTubeAuthStatus | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [reorderLoading, setReorderLoading] = useState(false);\r\n  const [showCreatePlaylist, setShowCreatePlaylist] = useState(false);\r\n  const [newPlaylistTitle, setNewPlaylistTitle] = useState(\"\");\r\n  const [newPlaylistDescription, setNewPlaylistDescription] = useState(\"\");\r\n\r\n  const checkAuthStatus = useCallback(async () => {\r\n    if (!restaurantId) {\r\n      toast.error(\"Restaurant ID is required\");\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      \"🎵 Verificando status do YouTube para restaurantId:\",\r\n      restaurantId\r\n    );\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        buildApiUrl(`/youtube-auth/${restaurantId}/status`)\r\n      );\r\n      const data = await response.json();\r\n\r\n      console.log(\"🎵 Resposta da API YouTube Status:\", {\r\n        status: response.status,\r\n        data,\r\n      });\r\n\r\n      if (response.ok && data.success) {\r\n        setAuthStatus(data);\r\n        onAuthStatusChange?.(data.isAuthenticated);\r\n      } else {\r\n        setAuthStatus({\r\n          isAuthenticated: false,\r\n          capabilities: [],\r\n          message: data.message || \"Não autenticado\",\r\n        });\r\n        onAuthStatusChange?.(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao verificar status:\", error);\r\n      setAuthStatus({\r\n        isAuthenticated: false,\r\n        capabilities: [],\r\n        message: \"Erro ao verificar status\",\r\n      });\r\n      onAuthStatusChange?.(false);\r\n      toast.error(\"Erro ao verificar status da autenticação\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId, onAuthStatusChange]);\r\n\r\n  const startAuthorization = useCallback(async () => {\r\n    if (!restaurantId) {\r\n      toast.error(\"Restaurant ID is required\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        buildApiUrl(`/youtube-auth/${restaurantId}/authorize`)\r\n      );\r\n      const data = await response.json();\r\n\r\n      if (data.success && data.authUrl) {\r\n        window.open(data.authUrl, \"youtube-auth\", \"width=600,height=700\");\r\n        toast.success(\"Janela de autorização aberta! Siga as instruções.\");\r\n\r\n        const checkInterval = setInterval(async () => {\r\n          await checkAuthStatus();\r\n          if (authStatus?.isAuthenticated) {\r\n            clearInterval(checkInterval);\r\n            toast.success(\"Autenticação concluída com sucesso!\");\r\n          }\r\n        }, 3000);\r\n\r\n        setTimeout(() => clearInterval(checkInterval), 300000);\r\n      } else {\r\n        toast.error(data.message || \"Erro ao iniciar autorização\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao iniciar autorização:\", error);\r\n      toast.error(\"Erro ao iniciar processo de autorização\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId, authStatus, checkAuthStatus]);\r\n\r\n  const createPlaylist = useCallback(async () => {\r\n    if (!newPlaylistTitle.trim()) {\r\n      toast.error(\"Título da playlist é obrigatório\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        buildApiUrl(`/youtube-auth/${restaurantId}/create-playlist`),\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({\r\n            title: newPlaylistTitle,\r\n            description: newPlaylistDescription,\r\n          }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        toast.success(\"Playlist criada com sucesso!\");\r\n        setShowCreatePlaylist(false);\r\n        setNewPlaylistTitle(\"\");\r\n        setNewPlaylistDescription(\"\");\r\n\r\n        if (data.playlistUrl) {\r\n          toast.success(\r\n            <div>\r\n              <p>Playlist criada!</p>\r\n              <a\r\n                href={data.playlistUrl}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-blue-500 underline\"\r\n              >\r\n                Ver no YouTube\r\n              </a>\r\n            </div>,\r\n            { duration: 5000 }\r\n          );\r\n        }\r\n      } else {\r\n        toast.error(data.message || \"Erro ao criar playlist\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar playlist:\", error);\r\n      toast.error(\"Erro ao criar playlist\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId, newPlaylistTitle, newPlaylistDescription]);\r\n\r\n  const reorderPlaylistByVotes = useCallback(\r\n    async (playlistId: string) => {\r\n      if (!playlistId) {\r\n        toast.error(\"Playlist ID is required\");\r\n        return;\r\n      }\r\n\r\n      setReorderLoading(true);\r\n      try {\r\n        const response = await fetch(\r\n          buildApiUrl(\r\n            `/youtube-auth/${restaurantId}/playlists/${playlistId}/reorder`\r\n          ),\r\n          {\r\n            method: \"POST\",\r\n          }\r\n        );\r\n\r\n        const data: PlaylistReorderResult = await response.json();\r\n\r\n        if (data.success) {\r\n          if (data.tracksReordered > 0) {\r\n            toast.success(\r\n              `${data.tracksReordered} músicas reordenadas baseado nos votos!`\r\n            );\r\n          } else {\r\n            toast.success(\"Playlist já está na ordem ideal!\");\r\n          }\r\n        } else {\r\n          toast.error(data.message || \"Erro ao reordenar playlist\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Erro ao reordenar playlist:\", error);\r\n        toast.error(\"Erro ao reordenar playlist\");\r\n      } finally {\r\n        setReorderLoading(false);\r\n      }\r\n    },\r\n    [restaurantId]\r\n  );\r\n\r\n  useEffect(() => {\r\n    console.log(\r\n      \"🎵 useEffect YouTubeAuthManager executado para restaurantId:\",\r\n      restaurantId\r\n    );\r\n    checkAuthStatus();\r\n    return () => {\r\n      // Cleanup any intervals or pending operations if component unmounts\r\n    };\r\n  }, [checkAuthStatus]);\r\n\r\n  if (loading && !authStatus) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <RefreshCw className=\"w-6 h-6 animate-spin text-purple-500\" />\r\n        <span className=\"ml-2\">Verificando autenticação...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center\">\r\n          <Youtube className=\"w-8 h-8 text-red-500 mr-3\" />\r\n          <div>\r\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\r\n              Controle de Playlist YouTube\r\n            </h2>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Gerencie playlists com controle total baseado em votações\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <button\r\n          onClick={checkAuthStatus}\r\n          disabled={loading}\r\n          className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n          aria-label=\"Refresh authentication status\"\r\n        >\r\n          <RefreshCw className={`w-5 h-5 ${loading ? \"animate-spin\" : \"\"}`} />\r\n        </button>\r\n      </div>\r\n\r\n      {authStatus ? (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3 }}\r\n          className=\"space-y-6\"\r\n        >\r\n          <div\r\n            className={`p-4 rounded-lg border-2 ${\r\n              authStatus.isAuthenticated\r\n                ? \"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20\"\r\n                : \"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20\"\r\n            }`}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              {authStatus.isAuthenticated ? (\r\n                <CheckCircle className=\"w-6 h-6 text-green-500 mr-3\" />\r\n              ) : (\r\n                <AlertCircle className=\"w-6 h-6 text-yellow-500 mr-3\" />\r\n              )}\r\n              <div>\r\n                <h3\r\n                  className={`font-semibold ${\r\n                    authStatus.isAuthenticated\r\n                      ? \"text-green-800 dark:text-green-200\"\r\n                      : \"text-yellow-800 dark:text-yellow-200\"\r\n                  }`}\r\n                >\r\n                  {authStatus.isAuthenticated\r\n                    ? \"Autenticado com YouTube\"\r\n                    : \"Não Autenticado\"}\r\n                </h3>\r\n                <p\r\n                  className={`text-sm ${\r\n                    authStatus.isAuthenticated\r\n                      ? \"text-green-600 dark:text-green-300\"\r\n                      : \"text-yellow-600 dark:text-yellow-300\"\r\n                  }`}\r\n                >\r\n                  {authStatus.message}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            {authStatus.capabilities.length > 0 ? (\r\n              authStatus.capabilities.map((capability, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\"\r\n                >\r\n                  <div className=\"w-2 h-2 bg-purple-500 rounded-full mr-3\"></div>\r\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                    {capability}\r\n                  </span>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Nenhuma capacidade disponível\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-4\">\r\n            {!authStatus.isAuthenticated ? (\r\n              <button\r\n                onClick={startAuthorization}\r\n                disabled={loading}\r\n                className=\"flex items-center px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n              >\r\n                <Youtube className=\"w-5 h-5 mr-2\" />\r\n                {loading ? \"Processando...\" : \"Conectar com YouTube\"}\r\n              </button>\r\n            ) : (\r\n              <>\r\n                <button\r\n                  onClick={() => setShowCreatePlaylist(true)}\r\n                  className=\"flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\"\r\n                >\r\n                  <Play className=\"w-4 h-4 mr-2\" />\r\n                  Criar Playlist\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => reorderPlaylistByVotes(\"current-playlist-id\")}\r\n                  disabled={reorderLoading}\r\n                  className=\"flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-colors\"\r\n                >\r\n                  <TrendingUp\r\n                    className={`w-4 h-4 mr-2 ${\r\n                      reorderLoading ? \"animate-spin\" : \"\"\r\n                    }`}\r\n                  />\r\n                  {reorderLoading ? \"Reordenando...\" : \"Reordenar por Votos\"}\r\n                </button>\r\n              </>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      ) : (\r\n        <div className=\"p-4 rounded-lg border-2 border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800\">\r\n          <div className=\"flex items-center\">\r\n            <AlertCircle className=\"w-6 h-6 text-gray-500 mr-3\" />\r\n            <div>\r\n              <h3 className=\"font-semibold text-gray-800 dark:text-gray-200\">\r\n                Carregando Status\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Verificando conexão com YouTube...\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4\">\r\n            <button\r\n              onClick={checkAuthStatus}\r\n              disabled={loading}\r\n              className=\"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors\"\r\n            >\r\n              <RefreshCw\r\n                className={`w-4 h-4 mr-2 ${loading ? \"animate-spin\" : \"\"}`}\r\n              />\r\n              {loading ? \"Verificando...\" : \"Tentar Novamente\"}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showCreatePlaylist && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.3 }}\r\n            className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\"\r\n          >\r\n            <h3 className=\"text-lg font-bold text-gray-900 dark:text-white mb-4\">\r\n              Criar Nova Playlist\r\n            </h3>\r\n\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label\r\n                  htmlFor=\"playlist-title\"\r\n                  className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n                >\r\n                  Título da Playlist *\r\n                </label>\r\n                <input\r\n                  id=\"playlist-title\"\r\n                  type=\"text\"\r\n                  value={newPlaylistTitle}\r\n                  onChange={(e) => setNewPlaylistTitle(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\"\r\n                  placeholder=\"Ex: Playlist Interativa - Restaurante\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label\r\n                  htmlFor=\"playlist-description\"\r\n                  className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n                >\r\n                  Descrição\r\n                </label>\r\n                <textarea\r\n                  id=\"playlist-description\"\r\n                  value={newPlaylistDescription}\r\n                  onChange={(e) => setNewPlaylistDescription(e.target.value)}\r\n                  rows={3}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\"\r\n                  placeholder=\"Playlist controlada pelos clientes através de votações...\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex justify-end space-x-3 mt-6\">\r\n              <button\r\n                onClick={() => setShowCreatePlaylist(false)}\r\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\r\n              >\r\n                Cancelar\r\n              </button>\r\n              <button\r\n                onClick={createPlaylist}\r\n                disabled={loading || !newPlaylistTitle.trim()}\r\n                className=\"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? \"Criando...\" : \"Criar Playlist\"}\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Settings,\r\n  Save,\r\n  Clock,\r\n  Users,\r\n  Music,\r\n  Shield,\r\n  Palette,\r\n  Bell,\r\n  Globe,\r\n  Volume2,\r\n  Eye,\r\n  Lock,\r\n  Youtube,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { YouTubeAuthManager } from \"@/components/restaurant/YouTubeAuthManager\";\r\nimport { useSettings } from \"@/store\";\r\nimport { useRestaurantContext } from \"@/components/restaurant/RestaurantDashboard\";\r\n\r\ninterface RestaurantConfig {\r\n  general: {\r\n    name: string;\r\n    description: string;\r\n    timezone: string;\r\n    language: string;\r\n  };\r\n  interface: {\r\n    theme: \"light\" | \"dark\" | \"auto\";\r\n    primaryColor: string;\r\n    allowSuggestions: boolean;\r\n    allowVoting: boolean;\r\n    showQueue: boolean;\r\n    showVoteCounts: boolean;\r\n    maxSuggestionsPerUser: number;\r\n  };\r\n  moderation: {\r\n    autoApprove: boolean;\r\n    requireModeration: boolean;\r\n    bannedWords: string[];\r\n    maxVotesForAutoApproval: number;\r\n    minVotesForAutoRejection: number;\r\n  };\r\n  schedule: {\r\n    enabled: boolean;\r\n    openTime: string;\r\n    closeTime: string;\r\n    timezone: string;\r\n    closedMessage: string;\r\n  };\r\n  notifications: {\r\n    emailNotifications: boolean;\r\n    newSuggestionAlert: boolean;\r\n    highVoteAlert: boolean;\r\n    moderationAlert: boolean;\r\n  };\r\n  audio: {\r\n    volume: number;\r\n    fadeInDuration: number;\r\n    fadeOutDuration: number;\r\n    crossfade: boolean;\r\n  };\r\n}\r\n\r\nconst RestaurantSettings: React.FC = () => {\r\n  const { settings, updateSettings } = useSettings();\r\n  const [config, setConfig] = useState<RestaurantConfig>({\r\n    general: {\r\n      name: \"Restaurante Demo\",\r\n      description: \"Um restaurante incrível com playlist interativa\",\r\n      timezone: \"America/Sao_Paulo\",\r\n      language: \"pt-BR\",\r\n    },\r\n    interface: {\r\n      theme: \"auto\",\r\n      primaryColor: \"#3B82F6\",\r\n      allowSuggestions: true,\r\n      allowVoting: true,\r\n      showQueue: true,\r\n      showVoteCounts: true,\r\n      maxSuggestionsPerUser: 5,\r\n    },\r\n    moderation: {\r\n      autoApprove: false,\r\n      requireModeration: true,\r\n      bannedWords: [\"palavra1\", \"palavra2\"],\r\n      maxVotesForAutoApproval: 10,\r\n      minVotesForAutoRejection: -5,\r\n    },\r\n    schedule: {\r\n      enabled: true,\r\n      openTime: \"11:00\",\r\n      closeTime: \"23:00\",\r\n      timezone: \"America/Sao_Paulo\",\r\n      closedMessage:\r\n        \"Estamos fechados. Volte durante nosso horário de funcionamento!\",\r\n    },\r\n    notifications: {\r\n      emailNotifications: true,\r\n      newSuggestionAlert: true,\r\n      highVoteAlert: true,\r\n      moderationAlert: true,\r\n    },\r\n    audio: {\r\n      volume: 75,\r\n      fadeInDuration: 3,\r\n      fadeOutDuration: 3,\r\n      crossfade: true,\r\n    },\r\n  });\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [activeTab, setActiveTab] = useState(\"general\");\r\n  const { restaurantId } = useRestaurantContext();\r\n\r\n  const saveSettings = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Em uma implementação real, isso salvaria as configurações na API\r\n      await new Promise((resolve) => setTimeout(resolve, 1500));\r\n      toast.success(\"Configurações salvas com sucesso!\");\r\n    } catch (error) {\r\n      toast.error(\"Erro ao salvar configurações\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const updateConfig = (\r\n    section: keyof RestaurantConfig,\r\n    field: string,\r\n    value: any\r\n  ) => {\r\n    setConfig((prev) => ({\r\n      ...prev,\r\n      [section]: {\r\n        ...prev[section],\r\n        [field]: value,\r\n      },\r\n    }));\r\n  };\r\n\r\n  const tabs = [\r\n    { id: \"general\", name: \"Geral\", icon: Settings },\r\n    { id: \"interface\", name: \"Interface\", icon: Palette },\r\n    { id: \"moderation\", name: \"Moderação\", icon: Shield },\r\n    { id: \"schedule\", name: \"Horários\", icon: Clock },\r\n    { id: \"notifications\", name: \"Notificações\", icon: Bell },\r\n    { id: \"audio\", name: \"Áudio\", icon: Volume2 },\r\n    { id: \"youtube\", name: \"YouTube\", icon: Youtube },\r\n  ];\r\n\r\n  const renderGeneralSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Nome do Restaurante\r\n        </label>\r\n        <input\r\n          type=\"text\"\r\n          value={config.general.name}\r\n          onChange={(e) => updateConfig(\"general\", \"name\", e.target.value)}\r\n          className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n        />\r\n      </div>\r\n\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Descrição\r\n        </label>\r\n        <textarea\r\n          value={config.general.description}\r\n          onChange={(e) =>\r\n            updateConfig(\"general\", \"description\", e.target.value)\r\n          }\r\n          rows={3}\r\n          className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Fuso Horário\r\n          </label>\r\n          <select\r\n            value={config.general.timezone}\r\n            onChange={(e) =>\r\n              updateConfig(\"general\", \"timezone\", e.target.value)\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"America/Sao_Paulo\">São Paulo (GMT-3)</option>\r\n            <option value=\"America/New_York\">Nova York (GMT-5)</option>\r\n            <option value=\"Europe/London\">Londres (GMT+0)</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Idioma\r\n          </label>\r\n          <select\r\n            value={config.general.language}\r\n            onChange={(e) =>\r\n              updateConfig(\"general\", \"language\", e.target.value)\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"pt-BR\">Português (Brasil)</option>\r\n            <option value=\"en-US\">English (US)</option>\r\n            <option value=\"es-ES\">Español</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderInterfaceSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Tema\r\n          </label>\r\n          <select\r\n            value={config.interface.theme}\r\n            onChange={(e) => updateConfig(\"interface\", \"theme\", e.target.value)}\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"light\">Claro</option>\r\n            <option value=\"dark\">Escuro</option>\r\n            <option value=\"auto\">Automático</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Cor Principal\r\n          </label>\r\n          <input\r\n            type=\"color\"\r\n            value={config.interface.primaryColor}\r\n            onChange={(e) =>\r\n              updateConfig(\"interface\", \"primaryColor\", e.target.value)\r\n            }\r\n            className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Posição das Notificações\r\n          </label>\r\n          <select\r\n            value={settings.notificationPosition || 'top-left'}\r\n            onChange={(e) => updateSettings({ notificationPosition: e.target.value as any })}\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"top-right\">Topo Direita</option>\r\n            <option value=\"top-left\">Topo Esquerda</option>\r\n            <option value=\"bottom-right\">Base Direita</option>\r\n            <option value=\"bottom-left\">Base Esquerda</option>\r\n          </select>\r\n          <p className=\"text-sm text-gray-500 mt-1\">Ajusta onde os avisos aparecem na tela do restaurante</p>\r\n        </div>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Permitir Sugestões\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Clientes podem sugerir músicas\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.interface.allowSuggestions}\r\n              onChange={(e) =>\r\n                updateConfig(\"interface\", \"allowSuggestions\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Permitir Votação\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Clientes podem votar em sugestões\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.interface.allowVoting}\r\n              onChange={(e) =>\r\n                updateConfig(\"interface\", \"allowVoting\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Mostrar Fila\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Exibir fila de reprodução para clientes\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.interface.showQueue}\r\n              onChange={(e) =>\r\n                updateConfig(\"interface\", \"showQueue\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Máximo de Sugestões por Cliente\r\n        </label>\r\n        <input\r\n          type=\"number\"\r\n          min=\"1\"\r\n          max=\"20\"\r\n          value={config.interface.maxSuggestionsPerUser}\r\n          onChange={(e) =>\r\n            updateConfig(\r\n              \"interface\",\r\n              \"maxSuggestionsPerUser\",\r\n              parseInt(e.target.value)\r\n            )\r\n          }\r\n          className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderModerationSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Aprovação Automática\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Aprovar sugestões automaticamente com base em votos\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.moderation.autoApprove}\r\n              onChange={(e) =>\r\n                updateConfig(\"moderation\", \"autoApprove\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Requer Moderação\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Todas as sugestões precisam ser moderadas\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.moderation.requireModeration}\r\n              onChange={(e) =>\r\n                updateConfig(\r\n                  \"moderation\",\r\n                  \"requireModeration\",\r\n                  e.target.checked\r\n                )\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Votos para Aprovação Automática\r\n          </label>\r\n          <input\r\n            type=\"number\"\r\n            value={config.moderation.maxVotesForAutoApproval}\r\n            onChange={(e) =>\r\n              updateConfig(\r\n                \"moderation\",\r\n                \"maxVotesForAutoApproval\",\r\n                parseInt(e.target.value)\r\n              )\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Votos para Rejeição Automática\r\n          </label>\r\n          <input\r\n            type=\"number\"\r\n            value={config.moderation.minVotesForAutoRejection}\r\n            onChange={(e) =>\r\n              updateConfig(\r\n                \"moderation\",\r\n                \"minVotesForAutoRejection\",\r\n                parseInt(e.target.value)\r\n              )\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderCurrentTab = () => {\r\n    switch (activeTab) {\r\n      case \"general\":\r\n        return renderGeneralSettings();\r\n      case \"interface\":\r\n        return renderInterfaceSettings();\r\n      case \"moderation\":\r\n        return renderModerationSettings();\r\n      case \"youtube\":\r\n        return renderYouTubeSettings();\r\n      default:\r\n        return (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            Em desenvolvimento...\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  const renderYouTubeSettings = () => {\r\n    console.log(\r\n      \"🎵 Renderizando configurações do YouTube para restaurantId:\",\r\n      restaurantId\r\n    );\r\n\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n            Integração com YouTube\r\n          </h3>\r\n          <p className=\"text-gray-600 dark:text-gray-400 text-sm mb-6\">\r\n            Configure a autenticação com YouTube para controlar playlists em\r\n            tempo real baseado nas votações dos clientes.\r\n          </p>\r\n        </div>\r\n\r\n        <YouTubeAuthManager\r\n          restaurantId={restaurantId}\r\n          onAuthStatusChange={(isAuthenticated) => {\r\n            console.log(\"YouTube Auth Status:\", isAuthenticated);\r\n          }}\r\n        />\r\n\r\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\r\n          <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">\r\n            💡 Como funciona\r\n          </h4>\r\n          <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1\">\r\n            <li>• Conecte sua conta YouTube Premium</li>\r\n            <li>• Crie playlists controláveis pelo sistema</li>\r\n            <li>\r\n              • As votações dos clientes reordenam automaticamente as músicas\r\n            </li>\r\n            <li>• Controle total sobre a ordem de reprodução</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            Configurações do Restaurante\r\n          </h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Personalize a experiência do seu restaurante\r\n          </p>\r\n        </div>\r\n\r\n        <button\r\n          onClick={saveSettings}\r\n          disabled={loading}\r\n          className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2\"\r\n        >\r\n          <Save className=\"w-4 h-4\" />\r\n          <span>{loading ? \"Salvando...\" : \"Salvar\"}</span>\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n          <nav className=\"flex space-x-8 px-6\">\r\n            {tabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                onClick={() => setActiveTab(tab.id)}\r\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${\r\n                  activeTab === tab.id\r\n                    ? \"border-blue-500 text-blue-600 dark:text-blue-400\"\r\n                    : \"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300\"\r\n                }`}\r\n              >\r\n                <tab.icon className=\"w-4 h-4\" />\r\n                <span>{tab.name}</span>\r\n              </button>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Tab Content */}\r\n        <div className=\"p-6\">{renderCurrentTab()}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RestaurantSettings;\r\n"], "names": ["YouTubeAuthManager", "restaurantId", "onAuthStatusChange", "authStatus", "setAuthStatus", "useState", "loading", "setLoading", "reorderLoading", "setReorderLoading", "showCreatePlaylist", "setShowCreatePlaylist", "newPlaylistTitle", "setNewPlaylistTitle", "newPlaylistDescription", "setNewPlaylistDescription", "checkAuthStatus", "useCallback", "toast", "response", "buildApiUrl", "data", "error", "startAuthorization", "checkInterval", "createPlaylist", "jsx", "reorderPlaylistByVotes", "playlistId", "useEffect", "jsxs", "RefreshCw", "Youtube", "motion", "CheckCircle", "AlertCircle", "capability", "index", "Fragment", "Play", "TrendingUp", "e", "RestaurantSettings", "settings", "updateSettings", "useSettings", "config", "setConfig", "activeTab", "setActiveTab", "useRestaurantContext", "saveSettings", "resolve", "updateConfig", "section", "field", "value", "prev", "tabs", "Settings", "Palette", "Shield", "Clock", "Bell", "Volume2", "renderGeneralSettings", "renderInterfaceSettings", "renderModerationSettings", "renderCurrentTab", "renderYouTubeSettings", "isAuthenticated", "Save", "tab"], "mappings": "gTAuCO,MAAMA,EAAwD,CAAC,CACpE,aAAAC,EACA,mBAAAC,CACF,IAAM,CACI,QAAA,IACN,sDACAD,CAAA,EAGF,KAAM,CAACE,EAAYC,CAAa,EAAIC,WAAmC,IAAI,EACrE,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAK,EACtC,CAACG,EAAgBC,CAAiB,EAAIJ,WAAS,EAAK,EACpD,CAACK,EAAoBC,CAAqB,EAAIN,WAAS,EAAK,EAC5D,CAACO,EAAkBC,CAAmB,EAAIR,WAAS,EAAE,EACrD,CAACS,EAAwBC,CAAyB,EAAIV,WAAS,EAAE,EAEjEW,EAAkBC,EAAAA,YAAY,SAAY,CAC9C,GAAI,CAAChB,EAAc,CACjBiB,EAAM,MAAM,2BAA2B,EACvC,MACF,CAEQ,QAAA,IACN,sDACAjB,CAAA,EAEFM,EAAW,EAAI,EACX,GAAA,CACF,MAAMY,EAAW,MAAM,MACrBC,EAAY,iBAAiBnB,CAAY,SAAS,CAAA,EAE9CoB,EAAO,MAAMF,EAAS,OAE5B,QAAQ,IAAI,qCAAsC,CAChD,OAAQA,EAAS,OACjB,KAAAE,CAAA,CACD,EAEGF,EAAS,IAAME,EAAK,SACtBjB,EAAciB,CAAI,EAClBnB,GAAA,MAAAA,EAAqBmB,EAAK,mBAEZjB,EAAA,CACZ,gBAAiB,GACjB,aAAc,CAAC,EACf,QAASiB,EAAK,SAAW,iBAAA,CAC1B,EACDnB,GAAA,MAAAA,EAAqB,WAEhBoB,EAAO,CACN,QAAA,MAAM,4BAA6BA,CAAK,EAClClB,EAAA,CACZ,gBAAiB,GACjB,aAAc,CAAC,EACf,QAAS,0BAAA,CACV,EACDF,GAAA,MAAAA,EAAqB,IACrBgB,EAAM,MAAM,0CAA0C,CAAA,QACtD,CACAX,EAAW,EAAK,CAClB,CAAA,EACC,CAACN,EAAcC,CAAkB,CAAC,EAE/BqB,EAAqBN,EAAAA,YAAY,SAAY,CACjD,GAAI,CAAChB,EAAc,CACjBiB,EAAM,MAAM,2BAA2B,EACvC,MACF,CAEAX,EAAW,EAAI,EACX,GAAA,CAII,MAAAc,EAAO,MAHI,MAAM,MACrBD,EAAY,iBAAiBnB,CAAY,YAAY,CAAA,GAE3B,OAExB,GAAAoB,EAAK,SAAWA,EAAK,QAAS,CAChC,OAAO,KAAKA,EAAK,QAAS,eAAgB,sBAAsB,EAChEH,EAAM,QAAQ,mDAAmD,EAE3D,MAAAM,EAAgB,YAAY,SAAY,CAC5C,MAAMR,EAAgB,EAClBb,GAAA,MAAAA,EAAY,kBACd,cAAcqB,CAAa,EAC3BN,EAAM,QAAQ,qCAAqC,IAEpD,GAAI,EAEP,WAAW,IAAM,cAAcM,CAAa,EAAG,GAAM,CAAA,MAE/CN,EAAA,MAAMG,EAAK,SAAW,6BAA6B,QAEpDC,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,EACnDJ,EAAM,MAAM,yCAAyC,CAAA,QACrD,CACAX,EAAW,EAAK,CAClB,CACC,EAAA,CAACN,EAAcE,EAAYa,CAAe,CAAC,EAExCS,EAAiBR,EAAAA,YAAY,SAAY,CACzC,GAAA,CAACL,EAAiB,OAAQ,CAC5BM,EAAM,MAAM,kCAAkC,EAC9C,MACF,CAEAX,EAAW,EAAI,EACX,GAAA,CAeI,MAAAc,EAAO,MAdI,MAAM,MACrBD,EAAY,iBAAiBnB,CAAY,kBAAkB,EAC3D,CACE,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CACnB,MAAOW,EACP,YAAaE,CAAA,CACd,CACH,CAAA,GAG0B,OAExBO,EAAK,SACPH,EAAM,QAAQ,8BAA8B,EAC5CP,EAAsB,EAAK,EAC3BE,EAAoB,EAAE,EACtBE,EAA0B,EAAE,EAExBM,EAAK,aACDH,EAAA,eACH,MACC,CAAA,SAAA,CAAAQ,EAAAA,IAAC,KAAE,SAAgB,kBAAA,CAAA,EACnBA,EAAA,IAAC,IAAA,CACC,KAAML,EAAK,YACX,OAAO,SACP,IAAI,sBACJ,UAAU,0BACX,SAAA,gBAAA,CAED,CAAA,EACF,EACA,CAAE,SAAU,GAAK,CAAA,GAIfH,EAAA,MAAMG,EAAK,SAAW,wBAAwB,QAE/CC,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,EAC9CJ,EAAM,MAAM,wBAAwB,CAAA,QACpC,CACAX,EAAW,EAAK,CAClB,CACC,EAAA,CAACN,EAAcW,EAAkBE,CAAsB,CAAC,EAErDa,EAAyBV,EAAA,YAC7B,MAAOW,GAAuB,CAC5B,GAAI,CAACA,EAAY,CACfV,EAAM,MAAM,yBAAyB,EACrC,MACF,CAEAT,EAAkB,EAAI,EAClB,GAAA,CAUI,MAAAY,EAA8B,MATnB,MAAM,MACrBD,EACE,iBAAiBnB,CAAY,cAAc2B,CAAU,UACvD,EACA,CACE,OAAQ,MACV,CAAA,GAGiD,OAE/CP,EAAK,QACHA,EAAK,gBAAkB,EACnBH,EAAA,QACJ,GAAGG,EAAK,eAAe,yCAAA,EAGzBH,EAAM,QAAQ,kCAAkC,EAG5CA,EAAA,MAAMG,EAAK,SAAW,4BAA4B,QAEnDC,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,EAClDJ,EAAM,MAAM,4BAA4B,CAAA,QACxC,CACAT,EAAkB,EAAK,CACzB,CACF,EACA,CAACR,CAAY,CAAA,EAcX,OAXJ4B,EAAAA,UAAU,KACA,QAAA,IACN,+DACA5B,CAAA,EAEce,IACT,IAAM,CAAA,GAGZ,CAACA,CAAe,CAAC,EAEhBV,GAAW,CAACH,EAEZ2B,EAAA,KAAC,MAAI,CAAA,UAAU,uCACb,SAAA,CAACJ,EAAAA,IAAAK,EAAA,CAAU,UAAU,sCAAuC,CAAA,EAC3DL,EAAA,IAAA,OAAA,CAAK,UAAU,OAAO,SAA2B,8BAAA,CACpD,CAAA,CAAA,EAKFI,EAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACJ,EAAAA,IAAAM,EAAA,CAAQ,UAAU,2BAA4B,CAAA,SAC9C,MACC,CAAA,SAAA,CAACN,EAAA,IAAA,KAAA,CAAG,UAAU,kDAAkD,SAEhE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,4DAAA,CAAA,EACF,CAAA,EACF,EAEAA,EAAA,IAAC,SAAA,CACC,QAASV,EACT,SAAUV,EACV,UAAU,oFACV,aAAW,gCAEX,eAACyB,EAAU,CAAA,UAAW,WAAWzB,EAAU,eAAiB,EAAE,GAAI,CAAA,CACpE,CAAA,EACF,EAECH,EACC2B,EAAA,KAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,YAEV,SAAA,CAAAP,EAAA,IAAC,MAAA,CACC,UAAW,2BACTvB,EAAW,gBACP,0EACA,6EACN,GAEA,SAAA2B,EAAA,KAAC,MAAI,CAAA,UAAU,oBACZ,SAAA,CAAW3B,EAAA,sBACT+B,EAAY,CAAA,UAAU,8BAA8B,EAErDR,EAAA,IAACS,EAAY,CAAA,UAAU,8BAA+B,CAAA,SAEvD,MACC,CAAA,SAAA,CAAAT,EAAA,IAAC,KAAA,CACC,UAAW,iBACTvB,EAAW,gBACP,qCACA,sCACN,GAEC,SAAAA,EAAW,gBACR,0BACA,iBAAA,CACN,EACAuB,EAAA,IAAC,IAAA,CACC,UAAW,WACTvB,EAAW,gBACP,qCACA,sCACN,GAEC,SAAWA,EAAA,OAAA,CACd,CAAA,EACF,CAAA,EACF,CAAA,CACF,EAECuB,EAAA,IAAA,MAAA,CAAI,UAAU,wCACZ,SAAWvB,EAAA,aAAa,OAAS,EAChCA,EAAW,aAAa,IAAI,CAACiC,EAAYC,IACvCP,EAAA,KAAC,MAAA,CAEC,UAAU,+DAEV,SAAA,CAACJ,EAAAA,IAAA,MAAA,CAAI,UAAU,yCAA0C,CAAA,EACxDA,EAAA,IAAA,OAAA,CAAK,UAAU,2CACb,SACHU,EAAA,CAAA,CAAA,EANKC,CAAA,CAQR,EAEDX,EAAAA,IAAC,KAAE,UAAU,2CAA2C,wCAExD,CAAA,EAEJ,QAEC,MAAI,CAAA,UAAU,uBACZ,SAACvB,EAAW,gBAWT2B,EAAA,KAAAQ,WAAA,CAAA,SAAA,CAAAR,EAAA,KAAC,SAAA,CACC,QAAS,IAAMnB,EAAsB,EAAI,EACzC,UAAU,wGAEV,SAAA,CAACe,EAAAA,IAAAa,EAAA,CAAK,UAAU,cAAe,CAAA,EAAE,gBAAA,CAAA,CAEnC,EAEAT,EAAA,KAAC,SAAA,CACC,QAAS,IAAMH,EAAuB,qBAAqB,EAC3D,SAAUnB,EACV,UAAU,0HAEV,SAAA,CAAAkB,EAAA,IAACc,EAAA,CACC,UAAW,gBACThC,EAAiB,eAAiB,EACpC,EAAA,CACF,EACCA,EAAiB,iBAAmB,qBAAA,CAAA,CACvC,CAAA,CAAA,CACF,EA9BAsB,EAAA,KAAC,SAAA,CACC,QAASP,EACT,SAAUjB,EACV,UAAU,kJAEV,SAAA,CAACoB,EAAAA,IAAAM,EAAA,CAAQ,UAAU,cAAe,CAAA,EACjC1B,EAAU,iBAAmB,sBAAA,CAAA,CAAA,CA0BpC,CAAA,CAAA,CAAA,CAGF,EAAAwB,EAAA,KAAC,MAAI,CAAA,UAAU,2FACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACJ,EAAAA,IAAAS,EAAA,CAAY,UAAU,4BAA6B,CAAA,SACnD,MACC,CAAA,SAAA,CAACT,EAAA,IAAA,KAAA,CAAG,UAAU,iDAAiD,SAE/D,oBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,qCAAA,CAAA,EACF,CAAA,EACF,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,OACb,SAAAI,EAAA,KAAC,SAAA,CACC,QAASd,EACT,SAAUV,EACV,UAAU,wHAEV,SAAA,CAAAoB,EAAA,IAACK,EAAA,CACC,UAAW,gBAAgBzB,EAAU,eAAiB,EAAE,EAAA,CAC1D,EACCA,EAAU,iBAAmB,kBAAA,CAAA,CAAA,EAElC,CAAA,EACF,EAGDI,GACCgB,EAAA,IAAC,MAAI,CAAA,UAAU,6EACb,SAAAI,EAAA,KAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,MAAO,EAAI,EAClC,QAAS,CAAE,QAAS,EAAG,MAAO,CAAE,EAChC,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,gEAEV,SAAA,CAACP,EAAA,IAAA,KAAA,CAAG,UAAU,uDAAuD,SAErE,sBAAA,EAEAI,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,QAAQ,iBACR,UAAU,kEACX,SAAA,sBAAA,CAED,EACAA,EAAA,IAAC,QAAA,CACC,GAAG,iBACH,KAAK,OACL,MAAOd,EACP,SAAW6B,GAAM5B,EAAoB4B,EAAE,OAAO,KAAK,EACnD,UAAU,8IACV,YAAY,uCAAA,CACd,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAAf,EAAA,IAAC,QAAA,CACC,QAAQ,uBACR,UAAU,kEACX,SAAA,WAAA,CAED,EACAA,EAAA,IAAC,WAAA,CACC,GAAG,uBACH,MAAOZ,EACP,SAAW2B,GAAM1B,EAA0B0B,EAAE,OAAO,KAAK,EACzD,KAAM,EACN,UAAU,8IACV,YAAY,2DAAA,CACd,CAAA,EACF,CAAA,EACF,EAEAX,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAJ,EAAA,IAAC,SAAA,CACC,QAAS,IAAMf,EAAsB,EAAK,EAC1C,UAAU,0FACX,SAAA,UAAA,CAED,EACAe,EAAA,IAAC,SAAA,CACC,QAASD,EACT,SAAUnB,GAAW,CAACM,EAAiB,KAAK,EAC5C,UAAU,oHAET,WAAU,aAAe,gBAAA,CAC5B,CAAA,EACF,CAAA,CAAA,CAAA,EAEJ,CAEJ,CAAA,CAAA,CAEJ,EC7ZM8B,EAA+B,IAAM,CACzC,KAAM,CAAE,SAAAC,EAAU,eAAAC,CAAe,EAAIC,EAAY,EAC3C,CAACC,EAAQC,CAAS,EAAI1C,WAA2B,CACrD,QAAS,CACP,KAAM,mBACN,YAAa,kDACb,SAAU,oBACV,SAAU,OACZ,EACA,UAAW,CACT,MAAO,OACP,aAAc,UACd,iBAAkB,GAClB,YAAa,GACb,UAAW,GACX,eAAgB,GAChB,sBAAuB,CACzB,EACA,WAAY,CACV,YAAa,GACb,kBAAmB,GACnB,YAAa,CAAC,WAAY,UAAU,EACpC,wBAAyB,GACzB,yBAA0B,EAC5B,EACA,SAAU,CACR,QAAS,GACT,SAAU,QACV,UAAW,QACX,SAAU,oBACV,cACE,iEACJ,EACA,cAAe,CACb,mBAAoB,GACpB,mBAAoB,GACpB,cAAe,GACf,gBAAiB,EACnB,EACA,MAAO,CACL,OAAQ,GACR,eAAgB,EAChB,gBAAiB,EACjB,UAAW,EACb,CAAA,CACD,EAEK,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAK,EACtC,CAAC2C,EAAWC,CAAY,EAAI5C,WAAS,SAAS,EAC9C,CAAE,aAAAJ,GAAiBiD,IAEnBC,EAAe,SAAY,CAC/B5C,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAAS6C,GAAY,WAAWA,EAAS,IAAI,CAAC,EACxDlC,EAAM,QAAQ,mCAAmC,OACnC,CACdA,EAAM,MAAM,8BAA8B,CAAA,QAC1C,CACAX,EAAW,EAAK,CAClB,CAAA,EAGI8C,EAAe,CACnBC,EACAC,EACAC,IACG,CACHT,EAAWU,IAAU,CACnB,GAAGA,EACH,CAACH,CAAO,EAAG,CACT,GAAGG,EAAKH,CAAO,EACf,CAACC,CAAK,EAAGC,CACX,CACA,EAAA,CAAA,EAGEE,EAAO,CACX,CAAE,GAAI,UAAW,KAAM,QAAS,KAAMC,CAAS,EAC/C,CAAE,GAAI,YAAa,KAAM,YAAa,KAAMC,CAAQ,EACpD,CAAE,GAAI,aAAc,KAAM,YAAa,KAAMC,CAAO,EACpD,CAAE,GAAI,WAAY,KAAM,WAAY,KAAMC,CAAM,EAChD,CAAE,GAAI,gBAAiB,KAAM,eAAgB,KAAMC,CAAK,EACxD,CAAE,GAAI,QAAS,KAAM,QAAS,KAAMC,CAAQ,EAC5C,CAAE,GAAI,UAAW,KAAM,UAAW,KAAMhC,CAAQ,CAAA,EAG5CiC,EAAwB,IAC3BnC,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,sBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoB,EAAO,QAAQ,KACtB,SAAWL,GAAMY,EAAa,UAAW,OAAQZ,EAAE,OAAO,KAAK,EAC/D,UAAU,iIAAA,CACZ,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACf,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,YAAA,EACAA,EAAA,IAAC,WAAA,CACC,MAAOoB,EAAO,QAAQ,YACtB,SAAWL,GACTY,EAAa,UAAW,cAAeZ,EAAE,OAAO,KAAK,EAEvD,KAAM,EACN,UAAU,iIAAA,CACZ,CAAA,EACF,EAEAX,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,eAAA,EACAI,EAAA,KAAC,SAAA,CACC,MAAOgB,EAAO,QAAQ,SACtB,SAAWL,GACTY,EAAa,UAAW,WAAYZ,EAAE,OAAO,KAAK,EAEpD,UAAU,kIAEV,SAAA,CAACf,EAAA,IAAA,SAAA,CAAO,MAAM,oBAAoB,SAAiB,oBAAA,EAClDA,EAAA,IAAA,SAAA,CAAO,MAAM,mBAAmB,SAAiB,oBAAA,EACjDA,EAAA,IAAA,SAAA,CAAO,MAAM,gBAAgB,SAAe,kBAAA,CAAA,CAAA,CAC/C,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,SAAA,EACAI,EAAA,KAAC,SAAA,CACC,MAAOgB,EAAO,QAAQ,SACtB,SAAWL,GACTY,EAAa,UAAW,WAAYZ,EAAE,OAAO,KAAK,EAEpD,UAAU,kIAEV,SAAA,CAACf,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAkB,qBAAA,EACvCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAY,eAAA,EACjCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAO,UAAA,CAAA,CAAA,CAC/B,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,EAGIwC,EAA0B,IAC7BpC,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,OAAA,EACAI,EAAA,KAAC,SAAA,CACC,MAAOgB,EAAO,UAAU,MACxB,SAAWL,GAAMY,EAAa,YAAa,QAASZ,EAAE,OAAO,KAAK,EAClE,UAAU,kIAEV,SAAA,CAACf,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAM,SAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAU,aAAA,CAAA,CAAA,CACjC,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,gBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,QACL,MAAOoB,EAAO,UAAU,aACxB,SAAWL,GACTY,EAAa,YAAa,eAAgBZ,EAAE,OAAO,KAAK,EAE1D,UAAU,oEAAA,CACZ,CAAA,EACF,CAAA,EACF,EAEAX,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,2BAAA,EACAI,EAAA,KAAC,SAAA,CACC,MAAOa,EAAS,sBAAwB,WACxC,SAAWF,GAAMG,EAAe,CAAE,qBAAsBH,EAAE,OAAO,MAAc,EAC/E,UAAU,kIAEV,SAAA,CAACf,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAY,eAAA,EACrCA,EAAA,IAAA,SAAA,CAAO,MAAM,WAAW,SAAa,gBAAA,EACrCA,EAAA,IAAA,SAAA,CAAO,MAAM,eAAe,SAAY,eAAA,EACxCA,EAAA,IAAA,SAAA,CAAO,MAAM,cAAc,SAAa,gBAAA,CAAA,CAAA,CAC3C,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAAqD,wDAAA,CAAA,EACjG,EACAI,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,qBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,iCAAA,CAAA,EACF,EACAI,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASoB,EAAO,UAAU,iBAC1B,SAAWL,GACTY,EAAa,YAAa,mBAAoBZ,EAAE,OAAO,OAAO,EAEhE,UAAU,cAAA,CACZ,EACAf,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAI,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,oCAAA,CAAA,EACF,EACAI,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASoB,EAAO,UAAU,YAC1B,SAAWL,GACTY,EAAa,YAAa,cAAeZ,EAAE,OAAO,OAAO,EAE3D,UAAU,cAAA,CACZ,EACAf,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAI,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,eAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,0CAAA,CAAA,EACF,EACAI,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASoB,EAAO,UAAU,UAC1B,SAAWL,GACTY,EAAa,YAAa,YAAaZ,EAAE,OAAO,OAAO,EAEzD,UAAU,cAAA,CACZ,EACAf,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,kCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,IAAI,IACJ,IAAI,KACJ,MAAOoB,EAAO,UAAU,sBACxB,SAAWL,GACTY,EACE,YACA,wBACA,SAASZ,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,iIAAA,CACZ,CAAA,EACF,CACF,CAAA,CAAA,EAGI0B,EAA2B,IAC9BrC,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,uBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,sDAAA,CAAA,EACF,EACAI,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASoB,EAAO,WAAW,YAC3B,SAAWL,GACTY,EAAa,aAAc,cAAeZ,EAAE,OAAO,OAAO,EAE5D,UAAU,cAAA,CACZ,EACAf,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAI,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,4CAAA,CAAA,EACF,EACAI,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASoB,EAAO,WAAW,kBAC3B,SAAWL,GACTY,EACE,aACA,oBACAZ,EAAE,OAAO,OACX,EAEF,UAAU,cAAA,CACZ,EACAf,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,CAAA,EACF,EAEAI,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,kCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOoB,EAAO,WAAW,wBACzB,SAAWL,GACTY,EACE,aACA,0BACA,SAASZ,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,iIAAA,CACZ,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACf,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,iCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOoB,EAAO,WAAW,yBACzB,SAAWL,GACTY,EACE,aACA,2BACA,SAASZ,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,iIAAA,CACZ,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,EAGI2B,EAAmB,IAAM,CAC7B,OAAQpB,EAAW,CACjB,IAAK,UACH,OAAOiB,EAAsB,EAC/B,IAAK,YACH,OAAOC,EAAwB,EACjC,IAAK,aACH,OAAOC,EAAyB,EAClC,IAAK,UACH,OAAOE,EAAsB,EAC/B,QACE,OACG3C,EAAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,uBAAA,CAAA,CAEN,CAAA,EAGI2C,EAAwB,KACpB,QAAA,IACN,8DACApE,CAAA,EAIA6B,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,yBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAG7D,iHAAA,CAAA,EACF,EAEAA,EAAA,IAAC1B,EAAA,CACC,aAAAC,EACA,mBAAqBqE,GAAoB,CAC/B,QAAA,IAAI,uBAAwBA,CAAe,CACrD,CAAA,CACF,EAEAxC,EAAAA,KAAC,MAAI,CAAA,UAAU,4FACb,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,oDAAoD,SAElE,mBAAA,EACAI,EAAAA,KAAC,KAAG,CAAA,UAAU,qDACZ,SAAA,CAAAJ,EAAAA,IAAC,MAAG,SAAmC,qCAAA,CAAA,EACvCA,EAAAA,IAAC,MAAG,SAA0C,4CAAA,CAAA,EAC9CA,EAAAA,IAAC,MAAG,SAEJ,iEAAA,CAAA,EACAA,EAAAA,IAAC,MAAG,SAA4C,8CAAA,CAAA,CAAA,EAClD,CAAA,EACF,CACF,CAAA,CAAA,GAKF,OAAAI,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACJ,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAEjE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,+CAAA,CAAA,EACF,EAEAI,EAAA,KAAC,SAAA,CACC,QAASqB,EACT,SAAU7C,EACV,UAAU,gHAEV,SAAA,CAACoB,EAAAA,IAAA6C,EAAA,CAAK,UAAU,SAAU,CAAA,EACzB7C,EAAA,IAAA,OAAA,CAAM,SAAUpB,EAAA,cAAgB,SAAS,CAAA,CAAA,CAC5C,CAAA,EACF,EAEAwB,EAAAA,KAAC,MAAI,CAAA,UAAU,8CAEb,SAAA,CAACJ,EAAA,IAAA,MAAA,CAAI,UAAU,gDACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,sBACZ,SAAAgC,EAAK,IAAKc,GACT1C,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMmB,EAAauB,EAAI,EAAE,EAClC,UAAW,0FACTxB,IAAcwB,EAAI,GACd,mDACA,wHACN,GAEA,SAAA,CAAA9C,EAAAA,IAAC8C,EAAI,KAAJ,CAAS,UAAU,SAAU,CAAA,EAC9B9C,EAAAA,IAAC,OAAM,CAAA,SAAA8C,EAAI,IAAK,CAAA,CAAA,CAAA,EATXA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGC9C,EAAA,IAAA,MAAA,CAAI,UAAU,MAAO,aAAmB,CAAA,EAC3C,CACF,CAAA,CAAA,CAEJ"}