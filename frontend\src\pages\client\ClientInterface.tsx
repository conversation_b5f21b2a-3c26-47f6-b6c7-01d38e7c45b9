import React, { useState, useEffect, useCallback } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  ThumbsUp,
  ThumbsDown,
  Search,
  Play,
  Users,
  Heart,
  Star,
  TrendingUp,
  Headphones,
  RefreshCw,
  User,
  CreditCard,
  Mic,
  Trophy,
  X,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "@/config/api";
import sessionService, { ClientSession, UserStats } from "@/services/sessionService";
import type { Suggestion as SuggestionType } from "@/types";
import { useWebSocket } from "@/services/websocket";
import MusicFilters from "@/components/music/MusicFilters";
import PaymentModal from "@/components/client/PaymentModal";
import KaraokePlayer from "@/components/client/KaraokePlayer";
import PlaybackQueue from "@/components/client/PlaybackQueue";
import ClientProfile from "@/components/client/ClientProfile";
// NewSuggestionAlert removido (fluxo legado de sugestões descontinuado)
import { TableLeaderboard } from "@/components/client/TableLeaderboard";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName: string;
  viewCount: number;
  publishedAt: string;
  youtubeVideoId: string;
  genre?: string;
}

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  status: "pending" | "approved" | "rejected";
  upvotes: number;
  downvotes: number;
  score: number;
  createdAt: string;
  isPaid?: boolean;
  clientSessionId?: string;
  duration?: number;
  thumbnailUrl?: string;
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  isOpen: boolean;
}

/**
 * Hook personalizado para gerenciar requisições com retry
 */
const useFetchWithRetry = () => {
  return useCallback(
    async (
      url: string,
      options: RequestInit = {},
      retries = 3,
      delay = 1000
    ): Promise<Response> => {
      let lastError: Error;

      for (let attempt = 0; attempt < retries; attempt++) {
        try {
          const response = await fetch(url, options);
          if (response.ok) return response;

          // Tentar extrair mensagem de erro do corpo da resposta
          let errorMessage = `Erro na requisição: ${response.status} ${response.statusText}`;
          try {
            const errorData = await response.json();
            if (errorData.error) {
              errorMessage = errorData.error;
            } else if (errorData.message) {
              errorMessage = errorData.message;
            }
          } catch {
            // Se não conseguir parsear JSON, usar mensagem padrão
          }

          lastError = new Error(errorMessage);

          if (response.status === 401 || response.status === 403) {
            // Não tente novamente para erros de autorização
            throw lastError;
          }

          if (response.status === 409) {
            // Não tente novamente para conflitos (música já sugerida)
            throw lastError;
          }
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));

          if (attempt === retries - 1) break;

          console.warn(
            `Tentativa ${
              attempt + 1
            } falhou, tentando novamente em ${delay}ms...`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }

      throw lastError!;
    },
    []
  );
};

/**
 * Hook para gerenciar os badges do usuário
 */
const useBadgeManager = () => {
  const getBadges = useCallback((stats: UserStats): string[] => {
    const badges = [];
    if (stats.suggestionsCount >= 1) badges.push("🎵 Primeira Sugestão");
    if (stats.suggestionsCount >= 5) badges.push("🎶 Melomaníaco");
    if (stats.suggestionsCount >= 10) badges.push("🎸 DJ Amador");
    if (stats.votesCount >= 10) badges.push("👍 Crítico Musical");
    if (stats.votesCount >= 25) badges.push("⭐ Especialista");
    if (stats.streak >= 3) badges.push("🔥 Em Chamas");
    if (stats.points >= 500) badges.push("🏆 Lenda");
    return badges;
  }, []);

  return { getBadges };
};

const ClientInterface: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [searchParams] = useSearchParams();
  const tableNumber = searchParams.get("table");
  const fetchWithRetry = useFetchWithRetry();
  const { getBadges } = useBadgeManager();

  // Agrupando estados relacionados
  // 1. Estado do restaurante
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);

  // 2. Estados de busca e playlist
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [playlistPage, setPlaylistPage] = useState(1);
  const [playlistLoading, setPlaylistLoading] = useState(false);
  const [hasMorePlaylist, setHasMorePlaylist] = useState(true);
  const [totalPlaylistSongs, setTotalPlaylistSongs] = useState(0);

  // 3. Estado de reprodução atual (fila unificada via PlaybackQueue)
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Suggestion | null>(
    null
  );
  const [showQueue, setShowQueue] = useState(true);
  const [queueCollapsed, setQueueCollapsed] = useState(false);

  // 4. Estados de sessão e usuário
  const [session, setSession] = useState<ClientSession | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    points: 0,
    level: 1,
    badges: [],
    suggestionsCount: 0,
    votesCount: 0,
    streak: 0,
  });
  const [clientName, setClientName] = useState("");

  // 5. Estados de UI e modais
  const [loading, setLoading] = useState(true);
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [showBadgeEarned, setShowBadgeEarned] = useState<string | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedSongForPayment, setSelectedSongForPayment] =
    useState<Song | null>(null);
  const [showKaraoke, setShowKaraoke] = useState(false);
  const [karaokeData, setKaraokeData] = useState<Suggestion | null>(null);
  const [showProfile, setShowProfile] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);

  const { isConnected, joinRestaurant, on, off } = useWebSocket();

  // Likes globais agora estão integrados na PlaybackQueue; seção antiga removida

  // Funções para gerenciamento de estado e interações com API

  /**
   * Carrega informações do restaurante atual
   */
  const loadRestaurantInfo = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    try {
      const response = await fetchWithRetry(
        buildApiUrl(`/restaurants/${restaurantId}`),
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      const data = await response.json();

      setRestaurant(
        data.restaurant || {
          id: restaurantId,
          name: "Restaurante Demo",
          description: "Ambiente acolhedor com música interativa",
          isActive: true,
          isOpen: true,
        }
      );

      console.log(
        `🏪 Entrou na sala do restaurante: ${
          data.restaurant?.name || "Restaurante Demo"
        }`
      );
    } catch (error) {
      console.error("Erro ao carregar restaurante:", error);

      // Dados de fallback para desenvolvimento
      setRestaurant({
        id: restaurantId,
        name: "Restaurante Demo",
        description: "Ambiente acolhedor com música interativa",
        isActive: true,
        isOpen: true,
      });

      toast.error("Erro ao carregar dados do restaurante", {
        duration: 4000,
        icon: "⚠️",
      });
    }
  }, [restaurantId, fetchWithRetry]);


  /**
   * Carrega a música que está tocando atualmente
   */
  const loadCurrentlyPlaying = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    try {
      const response = await fetchWithRetry(
        buildApiUrl(`/playback/${restaurantId}/state`),
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      const data = await response.json();

      if (data.success === false) {
        throw new Error(data.message || "Erro ao carregar música atual");
      }

      setCurrentlyPlaying(data.state?.currentTrack || null);
    } catch (error) {
      console.error("Erro ao carregar música atual:", error);
      setCurrentlyPlaying(null);
    }
  }, [restaurantId, fetchWithRetry]);

  /**
   * Carrega a playlist disponível do restaurante
   */
  const loadAvailableMusic = useCallback(
    async (page: number = 1, loadMore: boolean = false) => {
      if (!restaurantId) {
        toast.error("ID do restaurante não encontrado");
        return;
      }

      setPlaylistLoading(true);

      try {
        const limit = 24;
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
        });

        // Aplicar filtros
        if (activeFilters.length) {
          const genreFilters = activeFilters.filter((f) =>
            ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(
              f
            )
          );

          if (genreFilters.length) {
            params.append("genres", genreFilters.join(","));
          }

          const moodFilters = activeFilters.filter((f) =>
            ["happy", "sad", "energetic", "calm"].includes(f)
          );

          if (moodFilters.length) {
            params.append("moods", moodFilters.join(","));
          }
        }

        const response = await fetchWithRetry(
          buildApiUrl(
            `/restaurants/${restaurantId}/playlist?${params.toString()}`
          ),
          { headers: { "Content-Type": "application/json" } }
        );

        const data = await response.json();

        if (data.success && data.results) {
          const transformedResults = data.results.map((track: any) => ({
            id: track.youtubeVideoId || track.id,
            title: track.title,
            artist: track.artist,
            duration: track.duration || 0,
            formattedDuration: track.formattedDuration || "0:00",
            thumbnailUrl:
              track.thumbnailUrl ||
              `https://img.youtube.com/vi/${
                track.youtubeVideoId || track.id
              }/mqdefault.jpg`,
            youtubeVideoId: track.youtubeVideoId || track.id,
            channelName: track.artist,
            viewCount: track.viewCount || 0,
            publishedAt: track.addedAt || new Date().toISOString(),
            // Backend retorna 'genres' (array). Usar o primeiro para exibição.
            genre: Array.isArray(track.genres) && track.genres.length
              ? track.genres[0]
              : track.genre,
          }));

          setSearchResults((prev) =>
            loadMore ? [...prev, ...transformedResults] : transformedResults
          );
          setTotalPlaylistSongs(data.total || transformedResults.length);
          setHasMorePlaylist(transformedResults.length === limit);
          setPlaylistPage(page);
        } else {
          if (!loadMore) {
            setSearchResults([]);
            toast("Nenhuma música encontrada na playlist", { icon: "ℹ️" });
          }
        }
      } catch (error) {
        console.error("Erro ao carregar playlist:", error);
        toast.error("Erro ao carregar playlist do restaurante");

        if (!loadMore) {
          setSearchResults([]);
        }
      } finally {
        setPlaylistLoading(false);
      }
    },
    [restaurantId, activeFilters, fetchWithRetry]
  );

  /**
   * Busca músicas com base na query de pesquisa
   */
  const searchSongs = useCallback(async () => {
    if (!searchQuery.trim()) {
      toast.error("Digite uma busca válida");
      return;
    }

    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    setSearchLoading(true);

    try {
      const params = new URLSearchParams({
        q: encodeURIComponent(searchQuery.trim()),
      });

      // Aplicar filtros
      if (activeFilters.length) {
        const genreFilters = activeFilters.filter((f) =>
          ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(f)
        );

        if (genreFilters.length) {
          params.append("genres", genreFilters.join(","));
        }

        const moodFilters = activeFilters.filter((f) =>
          ["happy", "sad", "energetic", "calm"].includes(f)
        );

        if (moodFilters.length) {
          params.append("moods", moodFilters.join(","));
        }
      }

      const response = await fetchWithRetry(
        buildApiUrl(`/search/music?${params.toString()}`),
        { headers: { "Content-Type": "application/json" } }
      );

      const data = await response.json();

      if (data.success && data.results) {
        const transformedResults = data.results.map((track: any) => ({
          id: track.youtubeVideoId || track.id,
          title: track.title,
          artist: track.artist,
          duration: track.duration || 0,
          formattedDuration: track.formattedDuration || "0:00",
          thumbnailUrl:
            track.thumbnailUrl ||
            `https://img.youtube.com/vi/${
              track.youtubeVideoId || track.id
            }/mqdefault.jpg`,
          youtubeVideoId: track.youtubeVideoId || track.id,
          channelName: track.artist,
          viewCount: track.viewCount || 0,
          publishedAt: track.addedAt || new Date().toISOString(),
          genre: Array.isArray(track.genres) && track.genres.length
            ? track.genres[0]
            : track.genre,
        }));

        setSearchResults(transformedResults);
        setTotalPlaylistSongs(data.total || transformedResults.length);
        setHasMorePlaylist(false);
        setPlaylistPage(1);

        if (transformedResults.length > 0) {
          toast.success(
            `${transformedResults.length} música(s) encontrada(s)`,
            {
              icon: "🔍",
            }
          );
        } else {
          toast(`Nenhuma música encontrada para "${searchQuery}"`, {
            icon: "🔍",
          });
        }
      } else {
        setSearchResults([]);
        toast(`Nenhuma música encontrada para "${searchQuery}"`, {
          icon: "🔍",
        });
      }
    } catch (error) {
      console.error("Erro ao buscar músicas:", error);
      toast.error("Erro ao buscar músicas");
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  }, [searchQuery, restaurantId, activeFilters, fetchWithRetry]);

  /**
   * Inicializa a sessão do cliente
   */
  const initializeSession = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    setLoading(true);

    try {
      const newSession = await sessionService.forceNewSession(
        restaurantId,
        tableNumber || undefined,
        clientName || undefined
      );

      setSession(newSession);
      setUserStats({
        points: newSession.points || 0,
        level: newSession.level || 1,
        badges: newSession.badges || [],
        suggestionsCount: newSession.suggestionsCount || 0,
        votesCount: newSession.votesCount || 0,
        streak: newSession.streak || 0,
      });

      // Se não tiver nome definido, mostrar input
      if (!newSession.clientName && !clientName) {
        setShowNameInput(true);
      }
    } catch (error) {
      console.error("Erro ao inicializar sessão:", error);
      toast.error("Erro ao inicializar sessão", {
        duration: 4000,
        icon: "⚠️",
      });
    } finally {
      setLoading(false);
    }
  }, [restaurantId, tableNumber, clientName]);

  /**
   * Concede pontos ao usuário com base em ações
   */
  const awardPoints = useCallback(
    (action: "suggest" | "vote", amount: number = 10) => {
      setUserStats((prev) => {
        // Atualizar estatísticas
        const newStats = {
          ...prev,
          points: prev.points + amount,
          suggestionsCount:
            action === "suggest"
              ? prev.suggestionsCount + 1
              : prev.suggestionsCount,
          votesCount: action === "vote" ? prev.votesCount + 1 : prev.votesCount,
          streak: prev.streak + 1,
        };

        // Calcular novo nível
        const newLevel = Math.floor(newStats.points / 100) + 1;

        // Obter badges atualizadas
        const newBadges = getBadges(newStats);

        // Verificar se subiu de nível
        if (newLevel > prev.level) {
          setShowLevelUp(true);
          setTimeout(() => setShowLevelUp(false), 3000);

          toast.success(`🎉 Level Up! Agora você é nível ${newLevel}!`, {
            duration: 5000,
            icon: "🏆",
          });
        }

        // Verificar se ganhou nova badge
        const earnedBadge = newBadges.find(
          (badge) => !prev.badges.includes(badge)
        );
        if (earnedBadge) {
          sessionService.awardBadge(earnedBadge);
          setShowBadgeEarned(earnedBadge);
          setTimeout(() => setShowBadgeEarned(null), 3000);

          toast.success(`🏆 Nova conquista: ${earnedBadge}!`, {
            duration: 5000,
            icon: "🎖️",
          });
        }

        return {
          ...newStats,
          level: newLevel,
          badges: newBadges,
        };
      });
    },
    [getBadges]
  );


  /**
   * Prepara uma música para pagamento e prioridade
   */
  const suggestSongWithPayment = useCallback((song: Song) => {
    setSelectedSongForPayment(song);
    setShowPaymentModal(true);
  }, []);

  // Registrar voto normal (gratuito) na playlist colaborativa
  const voteOnSong = useCallback(
    async (song: Song) => {
      if (!session || !restaurantId) {
        toast.error("Sessão ou restaurante não encontrado");
        return;
      }

      try {
        const res = await fetchWithRetry(
          buildApiUrl(`/collaborative-playlist/${restaurantId}/vote`),
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              youtubeVideoId: song.youtubeVideoId,
              tableNumber: session.tableNumber,
              clientSessionId: session.id,
            }),
          }
        );
        const data = await res.json();
        if (data?.success === false) {
          throw new Error(data?.message || "Falha ao registrar voto");
        }
        toast.success(`Voto registrado para "${song.title}" ✅`);
        awardPoints("vote", 10);
      } catch (e) {
        console.error("Erro ao registrar voto:", e);
        toast.error(
          e instanceof Error ? e.message : "Erro ao registrar voto"
        );
      }
    },
    [session, restaurantId, fetchWithRetry, awardPoints]
  );

  /**
   * Processa o pagamento bem-sucedido de uma música
   */
  const handlePaymentSuccess = useCallback(
    async ({ paymentId, amountCents, clientMessage, clientName: paidClientName }: { paymentId: string; amountCents: number; clientMessage?: string; clientName?: string }) => {
      if (!selectedSongForPayment || !session || !restaurantId) {
        toast.error("Dados insuficientes para processar pagamento");
        return;
      }

      try {
        const response = await fetchWithRetry(
          buildApiUrl(`/collaborative-playlist/${restaurantId}/supervote`),
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              youtubeVideoId: selectedSongForPayment.youtubeVideoId,
              paymentAmount: (amountCents || 0) / 100,
              paymentId,
              tableNumber: session.tableNumber,
              clientSessionId: session.id,
              clientMessage: clientMessage || undefined,
              clientName: paidClientName || clientName || undefined,
            }),
          }
        );

        const data = await response.json();

        if (data.success === false) {
          throw new Error(data.message || "Erro ao processar pagamento");
        }

  toast.success(`SuperVoto aplicado em "${selectedSongForPayment.title}"! ⭐`);
  awardPoints("vote", 25);
        setSearchQuery("");

        if (searchQuery.trim()) {
          loadAvailableMusic(1);
        }

        // Preparar dados para karaokê
        setKaraokeData({
          id: selectedSongForPayment.id,
          title: selectedSongForPayment.title,
          artist: selectedSongForPayment.artist,
          thumbnailUrl: selectedSongForPayment.thumbnailUrl,
          duration: selectedSongForPayment.duration,
          youtubeVideoId: selectedSongForPayment.youtubeVideoId,
          status: "approved",
          upvotes: 0,
          downvotes: 0,
          score: 0,
          createdAt: new Date().toISOString(),
          isPaid: true,
          clientSessionId: session.id,
        });

        setShowPaymentModal(false);

        // Perguntar sobre karaokê após um breve delay
        setTimeout(() => {
          toast(
            (t) => (
              <div className="flex flex-col gap-2">
                <span>🎤 Quer cantar junto? Ative o "Cante Comigo"!</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setShowKaraoke(true);
                      toast.dismiss(t.id);
                    }}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
                  >
                    Sim, vamos cantar!
                  </button>
                  <button
                    onClick={() => toast.dismiss(t.id)}
                    className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                  >
                    Não, obrigado
                  </button>
                </div>
              </div>
            ),
            { duration: 8000 }
          );
        }, 2000);
      } catch (error) {
        console.error("Erro após pagamento:", error);
        toast.error("Erro ao processar pagamento", {
          duration: 4000,
          icon: "❌",
        });
      } finally {
        setSelectedSongForPayment(null);
      }
    },
    [
      selectedSongForPayment,
      session,
      restaurantId,
      loadAvailableMusic,
      searchQuery,
      fetchWithRetry,
      awardPoints,
    ]
  );


  /**
   * Salva o nome do cliente na sessão
   */
  const saveClientName = useCallback(async () => {
    if (!clientName.trim()) {
      toast.error("Por favor, digite seu nome");
      return;
    }

    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    try {
      const updatedSession = await sessionService.createSession(
        restaurantId,
        tableNumber || undefined,
        clientName
      );

      setSession(updatedSession);
      setShowNameInput(false);

      // Sincroniza nome no perfil local
      const profileKey = `clientProfile_${updatedSession.id}`;
      const savedProfile = localStorage.getItem(profileKey);
      if (savedProfile) {
        const parsed = JSON.parse(savedProfile);
        parsed.name = clientName;
        localStorage.setItem(profileKey, JSON.stringify(parsed));
      } else {
        localStorage.setItem(profileKey, JSON.stringify({
          name: clientName,
          avatar: "",
          joinedAt: new Date().toISOString(),
          level: 1,
          experience: 0,
          nextLevelExp: 100,
          title: "Novo Ouvinte",
          preferences: { favoriteGenres: [], notifications: true, autoShare: false },
        }));
      }

      toast.success(`Bem-vindo, ${clientName}! 🎵`, {
        duration: 4000,
        icon: "👋",
      });
    } catch (error) {
      console.error("Erro ao salvar nome:", error);
      toast.error("Erro ao salvar seu nome");
    }
  }, [clientName, restaurantId, tableNumber]);

  /**
   * Configura listeners para eventos WebSocket
   */
  const setupWebSocketListeners = useCallback(() => {
    // Playlist reordenada por votos: atualizar tocando agora
    const handlePlaylistReordered = () => {
      loadCurrentlyPlaying();
      toast.success("Fila atualizada pela votação!", { icon: "🔄" });
    };

    // Música tocando agora
    const handleNowPlaying = (data: { suggestion: SuggestionType }) => {
      setCurrentlyPlaying(data.suggestion as any);
      toast(`🎵 Tocando agora: ${data.suggestion.title}`, {
        duration: 5000,
        icon: "🎧",
      });
    };

  on("now-playing", handleNowPlaying);
  on("playlistReordered", handlePlaylistReordered);

    return () => {
      off("now-playing", handleNowPlaying);
      off("playlistReordered", handlePlaylistReordered);
    };
  }, [on, off, loadCurrentlyPlaying]);

  // Efeito para inicialização da aplicação
  useEffect(() => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    // Inicializar dados
    const initialize = async () => {
      setLoading(true);

      try {
        await Promise.all([
          initializeSession(),
          loadRestaurantInfo(),
          loadCurrentlyPlaying(),
          loadAvailableMusic(),
        ]);

        // Conectar ao WebSocket
        joinRestaurant(restaurantId);
      } catch (error) {
        console.error("Erro na inicialização:", error);
        toast.error("Erro ao carregar dados iniciais");
      } finally {
        setLoading(false);
      }
    };

    initialize();

    // Configurar WebSocket listeners
    const cleanupListeners = setupWebSocketListeners();

    // Limpar tudo ao desmontar
    return () => {
      cleanupListeners();
    };
  }, [restaurantId]); // Removidas as dependências que causam o erro

  // Efeito para carregar dados quando os filtros mudam
  useEffect(() => {
    if (restaurantId) {
      setPlaylistPage(1);
      loadAvailableMusic(1);
  }
  }, [activeFilters, restaurantId]); // Removidas dependências problemáticas

  // Tela de carregamento inicial
  if (loading && !restaurant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center text-white">
          <RefreshCw
            className="w-12 h-12 animate-spin mx-auto mb-4"
            aria-hidden="true"
          />
          <h2 className="text-xl font-semibold">Carregando...</h2>
          <p className="text-sm text-purple-300 mt-2">
            Conectando ao restaurante
          </p>
          <button
            onClick={() => {
              loadRestaurantInfo();
              loadCurrentlyPlaying();
              loadAvailableMusic();
            }}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            aria-label="Tentar novamente"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  // Componente principal da interface do cliente
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      {/* Cabeçalho */}
      <header className="bg-black/30 backdrop-blur-md border-b border-white/10 p-4 sm:p-6 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
              <Music className="w-6 h-6" aria-hidden="true" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">
                {restaurant?.name}
              </h1>
              {tableNumber && (
                <p className="text-sm text-purple-200">Mesa {tableNumber}</p>
              )}
            </div>
          </div>
          <p className="text-sm text-purple-200 mb-4">
            Escolha e vote nas músicas que vão animar seu momento!
          </p>

          {/* Status do usuário */}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4 text-sm">
            <div className="flex items-center gap-1" title="Seu nível atual">
              <Star className="w-4 h-4 text-yellow-400" aria-hidden="true" />
              <span>Nível {userStats.level}</span>
            </div>
            <div className="flex items-center gap-1" title="Seus pontos">
              <TrendingUp
                className="w-4 h-4 text-green-400"
                aria-hidden="true"
              />
              <span>{userStats.points} pts</span>
            </div>
            <div className="flex items-center gap-1" title="Músicas sugeridas">
              <Heart className="w-4 h-4 text-red-400" aria-hidden="true" />
              <span>{userStats.suggestionsCount}</span>
            </div>
            <div className="flex items-center gap-1" title="Votos realizados">
              <ThumbsUp className="w-4 h-4 text-blue-400" aria-hidden="true" />
              <span>{userStats.votesCount}</span>
            </div>

            {/* Botões de ação */}
            <button
              onClick={() => setShowProfile(true)}
              className="flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full hover:from-blue-600 hover:to-indigo-600 transition-transform hover:scale-105"
              aria-label="Ver perfil"
            >
              <User className="w-4 h-4" aria-hidden="true" />
              <span>Perfil</span>
            </button>
            <button
              onClick={() => setShowLeaderboard(true)}
              className="flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:from-purple-600 hover:to-pink-600 transition-transform hover:scale-105"
              aria-label="Ver ranking"
            >
              <Trophy className="w-4 h-4" aria-hidden="true" />
              <span>Ranking</span>
            </button>
          </div>

          {/* Badges do usuário */}
          {userStats.badges.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mt-3">
              {userStats.badges.slice(0, 3).map((badge, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-white/10 rounded-full text-xs border border-white/20"
                  title={badge}
                >
                  {badge}
                </span>
              ))}
              {userStats.badges.length > 3 && (
                <span
                  className="px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20"
                  title={`${userStats.badges.length - 3} badges adicionais`}
                >
                  +{userStats.badges.length - 3} mais
                </span>
              )}
            </div>
          )}

          {/* Indicador de status de conexão */}
          <div className="mt-2 flex items-center justify-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                isConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
              }`}
            ></div>
            <span className="text-xs text-purple-200">
              {isConnected ? "Conectado ao restaurante" : "Reconectando..."}
            </span>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-6 space-y-6">
        {/* Tocando agora */}
        {currentlyPlaying && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
            aria-labelledby="now-playing"
          >
            <h2
              id="now-playing"
              className="flex items-center gap-2 text-xl font-bold mb-4"
            >
              <Play className="w-5 h-5 text-green-400" aria-hidden="true" />
              Tocando Agora
            </h2>
            <div className="flex items-center gap-4">
              <img
                src={
                  currentlyPlaying.thumbnailUrl ||
                  `https://img.youtube.com/vi/${currentlyPlaying.youtubeVideoId}/mqdefault.jpg`
                }
                alt={`Capa de ${currentlyPlaying.title}`}
                className="w-16 h-16 rounded-lg object-cover"
                loading="lazy"
              />
              <div className="flex-1">
                <h3 className="font-semibold truncate">
                  {currentlyPlaying.title}
                </h3>
                <p className="text-purple-200 truncate">
                  {currentlyPlaying.artist}
                </p>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-green-400">
                  {currentlyPlaying.score > 0 ? "+" : ""}
                  {currentlyPlaying.score}
                </div>
                <div className="text-xs text-purple-200">votos</div>
              </div>
            </div>
          </motion.section>
        )}

        {/* Busca de músicas */}
        <section
          className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
          aria-labelledby="search"
        >
          <h2
            id="search"
            className="flex items-center gap-2 text-xl font-bold mb-4"
          >
            <Search className="w-5 h-5" aria-hidden="true" />
            Buscar Músicas
          </h2>
          <div className="flex gap-3 mb-4">
            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && searchSongs()}
                placeholder="Busque por música ou artista..."
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
                aria-label="Buscar músicas por título ou artista"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-12 top-1/2 -translate-y-1/2 text-white/60 hover:text-white"
                  aria-label="Limpar busca"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
            <button
              onClick={searchSongs}
              disabled={searchLoading || !searchQuery.trim()}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 whitespace-nowrap"
              aria-label="Buscar músicas"
            >
              {searchLoading ? (
                <RefreshCw
                  className="w-4 h-4 animate-spin"
                  aria-hidden="true"
                />
              ) : (
                <Search className="w-4 h-4" aria-hidden="true" />
              )}
              <span>Buscar</span>
            </button>
          </div>
        </section>

        {/* Playlist do restaurante */}
        <section
          className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
          aria-labelledby="playlist"
        >
          <div className="flex items-center justify-between mb-4">
            <h2
              id="playlist"
              className="flex items-center gap-2 text-xl font-bold"
            >
              <Music className="w-5 h-5" aria-hidden="true" />
              {searchQuery ? "Resultados da Busca" : "Playlist do Restaurante"}
            </h2>
            {totalPlaylistSongs > 0 && (
              <div className="text-sm text-purple-200">
                {searchResults.length} de {totalPlaylistSongs} músicas
              </div>
            )}
          </div>

          {/* Lista de músicas */}
          {playlistLoading && searchResults.length === 0 ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw
                className="w-8 h-8 text-purple-400 animate-spin"
                aria-hidden="true"
              />
              <span className="ml-3 text-purple-200">
                Carregando músicas...
              </span>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="text-center py-8">
              <Headphones
                className="w-12 h-12 text-purple-400 mx-auto mb-3"
                aria-hidden="true"
              />
              <p className="text-purple-200">Nenhuma música encontrada.</p>
              <p className="text-sm text-purple-300">
                Use a busca ou filtros para encontrar músicas.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {searchResults.map((song) => (
                  <motion.div
                    key={song.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.03 }}
                    className="bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all"
                    role="article"
                    aria-labelledby={`song-${song.id}`}
                  >
                    <img
                      src={song.thumbnailUrl}
                      alt={`Capa de ${song.title}`}
                      className="w-full h-32 object-cover rounded-lg mb-3"
                      loading="lazy"
                    />
                    <h3
                      id={`song-${song.id}`}
                      className="font-semibold text-sm truncate"
                    >
                      {song.title}
                    </h3>
                    <p className="text-purple-200 text-xs truncate mb-2">
                      {song.artist}
                    </p>
                    <div className="flex justify-between text-xs text-purple-300 mb-3">
                      <span>{song.formattedDuration}</span>
                      <span>{song.genre || "N/A"}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <button
                        onClick={() => voteOnSong(song)}
                        disabled={loading}
                        className="px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs"
                        aria-label={`Registrar voto para ${song.title}`}
                      >
                        <Music className="w-3 h-3" aria-hidden="true" />
                        <span>Voto</span>
                      </button>
                      <button
                        onClick={() => suggestSongWithPayment(song)}
                        disabled={loading}
                        className="px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs"
                        aria-label={`Adicionar ${song.title} com SuperVoto`}
                      >
                        <CreditCard className="w-3 h-3" aria-hidden="true" />
                        <span>SuperVoto</span>
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Botão para carregar mais */}
              {hasMorePlaylist && (
                <div className="text-center">
                  <button
                    onClick={() => loadAvailableMusic(playlistPage + 1, true)}
                    disabled={playlistLoading}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 mx-auto"
                    aria-label="Carregar mais músicas da playlist"
                  >
                    {playlistLoading ? (
                      <RefreshCw
                        className="w-4 h-4 animate-spin"
                        aria-hidden="true"
                      />
                    ) : (
                      <Music className="w-4 h-4" aria-hidden="true" />
                    )}
                    <span>
                      {playlistLoading
                        ? "Carregando..."
                        : "Carregar Mais Músicas"}
                    </span>
                  </button>
                </div>
              )}

              {/* Mensagem quando todas as músicas foram carregadas */}
              {!hasMorePlaylist && totalPlaylistSongs > 24 && (
                <p className="text-center text-sm text-purple-300">
                  🎵 Todas as {totalPlaylistSongs} músicas foram carregadas!
                </p>
              )}
            </>
          )}
        </section>

  {/* Fila de músicas legacy removida; PlaybackQueue é a fonte única */}

        {/* Playback Queue (componente separado) */}
        {showQueue && (
          <section
            className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
            aria-labelledby="playback-queue"
          >
            <PlaybackQueue
              restaurantId={restaurantId || ""}
              sessionId={session?.id || ""}
              isCollapsed={queueCollapsed}
              onToggleCollapse={() => setQueueCollapsed(!queueCollapsed)}
            />
          </section>
        )}

        {/* Rodapé */}
        <footer className="text-center py-6">
          <p className="text-sm text-purple-300">
            🎵 Powered by Sistema de Playlist Interativa
          </p>
          <p className="text-xs text-purple-400 mt-1">
            Sugestões são moderadas e podem levar alguns minutos para aparecer
            na fila
          </p>
        </footer>
      </main>

      {/* Modais e Componentes Flutuantes */}
      <AnimatePresence>
        {/* Level Up Notification */}
        {showLevelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50"
            role="alert"
            aria-live="polite"
          >
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🎉</div>
              <h3 className="text-xl font-bold mb-1">LEVEL UP!</h3>
              <p className="text-sm">
                Você alcançou o nível {userStats.level}!
              </p>
            </div>
          </motion.div>
        )}

        {/* Badge Earned Notification */}
        {showBadgeEarned && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50"
            role="alert"
            aria-live="polite"
          >
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🏆</div>
              <h3 className="text-xl font-bold mb-1">NOVA CONQUISTA!</h3>
              <p className="text-sm">{showBadgeEarned}</p>
            </div>
          </motion.div>
        )}

        {/* Name Input Modal */}
        {showNameInput && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            role="dialog"
            aria-modal="true"
            aria-labelledby="name-modal-title"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-900 rounded-xl p-6 max-w-md w-full shadow-2xl"
            >
              <div className="text-center mb-6">
                <User
                  className="w-12 h-12 text-purple-500 mx-auto mb-3"
                  aria-hidden="true"
                />
                <h3
                  id="name-modal-title"
                  className="text-xl font-bold text-gray-800 dark:text-white mb-2"
                >
                  Bem-vindo! 🎵
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Como podemos te chamar? (Opcional)
                </p>
              </div>
              <div className="space-y-4">
                <input
                  type="text"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Digite seu nome..."
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  onKeyPress={(e) => e.key === "Enter" && saveClientName()}
                  autoFocus
                  aria-label="Digite seu nome"
                />
                <div className="flex gap-3">
                  <button
                    onClick={saveClientName}
                    disabled={!clientName.trim()}
                    className="flex-1 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50"
                    aria-label="Confirmar nome"
                  >
                    Continuar
                  </button>
                  <button
                    onClick={() => setShowNameInput(false)}
                    className="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                    aria-label="Pular entrada de nome"
                  >
                    Pular
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Payment Modal */}
      {showPaymentModal && selectedSongForPayment && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedSongForPayment(null);
          }}
          suggestion={{
            id: selectedSongForPayment.id,
            title: selectedSongForPayment.title,
            artist: selectedSongForPayment.artist,
            thumbnailUrl: selectedSongForPayment.thumbnailUrl,
            duration: selectedSongForPayment.duration,
            youtubeVideoId: selectedSongForPayment.youtubeVideoId,
          }}
          sessionId={session?.sessionToken || ""}
          restaurantId={restaurantId || ""}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}

      {/* Karaoke Player */}
      {karaokeData && (
        <KaraokePlayer
          isOpen={showKaraoke}
          onClose={() => {
            setShowKaraoke(false);
            setKaraokeData(null);
          }}
          suggestion={karaokeData}
          sessionId={session?.id || ""}
          onVoteRequest={() =>
            toast.success("Votação solicitada! 🗳️", { duration: 4000 })
          }
        />
      )}

  {/* NewSuggestionAlert removido: fluxo de fila/votos unificado não usa mais este componente */}

      {/* Client Profile Modal */}
      {showProfile && session && (
        <ClientProfile
          isOpen={showProfile}
          onClose={() => setShowProfile(false)}
          restaurantId={restaurantId || ""}
          sessionId={session.id}
        />
      )}

      {/* Table Leaderboard */}
      <TableLeaderboard
  restaurantId={restaurantId || ""}
  currentTableNumber={tableNumber || undefined}
        isVisible={showLeaderboard}
        onClose={() => setShowLeaderboard(false)}
      />
    </div>
  );
};

export default ClientInterface;
