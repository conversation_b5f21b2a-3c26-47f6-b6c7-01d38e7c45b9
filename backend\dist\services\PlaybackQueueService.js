"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.playbackQueueService = void 0;
const database_1 = require("../config/database");
const Suggestion_1 = require("../models/Suggestion");
const Restaurant_1 = require("../models/Restaurant");
const ClientSession_1 = require("../models/ClientSession");
const redis_1 = require("../config/redis");
const NotificationService_1 = require("./NotificationService");
const YouTubeService_1 = require("./YouTubeService");
class PlaybackQueueService {
    constructor() {
        this.suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        this.restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        this.sessionRepository = database_1.AppDataSource.getRepository(ClientSession_1.ClientSession);
        this.youtubeService = new YouTubeService_1.YouTubeService();
    }
    // Obter fila de reprodução completa
    async getPlaybackQueue(restaurantId) {
        try {
            // Buscar sugestões aprovadas ordenadas por prioridade
            const suggestions = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.restaurant", "restaurant")
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.status = :status", { status: "approved" })
                .andWhere("suggestion.playedAt IS NULL") // Não tocadas ainda
                .orderBy("suggestion.isPaid", "DESC") // Pagas primeiro
                .addOrderBy("suggestion.createdAt", "ASC") // Depois por ordem de chegada
                .getMany();
            // Converter para QueueItem
            const queue = suggestions.map((suggestion, index) => ({
                id: `queue_${suggestion.id}`,
                suggestionId: suggestion.id,
                title: suggestion.title,
                artist: suggestion.artist,
                duration: suggestion.duration || 180,
                thumbnailUrl: suggestion.thumbnailUrl,
                youtubeVideoId: suggestion.youtubeVideoId,
                isPaid: suggestion.isPaid,
                paymentAmount: suggestion.paymentAmount
                    ? suggestion.paymentAmount / 100
                    : undefined,
                clientName: suggestion.clientSession?.clientName || suggestion.clientName,
                tableName: `Mesa ${suggestion.clientSession?.tableNumber || suggestion.tableNumber || "?"}`,
                sessionId: suggestion.clientSessionId,
                addedAt: suggestion.createdAt,
                position: index + 1,
                priority: suggestion.isPaid ? 1 : 2,
            }));
            // Anexar métricas globais de likes/dislikes (armazenadas no Redis)
            try {
                if (queue.length > 0 && redis_1.redisClient.isReady) {
                    const likeKeys = queue.map((item) => `analytics:likes:${restaurantId}:${item.youtubeVideoId}`);
                    const dislikeKeys = queue.map((item) => `analytics:dislikes:${restaurantId}:${item.youtubeVideoId}`);
                    const client = redis_1.redisClient.getClient();
                    const [likes, dislikes] = await Promise.all([
                        client.mGet(likeKeys),
                        client.mGet(dislikeKeys),
                    ]);
                    queue.forEach((item, idx) => {
                        const likeCount = parseInt(likes?.[idx] || "0", 10) || 0;
                        const dislikeCount = parseInt(dislikes?.[idx] || "0", 10) || 0;
                        item.likeCount = likeCount;
                        item.dislikeCount = dislikeCount;
                    });
                }
            }
            catch (e) {
                console.warn("Falha ao anexar métricas de likes/dislikes:", e);
            }
            // Calcular tempos estimados
            let cumulativeDuration = 0;
            queue.forEach((item, index) => {
                if (index === 0) {
                    item.estimatedPlayTime = new Date();
                }
                else {
                    item.estimatedPlayTime = new Date(Date.now() + cumulativeDuration * 1000);
                }
                cumulativeDuration += item.duration;
            });
            // Calcular estatísticas
            const stats = {
                totalItems: queue.length,
                paidItems: queue.filter((item) => item.isPaid).length,
                freeItems: queue.filter((item) => !item.isPaid).length,
                totalDuration: queue.reduce((sum, item) => sum + item.duration, 0),
                estimatedWaitTime: queue.length > 0
                    ? queue[queue.length - 1].estimatedPlayTime.getTime() - Date.now()
                    : 0,
                currentlyPlaying: queue[0],
                nextUp: queue[1],
            };
            return { queue, stats };
        }
        catch (error) {
            console.error("Erro ao obter fila de reprodução:", error);
            return {
                queue: [],
                stats: {
                    totalItems: 0,
                    paidItems: 0,
                    freeItems: 0,
                    totalDuration: 0,
                    estimatedWaitTime: 0,
                },
            };
        }
    }
    // Adicionar música à fila (chamado após pagamento)
    async addToQueue(suggestionId) {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant", "session"],
            });
            if (!suggestion) {
                return null;
            }
            // Marcar como aprovada e definir posição na fila
            suggestion.status = Suggestion_1.SuggestionStatus.APPROVED;
            suggestion.queuePosition = await this.getNextQueuePosition(suggestion.restaurant.id, suggestion.isPaid);
            await this.suggestionRepository.save(suggestion);
            // Criar item da fila
            const queueItem = {
                id: `queue_${suggestion.id}`,
                suggestionId: suggestion.id,
                title: suggestion.title,
                artist: suggestion.artist,
                duration: suggestion.duration || 180,
                thumbnailUrl: suggestion.thumbnailUrl,
                youtubeVideoId: suggestion.youtubeVideoId,
                isPaid: suggestion.isPaid,
                paymentAmount: suggestion.paymentAmount
                    ? suggestion.paymentAmount / 100
                    : undefined,
                clientName: suggestion.clientSession?.clientName || suggestion.clientName,
                tableName: `Mesa ${suggestion.clientSession?.tableNumber || suggestion.tableNumber || "?"}`,
                sessionId: suggestion.clientSessionId,
                addedAt: suggestion.createdAt,
                position: suggestion.queuePosition || 0,
                priority: suggestion.isPaid ? 1 : 2,
            };
            // Notificar adição à fila
            await NotificationService_1.notificationService.sendToRestaurant(suggestion.restaurant.id, {
                type: NotificationService_1.NotificationType.MUSIC,
                title: "🎵 Nova música na fila",
                message: `"${suggestion.title}" ${suggestion.isPaid ? "(PAGA)" : ""} adicionada à fila`,
                priority: suggestion.isPaid
                    ? NotificationService_1.NotificationPriority.HIGH
                    : NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    queueItem,
                    isPaid: suggestion.isPaid,
                },
            });
            // Notificar cliente
            await NotificationService_1.notificationService.sendToSession(suggestion.clientSessionId, {
                type: NotificationService_1.NotificationType.SUCCESS,
                title: "🎵 Música na fila!",
                message: `"${suggestion.title}" está na posição ${queueItem.position} da fila`,
                priority: NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    queueItem,
                    estimatedWait: this.formatDuration(queueItem.estimatedPlayTime
                        ? (queueItem.estimatedPlayTime.getTime() - Date.now()) / 1000
                        : 0),
                },
            });
            return queueItem;
        }
        catch (error) {
            console.error("Erro ao adicionar à fila:", error);
            return null;
        }
    }
    // Marcar música como tocando
    async markAsPlaying(suggestionId) {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant", "session"],
            });
            if (!suggestion)
                return;
            suggestion.status = Suggestion_1.SuggestionStatus.PLAYING;
            suggestion.playedAt = new Date();
            await this.suggestionRepository.save(suggestion);
            // Iniciar sessão de votação se for música paga
            if (suggestion.isPaid) {
                // Aqui você chamaria o serviço de votação competitiva
                // await competitiveVotingService.startVotingSession(suggestionId);
            }
            // Notificar que música começou
            await NotificationService_1.notificationService.sendToRestaurant(suggestion.restaurant.id, {
                type: NotificationService_1.NotificationType.MUSIC,
                title: "🎵 Tocando agora",
                message: `"${suggestion.title}" por ${suggestion.artist}`,
                priority: NotificationService_1.NotificationPriority.HIGH,
                category: "now_playing",
                data: {
                    suggestionId,
                    title: suggestion.title,
                    artist: suggestion.artist,
                    isPaid: suggestion.isPaid,
                    duration: suggestion.duration,
                },
            });
            // Notificar cliente que sua música começou
            await NotificationService_1.notificationService.sendToSession(suggestion.clientSessionId, {
                type: NotificationService_1.NotificationType.SUCCESS,
                title: "🎵 Sua música está tocando!",
                message: `"${suggestion.title}" começou a tocar agora`,
                priority: NotificationService_1.NotificationPriority.HIGH,
                category: "now_playing",
                data: {
                    suggestionId,
                    canStartKaraoke: suggestion.isPaid,
                },
            });
        }
        catch (error) {
            console.error("Erro ao marcar como tocando:", error);
        }
    }
    // Marcar música como concluída
    async markAsCompleted(suggestionId) {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant"],
            });
            if (!suggestion)
                return;
            suggestion.status = Suggestion_1.SuggestionStatus.COMPLETED;
            suggestion.completedAt = new Date();
            await this.suggestionRepository.save(suggestion);
            // Atualizar posições da fila
            await this.updateQueuePositions(suggestion.restaurant.id);
            // Notificar conclusão
            await NotificationService_1.notificationService.sendToRestaurant(suggestion.restaurant.id, {
                type: NotificationService_1.NotificationType.INFO,
                title: "🎵 Música concluída",
                message: `"${suggestion.title}" terminou de tocar`,
                priority: NotificationService_1.NotificationPriority.LOW,
                category: "completed",
                data: {
                    suggestionId,
                    title: suggestion.title,
                },
            });
        }
        catch (error) {
            console.error("Erro ao marcar como concluída:", error);
        }
    }
    // Remover música da fila
    async removeFromQueue(suggestionId, reason = "removed") {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant", "session"],
            });
            if (!suggestion)
                return false;
            suggestion.status = Suggestion_1.SuggestionStatus.REJECTED;
            suggestion.rejectionReason = reason;
            await this.suggestionRepository.save(suggestion);
            // Atualizar posições da fila
            await this.updateQueuePositions(suggestion.restaurant.id);
            // Notificar remoção
            await NotificationService_1.notificationService.sendToSession(suggestion.clientSessionId, {
                type: NotificationService_1.NotificationType.WARNING,
                title: "🚫 Música removida",
                message: `"${suggestion.title}" foi removida da fila`,
                priority: NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    suggestionId,
                    reason,
                },
            });
            return true;
        }
        catch (error) {
            console.error("Erro ao remover da fila:", error);
            return false;
        }
    }
    // Obter próxima posição na fila
    async getNextQueuePosition(restaurantId, isPaid) {
        const query = this.suggestionRepository
            .createQueryBuilder("suggestion")
            .leftJoinAndSelect("suggestion.restaurant", "restaurant")
            .where("restaurant.id = :restaurantId", { restaurantId })
            .andWhere("suggestion.status = :status", { status: "approved" })
            .andWhere("suggestion.playedAt IS NULL");
        if (isPaid) {
            // Músicas pagas: inserir no final das pagas, antes das gratuitas
            query.andWhere("suggestion.isPaid = true");
        }
        const maxPosition = await query
            .select("MAX(suggestion.queuePosition)", "maxPosition")
            .getRawOne();
        return (maxPosition?.maxPosition || 0) + 1;
    }
    // Atualizar posições da fila após mudanças
    async updateQueuePositions(restaurantId) {
        try {
            const suggestions = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.restaurant", "restaurant")
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.status = :status", { status: "approved" })
                .andWhere("suggestion.playedAt IS NULL")
                .orderBy("suggestion.isPaid", "DESC")
                .addOrderBy("suggestion.createdAt", "ASC")
                .getMany();
            // Atualizar posições
            for (let i = 0; i < suggestions.length; i++) {
                suggestions[i].queuePosition = i + 1;
                await this.suggestionRepository.save(suggestions[i]);
            }
        }
        catch (error) {
            console.error("Erro ao atualizar posições da fila:", error);
        }
    }
    // Obter estimativa de tempo para uma posição
    async getEstimatedWaitTime(restaurantId, position) {
        try {
            const { queue } = await this.getPlaybackQueue(restaurantId);
            if (position <= 0 || position > queue.length) {
                return 0;
            }
            // Somar duração de todas as músicas antes desta posição
            let totalDuration = 0;
            for (let i = 0; i < position - 1; i++) {
                totalDuration += queue[i].duration;
            }
            return totalDuration;
        }
        catch (error) {
            console.error("Erro ao calcular tempo de espera:", error);
            return 0;
        }
    }
    // Obter estatísticas da fila
    async getQueueStats(restaurantId) {
        try {
            const { stats } = await this.getPlaybackQueue(restaurantId);
            return stats;
        }
        catch (error) {
            console.error("Erro ao obter estatísticas da fila:", error);
            return {
                totalItems: 0,
                paidItems: 0,
                freeItems: 0,
                totalDuration: 0,
                estimatedWaitTime: 0,
            };
        }
    }
    // Reordenar fila (para admin)
    async reorderQueue(restaurantId, newOrder) {
        try {
            for (let i = 0; i < newOrder.length; i++) {
                const suggestionId = newOrder[i];
                await this.suggestionRepository.update({ id: suggestionId }, { queuePosition: i + 1 });
            }
            // Notificar mudança na fila
            await NotificationService_1.notificationService.sendToRestaurant(restaurantId, {
                type: NotificationService_1.NotificationType.INFO,
                title: "🔄 Fila reordenada",
                message: "A ordem da fila foi alterada pelo administrador",
                priority: NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    newOrder,
                },
            });
            return true;
        }
        catch (error) {
            console.error("Erro ao reordenar fila:", error);
            return false;
        }
    }
    // Utilitário para formatar duração
    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        if (minutes > 60) {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return `${hours}h ${remainingMinutes}m`;
        }
        return `${minutes}m ${remainingSeconds}s`;
    }
    // Limpar fila (remover músicas antigas)
    async clearOldQueue(restaurantId, olderThanHours = 24) {
        try {
            const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
            const result = await this.suggestionRepository
                .createQueryBuilder()
                .update(Suggestion_1.Suggestion)
                .set({ status: Suggestion_1.SuggestionStatus.EXPIRED })
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("status = :status", { status: "approved" })
                .andWhere("createdAt < :cutoffDate", { cutoffDate })
                .andWhere("playedAt IS NULL")
                .execute();
            return result.affected || 0;
        }
        catch (error) {
            console.error("Erro ao limpar fila antiga:", error);
            return 0;
        }
    }
    // Método combinado: criar sugestão e adicionar à fila
    async createSuggestionAndAddToQueue(suggestionData) {
        try {
            // Verificar se restaurante existe
            const restaurant = await this.restaurantRepository.findOne({
                where: { id: suggestionData.restaurantId, isActive: true },
            });
            if (!restaurant) {
                throw new Error("Restaurante não encontrado ou inativo");
            }
            // Verificar se sessão existe
            let session = await this.sessionRepository.findOne({
                where: {
                    sessionToken: suggestionData.clientSessionId,
                    restaurant: { id: suggestionData.restaurantId },
                },
            });
            if (!session) {
                // Criar sessão se não existir
                session = this.sessionRepository.create({
                    sessionToken: suggestionData.clientSessionId,
                    restaurant,
                    ipAddress: "unknown",
                    userAgent: "unknown",
                    lastActivity: new Date(),
                    isActive: true,
                });
                await this.sessionRepository.save(session);
            }
            // Buscar informações do vídeo no YouTube
            let videoInfo;
            try {
                videoInfo = await this.youtubeService.getVideoInfo(suggestionData.youtubeVideoId);
            }
            catch (error) {
                console.warn("Erro ao buscar info do YouTube, usando dados fornecidos:", error);
                videoInfo = {
                    title: suggestionData.title,
                    artist: suggestionData.artist,
                    duration: 180,
                    thumbnailUrl: `https://img.youtube.com/vi/${suggestionData.youtubeVideoId}/mqdefault.jpg`,
                };
            }
            // Criar sugestão
            const suggestion = new Suggestion_1.Suggestion();
            suggestion.youtubeVideoId = suggestionData.youtubeVideoId;
            suggestion.title = videoInfo.title || suggestionData.title;
            suggestion.artist = videoInfo.artist || suggestionData.artist;
            suggestion.duration = videoInfo.duration || 180;
            suggestion.thumbnailUrl = videoInfo.thumbnailUrl;
            suggestion.clientSessionId = suggestionData.clientSessionId;
            suggestion.status =
                suggestionData.status || Suggestion_1.SuggestionStatus.APPROVED;
            suggestion.isPaid = suggestionData.isPaid || false;
            suggestion.source = "client";
            suggestion.createdAt = new Date();
            suggestion.updatedAt = new Date();
            // Definir as relações
            suggestion.restaurant = restaurant;
            suggestion.sessionId = session.id;
            await this.suggestionRepository.save(suggestion);
            // Adicionar à fila
            return await this.addToQueue(suggestion.id);
        }
        catch (error) {
            console.error("Erro ao criar sugestão e adicionar à fila:", error);
            return null;
        }
    }
    // Promover música para fila prioritária
    async promoteTrack(restaurantId, trackId) {
        try {
            // Buscar a sugestão
            const suggestion = await this.suggestionRepository.findOne({
                where: {
                    id: trackId,
                    restaurant: { id: restaurantId },
                },
                relations: ["restaurant"],
            });
            if (!suggestion) {
                throw new Error("Música não encontrada");
            }
            // Marcar como paga (prioridade)
            suggestion.isPaid = true;
            suggestion.paymentAmount = 2.0; // Valor padrão para promoção
            await this.suggestionRepository.save(suggestion);
            // Atualizar cache da fila
            const queueKey = `queue:${restaurantId}`;
            const cachedQueue = await redis_1.redisClient.getClient().get(queueKey);
            if (cachedQueue) {
                const queue = JSON.parse(cachedQueue);
                const trackIndex = queue.findIndex((item) => item.suggestionId === trackId);
                if (trackIndex !== -1) {
                    // Atualizar item na fila
                    queue[trackIndex].isPaid = true;
                    queue[trackIndex].paymentAmount = 2.0;
                    queue[trackIndex].priority = 1; // Alta prioridade
                    // Reordenar fila (prioridade primeiro)
                    queue.sort((a, b) => a.priority - b.priority);
                    // Atualizar posições
                    queue.forEach((item, index) => {
                        item.position = index + 1;
                    });
                    // Salvar fila atualizada
                    await redis_1.redisClient
                        .getClient()
                        .setEx(queueKey, 3600, JSON.stringify(queue));
                }
            }
            console.log(`✅ Música ${suggestion.title} promovida para fila prioritária`);
        }
        catch (error) {
            console.error("Erro ao promover música:", error);
            throw error;
        }
    }
}
exports.playbackQueueService = new PlaybackQueueService();
exports.default = PlaybackQueueService;
//# sourceMappingURL=PlaybackQueueService.js.map