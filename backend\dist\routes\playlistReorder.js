"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../utils/validation");
const PlaylistReorderService_1 = require("../services/PlaylistReorderService");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const errors_1 = require("../utils/errors");
const router = (0, express_1.Router)();
/**
 * @swagger
 * /api/v1/playlist-reorder/status:
 *   get:
 *     summary: Obter status do serviço de reordenação automática
 *     tags: [Playlist Reorder]
 *     responses:
 *       200:
 *         description: Status do serviço
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 status:
 *                   type: object
 *                   properties:
 *                     isRunning:
 *                       type: boolean
 *                     nextExecution:
 *                       type: string
 *                       format: date-time
 *                     uptime:
 *                       type: number
 */
router.get("/status", auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const status = PlaylistReorderService_1.playlistReorderService.getStatus();
    res.json({
        success: true,
        status: {
            isRunning: status.isRunning,
            nextExecution: status.nextExecution?.toISOString(),
            uptime: Math.round(status.uptime),
            message: status.isRunning
                ? "Serviço de reordenação automática ativo"
                : "Serviço de reordenação automática parado",
        },
    });
}));
/**
 * @swagger
 * /api/v1/playlist-reorder/manual/{restaurantId}:
 *   post:
 *     summary: Executar reordenação manual para um restaurante
 *     tags: [Playlist Reorder]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do restaurante
 *     responses:
 *       200:
 *         description: Reordenação executada
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 result:
 *                   type: object
 *                   properties:
 *                     restaurantId:
 *                       type: string
 *                     playlistId:
 *                       type: string
 *                     tracksReordered:
 *                       type: number
 *                     message:
 *                       type: string
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 */
router.post("/manual/:restaurantId", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const result = await PlaylistReorderService_1.playlistReorderService.manualReorder(restaurantId);
    res.json({
        success: result.success,
        result: {
            restaurantId: result.restaurantId,
            playlistId: result.playlistId,
            tracksReordered: result.tracksReordered,
            message: result.message,
            timestamp: result.timestamp.toISOString(),
        },
    });
}));
/**
 * @swagger
 * /api/v1/playlist-reorder/start:
 *   post:
 *     summary: Iniciar serviço de reordenação automática
 *     tags: [Playlist Reorder]
 *     responses:
 *       200:
 *         description: Serviço iniciado
 */
router.post("/start", auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    PlaylistReorderService_1.playlistReorderService.startAutoReorder();
    res.json({
        success: true,
        message: "Serviço de reordenação automática iniciado",
    });
}));
/**
 * @swagger
 * /api/v1/playlist-reorder/stop:
 *   post:
 *     summary: Parar serviço de reordenação automática
 *     tags: [Playlist Reorder]
 *     responses:
 *       200:
 *         description: Serviço parado
 */
router.post("/stop", auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    PlaylistReorderService_1.playlistReorderService.stopAutoReorder();
    res.json({
        success: true,
        message: "Serviço de reordenação automática parado",
    });
}));
exports.default = router;
//# sourceMappingURL=playlistReorder.js.map