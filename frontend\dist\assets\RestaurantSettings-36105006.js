import{V as o,b as v,j as e,f as E,u as R,c as T}from"./index-6e2e4ef2.js";import{r as n}from"./vendor-66b0ef43.js";import{R as C,al as P,m as A,_ as V,i as S,P as Y,T as M,u as z,d as $,aL as D,o as F,K as U,w as q,a4 as I}from"./ui-a5f8f5f0.js";import"./router-f729e475.js";import"./utils-08f61814.js";const B=({restaurantId:i,onAuthStatusChange:c})=>{console.log("🎵 YouTubeAuthManager renderizado com restaurantId:",i);const[a,h]=n.useState(null),[d,x]=n.useState(!1),[m,y]=n.useState(!1),[f,p]=n.useState(!1),[l,j]=n.useState(""),[b,k]=n.useState(""),g=n.useCallback(async()=>{if(!i){o.error("Restaurant ID is required");return}console.log("🎵 Verificando status do YouTube para restaurantId:",i),x(!0);try{const t=await fetch(v(`/youtube-auth/${i}/status`)),s=await t.json();console.log("🎵 Resposta da API YouTube Status:",{status:t.status,data:s}),t.ok&&s.success?(h(s),c==null||c(s.isAuthenticated)):(h({isAuthenticated:!1,capabilities:[],message:s.message||"Não autenticado"}),c==null||c(!1))}catch(t){console.error("Erro ao verificar status:",t),h({isAuthenticated:!1,capabilities:[],message:"Erro ao verificar status"}),c==null||c(!1),o.error("Erro ao verificar status da autenticação")}finally{x(!1)}},[i,c]),w=n.useCallback(async()=>{if(!i){o.error("Restaurant ID is required");return}x(!0);try{const s=await(await fetch(v(`/youtube-auth/${i}/authorize`))).json();if(s.success&&s.authUrl){window.open(s.authUrl,"youtube-auth","width=600,height=700"),o.success("Janela de autorização aberta! Siga as instruções.");const u=setInterval(async()=>{await g(),a!=null&&a.isAuthenticated&&(clearInterval(u),o.success("Autenticação concluída com sucesso!"))},3e3);setTimeout(()=>clearInterval(u),3e5)}else o.error(s.message||"Erro ao iniciar autorização")}catch(t){console.error("Erro ao iniciar autorização:",t),o.error("Erro ao iniciar processo de autorização")}finally{x(!1)}},[i,a,g]),N=n.useCallback(async()=>{if(!l.trim()){o.error("Título da playlist é obrigatório");return}x(!0);try{const s=await(await fetch(v(`/youtube-auth/${i}/create-playlist`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:l,description:b})})).json();s.success?(o.success("Playlist criada com sucesso!"),p(!1),j(""),k(""),s.playlistUrl&&o.success(e.jsxs("div",{children:[e.jsx("p",{children:"Playlist criada!"}),e.jsx("a",{href:s.playlistUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 underline",children:"Ver no YouTube"})]}),{duration:5e3})):o.error(s.message||"Erro ao criar playlist")}catch(t){console.error("Erro ao criar playlist:",t),o.error("Erro ao criar playlist")}finally{x(!1)}},[i,l,b]),r=n.useCallback(async t=>{if(!t){o.error("Playlist ID is required");return}y(!0);try{const u=await(await fetch(v(`/youtube-auth/${i}/playlists/${t}/reorder`),{method:"POST"})).json();u.success?u.tracksReordered>0?o.success(`${u.tracksReordered} músicas reordenadas baseado nos votos!`):o.success("Playlist já está na ordem ideal!"):o.error(u.message||"Erro ao reordenar playlist")}catch(s){console.error("Erro ao reordenar playlist:",s),o.error("Erro ao reordenar playlist")}finally{y(!1)}},[i]);return n.useEffect(()=>(console.log("🎵 useEffect YouTubeAuthManager executado para restaurantId:",i),g(),()=>{}),[g]),d&&!a?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(C,{className:"w-6 h-6 animate-spin text-purple-500"}),e.jsx("span",{className:"ml-2",children:"Verificando autenticação..."})]}):e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"w-8 h-8 text-red-500 mr-3"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Controle de Playlist YouTube"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Gerencie playlists com controle total baseado em votações"})]})]}),e.jsx("button",{onClick:g,disabled:d,className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200","aria-label":"Refresh authentication status",children:e.jsx(C,{className:`w-5 h-5 ${d?"animate-spin":""}`})})]}),a?e.jsxs(A.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"space-y-6",children:[e.jsx("div",{className:`p-4 rounded-lg border-2 ${a.isAuthenticated?"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20":"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20"}`,children:e.jsxs("div",{className:"flex items-center",children:[a.isAuthenticated?e.jsx(V,{className:"w-6 h-6 text-green-500 mr-3"}):e.jsx(S,{className:"w-6 h-6 text-yellow-500 mr-3"}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-semibold ${a.isAuthenticated?"text-green-800 dark:text-green-200":"text-yellow-800 dark:text-yellow-200"}`,children:a.isAuthenticated?"Autenticado com YouTube":"Não Autenticado"}),e.jsx("p",{className:`text-sm ${a.isAuthenticated?"text-green-600 dark:text-green-300":"text-yellow-600 dark:text-yellow-300"}`,children:a.message})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:a.capabilities.length>0?a.capabilities.map((t,s)=>e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-3"}),e.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:t})]},s)):e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhuma capacidade disponível"})}),e.jsx("div",{className:"flex flex-wrap gap-4",children:a.isAuthenticated?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>p(!0),className:"flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors",children:[e.jsx(Y,{className:"w-4 h-4 mr-2"}),"Criar Playlist"]}),e.jsxs("button",{onClick:()=>r("current-playlist-id"),disabled:m,className:"flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-colors",children:[e.jsx(M,{className:`w-4 h-4 mr-2 ${m?"animate-spin":""}`}),m?"Reordenando...":"Reordenar por Votos"]})]}):e.jsxs("button",{onClick:w,disabled:d,className:"flex items-center px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[e.jsx(P,{className:"w-5 h-5 mr-2"}),d?"Processando...":"Conectar com YouTube"]})})]}):e.jsxs("div",{className:"p-4 rounded-lg border-2 border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(S,{className:"w-6 h-6 text-gray-500 mr-3"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200",children:"Carregando Status"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Verificando conexão com YouTube..."})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("button",{onClick:g,disabled:d,className:"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors",children:[e.jsx(C,{className:`w-4 h-4 mr-2 ${d?"animate-spin":""}`}),d?"Verificando...":"Tentar Novamente"]})})]}),f&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(A.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-4",children:"Criar Nova Playlist"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"playlist-title",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Título da Playlist *"}),e.jsx("input",{id:"playlist-title",type:"text",value:l,onChange:t=>j(t.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Ex: Playlist Interativa - Restaurante"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"playlist-description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),e.jsx("textarea",{id:"playlist-description",value:b,onChange:t=>k(t.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Playlist controlada pelos clientes através de votações..."})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>p(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",children:"Cancelar"}),e.jsx("button",{onClick:N,disabled:d||!l.trim(),className:"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Criando...":"Criar Playlist"})]})]})})]})},H=()=>{const{settings:i,updateSettings:c}=E(),[a,h]=n.useState({general:{name:"Restaurante Demo",description:"Um restaurante incrível com playlist interativa",timezone:"America/Sao_Paulo",language:"pt-BR"},interface:{theme:"auto",primaryColor:"#3B82F6",allowSuggestions:!0,allowVoting:!0,showQueue:!0,showVoteCounts:!0,maxSuggestionsPerUser:5},moderation:{autoApprove:!1,requireModeration:!0,bannedWords:["palavra1","palavra2"],maxVotesForAutoApproval:10,minVotesForAutoRejection:-5},schedule:{enabled:!0,openTime:"11:00",closeTime:"23:00",timezone:"America/Sao_Paulo",closedMessage:"Estamos fechados. Volte durante nosso horário de funcionamento!"},notifications:{emailNotifications:!0,newSuggestionAlert:!0,highVoteAlert:!0,moderationAlert:!0},audio:{volume:75,fadeInDuration:3,fadeOutDuration:3,crossfade:!0}}),[d,x]=n.useState(!1),[m,y]=n.useState("general"),{restaurantId:f}=R(),p=async()=>{x(!0);try{await new Promise(r=>setTimeout(r,1500)),T.success("Configurações salvas com sucesso!")}catch{T.error("Erro ao salvar configurações")}finally{x(!1)}},l=(r,t,s)=>{h(u=>({...u,[r]:{...u[r],[t]:s}}))},j=[{id:"general",name:"Geral",icon:$},{id:"interface",name:"Interface",icon:D},{id:"moderation",name:"Moderação",icon:F},{id:"schedule",name:"Horários",icon:U},{id:"notifications",name:"Notificações",icon:q},{id:"audio",name:"Áudio",icon:I},{id:"youtube",name:"YouTube",icon:P}],b=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome do Restaurante"}),e.jsx("input",{type:"text",value:a.general.name,onChange:r=>l("general","name",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),e.jsx("textarea",{value:a.general.description,onChange:r=>l("general","description",r.target.value),rows:3,className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Fuso Horário"}),e.jsxs("select",{value:a.general.timezone,onChange:r=>l("general","timezone",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"America/Sao_Paulo",children:"São Paulo (GMT-3)"}),e.jsx("option",{value:"America/New_York",children:"Nova York (GMT-5)"}),e.jsx("option",{value:"Europe/London",children:"Londres (GMT+0)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Idioma"}),e.jsxs("select",{value:a.general.language,onChange:r=>l("general","language",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"pt-BR",children:"Português (Brasil)"}),e.jsx("option",{value:"en-US",children:"English (US)"}),e.jsx("option",{value:"es-ES",children:"Español"})]})]})]})]}),k=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tema"}),e.jsxs("select",{value:a.interface.theme,onChange:r=>l("interface","theme",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"light",children:"Claro"}),e.jsx("option",{value:"dark",children:"Escuro"}),e.jsx("option",{value:"auto",children:"Automático"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Cor Principal"}),e.jsx("input",{type:"color",value:a.interface.primaryColor,onChange:r=>l("interface","primaryColor",r.target.value),className:"w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Posição das Notificações"}),e.jsxs("select",{value:i.notificationPosition||"top-left",onChange:r=>c({notificationPosition:r.target.value}),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"top-right",children:"Topo Direita"}),e.jsx("option",{value:"top-left",children:"Topo Esquerda"}),e.jsx("option",{value:"bottom-right",children:"Base Direita"}),e.jsx("option",{value:"bottom-left",children:"Base Esquerda"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Ajusta onde os avisos aparecem na tela do restaurante"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Permitir Sugestões"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Clientes podem sugerir músicas"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.interface.allowSuggestions,onChange:r=>l("interface","allowSuggestions",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Permitir Votação"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Clientes podem votar em sugestões"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.interface.allowVoting,onChange:r=>l("interface","allowVoting",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Mostrar Fila"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Exibir fila de reprodução para clientes"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.interface.showQueue,onChange:r=>l("interface","showQueue",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Sugestões por Cliente"}),e.jsx("input",{type:"number",min:"1",max:"20",value:a.interface.maxSuggestionsPerUser,onChange:r=>l("interface","maxSuggestionsPerUser",parseInt(r.target.value)),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),g=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Aprovação Automática"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Aprovar sugestões automaticamente com base em votos"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.moderation.autoApprove,onChange:r=>l("moderation","autoApprove",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Requer Moderação"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Todas as sugestões precisam ser moderadas"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.moderation.requireModeration,onChange:r=>l("moderation","requireModeration",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Votos para Aprovação Automática"}),e.jsx("input",{type:"number",value:a.moderation.maxVotesForAutoApproval,onChange:r=>l("moderation","maxVotesForAutoApproval",parseInt(r.target.value)),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Votos para Rejeição Automática"}),e.jsx("input",{type:"number",value:a.moderation.minVotesForAutoRejection,onChange:r=>l("moderation","minVotesForAutoRejection",parseInt(r.target.value)),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),w=()=>{switch(m){case"general":return b();case"interface":return k();case"moderation":return g();case"youtube":return N();default:return e.jsx("div",{className:"text-center py-8 text-gray-500",children:"Em desenvolvimento..."})}},N=()=>(console.log("🎵 Renderizando configurações do YouTube para restaurantId:",f),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Integração com YouTube"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-6",children:"Configure a autenticação com YouTube para controlar playlists em tempo real baseado nas votações dos clientes."})]}),e.jsx(B,{restaurantId:f,onAuthStatusChange:r=>{console.log("YouTube Auth Status:",r)}}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2",children:"💡 Como funciona"}),e.jsxs("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• Conecte sua conta YouTube Premium"}),e.jsx("li",{children:"• Crie playlists controláveis pelo sistema"}),e.jsx("li",{children:"• As votações dos clientes reordenam automaticamente as músicas"}),e.jsx("li",{children:"• Controle total sobre a ordem de reprodução"})]})]})]}));return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Configurações do Restaurante"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Personalize a experiência do seu restaurante"})]}),e.jsxs("button",{onClick:p,disabled:d,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2",children:[e.jsx(z,{className:"w-4 h-4"}),e.jsx("span",{children:d?"Salvando...":"Salvar"})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsx("nav",{className:"flex space-x-8 px-6",children:j.map(r=>e.jsxs("button",{onClick:()=>y(r.id),className:`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${m===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"}`,children:[e.jsx(r.icon,{className:"w-4 h-4"}),e.jsx("span",{children:r.name})]},r.id))})}),e.jsx("div",{className:"p-6",children:w()})]})]})};export{H as default};
//# sourceMappingURL=RestaurantSettings-36105006.js.map
