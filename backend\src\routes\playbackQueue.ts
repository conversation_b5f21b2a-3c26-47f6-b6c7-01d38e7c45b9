import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import { playbackQueueService } from "../services/PlaybackQueueService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";
import { redisClient } from "../config/redis";

const router = Router();

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}:
 *   get:
 *     summary: Obter fila de reprodução
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Fila de reprodução
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const queueData = await playbackQueueService.getPlaybackQueue(restaurantId);

    res.json({
      success: true,
      queue: queueData.queue,
      stats: queueData.stats,
      currentlyPlaying: queueData.currentlyPlaying,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/like:
 *   post:
 *     summary: Registrar like/dislike global para uma música
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [youtubeVideoId, action]
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *               action:
 *                 type: string
 *                 enum: [like, dislike]
 *     responses:
 *       200:
 *         description: Contadores atualizados
 */
router.post(
  "/:restaurantId/like",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("youtubeVideoId")
      .notEmpty()
      .withMessage("ID do vídeo é obrigatório"),
    body("action").isIn(["like", "dislike"]).withMessage("Ação inválida"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { youtubeVideoId, action } = req.body as {
      youtubeVideoId: string;
      action: "like" | "dislike";
    };

    const likeKey = `analytics:likes:${restaurantId}:${youtubeVideoId}`;
    const dislikeKey = `analytics:dislikes:${restaurantId}:${youtubeVideoId}`;

    const client = redisClient.getClient();
    if (action === "like") {
      await client.incr(likeKey);
    } else {
      await client.incr(dislikeKey);
    }

    const [likeCountStr, dislikeCountStr] = await Promise.all([
      client.get(likeKey),
      client.get(dislikeKey),
    ]);

    const likeCount = parseInt(likeCountStr || "0", 10) || 0;
    const dislikeCount = parseInt(dislikeCountStr || "0", 10) || 0;

    res.json({
      success: true,
      youtubeVideoId,
      likeCount,
      dislikeCount,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/likes:
 *   get:
 *     summary: Obter contadores globais de likes/dislikes de uma lista de vídeos
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: ids
 *         schema:
 *           type: string
 *           example: "id1,id2,id3"
 *     responses:
 *       200:
 *         description: Contadores por id
 */
router.get(
  "/:restaurantId/likes",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("ids").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const idsParam = (req.query.ids as string) || "";
    const ids = idsParam
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean);

    if (ids.length === 0) {
      return res.json({ success: true, counts: {} });
    }

    const client = redisClient.getClient();
    const likeKeys = ids.map(
      (id) => `analytics:likes:${restaurantId}:${id}`
    );
    const dislikeKeys = ids.map(
      (id) => `analytics:dislikes:${restaurantId}:${id}`
    );

    const [likes, dislikes] = await Promise.all([
      client.mGet(likeKeys),
      client.mGet(dislikeKeys),
    ]);

    const counts: Record<string, { likeCount: number; dislikeCount: number }> = {};
    ids.forEach((id, idx) => {
      counts[id] = {
        likeCount: parseInt(likes?.[idx] || "0", 10) || 0,
        dislikeCount: parseInt(dislikes?.[idx] || "0", 10) || 0,
      };
    });

    res.json({ success: true, counts });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/stats:
 *   get:
 *     summary: Obter estatísticas da fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas da fila
 */
router.get(
  "/:restaurantId/stats",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const stats = await playbackQueueService.getQueueStats(restaurantId);

    res.json({
      success: true,
      stats,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/add/{suggestionId}:
 *   post:
 *     summary: Adicionar música à fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Música adicionada à fila
 */
// Rota para adicionar sugestão existente à fila
router.post(
  "/add/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;

    const queueItem = await playbackQueueService.addToQueue(suggestionId);

    if (!queueItem) {
      throw new NotFoundError("Sugestão não encontrada");
    }

    res.status(201).json({
      success: true,
      message: "Música adicionada à fila",
      queueItem,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/add:
 *   post:
 *     summary: Criar sugestão e adicionar à fila (rota combinada)
 *     tags: [Playback Queue]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               restaurantId:
 *                 type: string
 *               song:
 *                 type: object
 *                 properties:
 *                   videoId:
 *                     type: string
 *                   title:
 *                     type: string
 *                   artist:
 *                     type: string
 *               isPriority:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Música criada e adicionada à fila
 */
router.post(
  "/add",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("song.videoId").notEmpty().withMessage("ID do vídeo é obrigatório"),
    body("song.title").notEmpty().withMessage("Título da música é obrigatório"),
    body("song.artist").notEmpty().withMessage("Artista é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId, song, isPriority = false } = req.body;
    const sessionId = req.headers["x-session-id"] as string;

    if (!sessionId) {
      throw new ValidationError("Session ID é obrigatório");
    }

    // Primeiro, criar a sugestão
    const suggestionData = {
      youtubeVideoId: song.videoId,
      title: song.title,
      artist: song.artist,
      restaurantId,
      isPaid: isPriority,
      clientSessionId: sessionId,
      status: "approved", // Aprovar automaticamente para adicionar à fila
    };

    const queueItem = await playbackQueueService.createSuggestionAndAddToQueue(
      suggestionData
    );

    if (!queueItem) {
      throw new ValidationError("Erro ao criar sugestão e adicionar à fila");
    }

    res.status(201).json({
      success: true,
      message: "Música adicionada à fila",
      queueItem,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/play/{suggestionId}:
 *   post:
 *     summary: Marcar música como tocando
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Música marcada como tocando
 */
router.post(
  "/play/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;

    await playbackQueueService.markAsPlaying(suggestionId);

    res.json({
      success: true,
      message: "Música marcada como tocando",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/complete/{suggestionId}:
 *   post:
 *     summary: Marcar música como concluída
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Música marcada como concluída
 */
router.post(
  "/complete/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;

    await playbackQueueService.markAsCompleted(suggestionId);

    res.json({
      success: true,
      message: "Música marcada como concluída",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/remove/{suggestionId}:
 *   delete:
 *     summary: Remover música da fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 default: "removed by admin"
 *     responses:
 *       200:
 *         description: Música removida da fila
 */
router.delete(
  "/remove/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
    body("reason").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { suggestionId } = req.params;
    const { reason = "removed by admin" } = req.body;

    const success = await playbackQueueService.removeFromQueue(
      suggestionId,
      reason
    );

    if (!success) {
      throw new NotFoundError("Sugestão não encontrada");
    }

    res.json({
      success: true,
      message: "Música removida da fila",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/reorder:
 *   post:
 *     summary: Reordenar fila de reprodução
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newOrder
 *             properties:
 *               newOrder:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array de IDs das sugestões na nova ordem
 *     responses:
 *       200:
 *         description: Fila reordenada
 */
router.post(
  "/:restaurantId/reorder",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("newOrder").isArray().withMessage("Nova ordem deve ser um array"),
    body("newOrder.*").isString().withMessage("IDs devem ser strings"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { newOrder } = req.body;

    const success = await playbackQueueService.reorderQueue(
      restaurantId,
      newOrder
    );

    if (!success) {
      throw new ValidationError("Erro ao reordenar fila");
    }

    res.json({
      success: true,
      message: "Fila reordenada com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/wait-time:
 *   get:
 *     summary: Obter tempo de espera estimado
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: position
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *     responses:
 *       200:
 *         description: Tempo de espera estimado
 */
router.get(
  "/:restaurantId/wait-time",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("position")
      .isInt({ min: 1 })
      .withMessage("Posição deve ser um número positivo"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { position } = req.query;

    const waitTimeSeconds = await playbackQueueService.getEstimatedWaitTime(
      restaurantId,
      parseInt(position as string)
    );

    const waitTimeMinutes = Math.ceil(waitTimeSeconds / 60);
    const waitTimeFormatted =
      waitTimeMinutes > 60
        ? `${Math.floor(waitTimeMinutes / 60)}h ${waitTimeMinutes % 60}m`
        : `${waitTimeMinutes}m`;

    res.json({
      success: true,
      waitTime: {
        seconds: waitTimeSeconds,
        minutes: waitTimeMinutes,
        formatted: waitTimeFormatted,
        position: parseInt(position as string),
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/clear-old:
 *   post:
 *     summary: Limpar músicas antigas da fila
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               olderThanHours:
 *                 type: integer
 *                 default: 24
 *                 minimum: 1
 *     responses:
 *       200:
 *         description: Músicas antigas removidas
 */
router.post(
  "/:restaurantId/clear-old",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("olderThanHours").optional().isInt({ min: 1 }),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { olderThanHours = 24 } = req.body;

    const removedCount = await playbackQueueService.clearOldQueue(
      restaurantId,
      olderThanHours
    );

    res.json({
      success: true,
      message: `${removedCount} músicas antigas removidas da fila`,
      removedCount,
    });
  })
);

/**
 * @swagger
 * /api/v1/playback-queue/{restaurantId}/promote:
 *   post:
 *     summary: Promover música para fila prioritária
 *     tags: [Playback Queue]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               trackId:
 *                 type: string
 *                 description: ID da música a ser promovida
 *     responses:
 *       200:
 *         description: Música promovida com sucesso
 *       404:
 *         description: Música não encontrada
 */
router.post(
  "/:restaurantId/promote",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("trackId").notEmpty().withMessage("ID da música é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { trackId } = req.body;

    try {
      await playbackQueueService.promoteTrack(restaurantId, trackId);

      res.json({
        success: true,
        message: "Música promovida para fila prioritária com sucesso",
      });
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new ValidationError("Erro ao promover música");
    }
  })
);

export default router;
