import{u as U,b as N,c as i,j as e}from"./index-6e2e4ef2.js";import{r as d}from"./vendor-66b0ef43.js";import{af as q,R as z,K as O,o as w,d as Z,m as _,_ as G,aC as H,j as J,u as K,q as X}from"./ui-a5f8f5f0.js";import"./router-f729e475.js";import"./utils-08f61814.js";const te=()=>{const{restaurantId:o}=U(),[y,h]=d.useState([]),[A,x]=d.useState([]),[Q,v]=d.useState(!1);d.useEffect(()=>{console.log("🔍 AdvancedModeration - restaurantId:",o)},[o]);const[u,R]=d.useState("pending"),[l,m]=d.useState({name:"",type:"blacklist",value:""}),[j,b]=d.useState(!1),[g,p]=d.useState({autoApprove:!1,autoReject:!0,requireManualReview:!0,maxDuration:600,minVotesForAutoApproval:10,maxVotesForAutoRejection:-5,explicitContentFilter:!0,workingHours:{enabled:!0,start:"11:00",end:"23:00"}});d.useEffect(()=>{o&&(f(),M())},[o]);const f=async()=>{var r;if(o){v(!0);try{console.log("🔄 Carregando sugestões pendentes para moderação:",o);const a=N(`/suggestions/${o}`,{status:"pending",limit:"50",page:"1"}),t=await fetch(a);if(!t.ok)throw new Error(`Erro ao carregar sugestões: ${t.statusText}`);const n=await t.json();console.log("📡 Sugestões pendentes carregadas:",n);const c=((r=n.suggestions)==null?void 0:r.map(s=>({id:s.id,title:s.title,artist:s.artist,duration:S(s.duration),thumbnailUrl:s.thumbnailUrl||`https://img.youtube.com/vi/${s.youtubeVideoId}/mqdefault.jpg`,submittedBy:s.client_name||`Mesa ${s.table_number}`||"Cliente",submittedAt:s.createdAt,votes:s.voteCount||0,flags:s.moderationFlags||[],riskLevel:C(s),autoModerationResult:s.moderationResult||null})))||[];h(c)}catch(a){console.error("❌ Erro ao carregar sugestões pendentes:",a),i.error("Erro ao carregar sugestões pendentes"),h([])}finally{v(!1)}}},C=r=>{const a=r.moderationFlags||[],t=r.voteCount||0,n=r.duration||0;return a.includes("explicit")||a.includes("inappropriate")||t<-3?"high":n>600||a.length>0||t<0?"medium":"low"},S=r=>{const a=Math.floor(r/60),t=r%60;return`${a}:${t.toString().padStart(2,"0")}`},M=async()=>{if(o)try{console.log("🔄 Carregando regras de moderação para:",o),x([{id:"1",name:"Palavras Proibidas",type:"blacklist",value:"explicit,inappropriate,offensive",isActive:!0,createdAt:"2024-01-10T10:00:00Z"},{id:"2",name:"Gêneros Permitidos",type:"whitelist",value:"rock,pop,jazz,classical,blues",isActive:!0,createdAt:"2024-01-10T10:05:00Z"},{id:"3",name:"Duração Máxima",type:"duration",value:"600",isActive:!0,createdAt:"2024-01-10T10:10:00Z"}])}catch(r){console.error("Erro ao carregar regras:",r)}},k=async(r,a,t)=>{if(o)try{console.log(`🔍 Moderando sugestão ${r}:`,a,t);const n=N(`/suggestions/${o}/${r}/${a}`),c=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:t||`Moderação ${a==="approve"?"aprovada":"rejeitada"} pelo administrador`})});if(!c.ok)throw new Error(`Erro ao moderar sugestão: ${c.statusText}`);const s=await c.json();console.log("✅ Moderação realizada:",s),h(B=>B.filter(I=>I.id!==r)),i.success(`Sugestão ${a==="approve"?"aprovada":"rejeitada"} com sucesso!`),setTimeout(()=>{f()},1e3)}catch(n){console.error("❌ Erro ao moderar sugestão:",n),i.error("Erro ao moderar sugestão")}},$=async()=>{if(!l.name.trim()||!l.value.trim()){i.error("Nome e valor da regra são obrigatórios");return}const r={id:Date.now().toString(),name:l.name,type:l.type,value:l.value,isActive:!0,createdAt:new Date().toISOString()};x(a=>[...a,r]),m({name:"",type:"blacklist",value:""}),b(!1),i.success("Regra adicionada com sucesso!")},E=r=>{x(a=>a.map(t=>t.id===r?{...t,isActive:!t.isActive}:t))},T=r=>{x(a=>a.filter(t=>t.id!==r)),i.success("Regra removida")},D=r=>{switch(r){case"high":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";default:return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"}},L=r=>{switch(r){case"approve":return"text-green-600 dark:text-green-400";case"reject":return"text-red-600 dark:text-red-400";default:return"text-yellow-600 dark:text-yellow-400"}},P=()=>e.jsx("div",{className:"space-y-4",children:y.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(w,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Nenhuma sugestão pendente de moderação"})]}):y.map(r=>e.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("img",{src:r.thumbnailUrl,alt:r.title,className:"w-16 h-12 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:r.title}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[r.artist," • ",r.duration]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:[r.submittedBy," •"," ",new Date(r.submittedAt).toLocaleString("pt-BR")]}),r.flags.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:r.flags.map(a=>e.jsx("span",{className:"px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 text-xs rounded-full",children:a},a))}),r.autoModerationResult&&e.jsxs("div",{className:"mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs",children:[e.jsxs("p",{className:`font-medium ${L(r.autoModerationResult.action)}`,children:["Auto-moderação: ",r.autoModerationResult.action]}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:[r.autoModerationResult.reason," (",r.autoModerationResult.confidence,"% confiança)"]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${D(r.riskLevel)}`,children:r.riskLevel}),e.jsx("div",{className:"text-right",children:e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[r.votes," votos"]})}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:()=>k(r.id,"approve"),className:"p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg",title:"Aprovar",children:e.jsx(G,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>k(r.id,"reject"),className:"p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg",title:"Rejeitar",children:e.jsx(H,{className:"w-4 h-4"})})]})]})]})},r.id))}),V=()=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Regras de Moderação"}),e.jsxs("button",{onClick:()=>b(!j),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2",children:[e.jsx(J,{className:"w-4 h-4"}),e.jsx("span",{children:"Nova Regra"})]})]}),j&&e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsx("input",{type:"text",placeholder:"Nome da regra",value:l.name,onChange:r=>m({...l,name:r.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800"}),e.jsxs("select",{value:l.type,onChange:r=>m({...l,type:r.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800",children:[e.jsx("option",{value:"blacklist",children:"Lista Negra"}),e.jsx("option",{value:"whitelist",children:"Lista Branca"}),e.jsx("option",{value:"duration",children:"Duração"}),e.jsx("option",{value:"explicit",children:"Conteúdo Explícito"}),e.jsx("option",{value:"genre",children:"Gênero"})]}),e.jsx("input",{type:"text",placeholder:"Valor (separado por vírgulas)",value:l.value,onChange:r=>m({...l,value:r.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("button",{onClick:$,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2",children:[e.jsx(K,{className:"w-4 h-4"}),e.jsx("span",{children:"Salvar"})]}),e.jsx("button",{onClick:()=>b(!1),className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:"Cancelar"})]})]}),e.jsx("div",{className:"space-y-3",children:A.map(r=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:r.name}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Tipo: ",r.type," • Valor: ",r.value]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:["Criada em ",new Date(r.createdAt).toLocaleDateString("pt-BR")]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:r.isActive,onChange:()=>E(r.id),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]}),e.jsx("button",{onClick:()=>T(r.id),className:"p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg",children:e.jsx(X,{className:"w-4 h-4"})})]})]},r.id))})]}),F=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Configurações de Moderação"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Aprovação Automática"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Aprovar automaticamente sugestões com muitos votos positivos"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:g.autoApprove,onChange:r=>p(a=>({...a,autoApprove:r.target.checked})),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Rejeição Automática"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Rejeitar automaticamente sugestões com muitos votos negativos"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:g.autoReject,onChange:r=>p(a=>({...a,autoReject:r.target.checked})),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Votos para Aprovação Automática"}),e.jsx("input",{type:"number",value:g.minVotesForAutoApproval,onChange:r=>p(a=>({...a,minVotesForAutoApproval:parseInt(r.target.value)})),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Duração Máxima (segundos)"}),e.jsx("input",{type:"number",value:g.maxDuration,onChange:r=>p(a=>({...a,maxDuration:parseInt(r.target.value)})),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"})]})]})]})]});return o?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Moderação Avançada"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Sistema inteligente de moderação de conteúdo"})]}),e.jsx("button",{onClick:f,className:"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:e.jsx(z,{className:"w-4 h-4"})})]}),e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsx("nav",{className:"flex space-x-8",children:[{id:"pending",name:"Pendentes",icon:O},{id:"rules",name:"Regras",icon:w},{id:"settings",name:"Configurações",icon:Z}].map(r=>e.jsxs("button",{onClick:()=>R(r.id),className:`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${u===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}`,children:[e.jsx(r.icon,{className:"w-4 h-4"}),e.jsx("span",{children:r.name})]},r.id))})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[u==="pending"&&P(),u==="rules"&&V(),u==="settings"&&F()]})]}):e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx(q,{className:"w-12 h-12 text-amber-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Restaurante não encontrado"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Selecione um restaurante para acessar a moderação."})]})})};export{te as default};
//# sourceMappingURL=AdvancedModeration-db913f6a.js.map
