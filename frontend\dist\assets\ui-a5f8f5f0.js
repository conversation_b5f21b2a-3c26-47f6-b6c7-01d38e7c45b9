import{r as v,a as fs}from"./vendor-66b0ef43.js";const ds=v.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Jt=v.createContext({}),te=v.createContext(null),ee=typeof document<"u",je=ee?v.useLayoutEffect:v.useEffect,ps=v.createContext({strict:!1}),Be=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Fi="framerAppearId",ms="data-"+Be(Fi);function Oi(t,e,n,s){const{visualElement:i}=v.useContext(Jt),o=v.useContext(ps),r=v.useContext(te),a=v.useContext(ds).reducedMotion,c=v.useRef();s=s||o.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:i,props:n,presenceContext:r,blockInitialAnimation:r?r.initial===!1:!1,reducedMotionConfig:a}));const l=c.current;v.useInsertionEffect(()=>{l&&l.update(n,r)});const u=v.useRef(!!(n[ms]&&!window.HandoffComplete));return je(()=>{l&&(l.render(),u.current&&l.animationState&&l.animationState.animateChanges())}),v.useEffect(()=>{l&&(l.updateFeatures(),!u.current&&l.animationState&&l.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),l}function mt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Ii(t,e,n){return v.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):mt(n)&&(n.current=s))},[e])}function Lt(t){return typeof t=="string"||Array.isArray(t)}function ne(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const Fe=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Oe=["initial",...Fe];function se(t){return ne(t.animate)||Oe.some(e=>Lt(t[e]))}function ys(t){return!!(se(t)||t.variants)}function zi(t,e){if(se(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Lt(n)?n:void 0,animate:Lt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Ui(t){const{initial:e,animate:n}=zi(t,v.useContext(Jt));return v.useMemo(()=>({initial:e,animate:n}),[cn(e),cn(n)])}function cn(t){return Array.isArray(t)?t.join(" "):t}const ln={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Rt={};for(const t in ln)Rt[t]={isEnabled:e=>ln[t].some(n=>!!e[n])};function Hi(t){for(const e in t)Rt[e]={...Rt[e],...t[e]}}const Ie=v.createContext({}),gs=v.createContext({}),qi=Symbol.for("motionComponentSymbol");function Ni({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Hi(t);function o(a,c){let l;const u={...v.useContext(ds),...a,layoutId:Wi(a)},{isStatic:h}=u,f=Ui(a),d=s(a,h);if(!h&&ee){f.visualElement=Oi(i,d,u,e);const m=v.useContext(gs),y=v.useContext(ps).strict;f.visualElement&&(l=f.visualElement.loadFeatures(u,y,t,m))}return v.createElement(Jt.Provider,{value:f},l&&f.visualElement?v.createElement(l,{visualElement:f.visualElement,...u}):null,n(i,a,Ii(d,f.visualElement,c),d,h,f.visualElement))}const r=v.forwardRef(o);return r[qi]=i,r}function Wi({layoutId:t}){const e=v.useContext(Ie).id;return e&&t!==void 0?e+"-"+t:t}function Gi(t){function e(s,i={}){return Ni(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const $i=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ze(t){return typeof t!="string"||t.includes("-")?!1:!!($i.indexOf(t)>-1||/[A-Z]/.test(t))}const Gt={};function Ki(t){Object.assign(Gt,t)}const jt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ut=new Set(jt);function vs(t,{layout:e,layoutId:n}){return ut.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Gt[t]||t==="opacity")}const I=t=>!!(t&&t.getVelocity),_i={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Zi=jt.length;function Xi(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},s,i){let o="";for(let r=0;r<Zi;r++){const a=jt[r];if(t[a]!==void 0){const c=_i[a]||a;o+=`${c}(${t[a]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(t,s?"":o):n&&s&&(o="none"),o}const xs=t=>e=>typeof e=="string"&&e.startsWith(t),ks=xs("--"),Pe=xs("var(--"),Yi=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Qi=(t,e)=>e&&typeof t=="number"?e.transform(t):t,et=(t,e,n)=>Math.min(Math.max(n,t),e),ht={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Ct={...ht,transform:t=>et(0,1,t)},zt={...ht,default:1},St=t=>Math.round(t*1e5)/1e5,ie=/(-)?([\d]*\.?[\d])+/g,Ps=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Ji=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Bt(t){return typeof t=="string"}const Ft=t=>({test:e=>Bt(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=Ft("deg"),W=Ft("%"),b=Ft("px"),tr=Ft("vh"),er=Ft("vw"),un={...W,parse:t=>W.parse(t)/100,transform:t=>W.transform(t*100)},hn={...ht,transform:Math.round},Ms={borderWidth:b,borderTopWidth:b,borderRightWidth:b,borderBottomWidth:b,borderLeftWidth:b,borderRadius:b,radius:b,borderTopLeftRadius:b,borderTopRightRadius:b,borderBottomRightRadius:b,borderBottomLeftRadius:b,width:b,maxWidth:b,height:b,maxHeight:b,size:b,top:b,right:b,bottom:b,left:b,padding:b,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b,margin:b,marginTop:b,marginRight:b,marginBottom:b,marginLeft:b,rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:zt,scaleX:zt,scaleY:zt,scaleZ:zt,skew:Y,skewX:Y,skewY:Y,distance:b,translateX:b,translateY:b,translateZ:b,x:b,y:b,z:b,perspective:b,transformPerspective:b,opacity:Ct,originX:un,originY:un,originZ:b,zIndex:hn,fillOpacity:Ct,strokeOpacity:Ct,numOctaves:hn};function Ue(t,e,n,s){const{style:i,vars:o,transform:r,transformOrigin:a}=t;let c=!1,l=!1,u=!0;for(const h in e){const f=e[h];if(ks(h)){o[h]=f;continue}const d=Ms[h],m=Qi(f,d);if(ut.has(h)){if(c=!0,r[h]=m,!u)continue;f!==(d.default||0)&&(u=!1)}else h.startsWith("origin")?(l=!0,a[h]=m):i[h]=m}if(e.transform||(c||s?i.transform=Xi(t.transform,n,u,s):i.transform&&(i.transform="none")),l){const{originX:h="50%",originY:f="50%",originZ:d=0}=a;i.transformOrigin=`${h} ${f} ${d}`}}const He=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bs(t,e,n){for(const s in e)!I(e[s])&&!vs(s,n)&&(t[s]=e[s])}function nr({transformTemplate:t},e,n){return v.useMemo(()=>{const s=He();return Ue(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function sr(t,e,n){const s=t.style||{},i={};return bs(i,s,t),Object.assign(i,nr(t,e,n)),t.transformValues?t.transformValues(i):i}function ir(t,e,n){const s={},i=sr(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=i,s}const rr=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function $t(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rr.has(t)}let Ts=t=>!$t(t);function or(t){t&&(Ts=e=>e.startsWith("on")?!$t(e):t(e))}try{or(require("@emotion/is-prop-valid").default)}catch{}function ar(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Ts(i)||n===!0&&$t(i)||!e&&!$t(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function fn(t,e,n){return typeof t=="string"?t:b.transform(e+n*t)}function cr(t,e,n){const s=fn(e,t.x,t.width),i=fn(n,t.y,t.height);return`${s} ${i}`}const lr={offset:"stroke-dashoffset",array:"stroke-dasharray"},ur={offset:"strokeDashoffset",array:"strokeDasharray"};function hr(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?lr:ur;t[o.offset]=b.transform(-s);const r=b.transform(e),a=b.transform(n);t[o.array]=`${r} ${a}`}function qe(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:c=0,...l},u,h,f){if(Ue(t,l,u,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:y}=t;d.transform&&(y&&(m.transform=d.transform),delete d.transform),y&&(i!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=cr(y,i!==void 0?i:.5,o!==void 0?o:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),r!==void 0&&hr(d,r,a,c,!1)}const Vs=()=>({...He(),attrs:{}}),Ne=t=>typeof t=="string"&&t.toLowerCase()==="svg";function fr(t,e,n,s){const i=v.useMemo(()=>{const o=Vs();return qe(o,e,{enableHardwareAcceleration:!1},Ne(s),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};bs(o,t.style,t),i.style={...o,...i.style}}return i}function dr(t=!1){return(n,s,i,{latestValues:o},r)=>{const c=(ze(n)?fr:ir)(s,o,r,n),u={...ar(s,typeof n=="string",t),...c,ref:i},{children:h}=s,f=v.useMemo(()=>I(h)?h.get():h,[h]);return v.createElement(n,{...u,children:f})}}function ws(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}const Cs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ss(t,e,n,s){ws(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Cs.has(i)?i:Be(i),e.attrs[i])}function We(t,e){const{style:n}=t,s={};for(const i in n)(I(n[i])||e.style&&I(e.style[i])||vs(i,t))&&(s[i]=n[i]);return s}function As(t,e){const n=We(t,e);for(const s in t)if(I(t[s])||I(e[s])){const i=jt.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;n[i]=t[s]}return n}function Ge(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}function Ds(t){const e=v.useRef(null);return e.current===null&&(e.current=t()),e.current}const Kt=t=>Array.isArray(t),pr=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),mr=t=>Kt(t)?t[t.length-1]||0:t;function Nt(t){const e=I(t)?t.get():t;return pr(e)?e.toValue():e}function yr({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,o){const r={latestValues:gr(s,i,o,t),renderState:e()};return n&&(r.mount=a=>n(s,a,r)),r}const Ls=t=>(e,n)=>{const s=v.useContext(Jt),i=v.useContext(te),o=()=>yr(t,e,s,i);return n?o():Ds(o)};function gr(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Nt(o[f]);let{initial:r,animate:a}=t;const c=se(t),l=ys(t);e&&l&&!c&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;return h&&typeof h!="boolean"&&!ne(h)&&(Array.isArray(h)?h:[h]).forEach(d=>{const m=Ge(t,d);if(!m)return;const{transitionEnd:y,transition:k,...M}=m;for(const x in M){let g=M[x];if(Array.isArray(g)){const P=u?g.length-1:0;g=g[P]}g!==null&&(i[x]=g)}for(const x in y)i[x]=y[x]}),i}const R=t=>t;class dn{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const n=this.order.indexOf(e);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}function vr(t){let e=new dn,n=new dn,s=0,i=!1,o=!1;const r=new WeakSet,a={schedule:(c,l=!1,u=!1)=>{const h=u&&i,f=h?e:n;return l&&r.add(c),f.add(c)&&h&&i&&(s=e.order.length),c},cancel:c=>{n.remove(c),r.delete(c)},process:c=>{if(i){o=!0;return}if(i=!0,[e,n]=[n,e],n.clear(),s=e.order.length,s)for(let l=0;l<s;l++){const u=e.order[l];u(c),r.has(u)&&(a.schedule(u),t())}i=!1,o&&(o=!1,a.process(c))}};return a}const Ut=["prepare","read","update","preRender","render","postRender"],xr=40;function kr(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Ut.reduce((h,f)=>(h[f]=vr(()=>n=!0),h),{}),r=h=>o[h].process(i),a=()=>{const h=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(h-i.timestamp,xr),1),i.timestamp=h,i.isProcessing=!0,Ut.forEach(r),i.isProcessing=!1,n&&e&&(s=!1,t(a))},c=()=>{n=!0,s=!0,i.isProcessing||t(a)};return{schedule:Ut.reduce((h,f)=>{const d=o[f];return h[f]=(m,y=!1,k=!1)=>(n||c(),d.schedule(m,y,k)),h},{}),cancel:h=>Ut.forEach(f=>o[f].cancel(h)),state:i,steps:o}}const{schedule:C,cancel:Z,state:B,steps:ce}=kr(typeof requestAnimationFrame<"u"?requestAnimationFrame:R,!0),Pr={useVisualState:Ls({scrapeMotionValuesFromProps:As,createRenderState:Vs,onMount:(t,e,{renderState:n,latestValues:s})=>{C.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),C.render(()=>{qe(n,s,{enableHardwareAcceleration:!1},Ne(e.tagName),t.transformTemplate),Ss(e,n)})}})},Mr={useVisualState:Ls({scrapeMotionValuesFromProps:We,createRenderState:He})};function br(t,{forwardMotionProps:e=!1},n,s){return{...ze(t)?Pr:Mr,preloadedFeatures:n,useRender:dr(e),createVisualElement:s,Component:t}}function $(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const Rs=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function re(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const Tr=t=>e=>Rs(e)&&t(e,re(e));function K(t,e,n,s){return $(t,e,Tr(n),s)}const Vr=(t,e)=>n=>e(t(n)),J=(...t)=>t.reduce(Vr);function Es(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const pn=Es("dragHorizontal"),mn=Es("dragVertical");function js(t){let e=!1;if(t==="y")e=mn();else if(t==="x")e=pn();else{const n=pn(),s=mn();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Bs(){const t=js(!0);return t?(t(),!1):!0}class st{constructor(e){this.isMounted=!1,this.node=e}update(){}}function yn(t,e){const n="pointer"+(e?"enter":"leave"),s="onHover"+(e?"Start":"End"),i=(o,r)=>{if(o.pointerType==="touch"||Bs())return;const a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e),a[s]&&C.update(()=>a[s](o,r))};return K(t.current,n,i,{passive:!t.getProps()[s]})}class wr extends st{mount(){this.unmount=J(yn(this.node,!0),yn(this.node,!1))}unmount(){}}class Cr extends st{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=J($(this.node.current,"focus",()=>this.onFocus()),$(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Fs=(t,e)=>e?t===e?!0:Fs(t,e.parentElement):!1;function le(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,re(n))}class Sr extends st{constructor(){super(...arguments),this.removeStartListeners=R,this.removeEndListeners=R,this.removeAccessibleListeners=R,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),o=K(window,"pointerup",(a,c)=>{if(!this.checkPressEnd())return;const{onTap:l,onTapCancel:u,globalTapTarget:h}=this.node.getProps();C.update(()=>{!h&&!Fs(this.node.current,a.target)?u&&u(a,c):l&&l(a,c)})},{passive:!(s.onTap||s.onPointerUp)}),r=K(window,"pointercancel",(a,c)=>this.cancelPress(a,c),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=J(o,r),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=o=>{if(o.key!=="Enter"||this.isPressing)return;const r=a=>{a.key!=="Enter"||!this.checkPressEnd()||le("up",(c,l)=>{const{onTap:u}=this.node.getProps();u&&C.update(()=>u(c,l))})};this.removeEndListeners(),this.removeEndListeners=$(this.node.current,"keyup",r),le("down",(a,c)=>{this.startPress(a,c)})},n=$(this.node.current,"keydown",e),s=()=>{this.isPressing&&le("cancel",(o,r)=>this.cancelPress(o,r))},i=$(this.node.current,"blur",s);this.removeAccessibleListeners=J(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&C.update(()=>s(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Bs()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&C.update(()=>s(e,n))}mount(){const e=this.node.getProps(),n=K(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=$(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=J(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Me=new WeakMap,ue=new WeakMap,Ar=t=>{const e=Me.get(t.target);e&&e(t)},Dr=t=>{t.forEach(Ar)};function Lr({root:t,...e}){const n=t||document;ue.has(n)||ue.set(n,{});const s=ue.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Dr,{root:t,...e})),s[i]}function Rr(t,e,n){const s=Lr(e);return Me.set(t,n),s.observe(t),()=>{Me.delete(t),s.unobserve(t)}}const Er={some:0,all:1};class jr extends st{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Er[i]},a=c=>{const{isIntersecting:l}=c;if(this.isInView===l||(this.isInView=l,o&&!l&&this.hasEnteredView))return;l&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",l);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=l?u:h;f&&f(c)};return Rr(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Br(e,n))&&this.startObserver()}unmount(){}}function Br({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Fr={inView:{Feature:jr},tap:{Feature:Sr},focus:{Feature:Cr},hover:{Feature:wr}};function Os(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function Or(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function Ir(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function oe(t,e,n){const s=t.getProps();return Ge(s,e,n!==void 0?n:s.custom,Or(t),Ir(t))}let zr=R,$e=R;const tt=t=>t*1e3,_=t=>t/1e3,Ur={current:!1},Is=t=>Array.isArray(t)&&typeof t[0]=="number";function zs(t){return!!(!t||typeof t=="string"&&Us[t]||Is(t)||Array.isArray(t)&&t.every(zs))}const wt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Us={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wt([0,.65,.55,1]),circOut:wt([.55,0,1,.45]),backIn:wt([.31,.01,.66,-.59]),backOut:wt([.33,1.53,.69,.99])};function Hs(t){if(t)return Is(t)?wt(t):Array.isArray(t)?t.map(Hs):Us[t]}function Hr(t,e,n,{delay:s=0,duration:i,repeat:o=0,repeatType:r="loop",ease:a,times:c}={}){const l={[e]:n};c&&(l.offset=c);const u=Hs(a);return Array.isArray(u)&&(l.easing=u),t.animate(l,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"})}function qr(t,{repeat:e,repeatType:n="loop"}){const s=e&&n!=="loop"&&e%2===1?0:t.length-1;return t[s]}const qs=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Nr=1e-7,Wr=12;function Gr(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=qs(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>Nr&&++a<Wr);return r}function Ot(t,e,n,s){if(t===e&&n===s)return R;const i=o=>Gr(o,0,1,t,n);return o=>o===0||o===1?o:qs(i(o),e,s)}const $r=Ot(.42,0,1,1),Kr=Ot(0,0,.58,1),Ns=Ot(.42,0,.58,1),_r=t=>Array.isArray(t)&&typeof t[0]!="number",Ws=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Gs=t=>e=>1-t(1-e),Ke=t=>1-Math.sin(Math.acos(t)),$s=Gs(Ke),Zr=Ws(Ke),Ks=Ot(.33,1.53,.69,.99),_e=Gs(Ks),Xr=Ws(_e),Yr=t=>(t*=2)<1?.5*_e(t):.5*(2-Math.pow(2,-10*(t-1))),Qr={linear:R,easeIn:$r,easeInOut:Ns,easeOut:Kr,circIn:Ke,circInOut:Zr,circOut:$s,backIn:_e,backInOut:Xr,backOut:Ks,anticipate:Yr},gn=t=>{if(Array.isArray(t)){$e(t.length===4);const[e,n,s,i]=t;return Ot(e,n,s,i)}else if(typeof t=="string")return Qr[t];return t},Ze=(t,e)=>n=>!!(Bt(n)&&Ji.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),_s=(t,e,n)=>s=>{if(!Bt(s))return s;const[i,o,r,a]=s.match(ie);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},Jr=t=>et(0,255,t),he={...ht,transform:t=>Math.round(Jr(t))},lt={test:Ze("rgb","red"),parse:_s("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+he.transform(t)+", "+he.transform(e)+", "+he.transform(n)+", "+St(Ct.transform(s))+")"};function to(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const be={test:Ze("#"),parse:to,transform:lt.transform},yt={test:Ze("hsl","hue"),parse:_s("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+W.transform(St(e))+", "+W.transform(St(n))+", "+St(Ct.transform(s))+")"},O={test:t=>lt.test(t)||be.test(t)||yt.test(t),parse:t=>lt.test(t)?lt.parse(t):yt.test(t)?yt.parse(t):be.parse(t),transform:t=>Bt(t)?t:t.hasOwnProperty("red")?lt.transform(t):yt.transform(t)},L=(t,e,n)=>-n*t+n*e+t;function fe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function eo({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;i=fe(c,a,t+1/3),o=fe(c,a,t),r=fe(c,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}const de=(t,e,n)=>{const s=t*t;return Math.sqrt(Math.max(0,n*(e*e-s)+s))},no=[be,lt,yt],so=t=>no.find(e=>e.test(t));function vn(t){const e=so(t);let n=e.parse(t);return e===yt&&(n=eo(n)),n}const Zs=(t,e)=>{const n=vn(t),s=vn(e),i={...n};return o=>(i.red=de(n.red,s.red,o),i.green=de(n.green,s.green,o),i.blue=de(n.blue,s.blue,o),i.alpha=L(n.alpha,s.alpha,o),lt.transform(i))};function io(t){var e,n;return isNaN(t)&&Bt(t)&&(((e=t.match(ie))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Ps))===null||n===void 0?void 0:n.length)||0)>0}const Xs={regex:Yi,countKey:"Vars",token:"${v}",parse:R},Ys={regex:Ps,countKey:"Colors",token:"${c}",parse:O.parse},Qs={regex:ie,countKey:"Numbers",token:"${n}",parse:ht.parse};function pe(t,{regex:e,countKey:n,token:s,parse:i}){const o=t.tokenised.match(e);o&&(t["num"+n]=o.length,t.tokenised=t.tokenised.replace(e,s),t.values.push(...o.map(i)))}function _t(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&pe(n,Xs),pe(n,Ys),pe(n,Qs),n}function Js(t){return _t(t).values}function ti(t){const{values:e,numColors:n,numVars:s,tokenised:i}=_t(t),o=e.length;return r=>{let a=i;for(let c=0;c<o;c++)c<s?a=a.replace(Xs.token,r[c]):c<s+n?a=a.replace(Ys.token,O.transform(r[c])):a=a.replace(Qs.token,St(r[c]));return a}}const ro=t=>typeof t=="number"?0:t;function oo(t){const e=Js(t);return ti(t)(e.map(ro))}const nt={test:io,parse:Js,createTransformer:ti,getAnimatableNone:oo},ei=(t,e)=>n=>`${n>0?e:t}`;function ni(t,e){return typeof t=="number"?n=>L(t,e,n):O.test(t)?Zs(t,e):t.startsWith("var(")?ei(t,e):ii(t,e)}const si=(t,e)=>{const n=[...t],s=n.length,i=t.map((o,r)=>ni(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}},ao=(t,e)=>{const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=ni(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}},ii=(t,e)=>{const n=nt.createTransformer(e),s=_t(t),i=_t(e);return s.numVars===i.numVars&&s.numColors===i.numColors&&s.numNumbers>=i.numNumbers?J(si(s.values,i.values),n):ei(t,e)},Et=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},xn=(t,e)=>n=>L(t,e,n);function co(t){return typeof t=="number"?xn:typeof t=="string"?O.test(t)?Zs:ii:Array.isArray(t)?si:typeof t=="object"?ao:xn}function lo(t,e,n){const s=[],i=n||co(t[0]),o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const c=Array.isArray(e)?e[r]||R:e;a=J(c,a)}s.push(a)}return s}function ri(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if($e(o===e.length),o===1)return()=>e[0];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=lo(e,s,i),a=r.length,c=l=>{let u=0;if(a>1)for(;u<t.length-2&&!(l<t[u+1]);u++);const h=Et(t[u],t[u+1],l);return r[u](h)};return n?l=>c(et(t[0],t[o-1],l)):c}function uo(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Et(0,e,s);t.push(L(n,1,i))}}function ho(t){const e=[0];return uo(e,t.length-1),e}function fo(t,e){return t.map(n=>n*e)}function po(t,e){return t.map(()=>e||Ns).splice(0,t.length-1)}function Zt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=_r(s)?s.map(gn):gn(s),o={done:!1,value:e[0]},r=fo(n&&n.length===e.length?n:ho(e),t),a=ri(r,e,{ease:Array.isArray(i)?i:po(e,i)});return{calculatedDuration:t,next:c=>(o.value=a(c),o.done=c>=t,o)}}function oi(t,e){return e?t*(1e3/e):0}const mo=5;function ai(t,e,n){const s=Math.max(e-mo,0);return oi(n-t(s),e-s)}const me=.001,yo=.01,kn=10,go=.05,vo=1;function xo({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,o;zr(t<=tt(kn));let r=1-e;r=et(go,vo,r),t=et(yo,kn,_(t)),r<1?(i=l=>{const u=l*r,h=u*t,f=u-n,d=Te(l,r),m=Math.exp(-h);return me-f/d*m},o=l=>{const h=l*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(l,2)*t,m=Math.exp(-h),y=Te(Math.pow(l,2),r);return(-i(l)+me>0?-1:1)*((f-d)*m)/y}):(i=l=>{const u=Math.exp(-l*t),h=(l-n)*t+1;return-me+u*h},o=l=>{const u=Math.exp(-l*t),h=(n-l)*(t*t);return u*h});const a=5/t,c=Po(i,o,a);if(t=tt(t),isNaN(c))return{stiffness:100,damping:10,duration:t};{const l=Math.pow(c,2)*s;return{stiffness:l,damping:r*2*Math.sqrt(s*l),duration:t}}}const ko=12;function Po(t,e,n){let s=n;for(let i=1;i<ko;i++)s=s-t(s)/e(s);return s}function Te(t,e){return t*Math.sqrt(1-e*e)}const Mo=["duration","bounce"],bo=["stiffness","damping","mass"];function Pn(t,e){return e.some(n=>t[n]!==void 0)}function To(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!Pn(t,bo)&&Pn(t,Mo)){const n=xo(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function ci({keyframes:t,restDelta:e,restSpeed:n,...s}){const i=t[0],o=t[t.length-1],r={done:!1,value:i},{stiffness:a,damping:c,mass:l,duration:u,velocity:h,isResolvedFromDuration:f}=To({...s,velocity:-_(s.velocity||0)}),d=h||0,m=c/(2*Math.sqrt(a*l)),y=o-i,k=_(Math.sqrt(a/l)),M=Math.abs(y)<5;n||(n=M?.01:2),e||(e=M?.005:.5);let x;if(m<1){const g=Te(k,m);x=P=>{const T=Math.exp(-m*k*P);return o-T*((d+m*k*y)/g*Math.sin(g*P)+y*Math.cos(g*P))}}else if(m===1)x=g=>o-Math.exp(-k*g)*(y+(d+k*y)*g);else{const g=k*Math.sqrt(m*m-1);x=P=>{const T=Math.exp(-m*k*P),S=Math.min(g*P,300);return o-T*((d+m*k*y)*Math.sinh(S)+g*y*Math.cosh(S))/g}}return{calculatedDuration:f&&u||null,next:g=>{const P=x(g);if(f)r.done=g>=u;else{let T=d;g!==0&&(m<1?T=ai(x,g,P):T=0);const S=Math.abs(T)<=n,A=Math.abs(o-P)<=e;r.done=S&&A}return r.value=r.done?o:P,r}}}function Mn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:c,restDelta:l=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=V=>a!==void 0&&V<a||c!==void 0&&V>c,m=V=>a===void 0?c:c===void 0||Math.abs(a-V)<Math.abs(c-V)?a:c;let y=n*e;const k=h+y,M=r===void 0?k:r(k);M!==k&&(y=M-h);const x=V=>-y*Math.exp(-V/s),g=V=>M+x(V),P=V=>{const w=x(V),U=g(V);f.done=Math.abs(w)<=l,f.value=f.done?M:U};let T,S;const A=V=>{d(f.value)&&(T=V,S=ci({keyframes:[f.value,m(f.value)],velocity:ai(g,V,f.value),damping:i,stiffness:o,restDelta:l,restSpeed:u}))};return A(0),{calculatedDuration:null,next:V=>{let w=!1;return!S&&T===void 0&&(w=!0,P(V),A(V)),T!==void 0&&V>T?S.next(V-T):(!w&&P(V),f)}}}const Vo=t=>{const e=({timestamp:n})=>t(n);return{start:()=>C.update(e,!0),stop:()=>Z(e),now:()=>B.isProcessing?B.timestamp:performance.now()}},bn=2e4;function Tn(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<bn;)e+=n,s=t.next(e);return e>=bn?1/0:e}const wo={decay:Mn,inertia:Mn,tween:Zt,keyframes:Zt,spring:ci};function Xt({autoplay:t=!0,delay:e=0,driver:n=Vo,keyframes:s,type:i="keyframes",repeat:o=0,repeatDelay:r=0,repeatType:a="loop",onPlay:c,onStop:l,onComplete:u,onUpdate:h,...f}){let d=1,m=!1,y,k;const M=()=>{k=new Promise(D=>{y=D})};M();let x;const g=wo[i]||Zt;let P;g!==Zt&&typeof s[0]!="number"&&(P=ri([0,100],s,{clamp:!1}),s=[0,100]);const T=g({...f,keyframes:s});let S;a==="mirror"&&(S=g({...f,keyframes:[...s].reverse(),velocity:-(f.velocity||0)}));let A="idle",V=null,w=null,U=null;T.calculatedDuration===null&&o&&(T.calculatedDuration=Tn(T));const{calculatedDuration:ft}=T;let N=1/0,G=1/0;ft!==null&&(N=ft+r,G=N*(o+1)-r);let F=0;const dt=D=>{if(w===null)return;d>0&&(w=Math.min(w,D)),d<0&&(w=Math.min(D-G/d,w)),V!==null?F=V:F=Math.round(D-w)*d;const Mt=F-e*(d>=0?1:-1),sn=d>=0?Mt<0:Mt>G;F=Math.max(Mt,0),A==="finished"&&V===null&&(F=G);let rn=F,on=T;if(o){const ae=Math.min(F,G)/N;let It=Math.floor(ae),it=ae%1;!it&&ae>=1&&(it=1),it===1&&It--,It=Math.min(It,o+1),!!(It%2)&&(a==="reverse"?(it=1-it,r&&(it-=r/N)):a==="mirror"&&(on=S)),rn=et(0,1,it)*N}const bt=sn?{done:!1,value:s[0]}:on.next(rn);P&&(bt.value=P(bt.value));let{done:an}=bt;!sn&&ft!==null&&(an=d>=0?F>=G:F<=0);const Bi=V===null&&(A==="finished"||A==="running"&&an);return h&&h(bt.value),Bi&&Pt(),bt},j=()=>{x&&x.stop(),x=void 0},X=()=>{A="idle",j(),y(),M(),w=U=null},Pt=()=>{A="finished",u&&u(),j(),y()},pt=()=>{if(m)return;x||(x=n(dt));const D=x.now();c&&c(),V!==null?w=D-V:(!w||A==="finished")&&(w=D),A==="finished"&&M(),U=w,V=null,A="running",x.start()};t&&pt();const nn={then(D,Mt){return k.then(D,Mt)},get time(){return _(F)},set time(D){D=tt(D),F=D,V!==null||!x||d===0?V=D:w=x.now()-D/d},get duration(){const D=T.calculatedDuration===null?Tn(T):T.calculatedDuration;return _(D)},get speed(){return d},set speed(D){D===d||!x||(d=D,nn.time=_(F))},get state(){return A},play:pt,pause:()=>{A="paused",V=F},stop:()=>{m=!0,A!=="idle"&&(A="idle",l&&l(),X())},cancel:()=>{U!==null&&dt(U),X()},complete:()=>{A="finished"},sample:D=>(w=0,dt(D))};return nn}function Co(t){let e;return()=>(e===void 0&&(e=t()),e)}const So=Co(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ao=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ht=10,Do=2e4,Lo=(t,e)=>e.type==="spring"||t==="backgroundColor"||!zs(e.ease);function Ro(t,e,{onUpdate:n,onComplete:s,...i}){if(!(So()&&Ao.has(e)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let r=!1,a,c,l=!1;const u=()=>{c=new Promise(g=>{a=g})};u();let{keyframes:h,duration:f=300,ease:d,times:m}=i;if(Lo(e,i)){const g=Xt({...i,repeat:0,delay:0});let P={done:!1,value:h[0]};const T=[];let S=0;for(;!P.done&&S<Do;)P=g.sample(S),T.push(P.value),S+=Ht;m=void 0,h=T,f=S-Ht,d="linear"}const y=Hr(t.owner.current,e,h,{...i,duration:f,ease:d,times:m}),k=()=>{l=!1,y.cancel()},M=()=>{l=!0,C.update(k),a(),u()};return y.onfinish=()=>{l||(t.set(qr(h,i)),s&&s(),M())},{then(g,P){return c.then(g,P)},attachTimeline(g){return y.timeline=g,y.onfinish=null,R},get time(){return _(y.currentTime||0)},set time(g){y.currentTime=tt(g)},get speed(){return y.playbackRate},set speed(g){y.playbackRate=g},get duration(){return _(f)},play:()=>{r||(y.play(),Z(k))},pause:()=>y.pause(),stop:()=>{if(r=!0,y.playState==="idle")return;const{currentTime:g}=y;if(g){const P=Xt({...i,autoplay:!1});t.setWithVelocity(P.sample(g-Ht).value,P.sample(g).value,Ht)}M()},complete:()=>{l||y.finish()},cancel:M}}function Eo({keyframes:t,delay:e,onUpdate:n,onComplete:s}){const i=()=>(n&&n(t[t.length-1]),s&&s(),{time:0,speed:1,duration:0,play:R,pause:R,stop:R,then:o=>(o(),Promise.resolve()),cancel:R,complete:R});return e?Xt({keyframes:[0,1],duration:0,delay:e,onComplete:i}):i()}const jo={type:"spring",stiffness:500,damping:25,restSpeed:10},Bo=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Fo={type:"keyframes",duration:.8},Oo={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Io=(t,{keyframes:e})=>e.length>2?Fo:ut.has(t)?t.startsWith("scale")?Bo(e[1]):jo:Oo,Ve=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(nt.test(e)||e==="0")&&!e.startsWith("url(")),zo=new Set(["brightness","contrast","saturate","opacity"]);function Uo(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(ie)||[];if(!s)return t;const i=n.replace(s,"");let o=zo.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const Ho=/([a-z-]*)\(.*?\)/g,we={...nt,getAnimatableNone:t=>{const e=t.match(Ho);return e?e.map(Uo).join(" "):t}},qo={...Ms,color:O,backgroundColor:O,outlineColor:O,fill:O,stroke:O,borderColor:O,borderTopColor:O,borderRightColor:O,borderBottomColor:O,borderLeftColor:O,filter:we,WebkitFilter:we},Xe=t=>qo[t];function li(t,e){let n=Xe(t);return n!==we&&(n=nt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const ui=t=>/^0[^.\s]+$/.test(t);function No(t){if(typeof t=="number")return t===0;if(t!==null)return t==="none"||t==="0"||ui(t)}function Wo(t,e,n,s){const i=Ve(e,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const r=s.from!==void 0?s.from:t.get();let a;const c=[];for(let l=0;l<o.length;l++)o[l]===null&&(o[l]=l===0?r:o[l-1]),No(o[l])&&c.push(l),typeof o[l]=="string"&&o[l]!=="none"&&o[l]!=="0"&&(a=o[l]);if(i&&c.length&&a)for(let l=0;l<c.length;l++){const u=c[l];o[u]=li(e,a)}return o}function Go({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:c,elapsed:l,...u}){return!!Object.keys(u).length}function Ye(t,e){return t[e]||t.default||t}const $o={skipAnimations:!1},Qe=(t,e,n,s={})=>i=>{const o=Ye(s,t)||{},r=o.delay||s.delay||0;let{elapsed:a=0}=s;a=a-tt(r);const c=Wo(e,t,n,o),l=c[0],u=c[c.length-1],h=Ve(t,l),f=Ve(t,u);let d={keyframes:c,velocity:e.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:m=>{e.set(m),o.onUpdate&&o.onUpdate(m)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(Go(o)||(d={...d,...Io(t,d)}),d.duration&&(d.duration=tt(d.duration)),d.repeatDelay&&(d.repeatDelay=tt(d.repeatDelay)),!h||!f||Ur.current||o.type===!1||$o.skipAnimations)return Eo(d);if(!s.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const m=Ro(e,t,d);if(m)return m}return Xt(d)};function Yt(t){return!!(I(t)&&t.add)}const hi=t=>/^\-?\d*\.?\d+$/.test(t);function Je(t,e){t.indexOf(e)===-1&&t.push(e)}function tn(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class en{constructor(){this.subscriptions=[]}add(e){return Je(this.subscriptions,e),()=>tn(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ko=t=>!isNaN(parseFloat(t));class _o{constructor(e,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{this.prev=this.current,this.current=s;const{delta:o,timestamp:r}=B;this.lastUpdated!==r&&(this.timeDelta=o,this.lastUpdated=r,C.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>C.postRender(this.velocityCheck),this.velocityCheck=({timestamp:s})=>{s!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Ko(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new en);const s=this.events[e].add(n);return e==="change"?()=>{s(),C.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?oi(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function xt(t,e){return new _o(t,e)}const fi=t=>e=>e.test(t),Zo={test:t=>t==="auto",parse:t=>t},di=[ht,b,W,Y,er,tr,Zo],Tt=t=>di.find(fi(t)),Xo=[...di,O,nt],Yo=t=>Xo.find(fi(t));function Qo(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,xt(n))}function Jo(t,e){const n=oe(t,e);let{transitionEnd:s={},transition:i={},...o}=n?t.makeTargetAnimatable(n,!1):{};o={...o,...s};for(const r in o){const a=mr(o[r]);Qo(t,r,a)}}function ta(t,e,n){var s,i;const o=Object.keys(e).filter(a=>!t.hasValue(a)),r=o.length;if(r)for(let a=0;a<r;a++){const c=o[a],l=e[c];let u=null;Array.isArray(l)&&(u=l[0]),u===null&&(u=(i=(s=n[c])!==null&&s!==void 0?s:t.readValue(c))!==null&&i!==void 0?i:e[c]),u!=null&&(typeof u=="string"&&(hi(u)||ui(u))?u=parseFloat(u):!Yo(u)&&nt.test(l)&&(u=li(c,l)),t.addValue(c,xt(u,{owner:t})),n[c]===void 0&&(n[c]=u),u!==null&&t.setBaseTarget(c,u))}}function ea(t,e){return e?(e[t]||e.default||e).from:void 0}function na(t,e,n){const s={};for(const i in t){const o=ea(i,e);if(o!==void 0)s[i]=o;else{const r=n.getValue(i);r&&(s[i]=r.get())}}return s}function sa({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function ia(t,e){const n=t.get();if(Array.isArray(e)){for(let s=0;s<e.length;s++)if(e[s]!==n)return!0}else return n!==e}function pi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=t.makeTargetAnimatable(e);const c=t.getValue("willChange");s&&(o=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const h in a){const f=t.getValue(h),d=a[h];if(!f||d===void 0||u&&sa(u,h))continue;const m={delay:n,elapsed:0,...Ye(o||{},h)};if(window.HandoffAppearAnimations){const M=t.getProps()[ms];if(M){const x=window.HandoffAppearAnimations(M,h,f,C);x!==null&&(m.elapsed=x,m.isHandoff=!0)}}let y=!m.isHandoff&&!ia(f,d);if(m.type==="spring"&&(f.getVelocity()||m.velocity)&&(y=!1),f.animation&&(y=!1),y)continue;f.start(Qe(h,f,d,t.shouldReduceMotion&&ut.has(h)?{type:!1}:m));const k=f.animation;Yt(c)&&(c.add(h),k.then(()=>c.remove(h))),l.push(k)}return r&&Promise.all(l).then(()=>{r&&Jo(t,r)}),l}function Ce(t,e,n={}){const s=oe(t,e,n.custom);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(pi(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:l=0,staggerChildren:u,staggerDirection:h}=i;return ra(t,e,l+c,u,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[c,l]=a==="beforeChildren"?[o,r]:[r,o];return c().then(()=>l())}else return Promise.all([o(),r(n.delay)])}function ra(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,c=i===1?(l=0)=>l*s:(l=0)=>a-l*s;return Array.from(t.variantChildren).sort(oa).forEach((l,u)=>{l.notify("AnimationStart",e),r.push(Ce(l,e,{...o,delay:n+c(u)}).then(()=>l.notify("AnimationComplete",e)))}),Promise.all(r)}function oa(t,e){return t.sortNodePosition(e)}function aa(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>Ce(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=Ce(t,e,n);else{const i=typeof e=="function"?oe(t,e,n.custom):e;s=Promise.all(pi(t,i,n))}return s.then(()=>t.notify("AnimationComplete",e))}const ca=[...Fe].reverse(),la=Fe.length;function ua(t){return e=>Promise.all(e.map(({animation:n,options:s})=>aa(t,n,s)))}function ha(t){let e=ua(t);const n=da();let s=!0;const i=(c,l)=>{const u=oe(t,l);if(u){const{transition:h,transitionEnd:f,...d}=u;c={...c,...d,...f}}return c};function o(c){e=c(t)}function r(c,l){const u=t.getProps(),h=t.getVariantContext(!0)||{},f=[],d=new Set;let m={},y=1/0;for(let M=0;M<la;M++){const x=ca[M],g=n[x],P=u[x]!==void 0?u[x]:h[x],T=Lt(P),S=x===l?g.isActive:null;S===!1&&(y=M);let A=P===h[x]&&P!==u[x]&&T;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),g.protectedKeys={...m},!g.isActive&&S===null||!P&&!g.prevProp||ne(P)||typeof P=="boolean")continue;let w=fa(g.prevProp,P)||x===l&&g.isActive&&!A&&T||M>y&&T,U=!1;const ft=Array.isArray(P)?P:[P];let N=ft.reduce(i,{});S===!1&&(N={});const{prevResolvedValues:G={}}=g,F={...G,...N},dt=j=>{w=!0,d.has(j)&&(U=!0,d.delete(j)),g.needsAnimating[j]=!0};for(const j in F){const X=N[j],Pt=G[j];if(m.hasOwnProperty(j))continue;let pt=!1;Kt(X)&&Kt(Pt)?pt=!Os(X,Pt):pt=X!==Pt,pt?X!==void 0?dt(j):d.add(j):X!==void 0&&d.has(j)?dt(j):g.protectedKeys[j]=!0}g.prevProp=P,g.prevResolvedValues=N,g.isActive&&(m={...m,...N}),s&&t.blockInitialAnimation&&(w=!1),w&&(!A||U)&&f.push(...ft.map(j=>({animation:j,options:{type:x,...c}})))}if(d.size){const M={};d.forEach(x=>{const g=t.getBaseTarget(x);g!==void 0&&(M[x]=g)}),f.push({animation:M})}let k=!!f.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(k=!1),s=!1,k?e(f):Promise.resolve()}function a(c,l,u){var h;if(n[c].isActive===l)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(d=>{var m;return(m=d.animationState)===null||m===void 0?void 0:m.setActive(c,l)}),n[c].isActive=l;const f=r(u,c);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n}}function fa(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Os(e,t):!1}function rt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function da(){return{animate:rt(!0),whileInView:rt(),whileHover:rt(),whileTap:rt(),whileDrag:rt(),whileFocus:rt(),exit:rt()}}class pa extends st{constructor(e){super(e),e.animationState||(e.animationState=ha(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),ne(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let ma=0;class ya extends st{constructor(){super(...arguments),this.id=ma++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n,custom:s}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const o=this.node.animationState.setActive("exit",!e,{custom:s??this.node.getProps().custom});n&&!e&&o.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const ga={animation:{Feature:pa},exit:{Feature:ya}},Vn=(t,e)=>Math.abs(t-e);function va(t,e){const n=Vn(t.x,e.x),s=Vn(t.y,e.y);return Math.sqrt(n**2+s**2)}class mi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ge(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=va(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:y}=B;this.history.push({...m,timestamp:y});const{onStart:k,onMove:M}=this.handlers;f||(k&&k(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),M&&M(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ye(f,this.transformPagePoint),C.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const k=ge(h.type==="pointercancel"?this.lastMoveEventInfo:ye(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,k),m&&m(h,k)},!Rs(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=re(e),a=ye(r,this.transformPagePoint),{point:c}=a,{timestamp:l}=B;this.history=[{...c,timestamp:l}];const{onSessionStart:u}=n;u&&u(e,ge(a,this.history)),this.removeListeners=J(K(this.contextWindow,"pointermove",this.handlePointerMove),K(this.contextWindow,"pointerup",this.handlePointerUp),K(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function ye(t,e){return e?{point:e(t.point)}:t}function wn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ge({point:t},e){return{point:t,delta:wn(t,yi(e)),offset:wn(t,xa(e)),velocity:ka(e,.1)}}function xa(t){return t[0]}function yi(t){return t[t.length-1]}function ka(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=yi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>tt(e)));)n--;if(!s)return{x:0,y:0};const o=_(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function z(t){return t.max-t.min}function Se(t,e=0,n=.01){return Math.abs(t-e)<=n}function Cn(t,e,n,s=.5){t.origin=s,t.originPoint=L(e.min,e.max,t.origin),t.scale=z(n)/z(e),(Se(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=L(n.min,n.max,t.origin)-t.originPoint,(Se(t.translate)||isNaN(t.translate))&&(t.translate=0)}function At(t,e,n,s){Cn(t.x,e.x,n.x,s?s.originX:void 0),Cn(t.y,e.y,n.y,s?s.originY:void 0)}function Sn(t,e,n){t.min=n.min+e.min,t.max=t.min+z(e)}function Pa(t,e,n){Sn(t.x,e.x,n.x),Sn(t.y,e.y,n.y)}function An(t,e,n){t.min=e.min-n.min,t.max=t.min+z(e)}function Dt(t,e,n){An(t.x,e.x,n.x),An(t.y,e.y,n.y)}function Ma(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?L(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?L(n,t,s.max):Math.min(t,n)),t}function Dn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function ba(t,{top:e,left:n,bottom:s,right:i}){return{x:Dn(t.x,n,i),y:Dn(t.y,e,s)}}function Ln(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Ta(t,e){return{x:Ln(t.x,e.x),y:Ln(t.y,e.y)}}function Va(t,e){let n=.5;const s=z(t),i=z(e);return i>s?n=Et(e.min,e.max-s,t.min):s>i&&(n=Et(t.min,t.max-i,e.min)),et(0,1,n)}function wa(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Ae=.35;function Ca(t=Ae){return t===!1?t=0:t===!0&&(t=Ae),{x:Rn(t,"left","right"),y:Rn(t,"top","bottom")}}function Rn(t,e,n){return{min:En(t,e),max:En(t,n)}}function En(t,e){return typeof t=="number"?t:t[e]||0}const jn=()=>({translate:0,scale:1,origin:0,originPoint:0}),gt=()=>({x:jn(),y:jn()}),Bn=()=>({min:0,max:0}),E=()=>({x:Bn(),y:Bn()});function q(t){return[t("x"),t("y")]}function gi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Sa({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Aa(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function ve(t){return t===void 0||t===1}function De({scale:t,scaleX:e,scaleY:n}){return!ve(t)||!ve(e)||!ve(n)}function ot(t){return De(t)||vi(t)||t.z||t.rotate||t.rotateX||t.rotateY}function vi(t){return Fn(t.x)||Fn(t.y)}function Fn(t){return t&&t!=="0%"}function Qt(t,e,n){const s=t-n,i=e*s;return n+i}function On(t,e,n,s,i){return i!==void 0&&(t=Qt(t,i,s)),Qt(t,n,s)+e}function Le(t,e=0,n=1,s,i){t.min=On(t.min,e,n,s,i),t.max=On(t.max,e,n,s,i)}function xi(t,{x:e,y:n}){Le(t.x,e.translate,e.scale,e.originPoint),Le(t.y,n.translate,n.scale,n.originPoint)}function Da(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const c=o.instance;c&&c.style&&c.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&vt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,xi(t,r)),s&&ot(o.latestValues)&&vt(t,o.latestValues))}e.x=In(e.x),e.y=In(e.y)}function In(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Q(t,e){t.min=t.min+e,t.max=t.max+e}function zn(t,e,[n,s,i]){const o=e[i]!==void 0?e[i]:.5,r=L(t.min,t.max,o);Le(t,e[n],e[s],r,e.scale)}const La=["x","scaleX","originX"],Ra=["y","scaleY","originY"];function vt(t,e){zn(t.x,e,La),zn(t.y,e,Ra)}function ki(t,e){return gi(Aa(t.getBoundingClientRect(),e))}function Ea(t,e,n){const s=ki(t,n),{scroll:i}=e;return i&&(Q(s.x,i.offset.x),Q(s.y,i.offset.y)),s}const Pi=({current:t})=>t?t.ownerDocument.defaultView:null,ja=new WeakMap;class Ba{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=E(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(re(u,"page").point)},o=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=js(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),q(k=>{let M=this.getAxisMotionValue(k).get()||0;if(W.test(M)){const{projection:x}=this.visualElement;if(x&&x.layout){const g=x.layout.layoutBox[k];g&&(M=z(g)*(parseFloat(M)/100))}}this.originPoint[k]=M}),m&&C.update(()=>m(u,h),!1,!0);const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:y}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:k}=h;if(d&&this.currentDirection===null){this.currentDirection=Fa(k),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,k),this.updateAxis("y",h.point,k),this.visualElement.render(),y&&y(u,h)},a=(u,h)=>this.stop(u,h),c=()=>q(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:l}=this.getProps();this.panSession=new mi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,contextWindow:Pi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&C.update(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!qt(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=Ma(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;n&&mt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=ba(i.layoutBox,n):this.constraints=!1,this.elastic=Ca(s),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&q(r=>{this.getAxisMotionValue(r)&&(this.constraints[r]=wa(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!mt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Ea(s,i.root,this.visualElement.getTransformPagePoint());let r=Ta(i.layout.layoutBox,o);if(n){const a=n(Sa(r));this.hasMutatedConstraints=!!a,a&&(r=gi(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},l=q(u=>{if(!qt(u,n,this.currentDirection))return;let h=c&&c[u]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(l).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(Qe(e,s,0,n))}stopAnimation(){q(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){q(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n="_drag"+e.toUpperCase(),s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){q(n=>{const{drag:s}=this.getProps();if(!qt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-L(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!mt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};q(r=>{const a=this.getAxisMotionValue(r);if(a){const c=a.get();i[r]=Va({min:c,max:c},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),q(r=>{if(!qt(r,e,null))return;const a=this.getAxisMotionValue(r),{min:c,max:l}=this.constraints[r];a.set(L(c,l,i[r]))})}addListeners(){if(!this.visualElement.current)return;ja.set(this.visualElement,this);const e=this.visualElement.current,n=K(e,"pointerdown",c=>{const{drag:l,dragListener:u=!0}=this.getProps();l&&u&&this.start(c)}),s=()=>{const{dragConstraints:c}=this.getProps();mt(c)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),s();const r=$(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:c,hasLayoutChanged:l})=>{this.isDragging&&l&&(q(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=c[u].translate,h.set(h.get()+c[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=Ae,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function qt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Fa(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Oa extends st{constructor(e){super(e),this.removeGroupControls=R,this.removeListeners=R,this.controls=new Ba(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||R}unmount(){this.removeGroupControls(),this.removeListeners()}}const Un=t=>(e,n)=>{t&&C.update(()=>t(e,n))};class Ia extends st{constructor(){super(...arguments),this.removePointerDownListener=R}onPointerDown(e){this.session=new mi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Pi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:Un(e),onStart:Un(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&C.update(()=>i(o,r))}}}mount(){this.removePointerDownListener=K(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function za(){const t=v.useContext(te);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=v.useId();return v.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}const Wt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Hn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Vt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(b.test(t))t=parseFloat(t);else return t;const n=Hn(t,e.target.x),s=Hn(t,e.target.y);return`${n}% ${s}%`}},Ua={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=nt.parse(t);if(i.length>5)return s;const o=nt.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,c=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=c;const l=L(a,c,.5);return typeof i[2+r]=="number"&&(i[2+r]/=l),typeof i[3+r]=="number"&&(i[3+r]/=l),o(i)}};class Ha extends fs.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;Ki(qa),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Wt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,r=s.projection;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||C.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Mi(t){const[e,n]=za(),s=v.useContext(Ie);return fs.createElement(Ha,{...t,layoutGroup:s,switchLayoutGroup:v.useContext(gs),isPresent:e,safeToRemove:n})}const qa={borderRadius:{...Vt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Vt,borderTopRightRadius:Vt,borderBottomLeftRadius:Vt,borderBottomRightRadius:Vt,boxShadow:Ua},bi=["TopLeft","TopRight","BottomLeft","BottomRight"],Na=bi.length,qn=t=>typeof t=="string"?parseFloat(t):t,Nn=t=>typeof t=="number"||b.test(t);function Wa(t,e,n,s,i,o){i?(t.opacity=L(0,n.opacity!==void 0?n.opacity:1,Ga(s)),t.opacityExit=L(e.opacity!==void 0?e.opacity:1,0,$a(s))):o&&(t.opacity=L(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let r=0;r<Na;r++){const a=`border${bi[r]}Radius`;let c=Wn(e,a),l=Wn(n,a);if(c===void 0&&l===void 0)continue;c||(c=0),l||(l=0),c===0||l===0||Nn(c)===Nn(l)?(t[a]=Math.max(L(qn(c),qn(l),s),0),(W.test(l)||W.test(c))&&(t[a]+="%")):t[a]=l}(e.rotate||n.rotate)&&(t.rotate=L(e.rotate||0,n.rotate||0,s))}function Wn(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Ga=Ti(0,.5,$s),$a=Ti(.5,.95,R);function Ti(t,e,n){return s=>s<t?0:s>e?1:n(Et(t,e,s))}function Gn(t,e){t.min=e.min,t.max=e.max}function H(t,e){Gn(t.x,e.x),Gn(t.y,e.y)}function $n(t,e,n,s,i){return t-=e,t=Qt(t,1/n,s),i!==void 0&&(t=Qt(t,1/i,s)),t}function Ka(t,e=0,n=1,s=.5,i,o=t,r=t){if(W.test(e)&&(e=parseFloat(e),e=L(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=L(o.min,o.max,s);t===o&&(a-=e),t.min=$n(t.min,e,n,a,i),t.max=$n(t.max,e,n,a,i)}function Kn(t,e,[n,s,i],o,r){Ka(t,e[n],e[s],e[i],e.scale,o,r)}const _a=["x","scaleX","originX"],Za=["y","scaleY","originY"];function _n(t,e,n,s){Kn(t.x,e,_a,n?n.x:void 0,s?s.x:void 0),Kn(t.y,e,Za,n?n.y:void 0,s?s.y:void 0)}function Zn(t){return t.translate===0&&t.scale===1}function Vi(t){return Zn(t.x)&&Zn(t.y)}function Xa(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function wi(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Xn(t){return z(t.x)/z(t.y)}class Ya{constructor(){this.members=[]}add(e){Je(this.members,e),e.scheduleRender()}remove(e){if(tn(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Yn(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y;if((i||o)&&(s=`translate3d(${i}px, ${o}px, 0) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:c,rotateX:l,rotateY:u}=n;c&&(s+=`rotate(${c}deg) `),l&&(s+=`rotateX(${l}deg) `),u&&(s+=`rotateY(${u}deg) `)}const r=t.x.scale*e.x,a=t.y.scale*e.y;return(r!==1||a!==1)&&(s+=`scale(${r}, ${a})`),s||"none"}const Qa=(t,e)=>t.depth-e.depth;class Ja{constructor(){this.children=[],this.isDirty=!1}add(e){Je(this.children,e),this.isDirty=!0}remove(e){tn(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Qa),this.isDirty=!1,this.children.forEach(e)}}function tc(t,e){const n=performance.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(Z(s),t(o-e))};return C.read(s,!0),()=>Z(s)}function ec(t){window.MotionDebug&&window.MotionDebug.record(t)}function nc(t){return t instanceof SVGElement&&t.tagName!=="svg"}function sc(t,e,n){const s=I(t)?t:xt(t);return s.start(Qe("",s,e,n)),s.animation}const Qn=["","X","Y","Z"],ic={visibility:"hidden"},Jn=1e3;let rc=0;const at={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Ci({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=rc++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,at.totalNodes=at.resolvedTargetDeltas=at.recalculatedProjection=0,this.nodes.forEach(cc),this.nodes.forEach(dc),this.nodes.forEach(pc),this.nodes.forEach(lc),ec(at)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ja)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new en),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const c=this.eventHandlers.get(r);c&&c.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=nc(r),this.instance=r;const{layoutId:c,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(l||c)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=tc(f,250),Wt.hasAnimatedSinceResize&&(Wt.hasAnimatedSinceResize=!1,this.nodes.forEach(es))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&u&&(c||l)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||u.getDefaultTransition()||xc,{onLayoutAnimationStart:k,onLayoutAnimationComplete:M}=u.getProps(),x=!this.targetLayout||!wi(this.targetLayout,m)||d,g=!f&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,g);const P={...Ye(y,"layout"),onPlay:k,onComplete:M};(u.shouldReduceMotion||this.options.layoutRoot)&&(P.delay=0,P.type=!1),this.startAnimation(P)}else f||es(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(mc),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:c}=this.options;if(a===void 0&&!c)return;const l=this.getTransformTemplate();this.prevTransformTemplateValue=l?l(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ts);return}this.isUpdating||this.nodes.forEach(hc),this.isUpdating=!1,this.nodes.forEach(fc),this.nodes.forEach(oc),this.nodes.forEach(ac),this.clearAllSnapshots();const a=performance.now();B.delta=et(0,1e3/60,a-B.timestamp),B.timestamp=a,B.isProcessing=!0,ce.update.process(B),ce.preRender.process(B),ce.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(uc),this.sharedNodes.forEach(yc)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,C.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){C.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=E(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:r,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Vi(this.projectionDelta),c=this.getTransformTemplate(),l=c?c(this.latestValues,""):void 0,u=l!==this.prevTransformTemplateValue;r&&(a||ot(this.latestValues)||u)&&(i(this.instance,l),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return r&&(c=this.removeTransform(c)),kc(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:r}=this.options;if(!r)return E();const a=r.measureViewportBox(),{scroll:c}=this.root;return c&&(Q(a.x,c.offset.x),Q(a.y,c.offset.y)),a}removeElementScroll(r){const a=E();H(a,r);for(let c=0;c<this.path.length;c++){const l=this.path[c],{scroll:u,options:h}=l;if(l!==this.root&&u&&h.layoutScroll){if(u.isRoot){H(a,r);const{scroll:f}=this.root;f&&(Q(a.x,-f.offset.x),Q(a.y,-f.offset.y))}Q(a.x,u.offset.x),Q(a.y,u.offset.y)}}return a}applyTransform(r,a=!1){const c=E();H(c,r);for(let l=0;l<this.path.length;l++){const u=this.path[l];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&vt(c,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),ot(u.latestValues)&&vt(c,u.latestValues)}return ot(this.latestValues)&&vt(c,this.latestValues),c}removeTransform(r){const a=E();H(a,r);for(let c=0;c<this.path.length;c++){const l=this.path[c];if(!l.instance||!ot(l.latestValues))continue;De(l.latestValues)&&l.updateSnapshot();const u=E(),h=l.measurePageBox();H(u,h),_n(a,l.latestValues,l.snapshot?l.snapshot.layoutBox:void 0,u)}return ot(this.latestValues)&&_n(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var a;const c=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=c.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=c.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=c.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==c;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=E(),this.relativeTargetOrigin=E(),Dt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),H(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=E(),this.targetWithTransforms=E()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Pa(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):H(this.target,this.layout.layoutBox),xi(this.target,this.targetDelta)):H(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=E(),this.relativeTargetOrigin=E(),Dt(this.relativeTargetOrigin,this.target,d.target),H(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}at.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||De(this.parent.latestValues)||vi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var r;const a=this.getLead(),c=!!this.resumingFrom||this!==a;let l=!0;if((this.isProjectionDirty||!((r=this.parent)===null||r===void 0)&&r.isProjectionDirty)&&(l=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===B.timestamp&&(l=!1),l)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;H(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Da(this.layoutCorrected,this.treeScale,this.path,c),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:m}=a;if(!m){this.projectionTransform&&(this.projectionDelta=gt(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=gt(),this.projectionDeltaWithTransform=gt());const y=this.projectionTransform;At(this.projectionDelta,this.layoutCorrected,m,this.latestValues),this.projectionTransform=Yn(this.projectionDelta,this.treeScale),(this.projectionTransform!==y||this.treeScale.x!==f||this.treeScale.y!==d)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m)),at.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),r){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(r,a=!1){const c=this.snapshot,l=c?c.latestValues:{},u={...this.latestValues},h=gt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=E(),d=c?c.source:void 0,m=this.layout?this.layout.source:void 0,y=d!==m,k=this.getStack(),M=!k||k.members.length<=1,x=!!(y&&!M&&this.options.crossfade===!0&&!this.path.some(vc));this.animationProgress=0;let g;this.mixTargetDelta=P=>{const T=P/1e3;ns(h.x,r.x,T),ns(h.y,r.y,T),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),gc(this.relativeTarget,this.relativeTargetOrigin,f,T),g&&Xa(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=E()),H(g,this.relativeTarget)),y&&(this.animationValues=u,Wa(u,l,this.latestValues,T,x,M)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=C.update(()=>{Wt.hasAnimatedSinceResize=!0,this.currentAnimation=sc(0,Jn,{...r,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Jn),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:c,layout:l,latestValues:u}=r;if(!(!a||!c||!l)){if(this!==r&&this.layout&&l&&Si(this.options.animationType,this.layout.layoutBox,l.layoutBox)){c=this.target||E();const h=z(this.layout.layoutBox.x);c.x.min=r.target.x.min,c.x.max=c.x.min+h;const f=z(this.layout.layoutBox.y);c.y.min=r.target.y.min,c.y.max=c.y.min+f}H(a,c),vt(a,u),At(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new Ya),this.sharedNodes.get(r).add(a);const l=a.options.initialPromotionConfig;a.promote({transition:l?l.transition:void 0,preserveFollowOpacity:l&&l.shouldPreserveFollowOpacity?l.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var r;const{layoutId:a}=this.options;return a?((r=this.getStack())===null||r===void 0?void 0:r.lead)||this:this}getPrevLead(){var r;const{layoutId:a}=this.options;return a?(r=this.getStack())===null||r===void 0?void 0:r.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:c}={}){const l=this.getStack();l&&l.promote(this,c),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:c}=r;if((c.rotate||c.rotateX||c.rotateY||c.rotateZ)&&(a=!0),!a)return;const l={};for(let u=0;u<Qn.length;u++){const h="rotate"+Qn[u];c[h]&&(l[h]=c[h],r.setStaticValue(h,0))}r.render();for(const u in l)r.setStaticValue(u,l[u]);r.scheduleRender()}getProjectionStyles(r){var a,c;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ic;const l={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,l.opacity="",l.pointerEvents=Nt(r==null?void 0:r.pointerEvents)||"",l.transform=u?u(this.latestValues,""):"none",l;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=Nt(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!ot(this.latestValues)&&(y.transform=u?u({},""):"none",this.hasProjected=!1),y}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),l.transform=Yn(this.projectionDeltaWithTransform,this.treeScale,f),u&&(l.transform=u(f,l.transform));const{x:d,y:m}=this.projectionDelta;l.transformOrigin=`${d.origin*100}% ${m.origin*100}% 0`,h.animationValues?l.opacity=h===this?(c=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:l.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in Gt){if(f[y]===void 0)continue;const{correct:k,applyTo:M}=Gt[y],x=l.transform==="none"?f[y]:k(f[y],h);if(M){const g=M.length;for(let P=0;P<g;P++)l[M[P]]=x}else l[y]=x}return this.options.layoutId&&(l.pointerEvents=h===this?Nt(r==null?void 0:r.pointerEvents)||"":"none"),l}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(ts),this.root.sharedNodes.clear()}}}function oc(t){t.updateLayout()}function ac(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=n.source!==t.layout.source;o==="size"?q(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=z(f);f.min=s[h].min,f.max=f.min+d}):Si(o,n.layoutBox,s)&&q(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=z(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=gt();At(a,s,n.layoutBox);const c=gt();r?At(c,t.applyTransform(i,!0),n.measuredBox):At(c,s,n.layoutBox);const l=!Vi(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=E();Dt(m,n.layoutBox,f.layoutBox);const y=E();Dt(y,s,d.layoutBox),wi(m,y)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=m,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:c,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function cc(t){at.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function lc(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function uc(t){t.clearSnapshot()}function ts(t){t.clearMeasurements()}function hc(t){t.isLayoutDirty=!1}function fc(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function es(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function dc(t){t.resolveTargetDelta()}function pc(t){t.calcProjection()}function mc(t){t.resetRotation()}function yc(t){t.removeLeadSnapshot()}function ns(t,e,n){t.translate=L(e.translate,0,n),t.scale=L(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function ss(t,e,n,s){t.min=L(e.min,n.min,s),t.max=L(e.max,n.max,s)}function gc(t,e,n,s){ss(t.x,e.x,n.x,s),ss(t.y,e.y,n.y,s)}function vc(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const xc={duration:.45,ease:[.4,0,.1,1]},is=t=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(t),rs=is("applewebkit/")&&!is("chrome/")?Math.round:R;function os(t){t.min=rs(t.min),t.max=rs(t.max)}function kc(t){os(t.x),os(t.y)}function Si(t,e,n){return t==="position"||t==="preserve-aspect"&&!Se(Xn(e),Xn(n),.2)}const Pc=Ci({attachResizeListener:(t,e)=>$(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),xe={current:void 0},Ai=Ci({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!xe.current){const t=new Pc({});t.mount(window),t.setOptions({layoutScroll:!0}),xe.current=t}return xe.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Mc={pan:{Feature:Ia},drag:{Feature:Oa,ProjectionNode:Ai,MeasureLayout:Mi}},bc=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Tc(t){const e=bc.exec(t);if(!e)return[,];const[,n,s]=e;return[n,s]}function Re(t,e,n=1){const[s,i]=Tc(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return hi(r)?parseFloat(r):r}else return Pe(i)?Re(i,e,n+1):i}function Vc(t,{...e},n){const s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const o=i.get();if(!Pe(o))return;const r=Re(o,s);r&&i.set(r)});for(const i in e){const o=e[i];if(!Pe(o))continue;const r=Re(o,s);r&&(e[i]=r,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:e,transitionEnd:n}}const wc=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Di=t=>wc.has(t),Cc=t=>Object.keys(t).some(Di),as=t=>t===ht||t===b,cs=(t,e)=>parseFloat(t.split(", ")[e]),ls=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/);if(i)return cs(i[1],e);{const o=s.match(/^matrix\((.+)\)$/);return o?cs(o[1],t):0}},Sc=new Set(["x","y","z"]),Ac=jt.filter(t=>!Sc.has(t));function Dc(t){const e=[];return Ac.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const kt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:ls(4,13),y:ls(5,14)};kt.translateX=kt.x;kt.translateY=kt.y;const Lc=(t,e,n)=>{const s=e.measureViewportBox(),i=e.current,o=getComputedStyle(i),{display:r}=o,a={};r==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(l=>{a[l]=kt[l](s,o)}),e.render();const c=e.measureViewportBox();return n.forEach(l=>{const u=e.getValue(l);u&&u.jump(a[l]),t[l]=kt[l](c,o)}),t},Rc=(t,e,n={},s={})=>{e={...e},s={...s};const i=Object.keys(e).filter(Di);let o=[],r=!1;const a=[];if(i.forEach(c=>{const l=t.getValue(c);if(!t.hasValue(c))return;let u=n[c],h=Tt(u);const f=e[c];let d;if(Kt(f)){const m=f.length,y=f[0]===null?1:0;u=f[y],h=Tt(u);for(let k=y;k<m&&f[k]!==null;k++)d?$e(Tt(f[k])===d):d=Tt(f[k])}else d=Tt(f);if(h!==d)if(as(h)&&as(d)){const m=l.get();typeof m=="string"&&l.set(parseFloat(m)),typeof f=="string"?e[c]=parseFloat(f):Array.isArray(f)&&d===b&&(e[c]=f.map(parseFloat))}else h!=null&&h.transform&&(d!=null&&d.transform)&&(u===0||f===0)?u===0?l.set(d.transform(u)):e[c]=h.transform(f):(r||(o=Dc(t),r=!0),a.push(c),s[c]=s[c]!==void 0?s[c]:e[c],l.jump(f))}),a.length){const c=a.indexOf("height")>=0?window.pageYOffset:null,l=Lc(e,t,a);return o.length&&o.forEach(([u,h])=>{t.getValue(u).set(h)}),t.render(),ee&&c!==null&&window.scrollTo({top:c}),{target:l,transitionEnd:s}}else return{target:e,transitionEnd:s}};function Ec(t,e,n,s){return Cc(e)?Rc(t,e,n,s):{target:e,transitionEnd:s}}const jc=(t,e,n,s)=>{const i=Vc(t,e,s);return e=i.target,s=i.transitionEnd,Ec(t,e,n,s)},Ee={current:null},Li={current:!1};function Bc(){if(Li.current=!0,!!ee)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ee.current=t.matches;t.addListener(e),e()}else Ee.current=!1}function Fc(t,e,n){const{willChange:s}=e;for(const i in e){const o=e[i],r=n[i];if(I(o))t.addValue(i,o),Yt(s)&&s.add(i);else if(I(r))t.addValue(i,xt(o,{owner:t})),Yt(s)&&s.remove(i);else if(r!==o)if(t.hasValue(i)){const a=t.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=t.getStaticValue(i);t.addValue(i,xt(a!==void 0?a:o,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const us=new WeakMap,Ri=Object.keys(Rt),Oc=Ri.length,hs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Ic=Oe.length;class zc{constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>C.render(this.render,!1,!0);const{latestValues:a,renderState:c}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.isControllingVariants=se(n),this.isVariantNode=ys(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(n,{});for(const h in u){const f=u[h];a[h]!==void 0&&I(f)&&(f.set(a[h],!1),Yt(l)&&l.add(h))}}scrapeMotionValuesFromProps(e,n){return{}}mount(e){this.current=e,us.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Li.current||Bc(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ee.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){us.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,n){const s=ut.has(e),i=n.on("change",r=>{this.latestValues[e]=r,this.props.onUpdate&&C.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),o()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},s,i,o){let r,a;for(let c=0;c<Oc;c++){const l=Ri[c],{isEnabled:u,Feature:h,ProjectionNode:f,MeasureLayout:d}=Rt[l];f&&(r=f),u(n)&&(!this.features[l]&&h&&(this.features[l]=new h(this)),d&&(a=d))}if((this.type==="html"||this.type==="svg")&&!this.projection&&r){this.projection=new r(this.latestValues,this.parent&&this.parent.projection);const{layoutId:c,layout:l,drag:u,dragConstraints:h,layoutScroll:f,layoutRoot:d}=n;this.projection.setOptions({layoutId:c,layout:l,alwaysMeasureLayout:!!u||h&&mt(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof l=="string"?l:"both",initialPromotionConfig:o,layoutScroll:f,layoutRoot:d})}return a}updateFeatures(){for(const e in this.features){const n=this.features[e];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):E()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<hs.length;s++){const i=hs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=e["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Fc(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const s=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(s.initial=this.props.initial),s}const n={};for(let s=0;s<Ic;s++){const i=Oe[s],o=this.props[i];(Lt(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=xt(n,{owner:this}),this.addValue(e,s)),s}readValue(e){var n;return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props,i=typeof s=="string"||typeof s=="object"?(n=Ge(this.props,s))===null||n===void 0?void 0:n[e]:void 0;if(s&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!I(o)?o:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new en),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Ei extends zc{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s},{transformValues:i},o){let r=na(s,e||{},this);if(i&&(n&&(n=i(n)),s&&(s=i(s)),r&&(r=i(r))),o){ta(this,s,r);const a=jc(this,s,r,n);n=a.transitionEnd,s=a.target}return{transition:e,transitionEnd:n,...s}}}function Uc(t){return window.getComputedStyle(t)}class Hc extends Ei{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,n){if(ut.has(n)){const s=Xe(n);return s&&s.default||0}else{const s=Uc(e),i=(ks(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return ki(e,n)}build(e,n,s,i){Ue(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,n){return We(e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;I(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,s,i){ws(e,n,s,i)}}class qc extends Ei{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(ut.has(n)){const s=Xe(n);return s&&s.default||0}return n=Cs.has(n)?n:Be(n),e.getAttribute(n)}measureInstanceViewportBox(){return E()}scrapeMotionValuesFromProps(e,n){return As(e,n)}build(e,n,s,i){qe(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){Ss(e,n,s,i)}mount(e){this.isSVGTag=Ne(e.tagName),super.mount(e)}}const Nc=(t,e)=>ze(t)?new qc(e,{enableHardwareAcceleration:!1}):new Hc(e,{enableHardwareAcceleration:!0}),Wc={layout:{ProjectionNode:Ai,MeasureLayout:Mi}},Gc={...ga,...Fr,...Mc,...Wc},rl=Gi((t,e)=>br(t,e,Gc,Nc));function ji(){const t=v.useRef(!1);return je(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function $c(){const t=ji(),[e,n]=v.useState(0),s=v.useCallback(()=>{t.current&&n(e+1)},[e]);return[v.useCallback(()=>C.postRender(s),[s]),e]}class Kc extends v.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function _c({children:t,isPresent:e}){const n=v.useId(),s=v.useRef(null),i=v.useRef({width:0,height:0,top:0,left:0});return v.useInsertionEffect(()=>{const{width:o,height:r,top:a,left:c}=i.current;if(e||!s.current||!o||!r)return;s.current.dataset.motionPopId=n;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${r}px !important;
            top: ${a}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[e]),v.createElement(Kc,{isPresent:e,childRef:s,sizeRef:i},v.cloneElement(t,{ref:s}))}const ke=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:o,mode:r})=>{const a=Ds(Zc),c=v.useId(),l=v.useMemo(()=>({id:c,initial:e,isPresent:n,custom:i,onExitComplete:u=>{a.set(u,!0);for(const h of a.values())if(!h)return;s&&s()},register:u=>(a.set(u,!1),()=>a.delete(u))}),o?void 0:[n]);return v.useMemo(()=>{a.forEach((u,h)=>a.set(h,!1))},[n]),v.useEffect(()=>{!n&&!a.size&&s&&s()},[n]),r==="popLayout"&&(t=v.createElement(_c,{isPresent:n},t)),v.createElement(te.Provider,{value:l},t)};function Zc(){return new Map}function Xc(t){return v.useEffect(()=>()=>t(),[])}const ct=t=>t.key||"";function Yc(t,e){t.forEach(n=>{const s=ct(n);e.set(s,n)})}function Qc(t){const e=[];return v.Children.forEach(t,n=>{v.isValidElement(n)&&e.push(n)}),e}const ol=({children:t,custom:e,initial:n=!0,onExitComplete:s,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:r="sync"})=>{const a=v.useContext(Ie).forceRender||$c()[0],c=ji(),l=Qc(t);let u=l;const h=v.useRef(new Map).current,f=v.useRef(u),d=v.useRef(new Map).current,m=v.useRef(!0);if(je(()=>{m.current=!1,Yc(l,d),f.current=u}),Xc(()=>{m.current=!0,d.clear(),h.clear()}),m.current)return v.createElement(v.Fragment,null,u.map(x=>v.createElement(ke,{key:ct(x),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:o,mode:r},x)));u=[...u];const y=f.current.map(ct),k=l.map(ct),M=y.length;for(let x=0;x<M;x++){const g=y[x];k.indexOf(g)===-1&&!h.has(g)&&h.set(g,void 0)}return r==="wait"&&h.size&&(u=[]),h.forEach((x,g)=>{if(k.indexOf(g)!==-1)return;const P=d.get(g);if(!P)return;const T=y.indexOf(g);let S=x;if(!S){const A=()=>{h.delete(g);const V=Array.from(d.keys()).filter(w=>!k.includes(w));if(V.forEach(w=>d.delete(w)),f.current=l.filter(w=>{const U=ct(w);return U===g||V.includes(U)}),!h.size){if(c.current===!1)return;a(),s&&s()}};S=v.createElement(ke,{key:ct(P),isPresent:!1,onExitComplete:A,custom:e,presenceAffectsLayout:o,mode:r},P),h.set(g,S)}u.splice(T,0,S)}),u=u.map(x=>{const g=x.key;return h.has(g)?x:v.createElement(ke,{key:ct(x),isPresent:!0,presenceAffectsLayout:o,mode:r},x)}),v.createElement(v.Fragment,null,h.size?u:u.map(x=>v.cloneElement(x)))};var Jc={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const tl=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),el=(t,e)=>{const n=v.forwardRef(({color:s="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:r,children:a,...c},l)=>v.createElement("svg",{ref:l,...Jc,width:i,height:i,stroke:s,strokeWidth:r?Number(o)*24/Number(i):o,className:`lucide lucide-${tl(t)}`,...c},[...e.map(([u,h])=>v.createElement(u,h)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${t}`,n};var p=el;const al=p("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),cl=p("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),ll=p("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ul=p("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),hl=p("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),fl=p("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),dl=p("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),pl=p("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),ml=p("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]),yl=p("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),gl=p("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]),vl=p("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),xl=p("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]),kl=p("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),Pl=p("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Ml=p("Check",[["polyline",{points:"20 6 9 17 4 12",key:"10jjfj"}]]),bl=p("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Tl=p("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Vl=p("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),wl=p("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),Cl=p("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),Sl=p("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]),Al=p("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),Dl=p("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),Ll=p("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),Rl=p("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),El=p("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),jl=p("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Bl=p("FileImage",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["circle",{cx:"10",cy:"13",r:"2",key:"6v46hv"}],["path",{d:"m20 17-1.09-1.09a2 2 0 0 0-2.82 0L10 22",key:"17vly1"}]]),Fl=p("Gift",[["polyline",{points:"20 12 20 22 4 22 4 12",key:"nda8fc"}],["rect",{width:"20",height:"5",x:"2",y:"7",key:"wkgdzj"}],["line",{x1:"12",x2:"12",y1:"22",y2:"7",key:"1n8zgp"}],["path",{d:"M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z",key:"zighg4"}],["path",{d:"M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z",key:"1pa5tk"}]]),Ol=p("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",key:"nb9nel"}]]),Il=p("Grid",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"3",x2:"21",y1:"9",y2:"9",key:"1vqk6q"}],["line",{x1:"3",x2:"21",y1:"15",y2:"15",key:"o2sbyz"}],["line",{x1:"9",x2:"9",y1:"3",y2:"21",key:"13tij5"}],["line",{x1:"15",x2:"15",y1:"3",y2:"21",key:"1hpv9i"}]]),zl=p("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),Ul=p("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),Hl=p("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),ql=p("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),Nl=p("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]),Wl=p("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Gl=p("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),$l=p("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),Kl=p("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),_l=p("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),Zl=p("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),Xl=p("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),Yl=p("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]),Ql=p("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]),Jl=p("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),tu=p("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),eu=p("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),nu=p("Minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]),su=p("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]),iu=p("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]),ru=p("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]),ou=p("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]),au=p("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),cu=p("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),lu=p("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]]),uu=p("PlayCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]),hu=p("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),fu=p("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),du=p("Power",[["path",{d:"M18.36 6.64a9 9 0 1 1-12.73 0",key:"phirl6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"12",key:"aemgbe"}]]),pu=p("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]),mu=p("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),yu=p("Repeat",[["path",{d:"m17 2 4 4-4 4",key:"nntrym"}],["path",{d:"M3 11v-1a4 4 0 0 1 4-4h14",key:"84bu3i"}],["path",{d:"m7 22-4-4 4-4",key:"1wqhfi"}],["path",{d:"M21 13v1a4 4 0 0 1-4 4H3",key:"1rx37r"}]]),gu=p("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),vu=p("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]),xu=p("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),ku=p("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Pu=p("Settings2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]]),Mu=p("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),bu=p("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),Tu=p("ShieldOff",[["path",{d:"M19.69 14a6.9 6.9 0 0 0 .31-2V5l-8-3-3.16 1.18",key:"ungvgc"}],["path",{d:"M4.73 4.73 4 5v7c0 6 8 10 8 10a20.29 20.29 0 0 0 5.62-4.38",key:"1qf5yw"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),Vu=p("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]),wu=p("Shuffle",[["path",{d:"M2 18h1.4c1.3 0 2.5-.6 3.3-1.7l6.1-8.6c.7-1.1 2-1.7 3.3-1.7H22",key:"1wmou1"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 6h1.9c1.5 0 2.9.9 3.6 2.2",key:"10bdb2"}],["path",{d:"M22 18h-5.9c-1.3 0-2.6-.7-3.3-1.8l-.5-.8",key:"vgxac0"}],["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}]]),Cu=p("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]),Su=p("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]),Au=p("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),Du=p("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),Lu=p("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),Ru=p("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]),Eu=p("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]),ju=p("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),Bu=p("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),Fu=p("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]),Ou=p("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Iu=p("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),zu=p("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),Uu=p("Unlock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]),Hu=p("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),qu=p("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Nu=p("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Wu=p("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),Gu=p("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),$u=p("Vote",[["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}],["path",{d:"M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z",key:"1ezoue"}],["path",{d:"M22 19H2",key:"nuriw5"}]]),Ku=p("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),_u=p("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),Zu=p("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Xu=p("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Yu=p("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]),Qu=p("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);export{nu as $,ol as A,xl as B,Cl as C,Ll as D,El as E,uu as F,Ol as G,Ul as H,Lu as I,Bu as J,Vl as K,$l as L,_l as M,Ku as N,hl as O,hu as P,pu as Q,mu as R,Du as S,Iu as T,Nu as U,$u as V,_u as W,Xu as X,Au as Y,wl as Z,Pl as _,Gl as a,Yl as a0,tu as a1,gu as a2,Gu as a3,Wu as a4,bl as a5,Tl as a6,zu as a7,ml as a8,Ql as a9,ul as aA,Fl as aB,Zu as aC,Eu as aD,ou as aE,Bl as aF,cu as aG,Zl as aH,Ml as aI,vu as aJ,Ru as aK,iu as aL,Sl as aa,zl as ab,Fu as ac,Jl as ad,ql as ae,ll as af,Hu as ag,dl as ah,Il as ai,Nl as aj,du as ak,Yu as al,kl as am,Qu as an,gl as ao,eu as ap,Xl as aq,wu as ar,Cu as as,Su as at,yu as au,bu as av,Wl as aw,Pu as ax,Uu as ay,pl as az,jl as b,su as c,Mu as d,fl as e,yl as f,ju as g,ku as h,cl as i,fu as j,Rl as k,au as l,rl as m,ru as n,Vu as o,Tu as p,Ou as q,lu as r,Dl as s,al as t,xu as u,Al as v,vl as w,qu as x,Kl as y,Hl as z};
//# sourceMappingURL=ui-a5f8f5f0.js.map
